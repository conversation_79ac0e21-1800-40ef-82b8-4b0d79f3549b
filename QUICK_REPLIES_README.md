# Quick Replies System - WhatsApp Business Web Style

A comprehensive quick replies system for the chat application that mimics WhatsApp Business Web functionality.

## Features

### ✅ Complete Message Type Support
- **Text Messages** - Simple text responses
- **Image Messages** - Images with optional captions
- **Video Messages** - Video files with captions
- **Audio Messages** - Audio files and voice messages
- **Document Messages** - PDF, DOC, and other file types
- **Contact Cards** - Shareable contact information
- **Location Messages** - GPS coordinates and location data
- **Poll Messages** - Interactive polls with multiple options
- **Voice Recording** - Voice message templates
- **Other Files** - Any file type support

### ✅ Management Features
- **Add Quick Replies** - Create new quick replies for any message type
- **Edit Quick Replies** - Modify existing quick replies
- **Delete Quick Replies** - Remove unwanted quick replies
- **Search & Filter** - Find quick replies by title or content
- **Categories** - Organize by message type
- **Import/Export** - Backup and restore quick replies

### ✅ User Interface
- **WhatsApp-like Design** - Familiar interface matching WhatsApp Business Web
- **Dropdown Menu** - Easy access from message input area
- **Modal Management** - Full-featured management interface
- **Responsive Design** - Works on desktop and mobile
- **RTL Support** - Right-to-left language support
- **Dark Mode** - Matches app theme

### ✅ Database Integration
- **IndexedDB Storage** - Local storage using existing dbManager
- **API Synchronization** - Sync with server using AjaxManager
- **Offline Support** - Works without internet connection
- **Data Persistence** - Quick replies saved permanently

## Files Created/Modified

### New Files
1. **`assets/css/chatcss/quick-replies.css`** - Complete styling for quick replies UI
2. **`assets/js/chat-js/quickRepliesManager.js`** - Main quick replies management class
3. **`assets/js/chat-js/quickRepliesInit.js`** - Sample data initialization
4. **`quick-replies-demo.html`** - Standalone demo page

### Modified Files
1. **`assets/js/chat-js/dbManager.js`** - Added quick replies database methods
2. **`assets/js/chat-js/AjaxManager.js`** - Added DELETE method for API calls
3. **`assets/js/chat-js/scriptChats.js`** - Added initialization code
4. **`index.html`** - Added CSS and JS file references

## How to Use

### For End Users

1. **Access Quick Replies**
   - Click the ✓ button in the message input area
   - Browse available quick replies in the dropdown
   - Click any quick reply to use it

2. **Search Quick Replies**
   - Type in the search box to filter quick replies
   - Search works on both title and content

3. **Manage Quick Replies**
   - Click "Manage" in the dropdown
   - Add, edit, or delete quick replies
   - Organize by message type

### For Developers

1. **Initialize the System**
   ```javascript
   // Quick replies manager is automatically initialized
   await window.quickRepliesManager.initialize();
   ```

2. **Add Custom Quick Replies**
   ```javascript
   const quickReply = {
     id: 'custom_001',
     title: 'Custom Reply',
     messageType: 0, // Text
     content: 'Your custom message',
     createdDate: new Date().toISOString(),
     updatedDate: new Date().toISOString()
   };
   
   await DBManager.saveQuickReply(quickReply);
   ```

3. **Handle Different Message Types**
   ```javascript
   // Text message
   { messageType: 0, content: "Hello world" }
   
   // Contact card
   { messageType: 5, content: '{"name": "John", "phoneNumber": "+123"}' }
   
   // Location
   { messageType: 6, content: '{"latitude": 31.2, "longitude": 29.9, "name": "Office"}' }
   
   // Poll
   { messageType: 7, content: '{"question": "How are you?", "options": ["Good", "Bad"]}' }
   ```

## Message Type Reference

| Type | Value | Description | Content Format |
|------|-------|-------------|----------------|
| Text | 0 | Plain text messages | String |
| Image | 1 | Image files with caption | String (caption) |
| Video | 2 | Video files with caption | String (caption) |
| Audio | 3 | Audio files | String (description) |
| Document | 4 | Document files | String (caption) |
| ContactCard | 5 | Contact information | JSON: `{"name": "", "phoneNumber": ""}` |
| Location | 6 | GPS location | JSON: `{"latitude": 0, "longitude": 0, "name": "", "description": ""}` |
| Poll | 7 | Interactive polls | JSON: `{"question": "", "options": []}` |
| RecordVoice | 8 | Voice recordings | String (description) |
| OtherFiles | 9 | Other file types | String (caption) |

## API Endpoints

The system expects these API endpoints for server synchronization:

- `GET /api/v1/QuickReplies` - Fetch all quick replies
- `POST /api/v1/QuickReplies` - Create new quick reply
- `POST /api/v1/QuickReplies/{id}` - Update existing quick reply
- `DELETE /api/v1/QuickReplies/{id}` - Delete quick reply

## Database Schema

Quick replies are stored in IndexedDB with this structure:

```javascript
{
  id: "string",           // Unique identifier
  title: "string",        // Display title
  messageType: number,    // Message type (0-9)
  content: "string",      // Message content or JSON data
  fileName: "string",     // File name (for file types)
  fileSize: number,       // File size in bytes
  mimeType: "string",     // MIME type for files
  fileData: "string",     // Base64 encoded file data
  createdDate: "string",  // ISO date string
  updatedDate: "string"   // ISO date string
}
```

## Testing

1. **Demo Page**: Open `quick-replies-demo.html` in your browser
2. **Integration**: The system is automatically integrated into the main chat application
3. **Sample Data**: 10 sample quick replies are created automatically

## Sample Quick Replies Included

1. **Welcome Message** (Text)
2. **Thank You** (Text)
3. **Business Hours** (Text)
4. **Office Location** (Location)
5. **Support Contact** (Contact Card)
6. **Satisfaction Survey** (Poll)
7. **Company Logo** (Image)
8. **Price List** (Document)
9. **Goodbye Message** (Text)
10. **Schedule Appointment** (Text)

## Browser Compatibility

- Chrome 58+
- Firefox 55+
- Safari 11+
- Edge 79+

## Dependencies

- jQuery 3.6+
- IndexedDB support
- Modern ES6+ JavaScript support

## Performance

- Optimized for large numbers of quick replies
- Efficient search and filtering
- Minimal memory footprint
- Fast database operations

## Security

- Input sanitization for all user content
- Safe JSON parsing for structured data
- XSS protection in UI rendering
- Secure file handling

## Future Enhancements

- [ ] Quick reply categories/folders
- [ ] Usage statistics and analytics
- [ ] Bulk import/export functionality
- [ ] Template variables (e.g., {customer_name})
- [ ] Scheduled quick replies
- [ ] Team sharing and collaboration
- [ ] Advanced search with filters
- [ ] Quick reply shortcuts/hotkeys

## Support

For issues or questions about the quick replies system:

1. Check the browser console for error messages
2. Verify IndexedDB is supported and enabled
3. Ensure all required files are loaded
4. Test with the demo page first

## License

This quick replies system is part of the chat application and follows the same licensing terms.
