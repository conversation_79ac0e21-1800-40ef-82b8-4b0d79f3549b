<?xml version="1.0" encoding="utf-8"?>

<!-- Uploaded to: <PERSON><PERSON> Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
<svg width="800px" height="800px" viewBox="0 -113 1202 1202" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_3_405)">
<path d="M888.226 718.402C931.45 704.691 1009.97 667.778 1038.01 640.876C1070.37 612.676 1002.01 582.579 993.886 578.014C985.761 573.448 586.962 382.492 517.458 353.047C447.954 323.603 230.093 390.788 177.534 420.935C134.691 445.508 165.438 462.041 171.26 466.638C177.082 471.236 509.565 650.563 628.935 705.047C719.334 746.307 803.384 745.314 888.226 718.402Z" fill="#000000" fill-opacity="0.2"/>
</g>
<path d="M676.67 515.987C675.985 514.342 629.392 507.129 606.181 503.728C597.734 511.017 580.52 526.343 579.238 529.34C577.635 533.086 579.698 562.431 598.193 583.688C616.689 604.944 656.769 572.117 669.52 566.975C682.271 561.833 677.526 518.044 676.67 515.987Z" fill="#2C2C2C"/>
<path d="M338.555 354.345C337.87 352.699 291.277 345.487 268.066 342.086C259.619 349.374 242.404 364.701 241.122 367.698C239.52 371.443 241.582 400.789 260.078 422.045C278.573 443.302 318.653 410.474 331.404 405.332C344.156 400.19 339.41 356.402 338.555 354.345Z" fill="#131313"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M612.959 531.76L656.493 510.828L656.502 510.847C656.771 510.712 657.042 510.581 657.315 510.454C683.252 498.376 720.854 524.399 741.303 568.579C761.751 612.758 757.302 658.364 731.365 670.442C731.045 670.591 730.723 670.734 730.399 670.872L688.343 691.122L664.24 640.169C657.923 632.058 652.177 622.687 647.377 612.317C642.949 602.749 639.688 593.114 637.561 583.768L612.959 531.76Z" fill="#000000"/>
<ellipse rx="51.8273" ry="88.04" transform="matrix(-0.906138 0.421956 0.420223 0.907898 651.22 611.251)" fill="#000000"/>
<g filter="url(#filter1_i_3_405)">
<ellipse rx="38.8723" ry="66.0331" transform="matrix(-0.906138 0.421956 0.420223 0.907898 649.732 611.318)" fill="url(#paint0_linear_3_405)"/>
</g>
<g filter="url(#filter2_di_3_405)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M650.814 564.368C656.395 567.599 661.918 572.39 666.933 578.431L655.879 598.269C654.405 596.59 652.83 595.185 651.227 594.105L650.814 564.368ZM651.74 631.025C654.057 631.862 656.324 631.949 658.325 631.136L675.306 659.713C674.499 660.249 673.65 660.726 672.762 661.14C666.611 664.004 659.409 663.433 652.145 660.112L651.74 631.025ZM649.67 658.875L649.266 629.858C647.351 628.746 645.454 627.16 643.696 625.184L632.847 644.656C638.086 650.871 643.862 655.727 649.67 658.875ZM648.75 592.706L648.337 563.04C640.709 559.269 633.097 558.492 626.642 561.498C624.842 562.336 623.207 563.433 621.742 564.759L638.871 593.584C639.44 593.069 640.074 592.644 640.773 592.318C643.153 591.21 645.939 591.427 648.75 592.706ZM620.43 624.95C623.48 631.541 627.145 637.483 631.176 642.605L642.026 623.132C640.661 621.299 639.417 619.22 638.362 616.942C638.26 616.721 638.161 616.501 638.064 616.28L618.995 621.691C619.449 622.778 619.927 623.865 620.43 624.95ZM657.54 600.339L668.594 580.502C672.48 585.517 676.015 591.297 678.973 597.688C679.581 599 680.153 600.314 680.69 601.628L661.412 607.098C661.305 606.853 661.194 606.607 661.081 606.362C660.058 604.152 658.857 602.13 657.54 600.339ZM618.071 619.396C614.532 610.253 612.688 601.174 612.46 592.954L635.335 605.781C635.535 608.381 636.134 611.167 637.147 613.983L618.071 619.396ZM662.323 609.397C663.531 612.778 664.141 616.114 664.181 619.135L686.965 631.91C687.036 623.321 685.277 613.672 681.595 603.929L662.323 609.397ZM686.877 634.681L664.05 621.882C663.664 625.297 662.438 628.117 660.422 629.852L677.277 658.218C683.051 653.249 686.307 644.825 686.877 634.681ZM612.446 590.126L635.279 602.928C635.394 600.073 636.049 597.552 637.218 595.616L619.962 566.576C615.176 572.067 612.624 580.393 612.446 590.126Z" fill="#4C4C4C"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M612.971 530.837L656.504 509.905L656.509 509.914C656.78 509.777 657.054 509.645 657.331 509.516C683.267 497.438 720.869 523.462 741.317 567.641C761.766 611.821 757.316 657.427 731.38 669.505C731.139 669.617 730.897 669.726 730.655 669.831L688.354 690.199L664.229 639.199C657.922 631.095 652.186 621.736 647.393 611.38C642.971 601.827 639.714 592.208 637.586 582.875L612.971 530.837Z" fill="#000000"/>
<path d="M604.183 633.987C624.378 677.618 661.458 703.345 687.003 691.45C712.548 679.554 716.886 634.541 696.691 590.909C676.496 547.278 638.276 519.202 612.731 531.098C587.185 542.993 583.988 590.355 604.183 633.987Z" fill="#222222"/>
<g filter="url(#filter3_i_3_405)">
<ellipse rx="36.4324" ry="63.679" transform="matrix(-0.901928 0.432052 0.407225 0.913305 650.171 611.309)" fill="url(#paint1_linear_3_405)"/>
</g>
<g filter="url(#filter4_di_3_405)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M627.481 626.386C637.324 645.981 653.189 657.994 665.578 655.573L655.4 633.177C650.267 632.761 644.419 628.108 640.213 620.921L627.481 626.386ZM624.46 619.642C616.471 599.453 617.7 579.371 627.076 570.847L637.252 593.24C634.258 597.864 634.099 605.949 637.084 614.224L624.46 619.642ZM661.315 603.823C656.731 594.703 649.495 588.926 643.568 589.348L633.529 567.254C646.539 563.625 663.898 576.815 673.938 598.405L661.315 603.823ZM664.022 610.702L676.755 605.237C683.998 625.303 682.152 644.825 672.273 652.516L662.232 630.418C665.682 626.46 666.379 618.829 664.022 610.702Z" fill="#A9A8A8"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M281.753 372.506L321.599 361.607L321.604 361.626C321.861 361.55 322.12 361.479 322.38 361.411C346.059 355.249 373.692 382.943 384.1 423.267C394.508 463.59 383.749 501.275 360.07 507.437C359.775 507.514 359.48 507.585 359.185 507.651L320.661 518.21L308.182 471.481C304.146 463.715 300.779 454.992 298.35 445.582C296.11 436.903 294.851 428.347 294.489 420.201L281.753 372.506Z" fill="#000000"/>
<ellipse rx="44.3326" ry="75.2964" transform="matrix(-0.96712 0.251671 0.250086 0.968914 301.69 445.281)" fill="#000000"/>
<g filter="url(#filter5_i_3_405)">
<ellipse rx="33.251" ry="56.475" transform="matrix(-0.96712 0.251671 0.250086 0.968914 300.428 445.106)" fill="url(#paint2_linear_3_405)"/>
</g>
<g filter="url(#filter6_di_3_405)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M308.536 405.759C312.74 409.338 316.655 414.224 319.949 420.086L307.588 435.084C306.604 433.439 305.493 432.011 304.308 430.852L308.536 405.759ZM299.06 461.997C300.887 463.063 302.788 463.484 304.603 463.102L314.495 489.768C313.733 490.095 312.945 490.366 312.133 490.577C306.512 492.04 300.534 490.444 294.928 486.52L299.06 461.997ZM293.036 485.096L297.158 460.634C295.72 459.405 294.37 457.782 293.196 455.853L281.084 470.548C284.532 476.58 288.639 481.552 293.036 485.096ZM302.439 429.294L306.657 404.261C300.824 399.922 294.546 398.099 288.658 399.631C287.016 400.058 285.473 400.729 284.037 401.618L294.008 428.497C294.561 428.157 295.155 427.9 295.786 427.736C297.959 427.171 300.27 427.783 302.439 429.294ZM273.672 452.058C275.224 458.072 277.392 463.634 279.994 468.564L292.106 453.869C291.236 452.113 290.508 450.168 289.969 448.083C289.918 447.883 289.868 447.682 289.82 447.482L272.965 449.092C273.18 450.077 273.416 451.067 273.672 452.058ZM308.666 437.083L321.028 422.084C323.528 426.904 325.613 432.314 327.119 438.15C327.428 439.346 327.707 440.538 327.956 441.725L310.878 443.356C310.826 443.133 310.771 442.909 310.713 442.685C310.193 440.673 309.496 438.79 308.666 437.083ZM272.541 447.018C270.971 438.785 270.816 430.866 271.887 423.919L289.14 438.222C288.908 440.442 288.983 442.88 289.402 445.407L272.541 447.018ZM311.291 445.431C311.787 448.461 311.788 451.36 311.357 453.907L328.577 468.183C329.96 460.965 329.965 452.571 328.364 443.801L311.291 445.431ZM328.076 470.501L310.824 456.199C309.977 459.006 308.518 461.187 306.562 462.339L316.384 488.813C322.002 485.524 326.036 478.943 328.076 470.501ZM272.31 421.536L289.531 435.813C290.07 433.421 291.014 431.395 292.303 429.945L282.26 402.871C277.386 406.754 273.957 413.37 272.31 421.536Z" fill="#4C4C4C"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M308.946 475.782C302.735 466.129 297.602 454.461 294.253 441.485C291.094 429.248 289.885 417.255 290.395 406.311L281.647 373.551L302.763 367.776C306.995 362.543 312.215 358.893 318.283 357.314C341.962 351.152 369.595 378.846 380.003 419.17C389.945 457.69 380.572 493.801 359.072 502.327L359.784 504.974L320.554 519.256L308.946 475.782Z" fill="#000000"/>
<ellipse rx="44.1906" ry="75.0554" transform="matrix(-0.96712 0.251671 0.250086 0.968914 301.202 446.647)" fill="#222222"/>
<g filter="url(#filter7_i_3_405)">
<g filter="url(#filter8_i_3_405)">
<ellipse rx="31.1942" ry="54.5293" transform="matrix(-0.972143 0.231385 0.244478 0.970347 300.361 446.567)" fill="url(#paint3_linear_3_405)"/>
</g>
<g filter="url(#filter9_di_3_405)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M278.002 455.322C283.164 472.418 293.963 484.771 304.375 485.986L299.796 465.373C295.887 463.818 292.102 459.317 289.764 453.273L278.002 455.322ZM276.4 448.984C272.522 430.04 277.094 412.972 287.097 408.206L291.676 428.818C288.094 432.024 286.565 439.112 287.993 446.964L276.4 448.984ZM310.322 443.072C308.016 434.6 302.899 428.294 297.827 427.187L293.321 406.902C304.788 407.232 317.027 421.499 321.895 441.055L310.322 443.072ZM311.436 449.496L323.18 447.449C325.967 465.678 320.881 481.511 310.726 485.255L306.221 464.973C309.971 462.673 311.964 456.635 311.436 449.496Z" fill="#A9A8A8"/>
</g>
</g>
<g filter="url(#filter10_ii_3_405)">
<path d="M1030 573.669C1035.83 561.941 1034.22 551.572 1034.38 542.185L1034.05 518.243C1033.78 499.231 1033.74 482.939 1033.47 476.697C1033.2 470.456 1039.83 452.31 1006.28 419.322C976.148 389.701 910.367 355.773 894.038 351.645C877.709 347.516 807.458 249.529 781.575 230.532C704.27 173.794 509.533 120.955 461.561 121.622C433.758 120.83 318.368 180.261 305.522 188.441C239.101 230.737 284.147 213.453 235.524 277.036C232.438 280.313 230.49 296.585 231.041 305.886C231.095 306.809 230.985 307.732 230.665 308.599C229.368 312.106 227.312 317.487 227.097 318.541C225.575 325.997 230.969 362.85 231.069 370.033C231.185 378.424 229.735 393.669 231.515 402.12C233.294 410.571 254.907 430.529 255.08 427.964C254.949 418.55 255.55 416.854 257.82 401.754C263.99 378.178 275.137 367.622 284.098 365.605C302.843 361.387 320.139 368.891 334.043 392.452C359.468 435.536 351.217 463.689 353.606 482.907C353.843 484.819 355.094 486.421 356.816 487.285L583.241 600.867C587.846 603.177 592.606 599.314 592.227 594.176C592.166 593.356 592.093 592.222 591.971 590.474C591.238 579.989 590.687 530.603 621.103 519.628C647.903 509.957 682.621 543.411 699.094 570.699C712.295 592.569 723.032 636.589 725.89 662.288C726.132 664.459 727.416 666.364 729.372 667.335C745.554 675.363 790.501 696.051 856.415 674.334C995.904 628.375 1024.18 585.371 1030 573.669Z" fill="url(#paint4_linear_3_405)"/>
</g>
<mask id="mask0_3_405" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="226" y="121" width="809" height="563">
<path d="M1030 573.669C1035.83 561.941 1034.22 551.572 1034.38 542.185L1034.05 518.243C1033.78 499.231 1033.74 482.939 1033.47 476.697C1033.2 470.456 1039.83 452.31 1006.28 419.322C976.148 389.701 910.367 355.773 894.038 351.645C877.709 347.516 807.458 249.529 781.575 230.532C704.27 173.794 509.533 120.955 461.561 121.622C433.758 120.83 318.368 180.261 305.522 188.441C239.101 230.737 284.147 213.453 235.524 277.036C232.438 280.313 230.49 296.585 231.041 305.886C231.095 306.809 230.985 307.732 230.665 308.599C229.368 312.106 227.312 317.487 227.097 318.541C225.575 325.997 230.969 362.85 231.069 370.033C231.185 378.424 229.735 393.669 231.515 402.12C233.294 410.571 254.907 430.529 255.08 427.964C255.253 425.398 256.431 410.974 255.722 401.056C255.482 383.793 269.692 368.848 284.098 365.605C302.843 361.387 320.115 367.141 334.019 390.703C361.756 437.705 347.552 462.511 349.297 481.374C349.475 483.297 350.744 484.905 352.472 485.767L582.708 600.632C587.411 602.978 592.203 598.906 591.833 593.663C591.772 592.805 591.702 591.686 591.591 590.105C590.858 579.619 590.687 530.603 621.103 519.628C647.903 509.957 682.621 543.411 699.094 570.699C712.295 592.569 723.032 636.589 725.89 662.288C726.132 664.459 727.416 666.364 729.372 667.335C745.554 675.363 790.501 696.051 856.415 674.334C995.904 628.375 1024.18 585.371 1030 573.669Z" fill="url(#paint5_linear_3_405)"/>
</mask>
<g mask="url(#mask0_3_405)">
<g filter="url(#filter11_ddi_3_405)">
<path d="M656.981 431.79C634.04 414.572 635.586 392.534 599.958 330.193C597.83 326.47 599.356 321.691 603.28 319.962L780.821 241.755C783.382 240.627 786.672 240.172 788.803 241.985C794.334 246.689 807.957 260.729 841.512 301.384C862.722 327.082 866.776 333.51 882.502 349.013C884.377 350.861 880.691 355.875 872.765 362.594C847.497 384.014 695.436 460.65 656.981 431.79Z" fill="url(#paint6_linear_3_405)"/>
</g>
<g filter="url(#filter12_di_3_405)">
<path d="M440.585 251.106C390.618 227.391 376.951 224.629 342.397 238.348C324.929 245.284 303.304 252.958 300.37 264.997C297.435 277.036 348.851 304.94 363.89 312.99C371.239 316.924 633.456 451.048 627.833 435.091C620.942 415.535 583.507 337.309 569.737 317.574C560.873 304.872 490.553 274.821 440.585 251.106Z" fill="url(#paint7_linear_3_405)"/>
</g>
<g filter="url(#filter13_f_3_405)">
<path d="M723.271 532.132C659.241 503.447 622.977 466.826 584.179 433.43C698.783 465.68 938.702 536.405 935.026 541.276C930.431 547.365 913.373 554.749 856.415 559.385C800.339 563.95 730.602 535.416 723.271 532.132Z" fill="url(#paint8_linear_3_405)" fill-opacity="0.21"/>
</g>
<g filter="url(#filter14_f_3_405)">
<path d="M966.967 387.582C963.311 384.66 917.235 338.225 882.899 315.266C911.773 369.727 977.522 488.061 983.475 487.381C990.915 486.532 1024.92 479.675 1034.64 456.343C1044.2 433.373 971.538 391.235 966.967 387.582Z" fill="url(#paint9_linear_3_405)" fill-opacity="0.13"/>
</g>
<g filter="url(#filter15_di_3_405)">
<path d="M501.342 416.546C501.329 415.602 500.779 414.748 499.925 414.347L478.458 404.275C476.816 403.504 474.937 404.718 474.962 406.534L475.005 409.622C475.018 410.568 475.57 411.423 476.426 411.822L497.892 421.853C499.534 422.62 501.41 421.406 501.385 419.592L501.342 416.546Z" fill="#323232"/>
</g>
<g filter="url(#filter16_di_3_405)">
<path d="M378.856 364.52C378.843 363.576 378.292 362.722 377.439 362.321L355.972 352.248C354.329 351.478 352.451 352.692 352.476 354.508L352.519 357.596C352.532 358.541 353.084 359.396 353.94 359.796L375.406 369.827C377.048 370.594 378.924 369.38 378.899 367.566L378.856 364.52Z" fill="#323232"/>
</g>
<path d="M805.84 400.759L668.652 292.072C652.107 296.799 614.678 314.664 607.514 317.469C598.233 321.103 598.673 326.467 598.673 326.467C598.712 329.283 598.35 327.317 601.388 332.855C606.43 342.047 610.475 350.256 621.4 373.949C640.166 414.647 644.186 424.905 659.876 434.024C671.694 440.893 696.318 441.919 726.025 431.657C752.578 422.484 783.032 411.419 805.84 400.759Z" fill="#C4C4C4" fill-opacity="0.2"/>
<g opacity="0.17" filter="url(#filter17_f_3_405)">
<path d="M1002.11 448.907C963.042 480.713 875.773 526.618 836.625 533.978" stroke="url(#paint10_linear_3_405)" stroke-width="81.1568" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<g filter="url(#filter18_ddii_3_405)">
<path d="M1016.28 466.362C1023.81 455.885 1021.57 447.62 1020.26 434.612C1020.22 434.257 1020.13 433.88 1020.02 433.542C1016.55 423.311 1031.37 444.459 1033.1 459.213C1033.67 464.097 1033.39 466.579 1032.99 471.1C1032.81 473.168 1026.08 486.925 1026.75 489.638C1027.42 492.352 1026.98 512.869 1027.11 515.703C1027.25 518.538 1017.67 536.981 986.841 557.765C961.233 575.03 924.283 586.643 920.487 587.243C916.691 587.844 912.786 575.135 910.015 574.468C902.763 572.724 881.526 580.863 873.585 582.978C864.319 585.445 842.697 591.138 818.943 588.422C789.562 585.064 785.028 581.792 757.862 567.485C740.653 558.422 728.061 540.919 722.774 530.242C721.341 527.348 723.923 524.719 727.002 525.691C745.802 531.624 784.565 544.901 828.177 548.759C892.995 554.493 943.692 523.021 950.955 518.497C981.485 499.481 997.561 492.383 1016.28 466.362Z" fill="#272727"/>
</g>
<mask id="mask1_3_405" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="722" y="434" width="315" height="156">
<path d="M1016.31 466.237C1022.31 457.878 1022.49 448.233 1020.84 439.808C1019.94 435.248 1024.39 432.741 1026.84 436.727C1032.88 446.572 1035.5 454.977 1036.28 459.62C1036.86 464.504 1036.78 467.612 1036.39 472.133C1036.21 474.202 1025.85 486.068 1026.52 488.781C1027.19 491.494 1026.79 508.289 1026.87 514.257C1026.96 520.226 1017.75 536.853 986.932 557.656C961.336 574.936 924.393 586.572 920.597 587.174C916.802 587.777 912.888 575.071 910.116 574.406C902.863 572.666 881.631 580.818 873.692 582.937C864.427 585.41 842.809 591.116 819.053 588.415C789.669 585.074 785.132 581.805 757.957 567.515C740.693 558.438 728.068 540.872 722.799 530.206C721.375 527.324 723.932 524.753 727.012 525.723C745.794 531.636 784.601 544.911 828.26 548.747C893.083 554.441 943.76 522.939 951.019 518.411C981.537 499.376 997.609 492.268 1016.31 466.237Z" fill="#272727"/>
</mask>
<g mask="url(#mask1_3_405)">
<g filter="url(#filter19_d_3_405)">
<path d="M768.837 533.71L855.94 533.811C855.946 533.827 855.952 533.844 855.958 533.86C856.209 534.556 856.531 535.565 856.801 536.797C857.345 539.274 857.666 542.572 856.864 546.001C856.071 549.392 854.164 552.982 850.124 556.096C846.055 559.232 839.737 561.948 830.034 563.388C824.269 564.243 816.281 564.325 808.052 562.882C799.819 561.438 791.468 558.489 784.873 553.379C781.307 550.616 776.9 545.124 773.305 540.163C771.528 537.713 769.984 535.441 768.885 533.782C768.869 533.758 768.853 533.734 768.837 533.71Z" stroke="white" stroke-opacity="0.9" stroke-width="3.41524"/>
</g>
<g filter="url(#filter20_d_3_405)">
<path d="M1020.49 439.055C1020.5 439.068 1020.51 439.081 1020.51 439.094C1021.34 440.498 1022.37 442.525 1023.19 445.004C1024.84 449.957 1025.66 456.639 1022.45 463.801C1021.54 465.839 1020.22 467.205 1018.87 467.775C1017.61 468.304 1016.09 468.251 1014.35 466.971C1013.14 466.088 1011.5 465.753 1010 465.625C1008.44 465.49 1006.73 465.558 1005.19 465.697C1004.54 465.756 1003.9 465.829 1003.3 465.907L1020.49 439.055Z" stroke="white" stroke-opacity="0.9" stroke-width="3.41524"/>
</g>
</g>
</g>
<g filter="url(#filter21_f_3_405)">
<path d="M603.255 496.192L364.837 387.556C362.631 386.551 360.317 388.99 361.298 391.206C372.43 416.338 371.083 442.969 370.625 455.882C370.59 456.868 371.138 457.772 372.023 458.21L585.628 563.788C587.317 564.623 589.296 563.371 589.311 561.487C589.546 532.037 596.715 510.585 604.033 502.086C605.461 500.427 605.248 497.1 603.255 496.192Z" fill="url(#paint11_linear_3_405)" fill-opacity="0.12"/>
</g>
<defs>
<filter id="filter0_f_3_405" x="137.783" y="328.393" width="926.222" height="426.464" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8.67815" result="effect1_foregroundBlur_3_405"/>
</filter>
<filter id="filter1_i_3_405" x="604.889" y="549.148" width="89.6853" height="124.34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1.22965" operator="erode" in="SourceAlpha" result="effect1_innerShadow_3_405"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.614824"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3_405"/>
</filter>
<filter id="filter2_di_3_405" x="608.757" y="559.408" width="78.9477" height="106.036" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.491859"/>
<feGaussianBlur stdDeviation="0.368894"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.41 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_405"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3_405" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3.68894" dy="2.4593"/>
<feGaussianBlur stdDeviation="1.84447"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_3_405"/>
</filter>
<filter id="filter3_i_3_405" x="608.31" y="551.043" width="83.7218" height="120.532" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1.56332" operator="erode" in="SourceAlpha" result="effect1_innerShadow_3_405"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.781658"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3_405"/>
</filter>
<filter id="filter4_di_3_405" x="617.177" y="566.523" width="64.6425" height="90.6919" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.533156"/>
<feGaussianBlur stdDeviation="0.333223"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.59 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_405"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3_405" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.99934" dy="1.33289"/>
<feGaussianBlur stdDeviation="0.999668"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_3_405"/>
</filter>
<filter id="filter5_i_3_405" x="265.297" y="389.743" width="70.2628" height="110.727" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1.05175" operator="erode" in="SourceAlpha" result="effect1_innerShadow_3_405"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.525873"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3_405"/>
</filter>
<filter id="filter6_di_3_405" x="268.045" y="398.885" width="62.1767" height="94.3308" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.420698"/>
<feGaussianBlur stdDeviation="0.315524"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.41 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_405"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3_405" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3.15524" dy="2.10349"/>
<feGaussianBlur stdDeviation="1.57762"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_3_405"/>
</filter>
<filter id="filter7_i_3_405" x="267.226" y="393.158" width="66.269" height="106.818" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.935097" operator="erode" in="SourceAlpha" result="effect1_innerShadow_3_405"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.467549"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3_405"/>
</filter>
<filter id="filter8_i_3_405" x="267.226" y="393.158" width="66.269" height="106.818" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1.61918" operator="erode" in="SourceAlpha" result="effect1_innerShadow_3_405"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.80959"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3_405"/>
</filter>
<filter id="filter9_di_3_405" x="272.956" y="406.764" width="51.671" height="80.6032" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.552208"/>
<feGaussianBlur stdDeviation="0.34513"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.59 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_405"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3_405" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.07078" dy="1.38052"/>
<feGaussianBlur stdDeviation="1.03539"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_3_405"/>
</filter>
<filter id="filter10_ii_3_405" x="226.826" y="109.107" width="817.022" height="574.854" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="9.3799" dy="-12.5065"/>
<feGaussianBlur stdDeviation="37.5196"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3_405"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.56332"/>
<feGaussianBlur stdDeviation="3.12663"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_3_405" result="effect2_innerShadow_3_405"/>
</filter>
<filter id="filter11_ddi_3_405" x="594.076" y="237.077" width="291.366" height="204.817" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.4593" dy="1.22965"/>
<feGaussianBlur stdDeviation="1.22965"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.28 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_405"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.22965"/>
<feGaussianBlur stdDeviation="1.22965"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.56 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_3_405" result="effect2_dropShadow_3_405"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_3_405" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.22965" dy="3.68894"/>
<feGaussianBlur stdDeviation="1.84447"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_3_405"/>
</filter>
<filter id="filter12_di_3_405" x="294.214" y="230.081" width="334.614" height="210.697" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.905297"/>
<feGaussianBlur stdDeviation="0.452649"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.925374 0 0 0 0 0.925374 0 0 0 0 0.925374 0 0 0 0.37 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_405"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3_405" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-6.03563" dy="4.36889"/>
<feGaussianBlur stdDeviation="3.01781"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_3_405"/>
</filter>
<filter id="filter13_f_3_405" x="549.749" y="399" width="419.749" height="195.309" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="17.2151" result="effect1_foregroundBlur_3_405"/>
</filter>
<filter id="filter14_f_3_405" x="860.766" y="293.133" width="196.871" height="216.385" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="11.0668" result="effect1_foregroundBlur_3_405"/>
</filter>
<filter id="filter15_di_3_405" x="474.352" y="402.819" width="27.6429" height="22.3174" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.609879"/>
<feGaussianBlur stdDeviation="0.304939"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.78755 0 0 0 0 0.78755 0 0 0 0 0.78755 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_405"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3_405" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.04939"/>
<feGaussianBlur stdDeviation="1.5247"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.74 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_3_405"/>
</filter>
<filter id="filter16_di_3_405" x="351.866" y="350.793" width="27.6429" height="22.3174" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.609879"/>
<feGaussianBlur stdDeviation="0.304939"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.78755 0 0 0 0 0.78755 0 0 0 0 0.78755 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_405"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3_405" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.04939"/>
<feGaussianBlur stdDeviation="1.5247"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.74 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_3_405"/>
</filter>
<filter id="filter17_f_3_405" x="783.743" y="396.031" width="271.248" height="190.83" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6.14824" result="effect1_foregroundBlur_3_405"/>
</filter>
<filter id="filter18_ddii_3_405" x="720.939" y="428.704" width="313.909" height="170.282" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.07131"/>
<feGaussianBlur stdDeviation="0.714204"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_405"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.714204"/>
<feGaussianBlur stdDeviation="0.714204"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_3_405" result="effect2_dropShadow_3_405"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_3_405" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="9.84614"/>
<feGaussianBlur stdDeviation="10.0343"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_3_405"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.07294"/>
<feGaussianBlur stdDeviation="1.07294"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_3_405" result="effect4_innerShadow_3_405"/>
</filter>
<filter id="filter19_d_3_405" x="765.416" y="531.999" width="96.191" height="36.5414" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13841" dy="1.41412"/>
<feGaussianBlur stdDeviation="0.707062"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_405"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3_405" result="shape"/>
</filter>
<filter id="filter20_d_3_405" x="999.512" y="435.921" width="29.3925" height="36.7061" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.13841" dy="1.41412"/>
<feGaussianBlur stdDeviation="0.707062"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_405"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3_405" result="shape"/>
</filter>
<filter id="filter21_f_3_405" x="357.284" y="383.541" width="251.453" height="184.293" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.89119" result="effect1_foregroundBlur_3_405"/>
</filter>
<linearGradient id="paint0_linear_3_405" x1="91.6912" y1="100.732" x2="96.2567" y2="32.8232" gradientUnits="userSpaceOnUse">
<stop stop-color="#3F3F3F"/>
<stop offset="1" stop-color="#5C5C5C"/>
</linearGradient>
<linearGradient id="paint1_linear_3_405" x1="85.9362" y1="97.1409" x2="90.4651" y2="31.6704" gradientUnits="userSpaceOnUse">
<stop stop-color="#AAAAAA"/>
<stop offset="1" stop-color="#C2C2C2"/>
</linearGradient>
<linearGradient id="paint2_linear_3_405" x1="78.4318" y1="86.1513" x2="82.3358" y2="28.0721" gradientUnits="userSpaceOnUse">
<stop stop-color="#3F3F3F"/>
<stop offset="1" stop-color="#5C5C5C"/>
</linearGradient>
<linearGradient id="paint3_linear_3_405" x1="73.5804" y1="83.1833" x2="77.459" y2="27.1199" gradientUnits="userSpaceOnUse">
<stop stop-color="#AAAAAA"/>
<stop offset="1" stop-color="#C2C2C2"/>
</linearGradient>
<linearGradient id="paint4_linear_3_405" x1="363.908" y1="455.282" x2="1002.7" y2="355.736" gradientUnits="userSpaceOnUse">
<stop stop-color="#CECBCB"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint5_linear_3_405" x1="723.835" y1="561.684" x2="520.341" y2="113.004" gradientUnits="userSpaceOnUse">
<stop stop-color="#CECBCB"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint6_linear_3_405" x1="751.969" y1="96.8061" x2="533.08" y2="309.769" gradientUnits="userSpaceOnUse">
<stop stop-color="#E8EAE9"/>
<stop offset="0.498386" stop-color="#707072"/>
<stop offset="1" stop-color="#2F3032"/>
</linearGradient>
<linearGradient id="paint7_linear_3_405" x1="893.456" y1="884.684" x2="761.864" y2="206.189" gradientUnits="userSpaceOnUse">
<stop stop-color="#E8EAE9"/>
<stop offset="0.498386" stop-color="#707072"/>
<stop offset="1" stop-color="#2F3032"/>
</linearGradient>
<linearGradient id="paint8_linear_3_405" x1="749.21" y1="568.747" x2="775.618" y2="487.894" gradientUnits="userSpaceOnUse">
<stop stop-color="#ECEAEA"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint9_linear_3_405" x1="1060.06" y1="421.042" x2="956.683" y2="458.742" gradientUnits="userSpaceOnUse">
<stop stop-color="#ECEAEA"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint10_linear_3_405" x1="922.49" y1="531.905" x2="903.429" y2="496.069" gradientUnits="userSpaceOnUse">
<stop stop-color="#4B4B4B"/>
<stop offset="0.899322" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint11_linear_3_405" x1="449.183" y1="547.009" x2="502.163" y2="448.795" gradientUnits="userSpaceOnUse">
<stop stop-color="#040404"/>
<stop offset="1" stop-color="#3C3C3C" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>