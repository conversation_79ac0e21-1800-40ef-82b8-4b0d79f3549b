﻿/* Chat Profile Styles */

.chat-profile {
    position: fixed;
    top: 0;
    right: 0;
    width: 350px;
    height: 100%;
    background-color: #fff;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
    overflow-y: auto;
    direction: rtl;
}

.profile-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid #e0e0e0;
    position: relative;
}

.close-profile-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #555;
}

.profile-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin: 0 auto 15px;
    object-fit: cover;
    border: 2px solid #f0f0f0;
}

.profile-name {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 5px;
}

.profile-details {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.profile-about {
    font-size: 14px;
    color: #777;
    padding: 0 20px;
    text-align: center;
}

.profile-section {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    background: var(--profile-bg);
}

.members-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
}

/* Media section styles */
.media-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.media-tab {
    flex: 1;
    text-align: center;
    padding: 10px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
}

    .media-tab.active {
        color: #ffcf58;
        border-bottom: 2px solid #ffcf58;
    }

.media-items-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
}

.media-item {
    position: relative;
    cursor: pointer;
    border-radius: 5px;
    overflow: hidden;
    background-color: #f5f5f5;
}

.media-thumbnail {
    width: 100%;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
}

    .media-thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

.video-thumbnail .play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 24px;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.file-thumbnail {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffcf58;
    font-size: 24px;
}

.media-info {
    padding: 5px;
    font-size: 12px;
    color: #666;
    background-color: rgba(255, 255, 255, 0.8);
    position: absolute;
    bottom: 0;
    width: 100%;
}

.media-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}

.media-date, .media-size {
    font-size: 10px;
    color: #888;
}

.no-media-message {
    grid-column: span 3;
    text-align: center;
    padding: 20px;
    color: #888;
}

/* Members section styles */
.members-search {
    margin-bottom: 15px;
    position: relative;
}

.search-members-input {
    width: 100%;
    padding: 8px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    font-size: 14px;
    border-radius: 7px;
    height: 38px;
    font-size: 14px;
}

.members-container {
    max-height: 400px;
    overflow-y: auto;
}

.member-item {
    display: flex;
    align-items: center;
    padding: 10px 10px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
}

.member-avatar {
    position: relative;
    min-width: 50px;
    height: 50px;
    overflow: hidden;
    border-radius: 50%;
    /*    margin: 0px 5px 0 5px;*/
}

    .member-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

.member-info {
    flex: 1;
}

.member-info-name {
    font-size: 16px;
    margin-bottom: 3px;
    display: flex;
    align-items: center;
}
/*    .member-info-name h6 {
        padding-left: 10px;
    }*/

.current-user-label {
    font-size: 12px;
    color: #ffcf58;
    margin-right: 5px;
}

.member-role {
    font-size: 12px;
    color: #888;
    display: flex;
    align-items: center;
}

.role-icon {
    margin-left: 5px;
    font-size: 14px;
}

.owner-icon {
    color: #FFC107;
}

.admin-icon {
    color: #ffcf58;
}

.show-more-members-btn {
    display: block;
    width: 100%;
    padding: 10px;
    text-align: center;
    background-color: #f5f5f5;
    border: none;
    border-radius: 5px;
    margin-top: 10px;
    font-size: 16px;
    cursor: pointer;
    color: #ffcf58;
}

.no-members-message {
    text-align: center;
    padding: 20px;
    color: #888;
}

/* Group actions section */
.group-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.action-button {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    background-color: #f5f5f5;
    color: #333;
    width: 100%;
}

    .action-button i {
        margin-left: 10px;
        font-size: 18px;
    }

.add-members-btn {
    background-color: #ffcf58;
    color: white;
}

.leave-group-btn {
    background-color: #f44336;
    color: white;
}

/* Member action popup */
.member-action-popup {
    position: fixed;
    width: 300px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 1100;
    overflow: hidden;
}

.popup-header {
    padding: 15px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
}

    .popup-header img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-left: 10px;
    }

.popup-title {
    font-size: 16px;
    font-weight: bold;
}

.popup-options {
    padding: 10px 0;
}

.popup-option {
    padding: 12px 15px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

    .popup-option:hover {
        background-color: #f5f5f5;
    }

    .popup-option i {
        margin-left: 15px;
        font-size: 18px;
        color: #ffcf58;
    }

/* Member action menu (long press) */
.member-action-menu {
    position: absolute;
    width: 200px;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    z-index: 1100;
    overflow: hidden;
}

.menu-header {
    padding: 10px 15px;
    border-bottom: 1px solid #f0f0f0;
}

.menu-title {
    font-size: 14px;
    font-weight: bold;
}

.menu-options {
    padding: 5px 0;
}

.menu-option {
    padding: 10px 15px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

    .menu-option:hover {
        background-color: #f5f5f5;
    }

    .menu-option i {
        margin-left: 10px;
        font-size: 16px;
    }

.remove-option i {
    color: #f44336;
}

.role-option i {
    color: #ffcf58;
}

/* Contact selection modal */
.contact-selection-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1200;
}

.modal-content {
    width: 90%;
    max-width: 500px;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

    .modal-header h3 {
        margin: 0;
        font-size: 18px;
    }

.close-modal-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #555;
}

.modal-body {
    padding: 15px;
    overflow-y: auto;
    flex: 1;
}

.search-container {
    margin-bottom: 15px;
}

.contact-search-input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    font-size: 14px;
}

.contacts-list {
    max-height: 300px;
    overflow-y: auto;
}

.contact-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.contact-checkbox {
    margin-left: 15px;
}

.contact-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-left: 10px;
}

    .contact-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

.contact-info {
    flex: 1;
}

.contact-name {
    font-size: 14px;
    margin-bottom: 3px;
}

.contact-phone {
    font-size: 12px;
    color: #888;
}

.modal-footer {
    padding: 15px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

    .modal-footer button {
        padding: 8px 15px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    }

.cancel-btn {
    background-color: #f5f5f5;
    color: #333;
}

.add-selected-btn {
    background-color: #ffcf58;
    color: white;
}

/* Responsive styles */
@media (max-width: 576px) {
    .chat-profile {
        width: 100%;
    }
}


.ChatAbout .add-member {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 0.7px solid var(--block);
    border-right: none;
    cursor: pointer;
}

    .ChatAbout .add-member .iconBox {
        background: #ffcf58;
        display: flex;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        align-items: center;
        justify-content: center;
        margin-left: 10px;
    }

    .ChatAbout .add-member .head h4 {
        color: var(--new-g);
        font-size: 17px;
        font-weight: 400;
        padding-left: 10px;
        margin-bottom: -4px;
    }

    .ChatAbout .add-member .iconBox i {
        color: #fff;
        font-size: 20px;
        padding-bottom: 4px;
    }

.status-role-member {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 5px;
}

.member-status {
    font-size: 12px;
    color: var(--primary);
    margin: 3px 0px 0px;
}
/*.member-status.online {
    color: #ffcf58;
}*/
.member-type {
    margin: 0 8px;
    font-size: 10px;
    background-color: rgba(0, 168, 132, 0.1);
    color: rgb(0, 168, 132);
    padding: 2px 6px;
    border-radius: 4px;
}

.team-member {
    background-color: rgba(255, 152, 0, 0.1);
    color: rgb(255, 152, 0);
}
/* Contact Selection Modal Styles */
.contact-selection-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

    .contact-selection-modal .modal-content {
        width: 400px;
        max-width: 90%;
        max-height: 90vh;
        background-color: #fff;
        border-radius: 10px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .contact-selection-modal .header-Chat {
        display: flex;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #e0e0e0;
    }

    .contact-selection-modal .ICON {
        margin-right: 15px;
    }

    .contact-selection-modal .newText h2 {
        margin: 0;
        font-size: 18px;
    }

    .contact-selection-modal .selected-participants-container {
        display: flex;
        flex-wrap: wrap;
        padding: 10px;
        gap: 10px;
        border-bottom: 1px solid #e0e0e0;
        min-height: 40px;
        max-height: none; /* Remove any max-height constraint */
        height: auto; /* Allow it to grow naturally */
        display: none;
        overflow: visible; /* Allow content to be visible beyond the container */
    }

        .contact-selection-modal .selected-participants-container.has-selections {
            display: flex;
        }

    .contact-selection-modal .selected-contact {
        display: flex;
        align-items: center;
        background-color: #f0f2f5;
        border-radius: 15px;
        padding: 5px 10px;
    }

    .contact-selection-modal .selected-contact-avatar {
        width: 25px;
        height: 25px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 5px;
    }

        .contact-selection-modal .selected-contact-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

    .contact-selection-modal .selected-contact-name {
        font-size: 14px;
        margin-right: 5px;
    }

    .contact-selection-modal .remove-selected-contact {
        background: none;
        border: none;
        color: #666;
        font-size: 16px;
        cursor: pointer;
        padding: 0 5px;
    }

    .contact-selection-modal .search-bar {
        padding: 10px 15px;
        border-bottom: 1px solid #e0e0e0;
    }

        .contact-selection-modal .search-bar > div {
            position: relative;
            display: flex;
            align-items: center;
            background-color: #f0f2f5;
            border-radius: 20px;
            padding: 5px 10px;
        }

        .contact-selection-modal .search-bar input {
            flex: 1;
            border: none;
            background: transparent;
            padding: 8px;
            outline: none;
        }

        .contact-selection-modal .search-bar .search,
        .contact-selection-modal .search-bar .go-back {
            background: none;
            border: none;
            cursor: pointer;
        }

    .contact-selection-modal .contacts-list-container {
        flex: 1;
        overflow-y: auto;
        padding: 0 15px;
        position: relative;
        height: calc(100% - 180px);
    }

    .contact-selection-modal .search-bar div i {
        position: absolute;
        left: 30px;
        top: 7px;
        font-size: 1em;
        color: var(--icons);
        justify-content: center;
        align-items: center;
    }

    .contact-selection-modal .contact-letter-group {
        margin-bottom: 10px;
    }
    /* .contact-selection-modal .contact-letter-group  .contact-item{
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative;
    padding: 10px;
    display: flex
;
    align-items: center;
} */
    .contact-selection-modal .letter-header {
        padding: 5px 0;
        position: sticky;
        top: 0;
        background-color: #fff;
        z-index: 1;
    }

        .contact-selection-modal .letter-header h3 {
            font-size: 17px;
            font-weight: 300;
            color: var(--yur-name-abt, #128C7E);
            margin: 0;
        }

    /* .contact-selection-modal .contact-item {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 0.7px solid var(--block, #f0f2f5);
    border-right: none;
    cursor: pointer;
  } */

    .contact-selection-modal .contact-item:hover {
        background: #f0f0f0;
    }

    .contact-selection-modal .contact-item.selected {
        background-color: #e0f2f7;
    }

    .contact-selection-modal .h-text .head h4 {
        font-size: 16px;
        font-weight: 400;
        color: #111b21;
        letter-spacing: 0.4px;
    }

    .contact-selection-modal .imgBox {
        position: relative;
        min-width: 50px;
        height: 50px;
        overflow: hidden;
        border-radius: 50%;
        margin-left: 10px;
        margin-right: 0;
    }

    .contact-selection-modal .h-text {
        position: relative;
        width: 100%;
    }

    .contact-selection-modal .head {
        display: flex;
        justify-content: space-between;
        margin-bottom: -6px;
    }

        .contact-selection-modal .head h4 {
            font-size: 16px;
            font-weight: 400;
            color: var(--h4, #333);
            letter-spacing: 0.4px;
        }

    .contact-selection-modal .message {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

        .contact-selection-modal .message p {
            margin: 5px 0 0;
            font-size: 14px;
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
        }

    .contact-selection-modal .contact-checkbox {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 10px;
    }

    .contact-selection-modal .contact-item.selected .contact-checkbox {
        border-color: #128C7E;
        background-color: #128C7E;
    }

    .contact-selection-modal .checkbox-circle {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #fff;
        display: none;
    }

    .contact-selection-modal .contact-item.selected .checkbox-circle {
        display: block;
    }

    .contact-selection-modal .modal-actions {
        display: flex;
        justify-content: flex-end;
        padding: 15px;
        border-top: 1px solid #e0e0e0;
        gap: 10px;
    }

    .contact-selection-modal .cancel-btn,
    .contact-selection-modal .add-members-confirm-btn {
        padding: 8px 15px;
        border-radius: 5px;
        border: none;
        cursor: pointer;
    }

    .contact-selection-modal .add-members-confirm-btn {
        background-color: #128C7E;
        color: white;
    }

.contacts-list-container .contact-item {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 0.7px solid #2a39426b;
    border-right: none;
    cursor: pointer;
}

.contact-selection-modal .cancel-btn {
    background-color: #f0f2f5;
    color: #333;
}

.contact-selection-modal .add-members-confirm-btn {
    background-color: #128C7E;
    color: white;
}

    .contact-selection-modal .add-members-confirm-btn:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }

.contact-selection-modal .loading-contacts,
.contact-selection-modal .no-contacts-message,
.contact-selection-modal .error-message {
    padding: 20px;
    text-align: center;
    color: #666;
}

/* تحسينات واجهة المستخدم لشاشة اختيار جهات الاتصال */
.contacts-list-container .contact-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative; /* للسماح بوضع علامة الاختيار */
    padding: 10px;
    display: flex;
    align-items: center;
}

.contacts-list-container .block:hover {
    background-color: #f0f0f0; /* لون عند التحويم */
}

.contacts-list-container .block.selected {
    background-color: #e0f2f7; /* لون للخلفية عند الاختيار */
}

.contacts-list-container .contact-item.selection-overlay {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: #00a884; /* لون علامة الاختيار */
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    opacity: 0; /* مخفي افتراضيًا */
    transition: opacity 0.2s ease;
}

.contacts-list-container .block.selected .selection-overlay {
    opacity: 1; /* إظهار عند الاختيار */
}

.contact-letter-header {
    margin: 2px 5px 2px 5px;
    background-color: #eeeeee8a;
    font-weight: bold;
    /* color: #555; */
    /* position: sticky; */
    /* top: 0; */
    display: flex;
    align-items: center;
    padding: 0 8px 0 8px;
    border-radius: 4px;
    font-size: 10px;
}

.no-contacts, .no-results, .loading-contacts, .error-loading {
    padding: 20px;
    text-align: center;
    color: #888;
}

/* زر المتابعة */
.continue-button-container {
    position: absolute;
    bottom: 20px;
    left: 20px;
    z-index: 10;
    display: none; /* يبدأ مخفيًا */
    justify-content: center;
    align-items: center;
}
