/* App Loader Styles */
#loading-container {
    /*    position: fixed;
    top: 0;
    left: 0;*/
    width: 100%;
    padding: 20px;
    text-align: center;
    height: calc(100vh - 40px);
    background-color: #f0f2f5;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    background-clip: border-box;
    border: var(--bs-card-border-width) solid var(--bs-card-border-color);
    border-radius: var(--bs-card-border-radius);
}

/*#chat-container {
    display: none;*/ /* Initially hidden, will be shown after loading */
/*}*/

/* Loading State Styles */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 2rem;
    width: 100%;
}

.loading-spinner {
    width: 64px;
    height: 64px;
    margin-bottom: 1rem;
}

.spinner {
    width: 100%;
    height: 100%;
    border: 3px solid #e0e0e0;
    border-top: 3px solid #ffcf58;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    font-size: 1.2rem;
    color: #ffcf58;
    font-family: 'Cairo', sans-serif;
}

/* Progress Bar Styles */
.progress-container {
    width: 80%;
    max-width: 400px;
    margin: 1rem 0;
}

.chat-progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    width: 0;
    height: 100%;
    background-color: #ffcf58;
    transition: width 0.3s ease-in-out;
}

.progress-text {
    text-align: center;
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #666;
    font-family: 'Cairo', sans-serif;
}

.loading-details {
    margin-top: 1rem;
    font-size: 1rem;
    color: #666;
    text-align: center;
    font-family: 'Cairo', sans-serif;
}

/* Error State Styles */
.error-state {
    display: none;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.error-icon {
    color: #dc3545;
    margin-bottom: 1rem;
}

.error-message {
    font-size: 1.2rem;
    color: #dc3545;
    margin-bottom: 1rem;
    font-family: 'Cairo', sans-serif;
}

/* Offline State Styles */
.offline-state {
    display: none;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.offline-icon {
    color: #666;
    margin-bottom: 1rem;
}

.offline-message {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 0.5rem;
    font-family: 'Cairo', sans-serif;
}

.offline-submessage {
    font-size: 1rem;
    color: #999;
    margin-bottom: 1rem;
    font-family: 'Cairo', sans-serif;
}

/* Button Styles */
.retry-button {
    padding: 0.5rem 1.5rem;
    background-color: #ffcf58;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.retry-button:hover {
    background-color: #075E54;
}

.retry-button:active {
    background-color: #054D44;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .progress-container {
        width: 90%;
    }

    .loading-text,
    .error-message,
    .offline-message {
        font-size: 1rem;
    }

    .loading-details,
    .offline-submessage {
        font-size: 0.9rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: light) {

    .loading-text,
    .error-message,
    .offline-message {
        color: #111b21;
    }

    .offline-submessage {
        color: #8696a0;
    }

    .spinner {
        border-color: #e0e0e0;
        border-top-color: #ffcf58;
    }

    .chat-progress-bar {
        background-color: #2a3942;
    }

    .loading-details {
        color: #8696a0;
    }
}

/* RTL Support */
[dir="rtl"] .loading-text,
[dir="rtl"] .error-message,
[dir="rtl"] .offline-message,
[dir="rtl"] .offline-submessage,
[dir="rtl"] .loading-details {
    font-family: 'Cairo', sans-serif;
} 