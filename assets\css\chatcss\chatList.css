/* Chat List Loading Indicator */
.chat-loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    margin: 10px 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

    .chat-loading-indicator .loading-spinner {
        width: 40px;
        height: 40px;
        position: relative;
        margin-bottom: 10px;
    }

    .chat-loading-indicator .spinner {
        width: 100%;
        height: 100%;
        border: 3px solid #e0e0e0;
        border-top: 3px solid #128C7E;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    .chat-loading-indicator .loading-text {
        color: #128C7E;
        font-size: 14px;
        font-weight: 600;
        text-align: center;
    }

/* Chat List Container */
#chat-list {
    overflow-y: auto;
    height: calc(100vh - 120px);
    padding: 10px;
    scrollbar-width: thin;
    scrollbar-color: #128C7E #f0f2f5;
}

    #chat-list::-webkit-scrollbar {
        width: 6px;
    }

    #chat-list::-webkit-scrollbar-track {
        background: #f0f2f5;
    }

    #chat-list::-webkit-scrollbar-thumb {
        background-color: #128C7E;
        border-radius: 6px;
    }

/* Chat Item Styles */
.item-chat-list {
    display: flex;
    padding: 12px;
    border-bottom: 1px solid #f0f2f5;
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative;
}

    .item-chat-list:hover {
        background-color: #f5f6f6;
    }

    .item-chat-list.active {
        background-color: #e9edef;
    }

    .item-chat-list.unread {
        background-color: #f0f2f5;
    }

    .item-chat-list .imgBox {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 15px;
    }

        .item-chat-list .imgBox img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

    .item-chat-list .h-text {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .item-chat-list .head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
    }

        .item-chat-list .head h4 {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            color: #111b21;
        }

        .item-chat-list .head .time {
            font-size: 12px;
            color: #667781;
            margin: 0;
        }

    .item-chat-list .last-message-chat {
        display: flex;
        align-items: center;
    }

    .item-chat-list .tick-icon {
        margin-right: 5px;
        display: flex;
        align-items: center;
    }

    .item-chat-list .chat-text-icon {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .item-chat-list .text-last-message {
        font-size: 14px;
        color: #667781;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
    }

    .item-chat-list .unread {
        background-color: #25d366;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 600;
    }

    .item-chat-list .more-horizontal {
        background: none;
        border: none;
        color: #667781;
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .item-chat-list:hover .more-horizontal {
        opacity: 1;
    }

    .item-chat-list .more-horizontal:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

/* Dropdown Menu */
.dropdown-menu {
    min-width: 150px;
    padding: 8px 0;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: none;
}

.dropdown-item {
    padding: 8px 15px;
    font-size: 14px;
    color: #111b21;
    transition: background-color 0.2s ease;
}

    .dropdown-item:hover {
        background-color: #f0f2f5;
    }

/* Animations */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .chat-loading-indicator {
        background-color: rgba(17, 27, 33, 0.8);
    }

    .item-chat-list {
        border-bottom: 1px solid #2a3942;
    }

        .item-chat-list:hover {
            background-color: #202c33;
        }

        .item-chat-list.active {
            background-color: #2a3942;
        }

        .item-chat-list.unread {
            background-color: #182229;
        }

        .item-chat-list .head h4 {
            color: #e9edef;
        }

        .item-chat-list .head .time {
            color: #8696a0;
        }

        .item-chat-list .text-last-message {
            color: #8696a0;
        }

    .dropdown-menu {
        background-color: #233138;
    }

    .dropdown-item {
        color: #e9edef;
    }

        .dropdown-item:hover {
            background-color: #182229;
        }

    #chat-list::-webkit-scrollbar-track {
        background: #111b21;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .item-chat-list .text-last-message {
        max-width: 150px;
    }
}

@media (max-width: 576px) {
    .item-chat-list .text-last-message {
        max-width: 100px;
    }

    .item-chat-list .imgBox {
        width: 40px;
        height: 40px;
    }
}
