/* Chat List Item Styles */
.block.item-chat-list {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative;
}

    .block.item-chat-list:hover {
        background-color: var(--hover-bg-color);
    }

    .block.item-chat-list.active {
        background-color: rgba(0, 0, 0, 0.05);
    }

    /* Image Box */
    .block.item-chat-list .imgBox {
        position: relative;
        margin-right: 12px;
        flex-shrink: 0;
    }

        .block.item-chat-list .imgBox .cover {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
        }

    /* Online Status Indicator */
    .block.item-chat-list .status-indicator {
        position: absolute;
        bottom: 2px;
        right: 2px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid var(--bg-color);
    }

        .block.item-chat-list .status-indicator.online {
            background-color: #4CAF50;
        }

        .block.item-chat-list .status-indicator.offline {
            background-color: #9E9E9E;
        }

        .block.item-chat-list .status-indicator.typing {
            background-color: #2196F3;
            animation: pulse 1.5s infinite;
        }

    /* Text Content */
    .block.item-chat-list .h-text {
        flex-grow: 1;
        min-width: 0; /* Ensures text truncation works */
    }

        .block.item-chat-list .h-text .head {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

            .block.item-chat-list .h-text .head h4 {
                margin: 0;
                font-size: 16px;
                font-weight: 500;
                color: var(--text-color);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 200px;
            }

            .block.item-chat-list .h-text .head .time {
                margin: 0;
                font-size: 12px;
                color: var(--secondary-text-color);
                white-space: nowrap;
            }

    /* Last Message */
    .block.item-chat-list .last-message-chat {
        display: flex;
        align-items: center;
    }

    .block.item-chat-list .tick-icon {
        margin-right: 8px;
        flex-shrink: 0;
    }

        .block.item-chat-list .tick-icon svg {
            width: 16px;
            height: 16px;
        }

        .block.item-chat-list .tick-icon .blue-tick {
            color: #2196F3;
        }

        .block.item-chat-list .tick-icon .white-tick {
            color: var(--secondary-text-color);
        }

    .block.item-chat-list .chat-text-icon {
        flex-grow: 1;
        min-width: 0;
        display: flex;
        align-items: center;
    }

    .block.item-chat-list .text-last-message {
        font-size: 14px;
        color: var(--secondary-text-color);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
    }

    /* Unread Count */
    .block.item-chat-list .unread {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--primary-color);
        color: white;
        border-radius: 16px;
        min-width: 20px;
        height: 20px;
        padding: 0 6px;
        margin-left: 8px;
        font-size: 12px;
        font-weight: 500;
    }

    /* Dropdown Icon */
    .block.item-chat-list .dropdown-icon {
        margin-left: 8px;
        flex-shrink: 0;
    }

        .block.item-chat-list .dropdown-icon button {
            background: none;
            border: none;
            padding: 4px;
            cursor: pointer;
            color: var(--secondary-text-color);
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }

            .block.item-chat-list .dropdown-icon button:hover {
                background-color: var(--hover-bg-color);
            }

    /* Message Status Icons */
    .block.item-chat-list .message-status {
        display: flex;
        align-items: center;
        margin-right: 6px;
    }

        .block.item-chat-list .message-status .status-icon {
            width: 16px;
            height: 16px;
            margin-right: 2px;
        }

            .block.item-chat-list .message-status .status-icon.sent {
                color: #667781;
            }

            .block.item-chat-list .message-status .status-icon.delivered {
                color: #667781;
            }

            .block.item-chat-list .message-status .status-icon.read {
                color: #53bdeb;
            }

/* Animations */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
    .block.item-chat-list {
        border-bottom-color: var(--dark-border-color);
    }

        .block.item-chat-list:hover {
            background-color: var(--dark-hover-bg-color);
        }

        .block.item-chat-list.active {
            background-color: rgba(255, 255, 255, 0.08);
        }

        .block.item-chat-list .status-indicator {
            border-color: var(--dark-bg-color);
        }

        .block.item-chat-list .h-text .head h4 {
            color: var(--dark-text-color);
        }

        .block.item-chat-list .h-text .head .time,
        .block.item-chat-list .text-last-message,
        .block.item-chat-list .tick-icon .white-tick {
            color: var(--dark-secondary-text-color);
        }

        .block.item-chat-list .dropdown-icon button {
            color: var(--dark-secondary-text-color);
        }

            .block.item-chat-list .dropdown-icon button:hover {
                background-color: var(--dark-hover-bg-color);
            }
}

/* RTL Support */
[dir="rtl"] .block.item-chat-list .imgBox {
    margin-right: 0;
    margin-left: 12px;
}

[dir="rtl"] .block.item-chat-list .status-indicator {
    right: auto;
    left: 2px;
}

[dir="rtl"] .block.item-chat-list .tick-icon {
    margin-right: 0;
    margin-left: 8px;
}

[dir="rtl"] .block.item-chat-list .unread {
    margin-left: 0;
    margin-right: 8px;
}

[dir="rtl"] .block.item-chat-list .dropdown-icon {
    margin-left: 0;
    margin-right: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .block.item-chat-list {
        padding: 10px 12px;
    }

        .block.item-chat-list .imgBox .cover {
            width: 40px;
            height: 40px;
        }

        .block.item-chat-list .h-text .head h4 {
            font-size: 14px;
            max-width: 150px;
        }

        .block.item-chat-list .text-last-message {
            font-size: 12px;
            max-width: 150px;
        }

        .block.item-chat-list .h-text .head .time {
            font-size: 11px;
        }
}

@media (max-width: 480px) {
    .block.item-chat-list .h-text .head h4 {
        max-width: 120px;
    }

    .block.item-chat-list .text-last-message {
        max-width: 120px;
    }
}
