﻿/* File Attachment UI Styles */

/* Attachment Icon and Menu */
.chat-attach {
  position: relative;
}

.attachment-popup {
  position: absolute;
  bottom: 60px;
  left: 0;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 8px;
  display: none;
  z-index: 100;
  width: 280px;
}

.attachment-popup.show {
  display: block;
  animation: fadeIn 0.2s ease-in-out;
}

.attachment-option {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.attachment-option:hover {
  background-color: var(--block);
}

.attachment-option-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.attachment-option-text {
  color: var(--h4);
  font-size: 14px;
}

/* File Preview Modal */
.file-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.file-preview-container {
  width: 90%;
  max-width: 600px;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

.file-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-right);
}
.file-preview-header h3 {
  color: white;
}
.file-preview-title {
  color: var(--h4);
  font-size: 18px;
  font-weight: 500;
}

.file-preview-close {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.file-preview-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.file-preview-item {
  margin-bottom: 16px;
  position: relative;
}

.file-preview-image {
  width: 100%;
  border-radius: 8px;
  object-fit: contain;
  max-height: 400px;
}

.file-preview-details {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
}

.file-icon {
  max-width: 40px;
  max-height: 40px;
  width: auto;
  height: auto;
  display: flex;
}

.file-info {
  flex: 1;
}

.file-name {
  color: var(--h4);
  font-size: 10px;
  margin-bottom: 4px;
  word-break: break-all;
}

.file-size {
  color: var(--primary);
  font-size: 12px;
}

.file-preview-caption {
  margin-top: 16px;
}

.file-caption-input {
  width: 100%;
  background-color: white;
  /*    border: none;*/
  border-radius: 8px;
  padding: 12px;
  resize: none;
  outline: none;
}

.file-preview-footer {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid var(--border-right);
}

.send-file-btn {
  background-color: #ffcf58;
  color: white;
  border: none;
  border-radius: 24px;
  padding: 8px 24px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-file-btn:hover {
  color: #6c757d;
}

/* File Message Styles */
.file-message-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 4px;
}

/* Image Message */
.image-message img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  display: block;
}

/* Video Message */
.video-message {
  position: relative;
}

.video-thumbnail {
  width: 100%;
  max-height: 200px;
  border-radius: 8px;
  object-fit: cover;
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.video-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Document Message */
.file-message {
  display: flex;
  align-items: center;
  /* gap: 10px; */
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 8px;
  width: 100%;
}
/* Upload Status Overlays */
.upload-progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
}

.upload-progress-circle {
  width: 40px;
  height: 40px;
  border: 2px solid white;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

.upload-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(220, 53, 69, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
}

.upload-error-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

/* Download Status */
.download-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.file-preview-details {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.file-icon-img {
  flex-shrink: 0;
}
.file-info {
  display: flex;
  flex-direction: column;
}
.file-preview-details .file-name {
  color: var(--block);
  font-size: 10px;
  margin-bottom: 4px;
  word-break: break-all;
}
