﻿/* Group CHAT SCREEN */
.group {
  display: none;
  position: relative;
  flex: 30%;
  background-color: var(--side);
  border-right: 1px solid var(--border-right);
}
.teams-list {
    display: none;
    position: relative;
    flex: 30%;
    background-color: var(--side);
    border-right: 1px solid var(--border-right);
}
/* .header-Chat {
  position: relative;
  width: 100%;
  height: 110px;
  background: var(--header-bg);
  display: flex;
  padding: 0 15px;
  padding-top: 65px;
} */

.group .ICON {
  width: 53px;
}

.group .icons {
  flex: none;
  color: var(--head-title);
  padding: 0 7px;
  margin-left: 3px;
  display: inline-table;
}

.group .search-bar {
  margin-top: 7px;
  margin-bottom: 7px;
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 15px;
}

.group .search-bar div {
  width: 100%;
}

.group .search-bar div input {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  border-bottom: 2px solid rgba(134, 150, 160, 0.15);
  padding: 6px;
  height: 38px;
  font-size: 14px;
  align-items: center;
  color: var(--h4);
  padding-left: 45px;
}

.group .search-bar div input::placeholder {
  font-size: 13px;
  color: var(--primary);
}

.group .search-bar div i {
  position: absolute;
  left: 30px;
  top: 7px;
  font-size: 1em;
  color: var(--icons);
  justify-content: center;
  align-items: center;
}





.search-bar {
    margin-top: 7px;
    margin-bottom: 7px;
    position: relative;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 15px;
}

.search-bar div {
        width: 100%;
    }

.search-bar div input {
            width: 100%;
            border: none;
            outline: none;
            background: transparent;
            border-bottom: 2px solid rgba(134, 150, 160, 0.15);
            padding: 6px;
            height: 38px;
            font-size: 14px;
            align-items: center;
            color: var(--h4);
            padding-left: 45px;
        }

.search-bar div input::placeholder {
                font-size: 13px;
                color: var(--primary);
            }

.search-bar div i {
            position: absolute;
            left: 30px;
            top: 7px;
            font-size: 1em;
            color: var(--icons);
            justify-content: center;
            align-items: center;
        }
.chats-group {
  position: relative;
  height: calc(100% - 162px);
  overflow-y: auto;
}

    .chats-group .block {
        position: relative;
        width: 100%;
        display: flex;
        align-items: center;
        padding: 10px;
        border-bottom: 0.7px solid var(--block);
        border-right: none;
        cursor: pointer;
    }

.chats-group .block.top {
  border-top: 0.7px solid var(--block);
}

.chats-group .block.group {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 15px;
  cursor: pointer;
}

.chats-group .block.group .iconBox {
  background: #00a884;
  display: flex;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
}

.chats-group .block.group .iconBox i {
  color: #dfe5e7;
  font-size: 24px;
  padding-bottom: 4px;
}

.chats-group .block.group .head h4 {
  color: #dfe5e7;
  font-size: 17px;
  font-weight: 400;
  padding-left: 10px;
  margin-bottom: -4px;
}

.elements {
    position: relative;
    height: calc(100% - 162px);
    overflow-y: auto;
}
.elements .block.top {
    border-top: 0.7px solid var(--block);
}

.elements .block.group {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 15px;
    cursor: pointer;
}

.elements .block.group .iconBox {
    background: #00a884;
    display: flex;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    align-items: center;
    justify-content: center;
}

.elements .block.group .iconBox i {
    color: #dfe5e7;
    font-size: 24px;
    padding-bottom: 4px;
}

.elements .block.group .head h4 {
    color: #dfe5e7;
    font-size: 17px;
    font-weight: 400;
    padding-left: 10px;
    margin-bottom: -4px;
}
    .chats-group .a {
        padding-top: 5px;
        /* padding-bottom: 5px; */
        padding-right: 10px;
    }

.chats-group .a h3 {
  color: var(--yur-name-abt);
  font-size: 17px;
  font-weight: 300;
}

.chats-group .block:hover {
  background: var(--secondary);
}

.chats-group .block .imgBox {
  position: relative;
  min-width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 50%;
  margin-left: 10px;
}

.chats-group .block .h-text {
  position: relative;
  width: 100%;
}

.chats-group .block .h-text .head {
  display: flex;
  justify-content: space-between;
  margin-bottom: -6px;
}

.chats-group .block .h-text .head h4 {
  font-size: 16px;
  font-weight: 400;
  color: var(--h4);
  letter-spacing: 0.4px;
}

.elements .block:hover {
    background: var(--secondary);
}

.elements .block .imgBox {
    position: relative;
    min-width: 50px;
    height: 50px;
    overflow: hidden;
    border-radius: 50%;
    margin-left: 10px;
}

.elements .block .h-text {
    position: relative;
    width: 100%;
}

.elements .block .h-text .head {
        display: flex;
        justify-content: space-between;
        margin-bottom: -6px;
    }

.elements .block .h-text .head h4 {
            font-size: 16px;
            font-weight: 400;
            color: var(--h4);
            letter-spacing: 0.4px;
        }

.message {
  display: flex;
  justify-content: space-between;
  align-items: center;
}




/* css/group.css or style.css */
.participant-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding-right: 15px; /* Add padding for checkbox */
}

.participant-select {
    margin-right: 15px;
    position: relative;
    width: 20px;
    height: 20px;
}

    .participant-select input[type="checkbox"] {
        opacity: 0; /* Hide default checkbox */
        position: absolute;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }

    .participant-select label {
        width: 20px;
        height: 20px;
        background-color: var(--intro-svg4);
        border: 1px solid var(--intro-svg4);
        border-radius: 50%;
        display: inline-block;
        position: relative;
        cursor: pointer;
    }

        /* Style for the checkmark */
        .participant-select label::after {
            content: "";
            display: block;
            position: absolute;
            top: 3px;
            left: 6px;
            width: 6px;
            height: 10px;
            border: solid #fff;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
            opacity: 0; /* Hidden by default */
            transition: opacity 0.2s ease-in-out;
        }

    /* Show checkmark when checkbox is checked */
    .participant-select input[type="checkbox"]:checked + label::after {
        opacity: 1;
    }

    /* Change background color when checked */
    .participant-select input[type="checkbox"]:checked + label {
        background-color: var(--primary); /* Or your desired check color */
        border-color: var(--primary);
    }

.selected-participants-container {
    display: flex;
    flex-wrap: wrap;
    padding: 10px 15px;
    border-bottom: 1px solid var(--border-color);
    min-height: 40px; /* Ensure it has some height even when empty */
    gap: 5px;
}

.selected-participant-chip {
    display: flex;
    align-items: center;
    background-color: var(--intro-svg4);
    color: var(--selected-chip-text);
    padding: 3px 8px;
    border-radius: 15px;
    font-size: 0.9em;
}

    .selected-participant-chip img {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-left: 5px;
    }

    .selected-participant-chip .remove-participant {
        margin-right: 5px;
        cursor: pointer;
        font-weight: bold;
        line-height: 1;
    }

    .selected-participant-chip .admin-badge {
        margin-left: 5px;
        font-size: 0.8em;
        background-color: var(--primary);
        color: white;
        padding: 1px 4px;
        border-radius: 3px;
        cursor: pointer; /* To indicate it's clickable for toggling */
    }

.next-step-button-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
}

.next-step-button {
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: background-color 0.3s ease;
}

    .next-step-button:disabled {
        background-color: var(--icon-color); /* Greyed out */
        cursor: not-allowed;
    }

    .next-step-button svg {
        fill: white;
    }

/* css/create-group.css or style.css */
.create-group-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--app-bg);
    z-index: 101; /* Ensure it's above the main chat list */
    display: none; /* Hidden by default */
    flex-direction: column;
}

.group-info-section {
    display: flex;
    align-items: center;
    padding: 20px;
    background-color: var(--secondary); /* Slightly different background */
    border-bottom: 1px solid var(--border-color);
}

.group-picture-container {
    margin-right: 20px;
    position: relative;
}

.group-picture-placeholder {
    width: 60px;
    height: 60px;
    background-color: var(--icon-color); /* Placeholder color */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

    .group-picture-placeholder svg {
        fill: var(--app-bg);
    }

#groupPicturePreview {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    cursor: pointer;
}

.group-name-desc-container {
    flex-grow: 1;
}

    .group-name-desc-container input {
        width: 100%;
        background: transparent;
        border: none;
        border-bottom: 1px solid var(--primary);
        color: var(--intro-svg4);
        padding: 8px 0;
        margin-bottom: 10px;
        font-size: 1em;
    }

        .group-name-desc-container input:focus {
            outline: none;
            border-bottom: 2px solid var(--primary);
        }

        .group-name-desc-container input::placeholder {
            color: var(--text-secondary);
        }

.group-members-list-section {
    padding: 15px;
    flex-grow: 1;
    overflow-y: auto;
}

    .group-members-list-section h4 {
        color: var(--primary);
        font-size: 0.9em;
        margin-bottom: 10px;
        text-transform: uppercase;
    }

#finalMemberList {
    display: grid;
    grid-template-columns: repeat( auto-fill, minmax(80px, 1fr) ); /* Adjust column size */
    gap: 15px;
}

.final-member-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
}

    .final-member-item img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-bottom: 5px;
    }

    .final-member-item span {
        font-size: 0.8em;
        color: var(--text-primary);
        word-break: break-word;
    }

    .final-member-item .admin-indicator {
        position: absolute;
        top: 0;
        right: 5px;
        background-color: var(--primary);
        color: white;
        font-size: 0.7em;
        padding: 2px 4px;
        border-radius: 3px;
    }

.create-group-button-container {
    display: flex;
    justify-content: center;
    padding: 20px;
}

.create-group-button {
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: background-color 0.3s ease;
}

    .create-group-button:disabled {
        background-color: var(--icon-color); /* Greyed out */
        cursor: not-allowed;
    }

    .create-group-button svg {
        fill: white;
    }



/* here desgin two */
.app-notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(100%); /* ابدأ مخفيًا بالأسفل */
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    z-index: 1000;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease-out, opacity 0.3s ease-out;
    opacity: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

    .app-notification.show {
        transform: translateX(-50%) translateY(0); /* حركه للأعلى */
        opacity: 1;
    }

.notification-icon {
    font-weight: bold;
    font-size: 16px;
}

.notification-success {
    background-color: #4CAF50;
}

.notification-error {
    background-color: #f44336;
}

.notification-warning {
    background-color: #ff9800;
}

.notification-info {
    background-color: #2196F3;
}

.create-group-confirm-btn .loader {
    border: 2px solid #f3f3f3; /* Light grey */
    border-top: 2px solid #3498db; /* Blue */
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* تحسينات واجهة المستخدم لشاشة اختيار جهات الاتصال */
.chats-group .block, .elements .block {
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative; /* للسماح بوضع علامة الاختيار */
    padding: 10px;
    display: flex;
    align-items: center;
}

.chats-group .block:hover {
        background-color: #f0f0f0; /* لون عند التحويم */
    }

.chats-group .block.selected {
        background-color: #e0f2f7; /* لون للخلفية عند الاختيار */
    }

    .chats-group .block .selection-overlay {
        position: absolute;
        top: 5px;
        right: 5px;
        background-color: #00a884; /* لون علامة الاختيار */
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        opacity: 0; /* مخفي افتراضيًا */
        transition: opacity 0.2s ease;
    }

    .chats-group .block.selected .selection-overlay {
        opacity: 1; /* إظهار عند الاختيار */
    }

.contact-letter-header {
    margin: 2px 5px 2px 5px;
    background-color: #eeeeee8a;
    font-weight: bold;
    /* color: #555; */
    /* position: sticky; */
    /* top: 0; */
    display: flex;
    align-items: center;
    padding: 0 8px 0 8px;
    border-radius: 4px;
    font-size: 10px;
}

.no-contacts, .no-results, .loading-contacts, .error-loading ,.no-chats{
    padding: 20px;
    text-align: center;
    color: #888;
}

/* زر المتابعة */
.continue-button-container {
    position: absolute;
    bottom: 20px;
    left: 20px;
    z-index: 10;
    display: none; /* يبدأ مخفيًا */
    justify-content: center;
    align-items: center;
}

.continue-button {
    background-color: #ffcf58;
    color: white;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: background-color 0.2s ease, transform 0.1s ease;
}

    .continue-button:hover {
        background-color: #ffcf588a;
    }

    .continue-button:active {
        transform: scale(0.95);
    }

    .continue-button:disabled {
        background-color: #ccc;
        cursor: not-allowed;
        opacity: 0.7;
    }

/* شاشة تفاصيل المجموعة */
.create-group-details {
    overflow-y: auto;
    padding-bottom: 80px;
    position: relative;
    flex: 30%;
    background-color: var(--side);
    border-right: 1px solid var(--border-right);
}

.group-info-form {
    padding: 15px;
}

.group-avatar-section {
    text-align: center;
    margin-bottom: 20px;
    position: relative;
    width: 120px; /* حجم محدد */
    margin-left: auto;
    margin-right: auto;
}

.group-avatar-wrapper {
    display: inline-block;
    position: relative;
    cursor: pointer;
    border-radius: 50%;
    overflow: hidden; /* لإخفاء أجزاء الصورة الزائدة */
    width: 100px;
    height: 100px;
    background-color: #DFE5E7; /* لون خلفية افتراضي */
    display: flex;
    align-items: center;
    justify-content: center;
}

.group-avatar-preview svg {
    width: 70%; /* حجم الأيقونة الافتراضية */
    height: 70%;
    fill: #a0a0a0; /* لون الأيقونة الافتراضية */
}

.group-avatar-preview img.group-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover; /* لضمان ملء الصورة للدائرة */
}

.avatar-hint {
    font-size: 12px;
    color: #888;
    margin-top: 5px;
}

.form-group {
    margin-bottom: 15px;
}

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #555;
        font-size: 14px;
    }

    .form-group input[type="text"] {
        width: 100%;
        padding: 10px;
        border: none;
        border-bottom: 1px solid #ccc;
        font-size: 16px;
        background-color: transparent;
        transition: border-color 0.2s ease;
    }

        .form-group input[type="text"]:focus {
            outline: none;
            border-bottom-color: #00a884; /* لون واتساب */
        }

.selected-members-section h3 {
    font-size: 14px;
    color: #00a884;
    margin-bottom: 10px;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.selected-members-list-details {
    max-height: 200px; /* تحديد ارتفاع أقصى للقائمة */
    overflow-y: auto; /* إضافة تمرير إذا لزم الأمر */
}

.selected-member {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

    .selected-member:last-child {
        border-bottom: none;
    }

.member-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px; /* استخدام margin-right في العربية */
    margin-left: 10px; /* إضافة هامش أيسر */
}

.member-info {
    flex-grow: 1;
    margin-right: 10px; /* هامش بين المعلومات والأزرار */
}

.member-name {
    font-weight: bold;
    font-size: 13px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-break: break-all;
}


.member-role {
    font-size: 12px;
    color: #888;
}

    .member-role.role-admin {
        color: #00a884; /* لون مميز للمشرف */
        font-weight: bold;
    }

.member-actions button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    margin-left: 5px; /* هامش بين الأزرار */
    color: #888;
    transition: color 0.2s ease;
}

    .member-actions button:hover {
        color: #555;
    }

.member-actions .toggle-admin-btn:hover {
    color: #00a884; /* لون مميز عند التحويم */
}

.member-actions .remove-member-btn:hover {
    color: #f44336; /* لون مميز عند التحويم */
}

.create-group-footer {
    position: relative; /* تثبيت الزر في الأسفل */
    bottom: 0;
    left: 0;
    width: 100%; /* أو عرض القائمة الجانبية */
    padding: 15px;
    background-color: #f0f2f5; /* لون خلفية للفوتر */
    box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
    text-align: center; /* توسيط الزر */
    z-index: 5; /* لضمان ظهوره فوق قائمة الأعضاء */
}


.create-group-confirm-btn {
    background-color: #00a884;
    color: white;
    border: none;
    border-radius: 10px;
    padding: 10px 25px;
    font-size: 16px;
    width: 100%;
    /* text-align: center; */
    /* align-items: center; */
    cursor: pointer;
    transition: background-color 0.2s ease;
    /* للسماح بوضع النص والمؤشر بجانب بعض */
    align-items: center;
}

    .create-group-confirm-btn:hover:not(:disabled) {
        background-color: #008c6e;
    }

    .create-group-confirm-btn:disabled {
        background-color: #ccc;
        cursor: not-allowed;
        opacity: 0.7;
    }