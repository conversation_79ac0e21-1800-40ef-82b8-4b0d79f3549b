/* Right Side */
.container .Intro-Left {
    flex: 70%;
    display: block;
/*    position: relative;*/
    background: var(--intro-bg);
    border-bottom: 6px solid var(--intro-border);
    border-radius: var(--bs-card-border-radius) 0px 0px var(--bs-card-border-radius);
}

.Intro-Left .intro {
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
/*    position: absolute;*/
    align-items: center;
    flex-direction: column;
    justify-content: center;
}

.Intro-Left .intro-sub {
    display: flex;
    max-height: 100%;
    flex-direction: column;
}

.Intro-Left .intro-sub .intro-svg {
    display: flex;
    flex-direction: row;
    justify-content: center;
}

.Intro-Left .intro-text {
    font-size: 32px;
    margin-bottom: 0;
    margin-top: 30px;
    font-weight: 400;
    text-align: center;
    line-height: 32px;
    color: var(--intro-text);
}

.Intro-Left .intro-span {
    font-size: 14px;
    margin-top: 16px;
    font-weight: 400;
    line-height: 24px;
    text-align: center;
    color: var(--intro-sub-text);
}