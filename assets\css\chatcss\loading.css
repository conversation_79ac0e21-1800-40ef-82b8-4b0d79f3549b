﻿/* Reset and Base Styles */
/** {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Cairo", sans-serif;
    background-color: #f0f2f5;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}*/

/* Container Styles */
/*.loading-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
    text-align: center;
}*/
.loading-container {
/*    position: fixed;
    top: 0;
    left: 0;*/
    width: 100%;
/*    max-width: 400px;*/
    padding: 20px;
    text-align: center;
    height: calc(100vh - 40px);
    background-color: #f0f2f5;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Loading State Styles */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    position: relative;
}

.spinner {
    width: 100%;
    height: 100%;
    border: 3px solid #e0e0e0;
    border-top: 3px solid #ffcf58;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    color: #ffcf58;
    font-size: 18px;
    font-weight: 600;
}

/* Error State Styles */
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.error-icon {
    color: #dc3545;
    margin-bottom: 10px;
}

.error-message {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
}

/* Offline State Styles */
.offline-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.offline-icon {
    color: #6c757d;
    margin-bottom: 10px;
}

.offline-message {
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.offline-submessage {
    color: #6c757d;
    font-size: 14px;
}

/* Button Styles */
.retry-button {
    background-color: #ffcf58;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-family: "Cairo", sans-serif;
}

    .retry-button:hover {
        background-color: #075e54;
    }

    .retry-button:active {
        transform: scale(0.98);
    }

/* Animations */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 480px) {
    .loading-container {
        padding: 15px;
    }

    .loading-text,
    .error-message,
    .offline-message {
        font-size: 16px;
    }

    .retry-button {
        padding: 10px 20px;
        font-size: 14px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: light) {
    
    .loading-text,
    .error-message,
    .offline-message {
        color: #111b21;
    }

    .offline-submessage {
        color: #8696a0;
    }

    .spinner {
        border-color: #e0e0e0;
        border-top-color: #ffcf58;
    }

    .chat-progress-bar {
        background-color: #2a3942;
    }

    .loading-details {
        color: #8696a0;
    }
}

/* Progress Bar Styles */
.progress-container {
    width: 100%;
    max-width: 300px;
    margin-top: 10px;
}

.chat-progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background-color: #ffcf58;
    width: 0%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    margin-top: 8px;
    font-size: 14px;
    color: #ffcf58;
    font-weight: 600;
}

.loading-details {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}
