/* Message Container Styles */
.containerMessage {
    margin: 8px 0;
    padding: 0 16px;
}

/* Message Bubble Styles */
.chatMessage {
    max-width: 65%;
    padding: 8px 12px;
    border-radius: 8px;
    position: relative;
    word-wrap: break-word;
}

.my-chat {
    background-color: #DCF8C6;
    margin-left: auto;
    border-top-right-radius: 0;
}

.frnd-chat {
    background-color: #FFFFFF;
    margin-right: auto;
    border-top-left-radius: 0;
    box-shadow: 0 0.5px 0.5px rgba(0, 0, 0, 0.1);
}

/* Reply Context Styles */
.reply-context {
    border-left: 3px solid #128C7E;
    padding-left: 8px;
    margin-bottom: 4px;
    font-size: 0.85em;
}

.reply-sender {
    color: #128C7E;
    font-weight: 500;
    display: block;
}

.reply-content {
    color: #667781;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Message Footer Styles */
.msg-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 4px;
    font-size: 0.75em;
    color: #667781;
    margin-top: 4px;
}

/* File Message Styles */
.file-message {
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 500;
    margin-bottom: 4px;
}

.file-size {
    font-size: 0.85em;
    color: #667781;
}

/* Image Message Styles */
.image-message {
    max-width: 300px;
    border-radius: 8px;
    overflow: hidden;
}

.image-message img {
    width: 100%;
    height: auto;
    display: block;
}

/* Video Message Styles */
.video-message {
    max-width: 300px;
    position: relative;
}

.video-thumbnail {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

.video-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8em;
}

/* Audio Message Styles */
.audio-message {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 200px;
}

.audio-player {
    flex: 1;
    height: 32px;
}

/* Contact Card Styles */
.contact-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
}

.contact-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
}

.contact-info {
    flex: 1;
}

.contact-name {
    font-weight: 500;
    margin-bottom: 4px;
}

.contact-number {
    font-size: 0.85em;
    color: #667781;
}

/* Location Message Styles */
.location-message {
    max-width: 300px;
}

.location-map {
    width: 100%;
    height: 200px;
    border-radius: 8px;
    overflow: hidden;
}

.location-info {
    padding: 8px;
}

/* Poll Message Styles */
.poll-message {
    max-width: 300px;
}

.poll-title {
    font-weight: 500;
    margin-bottom: 12px;
}

.poll-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.poll-option {
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.poll-option:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.poll-option.selected {
    background-color: #DCF8C6;
}

/* Message Status Indicators */
.message-status {
    display: inline-flex;
    align-items: center;
    gap: 2px;
}

.message-status svg {
    width: 16px;
    height: 16px;
}

/* Message Actions Menu */
.message-actions {
    position: absolute;
    top: 0;
    right: 100%;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 4px 0;
    display: none;
}

.message-actions.show {
    display: block;
}

.message-action-item {
    padding: 8px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #667781;
}

.message-action-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Message Time */
.message-time {
    font-size: 0.75em;
    color: #667781;
}

/* Message Status Colors */
.status-sent {
    color: #667781;
}

.status-delivered {
    color: #4FC3F7;
}

.status-read {
    color: #4FC3F7;
} 