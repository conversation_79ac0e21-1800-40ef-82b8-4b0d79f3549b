/* Quick Replies Styles - WhatsApp Business Web Style */

/* Quick Replies Button */
.quick-replies-btn {
  background: transparent;
  border: none;
  color: var(--icons);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quick-replies-btn:hover {
  background-color: var(--icon-focus);
}

.quick-replies-btn svg {
  width: 24px;
  height: 24px;
}

/* Quick Replies Dropdown - Inline Style (like reply-div) */
.quick-replies-dropdown-inline {
  background: var(--secondary);
  border-radius: 8px;
  margin-bottom: 8px;
  border: 1px solid var(--border-right);
}

.quick-replies-container {
  background: var(--secondary);
  border-radius: 8px;
  overflow: hidden;
}

.quick-replies-header-inline {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-right);
  background: var(--block);
}

.quick-replies-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quick-replies-header-right {
  display: flex;
  align-items: center;
  gap: 4px;
}

.quick-replies-icon {
  width: 20px;
  height: 20px;
  background: var(--unread);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.quick-replies-title-inline {
  color: var(--h4);
  font-size: 14px;
  font-weight: 500;
}

.quick-replies-manage-btn-inline {
  background: transparent;
  border: none;
  color: var(--icons);
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.quick-replies-manage-btn-inline:hover {
  background-color: var(--icon-focus);
  color: var(--unread);
}

.quick-replies-close {
  background: transparent;
  border: none;
  color: var(--icons);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quick-replies-close:hover {
  background-color: var(--icon-focus);
}

.quick-replies-list-inline {
  max-height: 300px;
  overflow-y: auto;
}

/* Quick Replies Dropdown - Original Popup Style */
.quick-replies-dropdown {
  position: absolute;
  bottom: 50px;
  right: 10px;
  background: var(--dropdown);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(var(--shadow-rgb), 0.26);
  max-width: 300px;
  max-height: 400px;
  overflow: hidden;
  z-index: 1000;
  display: none;
  flex-direction: column;
}

.quick-replies-dropdown.show {
  display: flex;
}

/* Quick Replies Header */
.quick-replies-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-right);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-replies-title {
  color: var(--h4);
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.quick-replies-manage-btn {
  background: transparent;
  border: none;
  color: var(--icons);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 12px;
}

.quick-replies-manage-btn:hover {
  background-color: var(--icon-focus);
}

/* Search Box */
.quick-replies-search {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-right);
}

.quick-replies-search input {
  width: 100%;
  background: var(--block);
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  color: var(--h4);
  font-size: 14px;
  outline: none;
}

.quick-replies-search input::placeholder {
  color: var(--primary);
}

/* Quick Replies List */
.quick-replies-list {
  flex: 1;
  overflow-y: auto;
  max-height: 300px;
}

.quick-reply-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid rgba(var(--shadow-rgb), 0.1);
  transition: background-color 0.2s ease;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.quick-reply-item:hover {
  background-color: var(--listItemHover);
}

.quick-reply-item:last-child {
  border-bottom: none;
}

/* Inline quick reply items */
.quick-replies-list-inline .quick-reply-item {
  padding: 10px 16px;
  border-bottom: 1px solid var(--border-right);
}

.quick-replies-list-inline .quick-reply-item:hover {
  background-color: var(--listItemHover);
}

.quick-replies-list-inline .quick-reply-item:last-child {
  border-bottom: none;
}

.quick-replies-list-inline .quick-reply-item.selected {
  background-color: var(--listItemHover);
  border-left: 3px solid var(--unread);
}

/* Quick Reply Icon */
.quick-reply-icon {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 2px;
}

.quick-reply-icon.text {
  background: #25d366;
}
.quick-reply-icon.image {
  background: #ac44cf;
}
.quick-reply-icon.video {
  background: #d3396d;
}
.quick-reply-icon.audio {
  background: #ff9800;
}
.quick-reply-icon.document {
  background: #5157ae;
}
.quick-reply-icon.contact {
  background: #00bcd4;
}
.quick-reply-icon.location {
  background: #4caf50;
}
.quick-reply-icon.poll {
  background: #9c27b0;
}
.quick-reply-icon.voice {
  background: #ff5722;
}
.quick-reply-icon.file {
  background: #607d8b;
}

.quick-reply-icon svg {
  width: 12px;
  height: 12px;
  fill: white;
}

/* Quick Reply Content */
.quick-reply-content {
  flex: 1;
  min-width: 0;
}

.quick-reply-title {
  color: var(--h4);
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.quick-reply-preview {
  color: var(--primary);
  font-size: 12px;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Quick Replies Modal */
.quick-replies-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: none;
  align-items: center;
  justify-content: center;
}

.quick-replies-modal.show {
  display: flex;
}

.quick-replies-modal-content {
  background: var(--side);
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Modal Header */
.quick-replies-modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-right);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-replies-modal-title {
  color: var(--h4);
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

.quick-replies-modal-close {
  background: transparent;
  border: none;
  color: var(--icons);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  font-size: 20px;
}

.quick-replies-modal-close:hover {
  background-color: var(--icon-focus);
}

/* Modal Body */
.quick-replies-modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

/* Form Styles */
.quick-reply-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  color: var(--h4);
  font-size: 14px;
  font-weight: 500;
}

.form-input,
.form-select,
.form-textarea {
  background: var(--block);
  border: 1px solid var(--border-right);
  border-radius: 6px;
  padding: 12px;
  color: var(--h4);
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  border-color: var(--unread);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-file-input {
  display: none;
}

.form-file-label {
  background: var(--block);
  border: 2px dashed var(--border-right);
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s ease;
  color: var(--primary);
}

.form-file-label:hover {
  border-color: var(--unread);
}

.form-file-label.has-file {
  border-style: solid;
  border-color: var(--unread);
  background: rgba(0, 168, 132, 0.1);
}

/* File Preview */
.file-preview {
  display: none;
  margin-top: 12px;
  padding: 12px;
  background: var(--block);
  border-radius: 6px;
  border: 1px solid var(--border-right);
}

.file-preview.show {
  display: block;
}

.file-preview-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-preview-icon {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.file-preview-info {
  flex: 1;
  min-width: 0;
}

.file-preview-name {
  color: var(--h4);
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-preview-size {
  color: var(--primary);
  font-size: 12px;
  margin: 0;
}

.file-preview-remove {
  background: transparent;
  border: none;
  color: var(--icons);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
}

.file-preview-remove:hover {
  background-color: var(--icon-focus);
}

/* Modal Footer */
.quick-replies-modal-footer {
  padding: 20px 24px;
  border-top: 1px solid var(--border-right);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
}

.btn-secondary {
  background: var(--block);
  color: var(--h4);
}

.btn-secondary:hover {
  background: var(--listItemHover);
}

.btn-primary {
  background: var(--unread);
  color: white;
}

.btn-primary:hover {
  background: #00916a;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

/* Management List */
.quick-replies-management-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-reply-management-item {
  background: var(--block);
  border-radius: 6px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid var(--border-right);
}

.quick-reply-management-content {
  flex: 1;
  min-width: 0;
}

.quick-reply-management-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: transparent;
  border: none;
  color: var(--icons);
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
}

.action-btn:hover {
  background-color: var(--icon-focus);
}

.action-btn.edit:hover {
  color: var(--unread);
}

.action-btn.delete:hover {
  color: #dc3545;
}

/* Empty State */
.quick-replies-empty {
  text-align: center;
  padding: 40px 20px;
  color: var(--primary);
}

.quick-replies-empty-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  opacity: 0.5;
}

.quick-replies-empty-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: var(--h4);
}

.quick-replies-empty-text {
  font-size: 14px;
  margin: 0;
}

/* Loading State */
.quick-replies-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--primary);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-right);
  border-top: 2px solid var(--unread);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .quick-replies-dropdown {
    right: 5px;
    left: 5px;
    max-width: none;
  }

  .quick-replies-modal-content {
    width: 95%;
    margin: 20px;
  }

  .quick-replies-modal-body {
    padding: 16px;
  }

  .quick-replies-modal-footer {
    padding: 16px;
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

/* RTL Support */
[dir="rtl"] .quick-replies-dropdown {
  right: auto;
  left: 10px;
}

[dir="rtl"] .quick-reply-item {
  flex-direction: row-reverse;
}

[dir="rtl"] .quick-reply-management-item {
  flex-direction: row-reverse;
}

[dir="rtl"] .file-preview-content {
  flex-direction: row-reverse;
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: light) {
  .quick-replies-dropdown {
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .quick-replies-modal-content {
    background: white;
  }
}

/* Animation for dropdown */
.quick-replies-dropdown {
  transform: translateY(10px);
  opacity: 0;
  transition: all 0.2s ease;
}

.quick-replies-dropdown.show {
  transform: translateY(0);
  opacity: 1;
}

/* Scrollbar Styling */
.quick-replies-list::-webkit-scrollbar,
.quick-replies-modal-body::-webkit-scrollbar {
  width: 6px;
}

.quick-replies-list::-webkit-scrollbar-track,
.quick-replies-modal-body::-webkit-scrollbar-track {
  background: transparent;
}

.quick-replies-list::-webkit-scrollbar-thumb,
.quick-replies-modal-body::-webkit-scrollbar-thumb {
  background: var(--scrollBar);
  border-radius: 3px;
}

.quick-replies-list::-webkit-scrollbar-thumb:hover,
.quick-replies-modal-body::-webkit-scrollbar-thumb:hover {
  background: #5a6b7a;
}
