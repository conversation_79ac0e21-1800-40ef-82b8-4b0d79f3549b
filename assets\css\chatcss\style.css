.animate__animated {
  --animate-duration: 200ms;
  --animate-delay: 0.3s;
}

:root {
  --appBackground: #0a1014;
  --side: #111b21;
  --right-side: #111b21;
  --rightSide-opacity: 0.06;
  --secondary: #202c33;
  --border-right: rgba(233, 237, 239, 0.12);
  --icons: #aebac1;
  --tick-icons: #53bdeb;
  --primary: #8696a0;
  --block: #2a3942;
  --block-footer: #2a3942;
  --h4: #e9edef;
  --unread: #00a884;
  --dropdown: #233138;
  --listItem: #d1d7db;
  --chat-date-bg: #1e2a30;
  --chat-date: rgba(241, 241, 242, 0.92);
  --chat-encrption-bg: #1e2a30;
  --chat-encryption: #ffd279;
  --shadow-rgb: 11, 20, 26;
  --listItemHover: #182229;
  --chat: #005c4b;
  --icon-focus: hsla(0, 0%, 100%, 0.1);
  --scrollBar: #445561;
  --head-title: #d9dee0;
  --header-bg: #ffcf58;
  --new-g: #dfe5e7;
  --settings-icon: #8696a0;
  --profile-bg: #182229;
  --profile-BGG: #111b21;
  --yur-name-abt: #ffcf58;
  --border-not: rgba(134, 150, 160, 0.15);
  --checked-border: #ffcf58;
  --checked-bg: #ffcf58;
  --uncheck: rgb(209, 215, 219);
  --uncheck-circle: #8696a0;
  --last-seen-title: #54656f;
  --learn-more: #53bdeb;
  --b-con: #667781;
  --ul: #667781;
  --modal-bg: rgba(11, 20, 26, 0.368);
  --modal-content-bg: #3b4a54;
  --modal-text: #d1d7db;
  --modal-btn: rgba(134, 150, 160, 0.15);
  --modal-btn2: #00a884;
  --modal-btn-text: #111b21;
  --modal-btn-hover: #06cf9c;
  --modal-shadow: 11, 20, 26;
  --hover-wallpaper: #3b4a54;
  --border-active: #667781;
  --contact-info-h2: #d9dee0;
  --danger: #f15c6d;
  --status-text: #8696a0;
  --status-right-text: #9494a1;
  --intro-bg: #222e35;
  --intro-border: #ffcf58;
  --intro-svg1: #364147;
  --intro-svg2: #f1f1f2;
  --intro-opacity: 0.38;
  --intro-svg3: #eefaf6;
  --intro-svg4: #dff3ed;
  --intro-svg5: #dff3ed;
  --intro-text: rgba(233, 237, 239, 0.88);
  --intro-sub-text: #8696a0;
  --emoji-active-after: #00af9c;
  --emoji-tab-active: rgba(241, 241, 242, 0.63);
  --emoji-tab: rgba(241, 241, 242, 0.32);
  --emoji-label: rgba(241, 241, 242, 0.45);
}

.light-theme {
  --appBackground: linear-gradient(
    #00a884 0%,
    #00a884 130px,
    #d9dbd5 130px,
    #d9dbd5 100%
  );
  --side: #fff;
  --right-side: #efeae2;
  --rightSide-opacity: 0.488;
  --secondary: #f0f2f5;
  --border-right: #38353c55;
  --icons: #54656f;
  --tick-icons: #53bdeb;
  --primary: #54656f;
  --block: #e2e6ec;
  --block-footer: #fff;
  --h4: #111b21;
  --unread: #25d366;
  --dropdown: #fff;
  --shadow-rgb: 11, 20, 26;
  --listItem: ##3b4a54;
  --listItemHover: #f5f6f6;
  --chat: #d9fdd3;
  --chat-date-bg: #fff;
  --chat-date: #1e2a30;
  --chat-encrption-bg: #ffeecd;
  --chat-encryption: #54656f;
  --icon-focus: rgba(11, 20, 26, 0.1);
  --scrollBar: #c0c6ce;
  --head-title: #fff;
  --header-bg: #ffcf58;
  --new-g: #111b21;
  --settings-icon: #8696a0;
  --profile-bg: #fff;
  --profile-BGG: #f0f2f5;
  --yur-name-abt: #ffcf58;
  --border-not: #e9edef;
  --checked-border: #ffcf58;
  --checked-bg: #ffcf58;
  --uncheck: rgba(59, 74, 84);
  --uncheck-circle: #667781;
  --last-seen-title: #54656f;
  --learn-more: #027eb5;
  --b-con: #8696a0;
  --ul: #667781;
  --modal-bg: hsla(0, 0%, 100%, 0.85);
  --modal-content-bg: #fff;
  --modal-text: #3b4a54;
  --modal-btn: rgba(134, 150, 160, 0.15);
  --modal-btn2: #00a884;
  --modal-btn-text: #111b21;
  --modal-btn-hover: #06cf9c;
  --modal-shadow: 11, 20, 26;
  --hover-wallpaper: #fff;
  --border-active: #009de2;
  --contact-info-h2: #1e2a30;
  --danger: #ea0038;
  --status-text: gainsboro;
  --status-right-text: #9494a1;
  --intro-bg: #f0f2f5;
  --intro-border: #ffcf58;
  --intro-svg1: #ffcf585c;
  --intro-svg2: #ffcf585c;
  --intro-opacity: none;
  --intro-svg3: #fff;
  --intro-svg4: #ffcf5814;
  --intro-svg5: #ffcf5814;
  --intro-text: #41525d;
  --intro-sub-text: #667781;
  --emoji-active-after: #009688;
  --emoji-tab-active: rgba(0, 0, 0, 0.6);
  --emoji-label: rgba(0, 0, 0, 0.45);
  --emoji-tab: rgba(0, 0, 0, 0.32);
}

.body {
  display: flex;
  min-height: 100vh;
  align-items: center;
  justify-content: center;
  background: var(--appBackground);
  text-align: right;
  direction: rtl;
}

/* width */
::-webkit-scrollbar {
  width: 5px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--scrollBar);
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: var(--scrollBar);
}

.media {
  background: linear-gradient(
    #00a884 0%,
    #00a884 130px,
    #d9dbd5 130px,
    #d9dbd5 100%
  );
  padding: 20px 50px;
  color: hsla(0, 0%, 100%, 0.85);
  font-size: 20px;
  text-align: center;
  display: none;
}

@media only screen and (max-width: 650px) {
  .media {
    display: block;
  }

  .main {
    display: none !important;
  }
}

body .container {
  display: flex;
  width: 1620px;
  max-width: 100%;
  height: calc(100vh - 40px);
}

@media only screen and (max-width: 1440px) {
  body .container {
    padding: 0;
    height: calc(100vh - 0px);
  }
}

.container .leftSide {
  position: relative;
  flex: 30%;
  max-width: 30%;
  min-width: 30%;
  background-color: var(--side);
  /*  border-right: 1px solid var(--border-right);*/
}

@media screen and (max-width: 1000px) {
  .container .leftSide {
    flex: 0 0 45%;
  }
}

@media screen and (max-width: 748px) {
  body .container {
    min-width: 748px;
    width: 100%;
  }
}

.container .rightSide {
  position: relative;
  flex-direction: column;
  overflow: hidden;
  flex: 40%;
  background-color: var(--right-side);
}

.container .rightSide::before {
  content: "";
  top: 0;
  left: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  background: url("../images/bg-chat.png");
  opacity: var(--rightSide-opacity);
}

.header {
  position: relative;
  width: 100%;
  height: 60px;
  background: var(--secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.userImg {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 5px 0 5px;
}

.cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

#moon {
  height: auto;
  width: 37px;
  cursor: pointer;
}

.icons {
  flex: none;
  z-index: 1000;
  color: var(--icons);
  padding: 0 7px;
  margin: 0 1px 0 1px;
  display: inline-table;
}
.header-Chat .icons {
  flex: none;
  z-index: 1000;
  color: var(--head-title);
  padding: 0 7px;
  margin: 0 1px 0 1px;
  display: inline-table;
}

.leftSide .search-bar {
  margin-top: 7px;
  margin-bottom: 7px;
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 15px;
}

.leftSide .search-bar div {
  width: 100%;
}

.leftSide .search-bar div input {
  width: 100%;
  border: none;
  outline: none;
  background: var(--secondary);
  border-radius: 7px;
  padding: 6px;
  height: 38px;
  font-size: 14px;
  align-items: center;
  color: var(--h4);
  padding-left: 45px;
}

.leftSide .search-bar div input::placeholder {
  font-size: 13px;
  color: var(--primary);
}

.leftSide .search-bar div i {
  position: absolute;
  left: 30px;
  top: 7px;
  font-size: 1em;
  color: var(--icons);
  justify-content: center;
  align-items: center;
}

.chats {
  position: relative;
  height: calc(100% - 112px);
  overflow-y: auto;
}

.chats .block {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 0.7px solid var(--block);
  cursor: pointer;
}

.chats .block.active {
  background: var(--block);
  border-top: 0.2px solid var(--block);
}

.chats .block.active:hover {
  background: var(--block);
}

.chats .block:hover {
  background: var(--secondary);
}

.animate__animated.animate__fadeIn {
  --animation-delay: 800ms;
  --animation-duration: 0.3s;
}

.chats .block .imgBox {
  position: relative;
  min-width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 50%;
  margin: 0px 5px 0 5px;
}

.chats .block .h-text {
  position: relative;
  width: 100%;
}

.chats .block .h-text .head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 10px 0 10px;
}
.chats .block .h-text .head h4 {
  font-size: 12px;
  font-weight: 600;
  color: var(--h4);
  letter-spacing: 0.4px;
  margin-bottom: 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: keep-all;
}

.chats .block .h-text .head .time {
  font-size: 11px;
  font-weight: 400;
  color: var(--primary);
  margin-bottom: 0rem;
}

.chats .block.unread .h-text .head .time {
  color: var(--unread);
}

.chats .block .last-message-chat {
  display: flex;
  align-items: center;
  padding: 0 10px 0 10px;
}

.chats .block .last-message-chat .chat-text-icon {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  padding: 0 5px 0 5px;
}

.chats .block .last-message-chat .chat-text-icon .text-last-message {
  overflow: hidden;
  font-size: 13.5px;
  font-weight: 400;
  color: var(--primary);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.chats .block .last-message-chat .white-tick {
  color: var(--primary);
}

.chats .block .last-message-chat .blue-tick {
  color: var(--tick-icons);
}

.chats .block .last-message-chat .chat-text-icon .unread {
  display: flex;
}

.chats .block .last-message-chat .chat-text-icon .numb {
  background: var(--unread);
  color: #fff;
  font-weight: 500;
  min-width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.75em;
  margin-left: auto;
}

.containerMessage .row .numb {
  background: #fff;
  color: #333;
  font-weight: 500;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  /* margin-left: auto; */
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  padding: 1px;
}

.chats .block .hide {
  display: none;
  color: var(--primary);
  margin-left: 5px;
  transition: 0.3s ease;
}

.chats .block:hover .hide {
  display: block;
}

/* Right Side */
.imgText {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.imgText h4 {
  font-weight: 600;
  font-size: 14px;
  line-height: 1.2em;
  margin: 5px;
  color: var(--h4);
  /* margin-top: 10px; */
}

.imgText h4 span {
  font-weight: 400;
  color: var(--primary);
  font-size: 13px;
}

/* DropdownLeft Menu */
.dropLeft {
  display: flex;
  display: none;
  background: var(--dropdown);
  border-radius: 6px;
  box-shadow: 0 2px 2px 0 rgba(var(--shadow-rgb), 0.26),
    0 2px 10px 0 rgba(var(--shadow-rgb), 0.16);
  margin-top: 140px;
  margin-left: -260px;
  z-index: 100010;
  padding: 9px 0;
  position: relative;
  max-width: 340px;
}

.dropLeft {
  list-style: none;
  padding: 10px;
  margin-bottom: 0;
}

.dropLeft .listItem {
  position: relative;
  padding: 5px 10px 5px 10px;
  color: var(--listItem);
  font-size: 13px;
  cursor: pointer;
  box-sizing: border-box;
  display: block;
  height: 40px;
}

.dropLeft .listItem:hover {
  background: var(--listItemHover);
  border-radius: 5px;
}

/* Dropdown Menu */
.drop {
  top: 35px;
  left: 4px;
  width: 230px;
  z-index: 100;
  display: flex;
  display: none;
  padding: 9px 0;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
  border-radius: 3px;
  position: absolute;
  background: var(--dropdown);
  box-shadow: 0 2px 5px 0 rgba(var(--shadow-rgb), 0.26),
    0 2px 10px 0 rgba(var(--shadow-rgb), 0.16);
}

.chat-side {
  display: flex;
  margin-right: 48px;
}

/* .dropdown-icon {
  position: absolute;
} */

/* .pressed {
  margin: 0 10px 0 10px;
} */

.drop .listItem {
  position: relative;
  padding-right: 50px;
  padding-left: 24px;
  padding-top: 11px;
  color: var(--listItem);
  font-size: 13px;
  cursor: pointer;
  box-sizing: border-box;
  display: block;
  height: 40px;
}

.drop .listItem:hover {
  background: var(--listItemHover);
}

.chatBox {
  flex: 1;
  position: relative;
  overflow: hidden;
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  scrollbar-width: thin;
  scrollbar-color: #bdc3c7 transparent;
  gap: 8px;
  flex-direction: column;
  scroll-behavior: smooth;
}

.chatBox .chat__date-wrapper {
  text-align: center;
  margin: 10px 0 14px;
  position: relative;
}

.chatBox .chat__date {
  background: var(--chat-date-bg);
  color: var(--chat-date);
  display: inline-block;
  font-size: 0.75rem;
  padding: 7px 10px;
  border-radius: 5px;
}

.chatBox .chat__encryption-msg {
  background: var(--chat-encrption-bg);
  color: var(--chat-encryption);
  font-size: 12.5px;
  text-align: center;
  padding: 5px 12px 6px;
  position: relative;
  margin-bottom: 8px;
  border-radius: 5px;
  line-height: 20px;
}

.chatBox .chat__encryption-icon {
  color: var(--chat-encryption);
  margin-right: 5px;
  margin-bottom: 3px;
}

button {
  border: none;
  background-color: transparent;
}

.chatBox .my-chat .chat__msg-options {
  background: var(--chat);
}

.chatBox .frnd-chat .chat__msg-options {
  background: var(--block-footer);
}

.chat__msg-options-icon {
  color: var(--primary);
  width: 20px;
  height: 20px;
}

.chat__msg-options {
  opacity: 0;
  position: absolute;
  right: 5px;
  top: 5px;
  pointer-events: none;
  transition: all 0.3s ease-out;
}

.chatMessage:hover .chat__msg-options {
  opacity: 1;
  transition: all 0.3s ease-in;
}

.chatMessage {
  position: relative;
  display: flex;
  margin: 10px 0;
}

@media screen and (min-width: 1301px) {
  .chatMessage {
    max-width: 65%;
  }
}

.chatMessage {
  position: relative;
  display: flex;
  width: fit-content;
  max-width: 100%;
  padding: 6px 7px 8px 9px;
  border-radius: 7.5px;
  line-height: 20px;
  font-size: 13px;
  color: var(--h4);
}

.chatMessage.frnd-chat::before {
  content: "";
  position: absolute;
  top: 0;
  right: -12px;
  width: 20px;
  height: 20px;
  background: linear-gradient(
    135deg,
    var(--block-footer) 0%,
    var(--block-footer) 50%,
    transparent 50%,
    transparent
  );
}

.chatMessage .msg-footer {
  /* position: absolute; */
  display: flex;
  align-items: center;
  left: 0px;
  bottom: 3px;
  color: var(--primary);
  font-size: 0.7rem;
  font-weight: 500;
}

.chat__msg-filler {
  width: 70px;
  height: 3px;
  margin-left: 8px;
  display: inline-block;
  background: transparent;
}

.chat__msg-filler2 {
  width: 60px;
  height: 3px;
  display: inline-block;
  background: transparent;
}

.chatMessage .chat-icon--blue {
  color: var(--tick-icons);
  margin-left: 3px;
}

.chatMessage .chat-icon--text {
  color: var(--primary);
  margin-left: 3px;
}

.frnd-chat {
  background: var(--block-footer);
  margin-left: auto;
  width: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
}

.my-chat {
  background: var(--chat);
  margin-right: auto;
  width: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
}

.chatMessage.my-chat::before {
  content: "";
  position: absolute;
  top: 0;
  left: -12px;
  width: 20px;
  height: 20px;
  background: linear-gradient(
    225deg,
    var(--chat) 0%,
    var(--chat) 50%,
    transparent 50%,
    transparent
  );
}

/* Chat Input */
.chat-footer {
  position: relative;
  width: 100%;
  background: var(--secondary);
  justify-content: space-between;
  align-items: center;
}

.chat-footer .icons {
  color: var(--primary);
}

.chat-footer .send-message {
  position: relative;
  width: 100%;
  margin: 5px 10px;
  padding: 9px 12px 11px;
  background: var(--block-footer);
  border-radius: 6px;
  border: none;
  outline: none;
  color: var(--h4);
  font-size: 15px;
}

.chat-footer .send-message::placeholder {
  color: var(--primary);
  font-size: 15px;
}

.chat-footer .chat-attach {
  position: relative;
}

.chat-footer .chat-attach .popup {
  position: absolute;
  width: 110px !important;
  opacity: 1 !important;
  overflow: hidden;
  transition: background 0.3s linear;
  display: none;
  flex-direction: column;
  bottom: 40px;
}

.chat-footer .chat-attach .popup .popopIcons {
  margin-bottom: 7px;
}

.chat-input-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.chat-input-wrapper .hidden {
  display: none;
}

.chat-footer .emojis__wrapper--active {
  height: 40vh;
  min-height: 350px;
  transition: all 0.3s;
}

.emojis__wrapper {
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 0;
  min-height: 0;
  transition: all 0.3s;
  background: inherit;
}

.emojis__wrapper .emojis__tabs {
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  align-items: center;
  height: 50px;
}

.emojis__wrapper .emojis__tabs .emojis__tab {
  -webkit-flex: 1 1;
  flex: 1 1;
  padding: 10px 5px 12px;
  text-align: center;
  position: relative;
}

.emojis__tab--active .emojis__tab-icon {
  color: var(--emoji-tab-active);
}

.emojis__tab--active:after {
  content: "";
  position: absolute;
  height: 4px;
  width: 100%;
  bottom: 0;
  left: 0;
  background: var(--emoji-active-after);
}

.emojis__tab-icon {
  color: var(--emoji-tab);
}

.chat-footer .emojis__search {
  position: relative;
  width: 100%;
  margin: 5px 10px;
  padding: 8px 10px;
  background: var(--block-footer);
  border-radius: 5px;
  border: none;
  outline: none;
  color: var(--h4);
  font-size: 13px;
}

.chat-footer .emojis__search::placeholder {
  font-size: 13px;
  color: var(--primary);
}

.emojis__content {
  overflow-y: scroll;
  padding: 5px 20px;
  -webkit-flex: 1 1;
  flex: 1 1;
}

.emojis__label {
  margin-top: 15px;
  margin-left: 10px;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--emoji-label);
  font-size: 0.85rem;
}

.emojis__grid {
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-bottom: 25px;
}

.emoji {
  background: url(../images/emoji-sprite.png) transparent;
  width: 40px;
  height: 40px;
  background-size: 400px;
  background-repeat: no-repeat;
  width: 50px;
  height: 50px;
  background-size: 500px;
}

.emojis__emoji {
  margin-right: 2px;
  cursor: pointer;
}

/* Starred SCREEN */
.starred {
  display: none;
  position: relative;
  flex: 30%;
  background-color: var(--profile-BGG);
  border-right: 1px solid var(--border-right);
}

.header-Chat {
  position: relative;
  width: 100%;
  height: 110px;
  background: var(--header-bg);
  display: flex;
  padding: 0 15px;
  padding-top: 65px;
 
}
#QuickReplyList .header-Chat{
   justify-content: space-between;
  align-items: center;
}

.starred .icons {
  flex: none;
  color: var(--head-title);
  padding: 0 7px;
  margin-left: 3px;
  display: inline-table;
}

.chats-star {
  position: relative;
  height: calc(100% - 110px);
  overflow-y: auto;
}

.chats-star .text-Ani {
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  height: calc(100% - 60px);
  background: var(--profile-bg);
}

.chats-star .text-Ani p {
  font-size: 13px;
  font-weight: 400;
  color: var(--primary);
  display: -webkit-box;
  margin-bottom: 0rem;
}

.chats-star .block {
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  height: 60px;
  border-top: 0.7px solid var(--block);
  background: var(--profile-bg);
}

.chats-star .text-sec p {
  font-size: 13px;
  font-weight: 400;
  color: var(--primary);
  display: -webkit-box;
  margin-bottom: 0rem;
}

.last-time {
  font-size: 11px;
  font-weight: 400;
  overflow: hidden;
  font-size: 13.5px;
  font-weight: 400;
  color: var(--primary);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.chatBox .chat__unread-wrapper {
  text-align: center;
  margin: 10px 0 14px;
  position: relative;
}

.chatBox .chat__unread {
  position: relative;
  z-index: 2;
  display: inline-block;
  padding: 8px 16px;
  background-color: white;
  border-radius: 20px;
  font-size: 13px;
  font-weight: bold;
  color: #463c3c;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  text-align: center;
}

/* Message Area Styles */
/* Message Area Styles */
.message-area {
  display: none;
  flex: 1;
  height: 100%;
  flex-direction: column;
  background-color: #f0f2f5;
}

/* .message-area__header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #f0f2f5;
  border-bottom: 1px solid #e9edef;
}

.message-area__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
} */

/* #messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: calc(100vh - 180px); 
  scroll-behavior: smooth;
} 

.containerMessage {
  display: flex;
  flex-direction: column;
  max-width: 65%;
  margin: 4px 0;
}

.message {
  padding: 8px 12px;
  border-radius: 8px;
  position: relative;
  word-wrap: break-word;
}

/* .message--in {
  background-color: #ffffff;
  align-self: flex-start;
  border-bottom-left-radius: 4px;
}

.message--out {
  background-color: #d9fdd3;
  align-self: flex-end;
  border-bottom-right-radius: 4px;
}

.message__content {
  display: flex;
  flex-direction: column;
}

.message__text {
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 19px;
}

.message__time {
  font-size: 11px;
  color: #667781;
  align-self: flex-end;
} */

/* Loading indicator styles */
.loading {
  text-align: center;
  padding: 10px;
  color: #667781;
  font-size: 14px;
}

/* Input area styles */
/* .input-area {
  padding: 10px 16px;
  background-color: #f0f2f5;
  border-top: 1px solid #e9edef;
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-area input {
  flex: 1;
  padding: 9px 12px;
  border: none;
  border-radius: 8px;
  background-color: #ffffff;
  font-size: 15px;
  line-height: 20px;
}

.input-area input:focus {
  outline: none;
} */

/* Scrollbar styles */
#messages::-webkit-scrollbar {
  width: 6px;
}

#messages::-webkit-scrollbar-track {
  background: transparent;
}

#messages::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

#messages::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* ... existing code ... */
/* تنسيق الرسالة المُقتبسة */
#quoted-message {
  display: flex;
  align-items: center;
  background-color: #e5e7e9;
  padding: 5px;
  border-radius: 10px;
  margin-bottom: 0px;
  position: relative;
  overflow: hidden; /* التأكد من عدم تجاوز المحتوى */
}

#quoted-message::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 3px; /* عرض الحد */
  background-color: #1dab61; /* لون الحد */
  border-top-left-radius: 10px; /* تدوير الزاوية العلوية اليسرى */
  border-bottom-left-radius: 10px; /* تدوير الزاوية السفلية اليسرى */
  z-index: 1; /* للتأكد من ظهوره فوق المحتوى */
}

.quoted-content {
  flex-grow: 1;
  padding: 0 10px 0 10px;
}

.sender-name {
  color: #2e7d32;
  font-weight: bold;
  display: block;
}

.quoted-text {
  margin: 5px 0 0 0;
  color: #555;
}

.close-quoted {
  color: #777;
  cursor: pointer;
  margin-left: 10px;
  font-size: 16px;
  position: absolute;
  left: 10px;
  top: 10px;
}

/* تنسيق نافذة المودال (إن وُجدت) */
.modal-body {
  border-bottom: none;
  text-align: center;
  padding: 2rem;
}

.modal-body svg {
  height: 86px;
  width: 86px;
}

.modal-footer {
  border-top: none;
  justify-content: center;
}

.btn-delete {
  background-color: #ff3b30;
  color: white;
  border: none;
}

.btn-cancel {
  background-color: #34c759;
  color: white;
  border: none;
}

.modal-content {
  border-radius: 10px;
  width: 80%;
}

/* تنسيق منطقة إدخال الدردشة */

.chat-container {
  width: 100%;
  max-width: 600px; /* Limit chat width */
  display: flex;
  flex-direction: column;
}

.chat-input-container {
  display: flex;
  align-items: center;
  background-color: #f0f2f5;
  padding: 10px 10px 5px;
  width: 100%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
/* تنسيق عام لتجميع العناصر فوق بعضها */
/* Reply Context Block */
.reply-context {
  background-color: rgba(0, 0, 0, 0.05); /* Slightly transparent dark */
  padding: 6px 10px;
  border-radius: 6px;
  margin: -2px -5px 5px -5px; /* Negative margin pulls it closer to bubble edges, bottom margin creates space */
  border-left: 3px solid #73addf; /* Default incoming reply bar color (blueish) */
  font-size: 0.9em;
  overflow: hidden; /* Ensure border-radius clips content */
  position: relative; /* Needed for pseudo-elements or absolute positioning if used later */
}

/* Outgoing message reply context bar color */
.my-chat .reply-context {
  border-left-color: #6bc165; /* Green bar for replies in outgoing messages */
  background-color: rgba(0, 0, 0, 0.04); /* Slightly different bg on green */
}

/* Sender Name in Reply Block */
.reply-sender {
  display: block; /* Put sender on its own line */
  font-weight: 600;
  color: #73addf; /* Match the bar color by default */
  margin-bottom: 2px;
  /* Prevent overflow */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Sender Name color in outgoing messages */
.my-chat .reply-sender {
  color: #6bc165; /* Match the green bar */
}

/* Original Message Snippet in Reply Block */
.reply-content {
  display: block; /* Put snippet on its own line */
  color: #333; /* Darker text for snippet */
  opacity: 0.8;
  /* Limit to roughly one line and add ellipsis */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  /* max-height: 1.2em; Control height */
}

/* Main message text (the span directly inside chatMessage, after reply-context) */
.chatMessage > span:not([class]) {
  /* Select the span holding the main message text */
  display: inline; /* Allow it to flow with footer */
  margin-right: 5px; /* Small space before footer might start */
  hyphens: auto; /* Help break words if needed */
  margin-bottom: 0px;
}

/* Footer: Timestamp and Read Receipt */
.msg-footer {
  font-size: 0.75em;
  color: #888;
  text-align: right;
  margin-top: 3px;
  display: flex !important;
  justify-content: flex-end;
  align-items: center;
  height: 15px;
}

/* Ensure footer color consistency (can be overridden if needed) */
.my-chat .msg-footer {
  color: rgba(0, 0, 0, 0.45);
}

/* Space between time and icon */
.msg-footer > span {
  margin-right: 3px;
}

/* Read Receipt Icon Styling */
.chat-icon--blue {
  vertical-align: bottom; /* Align with text baseline */
  width: 16px;
  height: 15px;
  color: #53bdeb; /* WhatsApp blue tick color */
}

/* Spacer element to reserve space for the footer */
/* This prevents the last line of text overlapping the floated footer */
.chat__msg-filler {
  display: inline-block; /* Needs to be inline-block */
  width: 60px; /* Adjust based on typical footer width (time + icon) */
  height: 1px; /* Essentially invisible */
  vertical-align: bottom; /* Align with text */
}

/* Clearfix for float containment (though align-self often handles this in flex) */
.chatMessage::after {
  content: "";
  display: table;
  clear: both;
}

.chatMessage.frnd-chat .msg-footer {
  padding-left: 0px;
}

/* Image Message Styles */
.image-message {
  max-width: auto;
  border-radius: 8px;
  overflow: hidden;
}

.image-message img {
  width: 100%;
  height: auto;
  display: block;
}

/* .chatMessage > .image-message {
  display: inline;
  margin-right: 5px;
  hyphens: auto;
  padding-bottom: 10px;
} */

/* Video Message Styles */
.video-message {
  max-width: 300px;
  position: relative;
}

.video-thumbnail {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
}

.chatMessage .msg-footer .msg-status {
  left: 7px !important;
}

.card {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  height: var(--bs-card-height);
  word-wrap: break-word;
  background-color: var(--bs-card-bg);
  background-clip: border-box;
  border: var(--bs-card-border-width) solid var(--bs-card-border-color);
  border-radius: var(--bs-card-border-radius);
}

.msg-status .blue-tick {
  color: var(--tick-icons);
}
/* Online Status Indicator */
.block.item-chat-list .status-indicator {
  position: absolute;
  bottom: 2px;
  left: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid var(--bg-color);
}

.block.item-chat-list .status-indicator.online {
  background-color: #4caf50;
  border: 2px solid white;
}

.block.item-chat-list .status-indicator.offline {
  background-color: #9e9e9e;
}

.block.item-chat-list .status-indicator.typing {
  background-color: #2196f3;
  animation: pulse 1.5s infinite;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
}

.chatMessage.frnd-chat {
  position: relative;
  display: flex;
  width: fit-content;
  max-width: 100%;
  padding: 6px 7px 8px 9px;
  border-radius: 7.5px;
  line-height: 20px;
  font-size: 13px;
  color: var(--h4);
}

.chatMessage.frnd-chat .userName {
  color: #ea0b0b;
  font-size: 10px;
  font-weight: 600;
  letter-spacing: 0.4px;
  margin-right: 5px;
}

/* Styles for File Attachment Feature */
.popup .popopIcons .popup-icon-text {
  display: block;
  font-size: 0.8em;
  text-align: center;
  margin-top: 4px;
}

.file-preview-screen {
  position: fixed;
  bottom: 0; /* Or centered as a modal */
  left: 0;
  right: 0;
  /* width: 100%; */ /* Or a specific width if modal-like */
  max-width: 500px; /* Example for a centered modal on larger screens */
  margin: auto; /* For centering if max-width is set */
  background-color: var(--chat-bg-color, #f0f2f5); /* Use your theme variable */
  border-top: 1px solid var(--border-color, #e0e0e0);
  padding: 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1050; /* Ensure it's above other elements */
  display: flex;
  flex-direction: column;
  max-height: 70vh; /* Limit height */
}

.file-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color, #e0e0e0);
}

.file-preview-header h3 {
  margin: 0;
  font-size: 1.1em;
}

.preview-close-btn {
  background: none;
  border: none;
  font-size: 1.5em;
  cursor: pointer;
  color: var(--icon-color, #555);
}

.file-preview-area {
  text-align: center;
  margin-bottom: 10px;
  overflow-y: auto; /* Scroll if content is too tall */
  flex-grow: 1; /* Allow this area to take available space */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.file-preview-image,
.file-preview-video {
  max-width: 100%;
  max-height: 250px; /* Adjust as needed */
  border-radius: 8px;
  margin-bottom: 5px;
}

.file-preview-audio {
  width: 100%;
  margin-bottom: 5px;
}

.preview-file-icon {
  font-size: 3em; /* Feather icon size */
  color: var(--icon-color-primary, #007bff);
  margin-bottom: 5px;
}

.preview-file-name {
  font-size: 0.9em;
  color: var(--text-color-secondary, #666);
  word-break: break-all;
}

.file-preview-caption input[type="text"] {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color, #ccc);
  border-radius: 20px;
  margin-bottom: 10px;
  box-sizing: border-box;
}

.file-preview-actions {
  text-align: right;
}

#send-file-button {
  background-color: var(--primary-color, #007bff); /* Use theme variable */
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 20px;
  cursor: pointer;
}

#send-file-button:hover {
  background-color: var(--primary-dark-color, #0056b3);
}

/* Styles for file message in chat */
.message .file-attachment-container {
  padding: 8px;
  border-radius: 8px;
  background-color: var(
    --message-bg-light,
    #e6e6e6
  ); /* Slightly different for file messages */
  max-width: 250px; /* Or adjust as per your design */
}

.message.my-message .file-attachment-container {
  background-color: var(--my-message-bg-light, #dcf8c6);
}

.file-attachment-preview img,
.file-attachment-preview video {
  max-width: 100%;
  border-radius: 6px;
  display: block;
  cursor: pointer; /* To indicate it's clickable to view full */
}

.file-attachment-icon {
  font-size: 2.5em; /* Feather icon */
  display: block;
  text-align: center;
  padding: 10px;
  cursor: pointer;
}

.file-attachment-details .file-name {
  font-weight: bold;
  font-size: 0.9em;
  word-break: break-all;
  margin-top: 5px;
}

.file-attachment-details .file-size {
  font-size: 0.8em;
  color: var(--text-color-secondary, #777);
}

.file-attachment-caption {
  font-size: 0.9em;
  margin-top: 5px;
  white-space: pre-wrap; /* To respect newlines in caption */
  word-break: break-word;
}

.upload-progress-overlay {
  position: absolute;
  top: 0;
  left: 21px;
  width: 90%;
  height: 100%;
  background-color: rgb(0 0 0 / 23%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 6px; /* Match preview image/video */
  z-index: 1;
}

.upload-progress-overlay .progress-bar-value {
  /* Or just text for percentage */
  font-size: 0.9em;
}

.upload-progress-overlay .spinner {
  /* Add a small spinner if desired */
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  margin-bottom: 5px;
}

.retry-upload-button {
  color: var(--primary-color, #007bff);
  text-decoration: underline;
  cursor: pointer;
  font-size: 0.8em;
  display: block;
  margin-top: 5px;
}

/* أنماط عناصر إرفاق الملفات */
.file-upload-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: rtl;
}

.file-preview-container {
  width: 80%;
  max-width: 600px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.file-preview-header {
  padding: 15px;
  background-color: #ffcf58;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-preview-header h3 {
  margin: 0;
  font-size: 18px;
}

.file-preview-close {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.file-preview-content {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.file-preview-image {
  max-width: 100%;
  max-height: 50vh;
  object-fit: contain;
  border-radius: 8px;
}

.file-preview-details {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

/*.file-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    border-radius: 8px;
}*/

.file-info {
  flex: 1;
}

.file-name {
  font-weight: bold;
  margin-bottom: 4px;
  font-size: 14px;
  word-break: break-all;
}

.file-size {
  color: #666;
  font-size: 12px;
}

.file-preview-caption {
  margin-top: 15px;
}

.file-caption-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
  resize: none;
  font-family: inherit;
}

.file-preview-footer {
  padding: 15px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eaeaea;
}

.send-file-btn {
  background-color: #ffcf58;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 20px;
  cursor: pointer;
  font-weight: bold;
}

/* أنماط عناصر الملفات في الرسائل */
.file-message-container {
  position: relative;
  width: 200px;
  border-radius: 8px;
  overflow: hidden;
}

.image-message img {
  width: 100%;
  border-radius: 8px;
  cursor: pointer;
}

.video-message {
  position: relative;
}

.video-thumbnail {
  width: 100%;
  border-radius: 8px;
  cursor: pointer;
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.audio-message {
  display: flex;
  flex-direction: column;
  width: 250px;
}

.audio-player {
  width: 100%;
  height: 36px;
}

.file-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 8px;
  /*    width: 250px;*/
}

.my-chat .file-message {
  background-color: #ffffff6e;
  border: 1px solid #e2e6ec;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.file-icon i {
  font-size: 24px;
  color: #555;
}

/* .upload-progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 8px;
} */

.upload-progress-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  animation: spin 1s linear infinite;
}

.upload-progress-text {
  margin-top: 10px;
  font-size: 14px;
  font-weight: bold;
}

.upload-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(220, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 8px;
  cursor: pointer;
}

.upload-error-icon {
  font-size: 30px;
  margin-bottom: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* أنماط عناصر تسجيل الصوت */
.recording-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 10px 15px;
  background-color: #f0f0f0;
  border-radius: 8px;
  margin-bottom: 10px;
}

.recording-status {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.recording-timer {
  font-size: 16px;
  font-weight: bold;
  color: #e53935;
  margin-right: 15px;
  min-width: 45px;
}

.recording-waveform {
  width: 100%;
  height: 40px;
  margin: 0 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: #ffff;
  border-radius: 4px;
}

.waveform-canvas {
  width: 100%;
  height: 100%;
}

.enhanced-waveform {
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
  position: absolute;
  padding: 0 5px;
  background: #ffff;
  border-radius: 4px;
}

.enhanced-waveform span {
  width: 2px;
  background-color: #e2e6ec;
  margin: 0 1px;
  border-radius: 2px;
  flex: 1;
  transition: height 0.2s ease-in-out;
}

/* زيادة حيوية الشكل البصري في حالة التسجيل */
.recording-active .enhanced-waveform span {
  animation: pulse 1.5s infinite ease-in-out alternate;
}

@keyframes pulse {
  0% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

/* تأثير انتقالي للأعمدة أثناء ظهورها */
.enhanced-waveform span {
  transform-origin: bottom;
  animation: appear 0.3s ease-out;
}

@keyframes appear {
  from {
    transform: scaleY(0);
  }

  to {
    transform: scaleY(1);
  }
}

.recording-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.slide-to-cancel,
.slide-to-lock {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.slide-to-cancel i,
.slide-to-lock i {
  margin-right: 5px;
}

.stop-recording-btn {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background-color: #e53935;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recording-preview {
  display: flex;
  width: 100%;
  padding: 10px 15px;
  background-color: #f0f0f0;
  border-radius: 8px;
  margin-bottom: 10px;
  align-items: center;
  justify-content: space-between;
}

.preview-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.preview-play-btn {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background-color: #ffcf58;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

/* تعديل شكل واجهة المعاينة */
.preview-waveform {
  flex: 1;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  margin: 0 10px;
  position: relative;
  overflow: hidden;
}

/* تلوين الأعمدة في المعاينة */
.preview-waveform .enhanced-waveform span {
  background-color: #e2e6ec;
}

.preview-waveform::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #e2e6ec 0%, #e2e6ec) 100%;
  border-radius: 4px;
}

.preview-duration {
  font-size: 14px;
  color: #666;
  min-width: 45px;
  text-align: right;
}

.preview-actions {
  display: flex;
  align-items: center;
}

.preview-cancel-btn,
.preview-send-btn {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}

.preview-cancel-btn {
  background-color: #f0f0f0;
  color: #e53935;
}

.preview-send-btn {
  background-color: #ffcf58;
  color: white;
}

.send-voice-buttons {
  display: flex;
  align-items: center;
}

.voice-record-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ffcf58;
  color: #ffff;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.voice-record-btn.recording-active {
  background-color: #e53935;
  transform: scale(1.1);
}

.locked-recording .recording-actions {
  justify-content: flex-end;
}

/* أنماط رسائل الصوت في الدردشة */
.voice-message {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 8px;
  /* padding: 8px 10px; */
  width: 200px;
}

.voice-play-btn {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background-color: #ffcf58;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  /* margin-right: 10px; */
  flex-shrink: 0;
}

.voice-waveform {
  flex: 1;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  margin: 0 8px;
}

.voice-waveform .enhanced-waveform {
  z-index: 2;
  padding: 0 2px;
}

.voice-waveform .enhanced-waveform span {
  background-color: rgba(0, 0, 0, 0.3);
  width: 1px;
  margin: 0 1px;
}

/* تنشيط الأعمدة أثناء التشغيل */
.enhanced-waveform.playing span {
  animation: none;
}

.enhanced-waveform.playing span.active-bar {
  background-color: rgba(0, 0, 0, 0.6);
  animation: wave 0.5s infinite alternate;
}

@keyframes wave {
  0% {
    transform: scaleY(0.7);
  }

  100% {
    transform: scaleY(1);
  }
}

.my-chat .voice-waveform .enhanced-waveform span.active-bar {
  background-color: rgba(0, 0, 0, 0.7);
}

.my-chat .voice-waveform .enhanced-waveform span {
  background-color: rgba(0, 0, 0, 0.3);
}

.voice-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background-color: rgba(0, 128, 105, 0.2);
  border-radius: 4px;
  transition: width 0.1s linear;
  z-index: 1;
}

.voice-duration {
  font-size: 12px;
  color: #666;
  min-width: 40px;
  text-align: right;
  flex-shrink: 0;
}

.my-chat .voice-message {
  background-color: #ffffff6e;
  border: 1px solid #e2e6ec;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.my-chat .voice-play-btn {
  background-color: #ffcf58;
}

.my-chat .voice-progress {
  background-color: rgba(0, 168, 132, 0.2);
}

.file-message-container .voice-message .voice-waveform {
  flex: 1;
  height: 30px;
  background-color: transparent;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  display: flex;
}

.file-message-container .voice-message .voice-waveform .enhanced-waveform {
  align-items: center;
  height: 100%;
  width: 100%;
  position: absolute;
  padding: 5px;
  background: transparent;
  border-radius: 4px;
  /* margin: 0 8px; */
}

.voice-play-btn:hover {
  background-color: #fbc233;
}
/* Audio message styles */
.audio-message,
.voice-message {
  display: flex;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 8px;
  transition: opacity 0.3s ease;
}

.audio-player {
  width: 100%;
  height: 36px;
  border-radius: 18px;
}

.audio-player:disabled {
  opacity: 0.7;
}

.voice-message {
  display: flex;
  align-items: center;
}

.voice-waveform {
  flex: 1;
  height: 36px;
  position: relative;
  margin-right: 10px;
  background-color: #e0e0e0;
  border-radius: 18px;
  overflow: hidden;
}

.voice-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background-color: #ffcf58c7;
  transition: width 0.1s linear;
}

.enhanced-waveform {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}

.enhanced-waveform span {
  width: 2px;
  height: 60%;
  background-color: #999;
  display: inline-block;
}

.voice-duration {
  font-size: 12px;
  color: #777;
  width: 36px;
  text-align: right;
}

.download-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
}
/* Download button - state 1: ready to download */
.download-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.download-button:hover {
  background-color: rgba(0, 0, 0, 3);
  transform: translate(-50%, -50%) scale(1.05);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* WhatsApp-style file message styling */
.blurred-image,
.blurred-thumbnail {
  filter: blur(8px);
  transition: filter 0.3s ease;
}

.not-downloaded {
  opacity: 0.8;
}

.downloading {
  opacity: 0.9;
}

.downloaded {
  opacity: 1;
}

.download-progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.containerMessage .progress-container {
  position: relative;
  width: 40px;
  height: 40px;
}

.containerMessage .progress-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
}

.cancel-button {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;
}

.download-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(231, 76, 60, 0.7);
  color: white;
  z-index: 10;
  padding: 10px;
  text-align: center;
}

.retry-button {
  margin-top: 5px;
  background-color: white;
  color: #e74c3c;
  border: none;
  border-radius: 15px;
  padding: 3px 10px;
  font-size: 12px;
  cursor: pointer;
}

/* COMPREHENSIVE WHATSAPP-STYLE FILE UI STYLING */

/* Common file states */
.blurred-image,
.blurred-thumbnail {
  filter: blur(8px);
  transition: filter 0.3s ease;
}

.not-downloaded {
  opacity: 0.8;
}

.downloading {
  opacity: 0.9;
}

.download-failed {
  opacity: 0.8;
}

.downloaded {
  opacity: 1;
}

.wifi-waiting {
  opacity: 0.7;
}

/* Download progress overlay - state 2: downloading */
.download-progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 10;
  border-radius: 8px;
}

.progress-container {
  position: relative;
  width: 40px;
  height: 40px;
}

.progress-ring {
  transform: rotate(-90deg);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.progress-ring-circle {
  transition: stroke-dashoffset 0.3s ease;
}

.progress-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.cancel-button {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  transition: background-color 0.2s ease;
}

.cancel-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

/* WiFi waiting overlay - for files that will download when on WiFi */
.wifi-waiting-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  z-index: 10;
  border-radius: 8px;
  padding: 10px;
}

.wifi-icon {
  font-size: 24px;
  margin-bottom: 5px;
  color: #ddd;
}

.wifi-text {
  font-size: 12px;
  margin-bottom: 10px;
  color: #eee;
}

.download-now-button {
  background-color: rgba(37, 211, 102, 0.9);
  color: white;
  border-radius: 15px;
  padding: 5px 10px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.download-now-button:hover {
  background-color: rgba(37, 211, 102, 1);
}

/* Error overlay - state 5: download failed */
.download-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(231, 76, 60, 0.7);
  color: white;
  z-index: 10;
  padding: 10px;
  text-align: center;
  border-radius: 8px;
}

.download-error-overlay i {
  font-size: 24px;
  margin-bottom: 8px;
}

.download-error-overlay span {
  font-size: 12px;
  margin-bottom: 8px;
  line-height: 1.3;
}

.retry-button {
  margin-top: 5px;
  background-color: white;
  color: #e74c3c;
  border: none;
  border-radius: 15px;
  padding: 3px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background-color: #f8f8f8;
  transform: scale(1.05);
}

/* File-specific styling */
.file-message-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  margin: 5px 0;
}

/* Image message styles */
.image-message {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  max-width: 100%;
  transition: opacity 0.3s ease;
}

.image-message img {
  max-width: 100%;
  border-radius: 8px;
  transition: filter 0.3s ease;
}

/* Video message styles */
.video-message {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  transition: opacity 0.3s ease;
}

.video-container {
  position: relative;
}

.video-thumbnail {
  width: 100%;
  border-radius: 8px;
  transition: filter 0.3s ease;
}

.video-duration {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 5px;
  border-radius: 4px;
  font-size: 11px;
}

.video-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 9;
  transition: background-color 0.2s ease;
}

.video-play-button:hover {
  background-color: rgba(0, 0, 0, 3);
}

/* Document and file message styles */
.file-message {
  display: flex;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 8px;
  transition: opacity 0.3s ease;
}

.file-message.downloaded {
  cursor: pointer;
}

.document-message {
  display: flex;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 8px;
  transition: opacity 0.3s ease;
}

/*.file-icon {
    width: 40px;
    height: 40px;
    background-color: #ddd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-right: 10px;
    color: #555;
    transition: all 0.3s ease;
}*/

.downloaded-icon {
  color: #25d366 !important;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* File action buttons for documents */
.file-actions {
  display: flex;
  align-items: center;
  margin-left: 10px;
  gap: 8px;
}

.open-file-btn,
.download-file-btn {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.open-file-btn:hover,
.download-file-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.open-file-btn i,
.download-file-btn i {
  font-size: 16px;
  color: #555;
}

.my-chat .open-file-btn,
.my-chat .download-file-btn {
  background-color: rgba(255, 255, 255, 0.2);
}

.my-chat .open-file-btn:hover,
.my-chat .download-file-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.my-chat .open-file-btn i,
.my-chat .download-file-btn i {
  color: #333;
}

.file-name {
  font-weight: bold;
  margin-bottom: 2px;
  /* white-space: nowrap; */
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.file-size {
  font-size: 12px;
  color: #777;
}

/* File caption */
.file-caption {
  margin-top: 5px;
  font-size: 14px;
  padding: 0 5px;
}

/* WhatsApp-style Video Player Modal */
.video-player-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: none;
  align-items: center;
  justify-content: center;
}

.video-player-modal.active {
  display: flex;
}

.video-player-container {
  width: 90%;
  max-width: 800px;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.video-player-header {
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffcf58;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.video-title {
  color: white;
  font-size: 16px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.close-video-btn {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  transition: opacity 0.2s ease;
}

.close-video-btn:hover {
  opacity: 0.8;
}

.video-player-content {
  width: 100%;
  position: relative;
}

.fullscreen-video {
  width: 100%;
  display: block;
  background: #000;
}

/* Video Controls */
.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10px;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  display: flex;
  align-items: center;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-player-content:hover .video-controls {
  opacity: 1;
}

.play-pause-btn,
.volume-btn,
.fullscreen-btn {
  background: none;
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.play-pause-btn:hover,
.volume-btn:hover,
.fullscreen-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.video-time {
  color: white;
  font-size: 12px;
  min-width: 40px;
}

.current-time {
  text-align: right;
}

.total-duration {
  text-align: left;
}

.progress-container {
  flex: 1;
  height: 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
  transition: height 0.2s ease;
}

.progress-container:hover .progress-bar {
  height: 6px;
}

.progress-filled {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #25d366; /* WhatsApp green */
  border-radius: 2px;
  width: 0;
}

.buffer-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  width: 0;
}

.progress-handle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.progress-container:hover .progress-handle {
  opacity: 1;
}

/* Volume controls */
.volume-container {
  display: flex;
  align-items: center;
  position: relative;
}

.volume-slider {
  width: 0;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
  margin-left: 5px;
  transition: width 0.3s ease, height 0.2s ease;
  cursor: pointer;
}

.volume-container:hover .volume-slider {
  width: 60px;
  height: 6px;
}

.volume-progress {
  width: 100%;
  height: 100%;
  position: relative;
}

.volume-filled {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #25d366; /* WhatsApp green */
  border-radius: 2px;
  width: 80%; /* Default volume at 80% */
}

.volume-handle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  top: 50%;
  left: 80%; /* Default volume at 80% */
  transform: translate(-50%, -50%);
  cursor: pointer;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.volume-container:hover .volume-handle {
  opacity: 1;
}

/* Loading and error states */
.video-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.video-error-message {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  display: none;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  text-align: center;
  z-index: 3;
}

.video-loading .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #ffcf58;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.icon-container {
  position: absolute;
  left: 30px;
  bottom: 100%;
  transform: translateY(-10px); /* small offset */
  z-index: 10;
  width: 40px;
  height: 40px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* Optional: adds a shadow */
}

.notification-count {
  position: absolute;
  top: -5px;
  left: -5px;
  width: 20px;
  height: 20px;
  background-color: #25d366; /* WhatsApp green */
  color: white;
  font-size: 12px;
  border-radius: 50%;
  /* display: flex; */
  display: none;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.icon {
  font-size: 16px;
  color: #707070; /* Adjust the color as needed */
}
#scrollDownBtn {
  position: absolute; /* or fixed, if you want it always on screen */
  display: none; /* hidden until needed */
  cursor: pointer;
  z-index: 100;
}
