/**
 * <PERSON>Manager - Manages all AJAX requests in the application
 */

function getAccessToken() {
  const response = {
    token:
      "eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.eyJhRFJaQ1lBclFmSzZib1BaNGV4MTZRPT0iOiJPWU11cmZWdWlBc2NoOW1JV0xtY2RWZy9FSVNTdTJNRmRwZithVHNWWm9NPSIsIjYrZTFpUEVXUlU3N2wwMGF4cDZ5c0E9PSI6ImZFMzVITCtISVRORmRZT0NETVlydVJmYXJML2N2UExGeWI4OHd0QVhIdTg9IiwiSGVnV2lMWUpRU1M5WWVteUwvKzFaUT09IjoiVUE2SE45N3owSmcrTHZQb2lRV2E0V0l5SEZZY016R3VydjB0eWZFZ0Nwaz0iLCJleHhFeDZYbUlyTlVQWU1vTmpmYUU3QUtSWlhOejFla1E5Y2tPZ09qNGtZPSI6ImxtU2xha2NQb2M1aXU5K0JEVkdvR2VOWW5rREhtbFg1cVpRZzgxTThjeGs9IiwiZXhwIjoxNzU0NDkyNDM0fQ.3x0J9vXNEb097Jdlx2vEs2v9baQtFhp56E-VOZDlKZM",
    expiresOn: "2025-06-29T16:41:24Z",
  };
  return response;
}

class AjaxManager {
  constructor() {
    this.baseUrl = "http://*************:3000/chatproject/"; // Base URL for all API endpoints
    this.defaultHeaders = {
      "Content-Type": "application/json; charset=utf-8",
      "X-XSRF-TOKEN":
        document.querySelector("[name='AntiforgeryFieldname']")?.value || "",
      // 'Authorization': document.querySelector("[name='AntiforgeryFieldname']")?.value || ''
      Authorization: "Bearer " + getAccessToken().token,
    };
  }

  /**
   * Make a POST request
   * @param {string} endpoint - API endpoint
   * @param {object} data - Data to send
   * @param {object} options - Additional options
   * @returns {Promise} - Promise that resolves with the response
   */
  async post(endpoint, data, options = {}) {
    //console.log("post", endpoint, data);
    try {
      const response = await $.ajax({
        url: `${this.baseUrl}${endpoint}`,
        type: "POST",
        headers: { ...this.defaultHeaders, ...options.headers },
        data: JSON.stringify(data),
        dataType: "json",
        cache: false,
      });
      //console.log("response", response);

      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Make a GET request
   * @param {string} endpoint - API endpoint
   * @param {object} params - Query parameters
   * @param {object} options - Additional options
   * @returns {Promise} - Promise that resolves with the response
   */
  async get(endpoint, params = {}, options = {}) {
    //console.log("GET", endpoint);

    try {
      const response = await $.ajax({
        url: `${this.baseUrl}${endpoint}`,
        type: "GET",
        headers: { ...this.defaultHeaders, ...options.headers },
        data: params,
        dataType: "json",
        cache: false,
      });

      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Make a DELETE request
   * @param {string} endpoint - API endpoint
   * @param {object} options - Additional options
   * @returns {Promise} - Promise that resolves with the response
   */
  async delete(endpoint, options = {}) {
    try {
      const response = await $.ajax({
        url: `${this.baseUrl}${endpoint}`,
        type: "DELETE",
        headers: { ...this.defaultHeaders, ...options.headers },
        dataType: "json",
        cache: false,
      });

      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Handle successful response
   * @param {object} response - Response from the server
   * @returns {object} - Processed response
   */
  handleResponse(response) {
    if (response.success === false) {
      throw new Error(response.message || "Request failed");
    }
    return response.data;
  }

  /**
   * Handle error response
   * @param {object} error - Error object
   * @returns {object} - Error details
   */
  handleError(error) {
    let errorMessage = "An unexpected error occurred.";
    let errorDetails = null;

    if (error.status === 200) {
      errorMessage =
        "Received an unexpected HTML response. Please check the server for errors.";
    } else if (error.responseText && error.responseText.startsWith("<html")) {
      errorMessage =
        "Received an HTML response instead of JSON. Please check the server for errors.";
    } else {
      errorMessage = error.responseText || errorMessage;
      errorDetails = {
        status: error.status,
        statusText: error.statusText,
        responseText: error.responseText,
      };
    }

    console.error("Request failed:", errorMessage, errorDetails);
    return {
      success: false,
      message: errorMessage,
      details: errorDetails,
    };
  }
}
