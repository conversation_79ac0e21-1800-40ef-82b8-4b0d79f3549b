﻿class GroupDestination {
  constructor(groupService, userProcessor) {
    this.groupService = groupService;
    this.userProcessor = userProcessor;
    this.selectedParticipants = [];
    this.selectedContacts = []; // لتخزين { id, name, picture, isAdmin }
    this.groupImageBase64 = null; // لتخزين الصورة كـ Base64
    this.currentStep = "select"; // 'select' or 'create'
    this.isLoading = false; // لتتبع حالة التحميل عند الإنشاء

    this.elements = {
      // الشاشات
      mainScreen: document.getElementById("leftSid"), // شاشة اختيار جهات الاتصال
      groupSelectScreen: document.getElementById("group"), // شاشة اختيار جهات الاتصال
      createGroupScreen: document.getElementById("createGroupDetails"), // شاشة تفاصيل المجموعة

      selectedParticipantsContainer: document.querySelector(
        ".selected-participants-container"
      ),
      // أزرار التنقل والتحكم
      closeGroupSelectBtn: document.querySelector("#group .header-Chat .icons"),
      backToSelectBtn: document.getElementById("backToGroupSelectionBtn"),
      createGroupConfirmBtn: document.getElementById("createGroupConfirmBtn"),
      createGroupBtnText: document.querySelector(
        "#createGroupConfirmBtn .btn-text"
      ),
      createGroupBtnLoader: document.querySelector(
        "#createGroupConfirmBtn .loader"
      ),

      // عناصر شاشة اختيار جهات الاتصال
      contactsContainer: document.querySelector("#group .chats-group"),
      contactSearchInput: document.querySelector("#group .search-bar input"),
      continueBtnContainer: null, // سيتم إنشاؤه ديناميكيًا

      // عناصر شاشة إنشاء تفاصيل المجموعة
      groupNameInput: document.getElementById("groupNameInput"),
      groupDescInput: document.getElementById("groupDescInput"),
      groupImageInput: document.getElementById("groupImageInput"),
      groupAvatarPreview: document.getElementById("groupAvatarPreview"),
      // لا يوجد زر منفصل لتغيير الصورة، سنستخدم label
      selectedMembersList: document.getElementById(
        "selectedMembersListDetails"
      ),
      selectedMembersCount: document.getElementById(
        "selectedMembersCountDetails"
      ),
    };

    if (
      !this.elements.groupSelectScreen ||
      !this.elements.createGroupScreen ||
      !this.elements.contactsContainer
    ) {
      console.error("خطأ فادح: بعض عناصر واجهة المستخدم الأساسية مفقودة!");
      return; // منع التهيئة إذا كانت العناصر مفقودة
    }

    this.initialize();
  }

  /**
   * تهيئة وجهة إنشاء المجموعة
   */
  initialize() {
    this.setupEventListeners();
    this.loadAndRenderContacts();
  }

  /**
   * إعداد مستمعي الأحداث
   */
  setupEventListeners() {
    // --- شاشة اختيار جهات الاتصال ---
    if (this.elements.contactSearchInput) {
      this.elements.contactSearchInput.addEventListener("input", (e) =>
        this.filterContactsUI(e.target.value)
      );
    }
    if (this.elements.closeGroupSelectBtn) {
      this.elements.closeGroupSelectBtn.addEventListener("click", () =>
        this.closeEntireProcess()
      );
    }
    // استخدام تفويض الأحداث لقائمة جهات الاتصال
    if (this.elements.contactsContainer) {
      this.elements.contactsContainer.addEventListener("click", (e) => {
        const contactBlock = e.target.closest(".block");
        if (contactBlock) {
          this.handleContactSelection(contactBlock);
        }
      });
    }

    // --- شاشة إنشاء تفاصيل المجموعة ---
    if (this.elements.backToSelectBtn) {
      this.elements.backToSelectBtn.addEventListener("click", () =>
        this.navigateBackToSelect()
      );
    }
    if (this.elements.groupNameInput) {
      this.elements.groupNameInput.addEventListener("input", () =>
        this.validateGroupForm()
      );
    }
    // تغيير الصورة عند النقر على label/preview
    const avatarWrapper = document.querySelector(".group-avatar-wrapper");
    if (avatarWrapper && this.elements.groupImageInput) {
      this.elements.groupImageInput.addEventListener("change", (e) =>
        this.handleGroupImageChange(e)
      );
    }

    if (this.elements.createGroupConfirmBtn) {
      this.elements.createGroupConfirmBtn.addEventListener("click", () =>
        this.createGroup()
      );
    }
    // تفويض الأحداث لقائمة الأعضاء المحددين (للإزالة وتعيين المشرف)
    if (this.elements.selectedMembersList) {
      this.elements.selectedMembersList.addEventListener("click", (e) => {
        if (e.target.closest(".remove-member-btn")) {
          const contactId = parseInt(
            e.target.closest(".selected-member").dataset.contactId
          );
          this.removeMember(contactId);
          this.removeParticipant(contactId);
        } else if (e.target.closest(".toggle-admin-btn")) {
          const contactId = parseInt(
            e.target.closest(".selected-member").dataset.contactId
          );
          this.toggleAdminStatus(contactId);
        }
      });
    }
  }

  /**
   * تحميل وعرض قائمة جهات الاتصال
   */
  async loadAndRenderContacts() {
    //console.log("Loading contacts...");
    this.elements.contactsContainer.innerHTML =
      '<div class="loading-contacts">جاري تحميل جهات الاتصال...</div>'; // مؤشر تحميل
    try {
      const contacts = await this.userProcessor.fetchUsers(
        "",
        1,
        20,
        "Desc",
        3
      );
      //console.log("Contacts fetched:", contacts);
      this.renderContacts(contacts.users);
      if (contacts.length === 0 && !navigator.onLine) {
        this.elements.contactsContainer.innerHTML =
          '<div class="error-loading">لا يمكن تحميل جهات الاتصال في وضع عدم الاتصال.</div>';
      } else if (contacts.length === 0) {
        this.elements.contactsContainer.innerHTML =
          '<div class="no-contacts">لا توجد جهات اتصال لعرضها.</div>';
      }
    } catch (error) {
      console.error("Error loading and rendering contacts:", error);
      this.elements.contactsContainer.innerHTML =
        '<div class="error-loading">حدث خطأ أثناء تحميل جهات الاتصال.</div>';
    }
  }

  /**
   * عرض جهات الاتصال في الواجهة
   * @param {Array} contacts - قائمة جهات الاتصال
   */
  renderContacts(contacts) {
    this.elements.contactsContainer.innerHTML = ""; // مسح المحتوى السابق أو مؤشر التحميل
    if (!contacts || contacts.length === 0) {
      this.elements.contactsContainer.innerHTML =
        '<div class="no-contacts">لا توجد جهات اتصال.</div>';
      return;
    }

    // تجميع جهات الاتصال حسب الحرف الأول (اختياري لتحسين العرض)
    const groupedContacts = contacts.reduce((acc, contact) => {
      const firstLetter = contact.userName.charAt(0).toUpperCase();
      if (!acc[firstLetter]) {
        acc[firstLetter] = [];
      }
      acc[firstLetter].push(contact);
      return acc;
    }, {});

    // فرز الحروف الأبجدية
    const sortedLetters = Object.keys(groupedContacts).sort();

    sortedLetters.forEach((letter) => {
      // إضافة عنوان الحرف
      const letterHeader = document.createElement("div");
      letterHeader.className = "contact-letter-header"; // استخدم class للتحكم بالنمط
      letterHeader.innerHTML = `<span>${letter}</span>`;
      this.elements.contactsContainer.appendChild(letterHeader);

      // إضافة جهات الاتصال لهذا الحرف
      groupedContacts[letter].forEach((contact) => {
        const contactElement = this.createContactElement(contact);
        this.elements.contactsContainer.appendChild(contactElement);
      });
    });
    // إعادة تطبيق التحديدات الحالية إن وجدت
    this.reapplySelections();
  }

  /**
   * إنشاء عنصر HTML لجهة اتصال واحدة
   * @param {object} contact - بيانات جهة الاتصال
   */
  createContactElement(contact) {
    //////console.log("createContactElement contact", contact)
    const element = document.createElement("div");
    element.className = "block"; // الفئة الأساسية
    element.setAttribute("data-contact-id", contact.id); // مهم للتعرف على جهة الاتصال

    // إضافة فئة 'selected' إذا كانت جهة الاتصال محددة بالفعل
    if (this.selectedContacts.some((c) => c.id === contact.id)) {
      element.classList.add("selected");
    }

    element.innerHTML = `
              <div class="imgBox">
                  <img src="${
                    contact.picture || "/assets/avatars/imageGrey.jpg"
                  }" class="cover" alt="${contact.userName}">
                  
              </div>
              <div class="h-text">
                  <div class="head">
                      <h4 title="${contact.userName}" aria-label="${
      contact.userName
    }">${contact.userName}</h4>
                  </div>
                  <div class="message">
                      <p title="${contact.about || ""}" aria-label="${
      contact.about || ""
    }">
                          ${contact.about || "لا يوجد وصف"}
                      </p>
                  </div>
              </div>
          `;
    return element;
  }

  /**
   * إعادة تطبيق حالة الاختيار على عناصر جهات الاتصال بعد إعادة العرض
   */
  reapplySelections() {
    this.selectedContacts.forEach((selected) => {
      const element = this.elements.contactsContainer.querySelector(
        `.block[data-contact-id="${selected.id}"]`
      );
      if (element) {
        element.classList.add("selected");
      }
    });
    this.toggleContinueButton(); // تحديث حالة زر المتابعة
  }

  /**
   * ترشيح جهات الاتصال بناءً على نص البحث
   * @param {string} searchTerm - النص المدخل في حقل البحث
   */
  filterContactsUI(searchTerm) {
    const term = searchTerm.toLowerCase().trim();
    const contactBlocks =
      this.elements.contactsContainer.querySelectorAll(".block");
    let visibleCount = 0;
    let currentHeader = null;

    contactBlocks.forEach((block) => {
      const contactName =
        block.querySelector("h4")?.textContent.toLowerCase() || "";
      const header = block.previousElementSibling; // الحصول على عنوان الحرف السابق
      // التأكد من أنه عنصر عنوان الحرف
      const isHeader =
        header && header.classList.contains("contact-letter-header");

      if (contactName.includes(term)) {
        block.style.display = ""; // استخدام '' لإعادة النمط الافتراضي
        visibleCount++;
        // إظهار عنوان الحرف إذا كان هناك عنصر مطابق تحته
        if (isHeader) {
          header.style.display = "";
          currentHeader = header; // تتبع آخر عنوان ظاهر
        } else if (currentHeader) {
          currentHeader.style.display = ""; // إظهار العنوان السابق إذا كان العنصر الأول تحته
        }
      } else {
        block.style.display = "none";
        // إخفاء عنوان الحرف إذا لم يكن هناك عناصر مطابقة تحته في هذه الدورة
        // سيتم تحديثه إذا ظهر عنصر مطابق لاحقًا
        // if (isHeader) {
        //     header.style.display = 'none';
        // }
      }
    });

    // دورة ثانية لإخفاء العناوين التي لا تتبعها عناصر ظاهرة
    const headers = this.elements.contactsContainer.querySelectorAll(
      ".contact-letter-header"
    );
    headers.forEach((header) => {
      let nextElement = header.nextElementSibling;
      let hasVisibleChild = false;
      while (nextElement && nextElement.classList.contains("block")) {
        if (nextElement.style.display !== "none") {
          hasVisibleChild = true;
          break;
        }
        nextElement = nextElement.nextElementSibling;
      }
      if (!hasVisibleChild) {
        header.style.display = "none";
      } else {
        header.style.display = ""; // التأكد من إظهاره إذا كان لديه أبناء ظاهرون
      }
    });

    // يمكنك إضافة رسالة "لا توجد نتائج" إذا لزم الأمر
    const noResultsMsg =
      this.elements.contactsContainer.querySelector(".no-results");
    if (visibleCount === 0 && term !== "") {
      if (!noResultsMsg) {
        const msgDiv = document.createElement("div");
        msgDiv.className = "no-results";
        msgDiv.textContent = "لا توجد نتائج مطابقة.";
        this.elements.contactsContainer.appendChild(msgDiv);
      }
    } else if (noResultsMsg) {
      noResultsMsg.remove();
    }
  }

  /**
   * التعامل مع اختيار/إلغاء اختيار جهة اتصال
   * @param {HTMLElement} contactElement - عنصر جهة الاتصال الذي تم النقر عليه
   */
  handleContactSelection(contactElement) {
    const contactId = parseInt(contactElement.getAttribute("data-contact-id"));
    if (isNaN(contactId)) return;

    const contactName =
      contactElement.querySelector("h4")?.textContent || "Unknown";
    const contactImage =
      contactElement.querySelector("img")?.src ||
      "/assets/avatars/imageGrey.jpg";

    const isSelected = contactElement.classList.toggle("selected"); // تبديل الفئة وإرجاع الحالة الجديدة

    if (isSelected) {
      // التأكد من عدم الإضافة المكررة
      if (!this.selectedContacts.some((c) => c.id === contactId)) {
        this.selectedContacts.push({
          id: contactId,
          name: contactName,
          picture: contactImage,
          isAdmin: false, // افتراضيًا ليس مشرفًا
        });

        if (!this.selectedParticipants.some((c) => c.id === contactId)) {
          this.selectedParticipants.push({
            userId: contactId,
            name: contactName,
            imgSrc: contactImage,
            isAdmin: false,
          }); // Initially not admin
        }
      }
    } else {
      this.selectedContacts = this.selectedContacts.filter(
        (contact) => contact.id !== contactId
      );
      // Remove participant
      this.selectedParticipants = this.selectedParticipants.filter(
        (p) => p.userId !== contactId
      );
    }
    this.updateSelectedParticipantsUI();
    this.toggleContinueButton(); // تحديث حالة زر المتابعة
    //console.log("Selected contacts:", this.selectedContacts); // للتصحيح
  }

  /**
   * تبديل عرض زر المتابعة وإنشاؤه إذا لم يكن موجودًا
   */
  toggleContinueButton() {
    let continueButton = this.elements.continueBtnContainer;

    // إنشاء الزر إذا لم يكن موجودًا
    if (!continueButton) {
      continueButton = document.createElement("div");
      continueButton.className = "continue-button-container"; // حاوية للتحكم بالموضع
      continueButton.innerHTML = `
                <button id="continueToCreateGroupBtn" class="continue-button">
                                       <i class="fe fe-plus fe-24"></i>

                </button>
            `;
      this.elements.groupSelectScreen.appendChild(continueButton); // إضافة الحاوية للشاشة
      this.elements.continueBtnContainer = continueButton; // تخزين الحاوية
      // إضافة مستمع الحدث مرة واحدة عند الإنشاء
      continueButton
        .querySelector("button")
        .addEventListener("click", () => this.navigateToCreateGroupScreen());
    }

    // إظهار أو إخفاء الحاوية بناءً على وجود تحديدات
    const buttonElement = continueButton.querySelector("button");
    if (this.selectedContacts.length > 0) {
      continueButton.style.display = "flex"; // أو 'block' حسب التصميم
      buttonElement.disabled = false;
    } else {
      continueButton.style.display = "none";
      buttonElement.disabled = true;
    }
    // التأكد من أن الزر نفسه ممكّن/معطل بشكل صحيح
    buttonElement.disabled = this.selectedContacts.length === 0;
  }

  /**
   * الانتقال إلى شاشة إنشاء تفاصيل المجموعة
   */
  navigateToCreateGroupScreen() {
    if (this.selectedContacts.length === 0) {
      this.showNotification("الرجاء اختيار عضو واحد على الأقل.", "warning");
      return;
    }
    //console.log("Navigating to create group details screen...");
    this.elements.groupSelectScreen.style.display = "none";
    this.elements.createGroupScreen.style.display = "block"; // أو 'flex' حسب التصميم
    this.currentStep = "create";

    this.renderSelectedMembersDetails(); // عرض الأعضاء في الشاشة الثانية
    this.validateGroupForm(); // التحقق من النموذج مبدئيًا
  }

  /**
   * العودة إلى شاشة اختيار جهات الاتصال
   */
  navigateBackToSelect() {
    //console.log("Navigating back to contact selection...");
    this.elements.createGroupScreen.style.display = "none";
    this.elements.groupSelectScreen.style.display = "block"; // أو 'flex'
    this.elements.mainScreen.style.display = "block";
    this.currentStep = "select";
    // لا حاجة لإعادة تحميل جهات الاتصال هنا، فقط عرض الشاشة
    this.reapplySelections(); // التأكد من أن الاختيارات لا تزال ظاهرة في شاشة الاختيار
  }

  /**
   * عرض الأعضاء المحددين في شاشة إنشاء تفاصيل المجموعة
   */
  renderSelectedMembersDetails() {
    const membersList = this.elements.selectedMembersList;
    membersList.innerHTML = ""; // مسح القائمة الحالية
    this.elements.selectedMembersCount.textContent =
      this.selectedContacts.length; // تحديث العدد

    this.selectedContacts.forEach((contact) => {
      const memberElement = document.createElement("div");
      memberElement.className = "selected-member";
      memberElement.setAttribute("data-contact-id", contact.id);

      // تحديد دور المستخدم بناءً على isCurrentUser و isAdmin
      let roleText = "عضو";
      let roleClass = "role-member";
      // --- افترض وجود متغير currentUser.id ---
      const currentUserId = 1; // استبدل بمعرف المستخدم الحالي الفعلي
      if (contact.id === currentUserId) {
        roleText = "أنت (مشرف)";
        roleClass = "role-admin";
        contact.isAdmin = true; // المنشئ هو مشرف دائمًا
      } else if (contact.isAdmin) {
        roleText = "مشرف";
        roleClass = "role-admin";
      }

      memberElement.innerHTML = `
                <div class="member-avatar">
                    <img src="${
                      contact.picture || "images/default-avatar.png"
                    }" alt="${contact.name}">
                </div>
                <div class="member-info">
                    <div class="member-name">${contact.name}</div>
                    <div class="member-role ${roleClass}">${roleText}</div>
                </div>
                <div class="member-actions">
                     ${
                       contact.id !== currentUserId
                         ? `
                        <button class="toggle-admin-btn" title="${
                          contact.isAdmin ? "إلغاء الإشراف" : "تعيين كمشرف"
                        }">
                             ${
                               contact.isAdmin ? "👑" : "🛡️"
                             } <!-- أيقونات للمشرف/التعيين -->
                         </button>
                         <button class="remove-member-btn" title="إزالة العضو">
                            <svg viewBox="0 0 24 24" width="18" height="18"><path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"></path></svg>
                        </button>
                     `
                         : ""
                     }
                </div>
            `;
      membersList.appendChild(memberElement);
    });
  }

  /**
   * تبديل حالة المشرف لعضو
   * @param {number} contactId - معرف جهة الاتصال
   */
  toggleAdminStatus(contactId) {
    // --- افترض وجود currentUser.id ---
    const currentUserId = 1; // استبدل بمعرف المستخدم الحالي
    if (contactId === currentUserId) {
      //console.log("لا يمكن تغيير دور منشئ المجموعة.");
      return; // لا يمكن تغيير دور المنشئ
    }

    const contactIndex = this.selectedContacts.findIndex(
      (contact) => contact.id === contactId
    );
    if (contactIndex !== -1) {
      this.selectedContacts[contactIndex].isAdmin =
        !this.selectedContacts[contactIndex].isAdmin;
      this.renderSelectedMembersDetails(); // إعادة عرض القائمة لتحديث الواجهة
    }
  }

  /**
   * إزالة عضو من المجموعة قيد الإنشاء
   * @param {number} contactId - معرف جهة الاتصال
   */
  removeMember(contactId) {
    this.selectedContacts = this.selectedContacts.filter(
      (contact) => contact.id !== contactId
    );

    // تحديث حالة الاختيار في شاشة اختيار جهات الاتصال
    const contactElement = this.elements.contactsContainer.querySelector(
      `.block[data-contact-id="${contactId}"]`
    );
    if (contactElement) {
      contactElement.classList.remove("selected");
    }

    this.renderSelectedMembersDetails(); // إعادة عرض قائمة الأعضاء في شاشة التفاصيل
    this.validateGroupForm(); // إعادة التحقق من النموذج (قد يؤثر على زر الإنشاء)
    this.toggleContinueButton(); // تحديث زر المتابعة في شاشة الاختيار
  }

  /**
   * التعامل مع تغيير صورة المجموعة
   */
  handleGroupImageChange(event) {
    const file = event.target.files[0];
    if (file && file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = (e) => {
        this.groupImageBase64 = e.target.result; // تخزين كـ Base64
        this.elements.groupAvatarPreview.innerHTML = `
                    <img src="${this.groupImageBase64}" alt="معاينة صورة المجموعة" class="group-avatar-img">
                `;
      };
      reader.onerror = (error) => {
        console.error("Error reading file:", error);
        this.showNotification("حدث خطأ أثناء قراءة الصورة.", "error");
        this.groupImageBase64 = null; // إعادة تعيين الصورة في حالة الخطأ
        // إعادة عرض الأيقونة الافتراضية
        this.elements.groupAvatarPreview.innerHTML = `
                     <svg viewBox="0 0 60 60" width="100" height="100" class=""><path fill="#DFE5E7" d="M30 3C14.996 3 3 14.996 3 30s11.996 27 27 27 27-11.996 27-27S45.004 3 30 3zm0 51c-13.789 0-25-11.211-25-25 0-1.58.151-3.125.436-4.627l7.463 11.585c.479.742 1.33.967 2.11.541l7.423-4.018c.519-.28.868-.817.868-1.417v-9.926c0-1.669 1.565-2.925 3.146-2.497l4.132 1.123c.596.162 1.04.611 1.208 1.211l1.516 5.494c.446 1.616 2.639 2.025 3.697.747l3.658-4.462c.348-.425.866-.66 1.41-.66h4.273c1.377 0 2.5 1.123 2.5 2.5v1.373c3.67 2.446 6.147 6.574 6.147 11.369 0 7.718-6.282 14-14 14z"></path><path fill="#FFF" d="M48.436 25.373c-.285-1.499-.725-2.943-1.297-4.311l-4.132-1.123c-1.581-.428-3.146.828-3.146 2.497v9.926c0 .599-.349 1.136-.868 1.417l-7.423 4.018c-.78.426-1.631.201-2.11-.541l-7.463-11.585C19.151 26.875 19 28.42 19 30c0 6.065 4.935 11 11 11s11-4.935 11-11c0-4.795-3.077-8.923-7.147-10.369v-1.373c0-1.377-1.123-2.5-2.5-2.5h-4.273c-.544 0-1.062.235-1.41.66l-3.658 4.462c-1.058 1.278-3.251.869-3.697-.747L29.3 25.39c-.168-.6-.612-1.049-1.208-1.211l-4.132-1.123c1.582 5.113 6.139 8.943 11.807 8.943 6.617 0 12-5.383 12-12 0-.42-.024-.836-.069-1.249z"></path></svg>`;
      };
      reader.readAsDataURL(file); // قراءة الملف كـ Base64
    } else if (file) {
      this.showNotification(
        "الرجاء اختيار ملف صورة صالح (jpg, jpeg, png, gif, webp).",
        "error"
      );
      this.elements.groupImageInput.value = ""; // مسح حقل الإدخال
    }
  }

  /**
   * التحقق من صحة نموذج إنشاء المجموعة لتمكين زر الإنشاء
   */
  validateGroupForm() {
    const groupName = this.elements.groupNameInput.value.trim();
    const hasMembers = this.selectedContacts.length > 0;
    const isValid = groupName !== "" && hasMembers;

    this.elements.createGroupConfirmBtn.disabled = !isValid || this.isLoading;

    //console.log(
    //   `Form valid: ${isValid}, Group Name: "${groupName}", Members: ${this.selectedContacts.length}, Loading: ${this.isLoading}`
    // ); // للتصحيح
    return isValid;
  }

  /**
   * إنشاء المجموعة
   */
  async createGroup() {
    if (!this.validateGroupForm() || this.isLoading) {
      if (this.elements.groupNameInput.value.trim() === "") {
        this.showNotification("الرجاء إدخال اسم للمجموعة.", "warning");
      } else if (this.selectedContacts.length === 0) {
        this.showNotification("الرجاء اختيار عضو واحد على الأقل.", "warning");
      }
      return;
    }

    this.isLoading = true;
    this.elements.createGroupConfirmBtn.disabled = true;
    this.elements.createGroupBtnText.textContent = "جارٍ الإنشاء...";
    this.elements.createGroupBtnLoader.style.display = "inline-block"; // إظهار مؤشر التحميل

    // --- افترض وجود currentUser.id و companyId ---
    //const currentUserId = 1; // استبدل بمعرف المستخدم الحالي
    //const companyId = 1; // استبدل بمعرف الشركة الحالي

    //const groupData = {
    //    ChatName: this.elements.groupNameInput.value.trim(),
    //    ChatDescription: this.elements.groupDescInput.value.trim() || null, // إرسال null إذا كان فارغًا
    //    ChatPicture: this.groupImageBase64, // إرسال Base64 أو null
    //    ChatStatus: true,
    //    //CompanyID: null,
    //    GroupMembers: this.selectedContacts
    //        // تأكد من أن المستخدم الحالي (المنشئ) موجود ومشرف
    //        .map((contact) => ({
    //            UserID: contact.id,
    //            // تأكد من أن المنشئ دائمًا مشرف
    //            UserRoleInChat: contact.isAdmin ? 0 : 1, // 1: Admin, 2: Member
    //        })),
    //};
    //const base64File = await this.toBase64(this.groupImageBase64);
    const groupData = {
      teamName: this.elements.groupNameInput.value.trim(),
      teamDescription: this.elements.groupDescInput.value.trim() || null, // إرسال null إذا كان فارغًا
      teamPicture: this.groupImageBase64.split(",")[1], // إرسال Base64 أو null
      teamStatus: true,
      //CompanyID: null,
      teamMembers: this.selectedContacts
        // تأكد من أن المستخدم الحالي (المنشئ) موجود ومشرف
        .map((contact) => ({
          userID: contact.id,
          // تأكد من أن المنشئ دائمًا مشرف
          userRoleInChat: contact.isAdmin ? 0 : 1, // 1: Admin, 2: Member
        })),
    };

    // إضافة المنشئ كمشرف إذا لم يكن مختارًا بالفعل (احتياطي)
    //if (!groupData.GroupMembers.some((m) => m.UserID === currentUserId)) {
    //    groupData.GroupMembers.push({ UserID: currentUserId, UserRoleInChat: 1 });
    //}

    //////console.log("Attempting to create group with data:", groupData); // للتصحيح

    try {
      const result = await this.groupService.createGroup(groupData);
      //console.log("Group creation result:", result);

      // قد يكون result هو المجموعة المحفوظة محليًا (offline) أو المستلمة من الخادم
      if (result.resCode === 201) {
        this.showNotification(
          `تم إنشاء الفريق "${result.data.name}" بنجاح!`,
          "success"
        );
        this.navigateBackToSelect();
        this.closeEntireProcess(); // إغلاق وإعادة تعيين كل شيء
        //this.groupService.refreshChatList(); // تحديث قائمة المحادثات الرئيسية
      } else {
        // هذا لا يجب أن يحدث عادةً إذا كانت الخدمة تُرجع دائمًا شيئًا أو ترمي خطأ
        throw new Error("فشل إنشاء المجموعة لسبب غير معروف.");
      }
    } catch (error) {
      console.error("Error during group creation process:", error);
      this.showNotification(
        `فشل إنشاء المجموعة: ${error.message || "خطأ غير متوقع"}`,
        "error"
      );
    } finally {
      this.isLoading = false;
      this.elements.createGroupConfirmBtn.disabled = false; // أعد تمكين الزر حتى لو فشل
      this.elements.createGroupBtnText.textContent = "إنشاء";
      this.elements.createGroupBtnLoader.style.display = "none"; // إخفاء مؤشر التحميل
      this.validateGroupForm(); // أعد التحقق لضبط حالة الزر النهائية
    }
  }

  async toBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  }
  /**
   * إغلاق عملية إنشاء المجموعة بالكامل وإعادة تعيين كل شيء
   */
  closeEntireProcess() {
    //console.log("Closing group creation process...");
    // إخفاء كلا الشاشتين
    this.elements.groupSelectScreen.style.display = "none";
    this.elements.createGroupScreen.style.display = "none";
    this.elements.mainScreen.style.display = "block";
    this.elements.selectedParticipantsContainer.innerHTML = "";
    // إعادة تعيين الحالة الداخلية
    this.selectedContacts = [];
    this.selectedParticipants = [];
    this.groupImageBase64 = null;
    this.currentStep = "select";
    this.isLoading = false;

    // إعادة تعيين حقول النموذج
    if (this.elements.groupNameInput) this.elements.groupNameInput.value = "";
    if (this.elements.groupDescInput) this.elements.groupDescInput.value = "";
    if (this.elements.groupImageInput) this.elements.groupImageInput.value = ""; // مسح اختيار الملف
    if (this.elements.groupAvatarPreview) {
      // إعادة الأيقونة الافتراضية
      this.elements.groupAvatarPreview.innerHTML = `
                 <svg viewBox="0 0 60 60" width="100" height="100" class=""><path fill="#DFE5E7" d="M30 3C14.996 3 3 14.996 3 30s11.996 27 27 27 27-11.996 27-27S45.004 3 30 3zm0 51c-13.789 0-25-11.211-25-25 0-1.58.151-3.125.436-4.627l7.463 11.585c.479.742 1.33.967 2.11.541l7.423-4.018c.519-.28.868-.817.868-1.417v-9.926c0-1.669 1.565-2.925 3.146-2.497l4.132 1.123c.596.162 1.04.611 1.208 1.211l1.516 5.494c.446 1.616 2.639 2.025 3.697.747l3.658-4.462c.348-.425.866-.66 1.41-.66h4.273c1.377 0 2.5 1.123 2.5 2.5v1.373c3.67 2.446 6.147 6.574 6.147 11.369 0 7.718-6.282 14-14 14z"></path><path fill="#FFF" d="M48.436 25.373c-.285-1.499-.725-2.943-1.297-4.311l-4.132-1.123c-1.581-.428-3.146.828-3.146 2.497v9.926c0 .599-.349 1.136-.868 1.417l-7.423 4.018c-.78.426-1.631.201-2.11-.541l-7.463-11.585C19.151 26.875 19 28.42 19 30c0 6.065 4.935 11 11 11s11-4.935 11-11c0-4.795-3.077-8.923-7.147-10.369v-1.373c0-1.377-1.123-2.5-2.5-2.5h-4.273c-.544 0-1.062.235-1.41.66l-3.658 4.462c-1.058 1.278-3.251.869-3.697-.747L29.3 25.39c-.168-.6-.612-1.049-1.208-1.211l-4.132-1.123c1.582 5.113 6.139 8.943 11.807 8.943 6.617 0 12-5.383 12-12 0-.42-.024-.836-.069-1.249z"></path></svg>
             `;
    }
    if (this.elements.selectedMembersList)
      this.elements.selectedMembersList.innerHTML = "";
    if (this.elements.selectedMembersCount)
      this.elements.selectedMembersCount.textContent = "0";
    if (this.elements.createGroupConfirmBtn)
      this.elements.createGroupConfirmBtn.disabled = true;

    // إلغاء تحديد جهات الاتصال في شاشة الاختيار
    const selectedContactElements =
      this.elements.contactsContainer.querySelectorAll(".block.selected");
    selectedContactElements.forEach((el) => el.classList.remove("selected"));

    // إخفاء زر المتابعة
    if (this.elements.continueBtnContainer) {
      this.elements.continueBtnContainer.style.display = "none";
    }
    // يمكنك هنا أيضًا استدعاء دالة لإغلاق القائمة الجانبية الرئيسية إذا كانت مفتوحة
    // closeLeftSidebar();
  }

  /**
   * عرض إشعار للمستخدم
   * @param {string} message - نص الإشعار
   * @param {'success'|'error'|'warning'|'info'} type - نوع الإشعار
   */
  showNotification(message, type = "info") {
    // إنشاء عنصر الإشعار
    const notification = document.createElement("div");
    // إضافة فئات أساسية ونوعية
    notification.className = `app-notification notification-${type}`;
    notification.textContent = message;

    // إضافة أيقونة اختيارية بناءً على النوع
    let icon = "";
    switch (type) {
      case "success":
        icon = "✓";
        break;
      case "error":
        icon = "✕";
        break;
      case "warning":
        icon = "!";
        break;
      default:
        icon = "ℹ";
        break;
    }
    notification.innerHTML = `<span class="notification-icon">${icon}</span> ${message}`;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // جعل الإشعار يظهر بسلاسة
    requestAnimationFrame(() => {
      notification.classList.add("show");
    });

    // إزالة الإشعار بعد فترة
    setTimeout(() => {
      notification.classList.remove("show");
      // إزالة العنصر من الـ DOM بعد انتهاء الانتقال
      notification.addEventListener(
        "transitionend",
        () => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        },
        { once: true }
      ); // تأكد من أن المستمع يعمل مرة واحدة فقط
    }, 4000); // زيادة المدة قليلاً
  }

  // --- دوال مساعدة إضافية ---
  formatDate(dateString) {
    // تنفيذ دالة تنسيق التاريخ هنا (مثل mDate التي كانت مستخدمة سابقًا)
    // مثال بسيط:
    if (!dateString) return "";
    try {
      return new Date(dateString).toLocaleDateString("ar-EG", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch (e) {
      return dateString; // أعد السلسلة الأصلية في حالة الخطأ
    }
  }

  formatLastSeen(dateString) {
    // تنفيذ دالة تنسيق آخر ظهور هنا
    if (!dateString) return "غير معروف";
    // مثال بسيط باستخدام timeago.js (إذا تم تضمينها) أو منطق مخصص
    // return timeago.format(dateString, 'ar');
    const date = new Date(dateString);
    const now = new Date();
    const diffSeconds = Math.round((now - date) / 1000);
    const diffMinutes = Math.round(diffSeconds / 60);
    const diffHours = Math.round(diffMinutes / 60);
    const diffDays = Math.round(diffHours / 24);

    if (diffSeconds < 60) return `منذ ${diffSeconds} ثانية`;
    if (diffMinutes < 60) return `منذ ${diffMinutes} دقيقة`;
    if (diffHours < 24) return `منذ ${diffHours} ساعة`;
    if (diffDays === 1)
      return `أمس في ${date.toLocaleTimeString("ar-EG", {
        hour: "numeric",
        minute: "2-digit",
      })}`;
    if (diffDays < 7) return `منذ ${diffDays} أيام`;
    return date.toLocaleDateString("ar-EG");
  }

  updateSelectedParticipantsUI() {
    this.elements.selectedParticipantsContainer.innerHTML = ""; // Clear current chips
    this.selectedParticipants.forEach((p) => {
      const chip = document.createElement("div");
      chip.classList.add("selected-participant-chip");
      chip.dataset.userId = p.userId;

      const img = document.createElement("img");
      img.src = p.imgSrc;
      img.alt = p.name;
      chip.appendChild(img);

      const nameSpan = document.createElement("span");
      nameSpan.textContent = p.name.split(" ")[0]; // Show first name for brevity
      chip.appendChild(nameSpan);

      // Add admin badge/toggle if needed (example)
      if (p.isAdmin) {
        const adminBadge = document.createElement("span");
        adminBadge.classList.add("admin-badge");
        adminBadge.textContent = "Admin";
        adminBadge.onclick = (e) => {
          e.stopPropagation(); // Prevent chip removal
          toggleAdminStatus(p.userId);
        };
        chip.appendChild(adminBadge);
      }

      const removeBtn = document.createElement("span");
      removeBtn.classList.add("remove-participant");
      removeBtn.innerHTML = "&times;"; // 'x' symbol
      removeBtn.onclick = () => this.removeParticipant(p.userId);
      chip.appendChild(removeBtn);

      this.elements.selectedParticipantsContainer.appendChild(chip);
    });
    //participantCountSpan.textContent = this.selectedParticipants.length;
  }

  // Function to remove a participant from the selected list (called by chip 'x')
  removeParticipant(userId) {
    this.selectedParticipants = this.selectedParticipants.filter(
      (p) => p.userId !== userId
    );
    this.removeMember(userId);
    this.updateSelectedParticipantsUI();
    //updateGoToCreateGroupButton();
  }
}

// --- نقطة الدخول وتهيئة الكائنات ---
document.addEventListener("DOMContentLoaded", () => {
  // تهيئة الخدمات
  const groupService = new GroupService();
  const userProcessor = new UserProcessor();

  // تهيئة وجهة إنشاء المجموعة
  const groupDestination = new GroupDestination(groupService, userProcessor);

  // --- ربط زر فتح شاشة اختيار الأعضاء ---
  // افترض أن لديك زرًا في الواجهة الرئيسية لفتح عملية إنشاء المجموعة
  const openGroupCreationButton = document.getElementById("openCreateGroup"); // استبدل بالمعرف الفعلي
  if (openGroupCreationButton) {
    openGroupCreationButton.addEventListener("click", () => {
      // إظهار شاشة اختيار الأعضاء عند النقر
      groupDestination.elements.groupSelectScreen.style.display = "block"; // أو 'flex'
      groupDestination.loadAndRenderContacts(); // تحميل جهات الاتصال عند الفتح
    });
  }

  // --- تهيئة SignalR (إذا كنت تستخدمه) ---
  // const signalRManager = new SignalRManager(storageService.getToken());
  // signalRManager.startConnection()
  //     .then(() => {
  //        //console.log("SignalR Connected successfully.");
  //         groupService.setSocket(signalRManager.connection); // تمرير الاتصال للخدمة
  //         // يمكنك بدء الاستماع لأحداث SignalR هنا
  //     })
  //     .catch(err => console.error("SignalR Connection Error:", err));
});

// --- وظائف مساعدة للتحكم في عرض الشاشات (يمكن استدعاؤها من HTML onclick) ---
function showGroupSelectionScreen() {
  // يجب أن يكون لديك وصول لمثيل GroupDestination هنا
  // هذا الأسلوب غير مثالي، يفضل إدارة الحالة داخل الكائن
  const groupSelectScreen = document.getElementById("group");
  if (groupSelectScreen) {
    groupSelectScreen.style.display = "block"; // أو flex
    // قد تحتاج لاستدعاء دالة تحميل جهات الاتصال هنا إذا لم يتم تحميلها تلقائيًا
  }
}

function closeGroupSelectionScreen() {
  const groupSelectScreen = document.getElementById("group");
  if (groupSelectScreen) {
    groupSelectScreen.style.display = "none";
    // قد تحتاج لإعادة تعيين حالة الاختيار هنا
  }
}
