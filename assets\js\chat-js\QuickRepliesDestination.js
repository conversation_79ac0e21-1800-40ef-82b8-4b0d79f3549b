/**
 * فئة إدارة واجهة الردود السريعة - تتبع نمط GroupDestination
 */
class QuickRepliesDestination {
  constructor(quickRepliesService) {
    this.quickRepliesService = quickRepliesService;
    this.currentQuickReplies = [];
    this.filteredQuickReplies = [];
    this.currentEditingId = null;
    this.currentStep = "list"; // 'list', 'create', 'edit'
    this.isLoading = false;

    this.elements = {
      // الشاشات
      mainScreen: document.getElementById("leftSid"),
      quickRepliesListScreen: document.getElementById("QuickReplyList"),
      createQuickReplyScreen: document.getElementById(
        "createQuickReplyDetails"
      ),

      // أزرار التنقل والتحكم
      closeQuickRepliesBtn: document.querySelector(
        "#QuickReplyList .header-Chat .icons"
      ),
      backToListBtn: document.getElementById("backToQuickRepliesListBtn"),
      createQuickReplyConfirmBtn: document.getElementById(
        "createQuickReplyConfirmBtn"
      ),
      createQuickReplyBtnText: document.querySelector(
        "#createQuickReplyConfirmBtn .btn-text"
      ),
      createQuickReplyBtnLoader: document.querySelector(
        "#createQuickReplyConfirmBtn .loader"
      ),

      // عناصر شاشة قائمة الردود السريعة
      quickRepliesContainer: document.querySelector("#QuickReply-elementList"),
      quickRepliesSearchInput: document.querySelector("#QuickReply-search"),
      addQuickReplyBtn: null, // سيتم إنشاؤه ديناميكيًا

      // عناصر شاشة إنشاء/تعديل الرد السريع
      quickReplyTitleInput: document.getElementById("quickReplyTitleInput"),
      quickReplyTypeSelect: document.getElementById("quickReplyTypeSelect"),
      quickReplyContentInput: document.getElementById("quickReplyContentInput"),
      quickReplyFileInput: document.getElementById("quickReplyFileInput"),
      quickReplyFilePreview: document.getElementById("quickReplyFilePreview"),
    };

    if (this.elements.quickRepliesListScreen) {
      this.initializeEventListeners();
      this.createAddButton();
    }
  }

  /**
   * تهيئة مستمعي الأحداث
   */
  initializeEventListeners() {
    // زر إغلاق قائمة الردود السريعة
    if (this.elements.closeQuickRepliesBtn) {
      this.elements.closeQuickRepliesBtn.addEventListener("click", () => {
        this.closeQuickRepliesList();
      });
    }

    // زر الرجوع من شاشة الإنشاء
    if (this.elements.backToListBtn) {
      this.elements.backToListBtn.addEventListener("click", () => {
        this.showQuickRepliesList();
      });
    }

    // زر تأكيد إنشاء الرد السريع
    if (this.elements.createQuickReplyConfirmBtn) {
      this.elements.createQuickReplyConfirmBtn.addEventListener("click", () => {
        this.handleCreateQuickReply();
      });
    }

    // البحث في الردود السريعة
    if (this.elements.quickRepliesSearchInput) {
      this.elements.quickRepliesSearchInput.addEventListener("input", (e) => {
        this.handleSearch(e.target.value);
      });
    }

    // تغيير نوع الرسالة
    if (this.elements.quickReplyTypeSelect) {
      this.elements.quickReplyTypeSelect.addEventListener("change", (e) => {
        this.handleMessageTypeChange(parseInt(e.target.value));
      });
    }

    // مراقبة تغييرات النموذج للمعاينة
    if (this.elements.quickReplyTitleInput) {
      this.elements.quickReplyTitleInput.addEventListener("input", () => {
        this.updatePreview();
      });
    }

    if (this.elements.quickReplyContentInput) {
      this.elements.quickReplyContentInput.addEventListener("input", () => {
        this.updatePreview();
      });
    }

    // اختيار ملف
    if (this.elements.quickReplyFileInput) {
      this.elements.quickReplyFileInput.addEventListener("change", (e) => {
        this.handleFileSelection(e.target.files[0]);
      });
    }
  }

  /**
   * إنشاء زر إضافة رد سريع جديد
   */
  createAddButton() {
    if (!this.elements.quickRepliesContainer) return;

    const addButtonHtml = `
      <div class="add-quick-reply-btn" onclick="quickRepliesDestination.showCreateQuickReply()">
        <div class="add-btn-content">
          <div class="add-btn-icon">
            <i class="fe fe-plus"></i>
          </div>
          <div class="add-btn-text">
            <h4>إضافة رد سريع جديد</h4>
            <p>إنشاء رد سريع لاستخدامه في المحادثات</p>
          </div>
        </div>
      </div>
    `;

    this.elements.quickRepliesContainer.insertAdjacentHTML(
      "afterbegin",
      addButtonHtml
    );
  }

  /**
   * عرض قائمة الردود السريعة
   */
  async showQuickRepliesList() {
    try {
      this.isLoading = true;
      this.currentStep = "list";

      // إخفاء الشاشات الأخرى
      if (this.elements.createQuickReplyScreen) {
        this.elements.createQuickReplyScreen.style.display = "none";
      }

      // عرض شاشة القائمة
      if (this.elements.quickRepliesListScreen) {
        this.elements.quickRepliesListScreen.style.display = "block";
      }

      // تحميل الردود السريعة
      this.currentQuickReplies =
        await this.quickRepliesService.loadQuickReplies();
      this.filteredQuickReplies = [...this.currentQuickReplies];

      // عرض الردود السريعة
      this.renderQuickRepliesList();
    } catch (error) {
      console.error("خطأ في عرض قائمة الردود السريعة:", error);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * عرض شاشة إنشاء رد سريع جديد
   */
  showCreateQuickReply() {
    this.currentStep = "create";
    this.currentEditingId = null;

    // إخفاء شاشة القائمة
    if (this.elements.quickRepliesListScreen) {
      this.elements.quickRepliesListScreen.style.display = "none";
    }

    // عرض شاشة الإنشاء
    if (this.elements.createQuickReplyScreen) {
      this.elements.createQuickReplyScreen.style.display = "block";
    }

    // مسح النموذج
    this.clearForm();

    // تحديث عنوان الشاشة
    const titleElement =
      this.elements.createQuickReplyScreen.querySelector(".newText h2");
    if (titleElement) {
      titleElement.textContent = "إنشاء رد سريع جديد";
    }

    // تحديث نص الزر
    if (this.elements.createQuickReplyBtnText) {
      this.elements.createQuickReplyBtnText.textContent = "إنشاء الرد السريع";
    }
  }

  /**
   * عرض شاشة تعديل رد سريع
   */
  showEditQuickReply(quickReplyId) {
    const quickReply = this.quickRepliesService.getQuickReplyById(quickReplyId);
    if (!quickReply) {
      console.error("الرد السريع غير موجود:", quickReplyId);
      return;
    }

    this.currentStep = "edit";
    this.currentEditingId = quickReplyId;

    // إخفاء شاشة القائمة
    if (this.elements.quickRepliesListScreen) {
      this.elements.quickRepliesListScreen.style.display = "none";
    }

    // عرض شاشة التعديل
    if (this.elements.createQuickReplyScreen) {
      this.elements.createQuickReplyScreen.style.display = "block";
    }

    // ملء النموذج بالبيانات الحالية
    this.fillForm(quickReply);

    // تحديث عنوان الشاشة
    const titleElement =
      this.elements.createQuickReplyScreen.querySelector(".newText h2");
    if (titleElement) {
      titleElement.textContent = "تعديل الرد السريع";
    }

    // تحديث نص الزر
    if (this.elements.createQuickReplyBtnText) {
      this.elements.createQuickReplyBtnText.textContent = "حفظ التغييرات";
    }
  }

  /**
   * إغلاق قائمة الردود السريعة
   */
  closeQuickRepliesList() {
    if (this.elements.quickRepliesListScreen) {
      this.elements.quickRepliesListScreen.style.display = "none";
    }
    if (this.elements.createQuickReplyScreen) {
      this.elements.createQuickReplyScreen.style.display = "none";
    }
    if (this.elements.mainScreen) {
      this.elements.mainScreen.style.display = "block";
    }
  }

  /**
   * عرض الردود السريعة في القائمة
   */
  renderQuickRepliesList() {
    if (!this.elements.quickRepliesContainer) return;

    // الاحتفاظ بزر الإضافة
    const addButton = this.elements.quickRepliesContainer.querySelector(
      ".add-quick-reply-btn"
    );

    if (this.filteredQuickReplies.length === 0) {
      this.elements.quickRepliesContainer.innerHTML = "";
      if (addButton) {
        this.elements.quickRepliesContainer.appendChild(addButton);
      }

      const emptyStateHtml = `
        <div class="empty-state">
          <div class="empty-icon">
            <i class="fe fe-message-circle"></i>
          </div>
          <h3>لا توجد ردود سريعة</h3>
          <p>قم بإنشاء أول رد سريع لك</p>
        </div>
      `;
      this.elements.quickRepliesContainer.insertAdjacentHTML(
        "beforeend",
        emptyStateHtml
      );
      return;
    }

    const quickRepliesHtml = this.filteredQuickReplies
      .map((reply) => {
        const typeInfo = this.quickRepliesService.getMessageTypeInfo(
          reply.messageType
        );
        const previewText = this.quickRepliesService.getPreviewText(reply);

        return `
        <div class="block quick-reply-item" data-id="${reply.id}">
          <div class="imgBox quick-reply-type-icon" style="background-color: ${typeInfo.color}">
            <i class="fe fe-${typeInfo.icon}"></i>
          </div>
          <div class="h-text">
            <div class="head">
              <h4 title="${reply.title}" aria-label="${reply.title}">
                ${reply.title}
              </h4>
              <div class="quick-reply-actions">
                <button class="action-btn edit-btn" onclick="quickRepliesDestination.showEditQuickReply('${reply.id}')" title="تعديل">
                  <i class="fe fe-edit-2"></i>
                </button>
                <button class="action-btn delete-btn" onclick="quickRepliesDestination.deleteQuickReply('${reply.id}')" title="حذف">
                  <i class="fe fe-trash-2"></i>
                </button>
              </div>
            </div>
            <div class="message">
              <p title="${previewText}" aria-label="${previewText}">
                ${previewText}
              </p>
            </div>
          </div>
        </div>
      `;
      })
      .join("");

    this.elements.quickRepliesContainer.innerHTML = "";
    if (addButton) {
      this.elements.quickRepliesContainer.appendChild(addButton);
    }
    this.elements.quickRepliesContainer.insertAdjacentHTML(
      "beforeend",
      quickRepliesHtml
    );
  }

  /**
   * معالجة البحث
   */
  handleSearch(searchTerm) {
    this.filteredQuickReplies =
      this.quickRepliesService.searchQuickReplies(searchTerm);
    this.renderQuickRepliesList();
  }

  /**
   * معالجة إنشاء/تحديث الرد السريع
   */
  async handleCreateQuickReply() {
    try {
      if (this.isLoading) return;

      const formData = await this.getFormData();
      if (!this.validateForm(formData)) {
        return;
      }

      this.setLoading(true);

      if (this.currentStep === "create") {
        await this.quickRepliesService.createQuickReply(formData);
      } else if (this.currentStep === "edit") {
        await this.quickRepliesService.updateQuickReply(
          this.currentEditingId,
          formData
        );
      }

      // العودة إلى قائمة الردود السريعة
      await this.showQuickRepliesList();
    } catch (error) {
      console.error("خطأ في حفظ الرد السريع:", error);
      alert("حدث خطأ أثناء حفظ الرد السريع. يرجى المحاولة مرة أخرى.");
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * حذف رد سريع
   */
  async deleteQuickReply(quickReplyId) {
    if (!confirm("هل أنت متأكد من حذف هذا الرد السريع؟")) {
      return;
    }

    try {
      await this.quickRepliesService.deleteQuickReply(quickReplyId);
      await this.showQuickRepliesList();
    } catch (error) {
      console.error("خطأ في حذف الرد السريع:", error);
      alert("حدث خطأ أثناء حذف الرد السريع. يرجى المحاولة مرة أخرى.");
    }
  }

  /**
   * الحصول على بيانات النموذج
   */
  async getFormData() {
    const title = this.elements.quickReplyTitleInput?.value?.trim() || "";
    const messageType =
      parseInt(this.elements.quickReplyTypeSelect?.value) || 0;
    const content = this.elements.quickReplyContentInput?.value?.trim() || "";
    const file = this.elements.quickReplyFileInput?.files[0];

    const formData = {
      title,
      messageType,
      content,
    };

    // إضافة بيانات الملف إذا كان موجوداً
    if (file) {
      formData.fileName = file.name;
      formData.fileSize = file.size;
      formData.mimeType = file.type;
      // تحويل الملف إلى Base64
      formData.fileData = await this.quickRepliesService.fileToBase64(file);
    }

    return formData;
  }

  /**
   * التحقق من صحة النموذج
   */
  validateForm(formData) {
    if (!formData.title) {
      alert("يرجى إدخال عنوان للرد السريع");
      this.elements.quickReplyTitleInput?.focus();
      return false;
    }

    if (isNaN(formData.messageType)) {
      alert("يرجى اختيار نوع الرسالة");
      this.elements.quickReplyTypeSelect?.focus();
      return false;
    }

    if (!formData.content && ![1, 2, 3, 4, 9].includes(formData.messageType)) {
      alert("يرجى إدخال محتوى الرد السريع");
      this.elements.quickReplyContentInput?.focus();
      return false;
    }

    return true;
  }

  /**
   * مسح النموذج
   */
  clearForm() {
    if (this.elements.quickReplyTitleInput) {
      this.elements.quickReplyTitleInput.value = "";
    }
    if (this.elements.quickReplyTypeSelect) {
      this.elements.quickReplyTypeSelect.value = "0";
    }
    if (this.elements.quickReplyContentInput) {
      this.elements.quickReplyContentInput.value = "";
    }
    if (this.elements.quickReplyFileInput) {
      this.elements.quickReplyFileInput.value = "";
    }
    if (this.elements.quickReplyFilePreview) {
      this.elements.quickReplyFilePreview.style.display = "none";
    }
  }

  /**
   * ملء النموذج بالبيانات
   */
  fillForm(quickReply) {
    if (this.elements.quickReplyTitleInput) {
      this.elements.quickReplyTitleInput.value = quickReply.title || "";
    }
    if (this.elements.quickReplyTypeSelect) {
      this.elements.quickReplyTypeSelect.value = quickReply.messageType || 0;
    }
    if (this.elements.quickReplyContentInput) {
      this.elements.quickReplyContentInput.value = quickReply.content || "";
    }

    // معالجة نوع الرسالة
    this.handleMessageTypeChange(quickReply.messageType);
  }

  /**
   * معالجة تغيير نوع الرسالة
   */
  handleMessageTypeChange(messageType) {
    const contentGroup = document.getElementById("quickReplyContentGroup");
    const fileGroup = document.getElementById("quickReplyFileGroup");

    if (!contentGroup || !fileGroup) return;

    // إظهار/إخفاء الحقول حسب نوع الرسالة
    switch (messageType) {
      case 0: // نص
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder = "أدخل نص الرسالة";
        break;

      case 1: // صورة
      case 2: // فيديو
      case 3: // صوت
      case 4: // مستند
      case 9: // ملفات أخرى
        contentGroup.style.display = "block";
        fileGroup.style.display = "block";
        this.elements.quickReplyContentInput.placeholder =
          "أدخل تعليق (اختياري)";
        break;

      case 5: // جهة اتصال
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder =
          'أدخل بيانات جهة الاتصال كـ JSON: {"name": "الاسم", "phoneNumber": "+123456789"}';
        break;

      case 6: // موقع
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder =
          'أدخل بيانات الموقع كـ JSON: {"latitude": 0, "longitude": 0, "name": "اسم المكان"}';
        break;

      case 7: // استطلاع
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder =
          'أدخل بيانات الاستطلاع كـ JSON: {"question": "السؤال؟", "options": ["خيار 1", "خيار 2"]}';
        break;

      case 8: // تسجيل صوتي
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder =
          "أدخل وصف للرسالة الصوتية";
        break;
    }
  }

  /**
   * معالجة اختيار الملف
   */
  async handleFileSelection(file) {
    if (!file) return;

    try {
      // تحديد نوع الملف والأيقونة
      let fileIcon = "fe-file";
      let fileColor = "#607d8b";

      if (file.type.startsWith("image/")) {
        fileIcon = "fe-image";
        fileColor = "#ac44cf";
      } else if (file.type.startsWith("video/")) {
        fileIcon = "fe-video";
        fileColor = "#d3396d";
      } else if (file.type.startsWith("audio/")) {
        fileIcon = "fe-headphones";
        fileColor = "#ff9800";
      } else if (
        file.type === "application/pdf" ||
        file.name.endsWith(".pdf")
      ) {
        fileIcon = "fe-file-text";
        fileColor = "#dc3545";
      } else if (
        file.type.includes("word") ||
        file.name.endsWith(".doc") ||
        file.name.endsWith(".docx")
      ) {
        fileIcon = "fe-file-text";
        fileColor = "#2b579a";
      }

      // عرض معاينة الملف
      if (this.elements.quickReplyFilePreview) {
        const fileInfo = `
          <div class="file-info">
            <div class="file-info-icon" style="background: ${fileColor}">
              <i class="fe ${fileIcon}"></i>
            </div>
            <div class="file-info-details">
              <div class="file-info-name">${file.name}</div>
              <div class="file-info-size">${this.quickRepliesService.formatFileSize(
                file.size
              )}</div>
            </div>
            <button class="file-remove-btn" onclick="quickRepliesDestination.removeFile()" title="حذف الملف">
              <i class="fe fe-x"></i>
            </button>
          </div>
        `;
        this.elements.quickReplyFilePreview.innerHTML = fileInfo;
        this.elements.quickReplyFilePreview.style.display = "block";
        this.elements.quickReplyFilePreview.classList.add("animate-slide-up");
      }

      // تحديث المعاينة
      this.updatePreview();
    } catch (error) {
      console.error("خطأ في معالجة الملف:", error);
    }
  }

  /**
   * حذف الملف المختار
   */
  removeFile() {
    if (this.elements.quickReplyFileInput) {
      this.elements.quickReplyFileInput.value = "";
    }
    if (this.elements.quickReplyFilePreview) {
      this.elements.quickReplyFilePreview.style.display = "none";
      this.elements.quickReplyFilePreview.innerHTML = "";
    }
    this.updatePreview();
  }

  /**
   * تعيين حالة التحميل
   */
  setLoading(loading) {
    this.isLoading = loading;

    if (this.elements.createQuickReplyBtnText) {
      this.elements.createQuickReplyBtnText.style.display = loading
        ? "none"
        : "block";
    }

    if (this.elements.createQuickReplyBtnLoader) {
      this.elements.createQuickReplyBtnLoader.style.display = loading
        ? "block"
        : "none";
    }

    if (this.elements.createQuickReplyConfirmBtn) {
      this.elements.createQuickReplyConfirmBtn.disabled = loading;
    }
  }

  /**
   * تحديث معاينة الرسالة
   */
  updatePreview() {
    const title =
      this.elements.quickReplyTitleInput?.value?.trim() || "عنوان الرد السريع";
    const content =
      this.elements.quickReplyContentInput?.value?.trim() ||
      "محتوى الرسالة سيظهر هنا...";
    const messageType =
      parseInt(this.elements.quickReplyTypeSelect?.value) || 0;

    const previewTitle = document.getElementById("previewTitle");
    const previewBody = document.getElementById("previewBody");
    const previewIcon = document.getElementById("previewIcon");
    const previewGroup = document.getElementById("messagePreviewGroup");

    if (previewTitle) previewTitle.textContent = title;
    if (previewBody) previewBody.textContent = content;

    if (previewIcon) {
      const typeInfo = this.quickRepliesService.getMessageTypeInfo(messageType);
      previewIcon.innerHTML = `<i class="fe fe-${typeInfo.icon}"></i>`;
      previewIcon.style.background = typeInfo.color;
    }

    // إظهار المعاينة إذا كان هناك محتوى
    if (
      previewGroup &&
      (title !== "عنوان الرد السريع" ||
        content !== "محتوى الرسالة سيظهر هنا...")
    ) {
      previewGroup.style.display = "block";
      previewGroup.classList.add("animate-fade-scale");
    }
  }
}

// Global function for message type selection
function selectMessageType(messageType) {
  // Update hidden select
  const selectElement = document.getElementById("quickReplyTypeSelect");
  if (selectElement) {
    selectElement.value = messageType;
  }

  // Update visual selection
  const cards = document.querySelectorAll(".message-type-card");
  cards.forEach((card) => {
    card.classList.remove("selected");
    if (parseInt(card.dataset.type) === messageType) {
      card.classList.add("selected");
    }
  });

  // Trigger change event and update preview
  if (window.quickRepliesDestination) {
    window.quickRepliesDestination.handleMessageTypeChange(messageType);
    window.quickRepliesDestination.updatePreview();
  }
}
