/**
 * فئة إدارة واجهة الردود السريعة - تتبع نمط GroupDestination
 */
class QuickRepliesDestination {
  constructor(quickRepliesService) {
    this.quickRepliesService = quickRepliesService;
    this.currentQuickReplies = [];
    this.filteredQuickReplies = [];
    this.currentEditingId = null;
    this.currentStep = "list"; // 'list', 'create', 'edit'
    this.isLoading = false;

    this.elements = {
      // الشاشات
      mainScreen: document.getElementById("leftSid"),
      quickRepliesListScreen: document.getElementById("QuickReplyList"),
      createQuickReplyScreen: document.getElementById(
        "createQuickReplyDetails"
      ),

      // أزرار التنقل والتحكم
      closeQuickRepliesBtn: document.querySelector(
        "#QuickReplyList .header-Chat .icons"
      ),
      backToListBtn: document.getElementById("backToQuickRepliesListBtn"),
      createQuickReplyConfirmBtn: document.getElementById(
        "createQuickReplyConfirmBtn"
      ),
      createQuickReplyBtnText: document.querySelector(
        "#createQuickReplyConfirmBtn .btn-text"
      ),
      createQuickReplyBtnLoader: document.querySelector(
        "#createQuickReplyConfirmBtn .loader"
      ),

      // عناصر شاشة قائمة الردود السريعة
      quickRepliesContainer: document.querySelector("#QuickReply-elementList"),
      quickRepliesSearchInput: document.querySelector("#QuickReply-search"),
      addQuickReplyBtn: null, // سيتم إنشاؤه ديناميكيًا

      // عناصر شاشة إنشاء/تعديل الرد السريع
      quickReplyTitleInput: document.getElementById("quickReplyTitleInput"),
      quickReplyTypeSelect: document.getElementById("quickReplyTypeSelect"),
      quickReplyContentInput: document.getElementById("quickReplyContentInput"),
      quickReplyFileButton: document.getElementById("quickReplyFileButton"),

      quickReplyFileInput: document.getElementById("quickReplyFileInput"),
      quickReplyFilePreview: document.getElementById("quickReplyFilePreview"),
    };

    if (this.elements.quickRepliesListScreen) {
      this.initializeEventListeners();
      this.createAddButton();
    }
  }

  /**
   * تهيئة مستمعي الأحداث
   */
  initializeEventListeners() {
    // زر إغلاق قائمة الردود السريعة
    if (this.elements.closeQuickRepliesBtn) {
      this.elements.closeQuickRepliesBtn.addEventListener("click", () => {
        this.closeQuickRepliesList();
      });
    }

    // زر الرجوع من شاشة الإنشاء
    if (this.elements.backToListBtn) {
      this.elements.backToListBtn.addEventListener("click", () => {
        this.showQuickRepliesList();
      });
    }

    // زر تأكيد إنشاء الرد السريع
    if (this.elements.createQuickReplyConfirmBtn) {
      this.elements.createQuickReplyConfirmBtn.addEventListener("click", () => {
        this.handleCreateQuickReply();
      });
    }

    // البحث في الردود السريعة
    if (this.elements.quickRepliesSearchInput) {
      this.elements.quickRepliesSearchInput.addEventListener("input", (e) => {
        this.handleSearch(e.target.value);
      });
    }

    // تغيير نوع الرسالة
    if (this.elements.quickReplyTypeSelect) {
      this.elements.quickReplyTypeSelect.addEventListener("change", (e) => {
        this.handleMessageTypeChange(parseInt(e.target.value));
      });
    }
    if (this.elements.quickReplyFileButton) {
      this.elements.quickReplyFileButton.addEventListener("click", (e) => {
        this.elements.quickReplyFileInput.click();
      });
    }
    // اختيار ملف
    if (this.elements.quickReplyFileInput) {
      this.elements.quickReplyFileInput.addEventListener("change", (e) => {
        this.handleFileSelection(e.target.files[0]);
      });
    }
  }

  /**
   * إنشاء زر إضافة رد سريع جديد
   */
  createAddButton() {
    if (!this.elements.quickRepliesContainer) return;

    const addButtonHtml = `
      <div class="add-quick-reply-btn" onclick="quickRepliesDestination.showCreateQuickReply()">
        <div class="add-btn-content">
          <div class="add-btn-icon">
            <i class="fe fe-plus"></i>
          </div>
          <div class="add-btn-text">
            <h4>إضافة رد سريع جديد</h4>
            <p>إنشاء رد سريع لاستخدامه في المحادثات</p>
          </div>
        </div>
      </div>
    `;

    this.elements.quickRepliesContainer.insertAdjacentHTML(
      "afterbegin",
      addButtonHtml
    );
  }

  /**
   * عرض قائمة الردود السريعة
   */
  async showQuickRepliesList() {
    try {
      this.isLoading = true;
      this.currentStep = "list";

      // إخفاء الشاشات الأخرى
      if (this.elements.createQuickReplyScreen) {
        this.elements.createQuickReplyScreen.style.display = "none";
      }

      // عرض شاشة القائمة
      if (this.elements.quickRepliesListScreen) {
        this.elements.quickRepliesListScreen.style.display = "block";
      }

      // تحميل الردود السريعة
      this.currentQuickReplies =
        await this.quickRepliesService.loadQuickReplies();
      this.filteredQuickReplies = [...this.currentQuickReplies];

      // عرض الردود السريعة
      this.renderQuickRepliesList();
    } catch (error) {
      console.error("خطأ في عرض قائمة الردود السريعة:", error);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * عرض شاشة إنشاء رد سريع جديد
   */
  showCreateQuickReply() {
    this.currentStep = "create";
    this.currentEditingId = null;

    console.log(
      "showCreateQuickReply",
      document.getElementById("leftSid").style.display === "none"
    );
    // إخفاء شاشة القائمة
    if (this.elements.quickRepliesListScreen) {
      this.elements.quickRepliesListScreen.style.display = "none";
      document.getElementById("leftSid").style.display = "none";
    }

    // عرض شاشة الإنشاء
    if (this.elements.createQuickReplyScreen) {
      this.elements.createQuickReplyScreen.style.display = "block";
    }

    // مسح النموذج
    this.clearForm();

    // تحديث عنوان الشاشة
    const titleElement =
      this.elements.createQuickReplyScreen.querySelector(".newText h2");
    if (titleElement) {
      titleElement.textContent = "إنشاء رد سريع جديد";
    }

    // تحديث نص الزر
    if (this.elements.createQuickReplyBtnText) {
      this.elements.createQuickReplyBtnText.textContent = "إنشاء الرد السريع";
    }
  }

  /**
   * عرض شاشة تعديل رد سريع
   */
  showEditQuickReply(quickReplyId) {
    const quickReply = this.quickRepliesService.getQuickReplyById(quickReplyId);
    if (!quickReply) {
      console.error("الرد السريع غير موجود:", quickReplyId);
      return;
    }

    this.currentStep = "edit";
    this.currentEditingId = quickReplyId;

    // إخفاء شاشة القائمة
    if (this.elements.quickRepliesListScreen) {
      this.elements.quickRepliesListScreen.style.display = "none";
    }

    // عرض شاشة التعديل
    if (this.elements.createQuickReplyScreen) {
      this.elements.createQuickReplyScreen.style.display = "block";
    }

    // ملء النموذج بالبيانات الحالية
    this.fillForm(quickReply);

    // تحديث عنوان الشاشة
    const titleElement =
      this.elements.createQuickReplyScreen.querySelector(".newText h2");
    if (titleElement) {
      titleElement.textContent = "تعديل الرد السريع";
    }

    // تحديث نص الزر
    if (this.elements.createQuickReplyBtnText) {
      this.elements.createQuickReplyBtnText.textContent = "حفظ التغييرات";
    }
  }

  /**
   * إغلاق قائمة الردود السريعة
   */
  closeQuickRepliesList() {
    if (this.elements.quickRepliesListScreen) {
      this.elements.quickRepliesListScreen.style.display = "none";
    }
    if (this.elements.createQuickReplyScreen) {
      this.elements.createQuickReplyScreen.style.display = "none";
    }
    if (this.elements.mainScreen) {
      this.elements.mainScreen.style.display = "block";
    }
  }

  /**
   * عرض الردود السريعة في القائمة
   */
  renderQuickRepliesList() {
    if (!this.elements.quickRepliesContainer) return;

    // الاحتفاظ بزر الإضافة
    const addButton = this.elements.quickRepliesContainer.querySelector(
      ".add-quick-reply-btn"
    );

    if (this.filteredQuickReplies.length === 0) {
      this.elements.quickRepliesContainer.innerHTML = "";
      if (addButton) {
        this.elements.quickRepliesContainer.appendChild(addButton);
      }

      const emptyStateHtml = `
        <div class="empty-state">
          <div class="empty-icon">
            <i class="fe fe-message-circle"></i>
          </div>
          <h3>لا توجد ردود سريعة</h3>
          <p>قم بإنشاء أول رد سريع لك</p>
        </div>
      `;
      this.elements.quickRepliesContainer.insertAdjacentHTML(
        "beforeend",
        emptyStateHtml
      );
      return;
    }

    const quickRepliesHtml = this.filteredQuickReplies
      .map((reply) => {
        const typeInfo = this.quickRepliesService.getMessageTypeInfo(
          reply.messageType
        );
        const previewText = this.quickRepliesService.getPreviewText(reply);

        return `
        <div class="block quick-reply-item" data-id="${reply.id}">
          <div class="imgBox quick-reply-type-icon" style="background-color: ${typeInfo.color}">
            <i class="fe fe-${typeInfo.icon}"></i>
          </div>
          <div class="h-text">
            <div class="head">
              <h4 title="${reply.title}" aria-label="${reply.title}">
                ${reply.title}
              </h4>
              <div class="quick-reply-actions">
                <button class="action-btn edit-btn" onclick="quickRepliesDestination.showEditQuickReply('${reply.id}')" title="تعديل">
                  <i class="fe fe-edit-2"></i>
                </button>
                <button class="action-btn delete-btn" onclick="quickRepliesDestination.deleteQuickReply('${reply.id}')" title="حذف">
                  <i class="fe fe-trash-2"></i>
                </button>
              </div>
            </div>
            <div class="message">
              <p title="${previewText}" aria-label="${previewText}">
                ${previewText}
              </p>
            </div>
          </div>
        </div>
      `;
      })
      .join("");

    this.elements.quickRepliesContainer.innerHTML = "";
    if (addButton) {
      this.elements.quickRepliesContainer.appendChild(addButton);
    }
    this.elements.quickRepliesContainer.insertAdjacentHTML(
      "beforeend",
      quickRepliesHtml
    );
  }

  /**
   * معالجة البحث
   */
  handleSearch(searchTerm) {
    this.filteredQuickReplies =
      this.quickRepliesService.searchQuickReplies(searchTerm);
    this.renderQuickRepliesList();
  }

  /**
   * معالجة إنشاء/تحديث الرد السريع
   */
  async handleCreateQuickReply() {
    try {
      if (this.isLoading) return;

      const formData = await this.getFormData();
      if (!this.validateForm(formData)) {
        return;
      }

      this.setLoading(true);

      if (this.currentStep === "create") {
        await this.quickRepliesService.createQuickReply(formData);
      } else if (this.currentStep === "edit") {
        await this.quickRepliesService.updateQuickReply(
          this.currentEditingId,
          formData
        );
      }

      // العودة إلى قائمة الردود السريعة
      await this.showQuickRepliesList();
    } catch (error) {
      console.error("خطأ في حفظ الرد السريع:", error);
      alert("حدث خطأ أثناء حفظ الرد السريع. يرجى المحاولة مرة أخرى.");
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * حذف رد سريع
   */
  async deleteQuickReply(quickReplyId) {
    if (!confirm("هل أنت متأكد من حذف هذا الرد السريع؟")) {
      return;
    }

    try {
      await this.quickRepliesService.deleteQuickReply(quickReplyId);
      await this.showQuickRepliesList();
    } catch (error) {
      console.error("خطأ في حذف الرد السريع:", error);
      alert("حدث خطأ أثناء حذف الرد السريع. يرجى المحاولة مرة أخرى.");
    }
  }

  /**
   * الحصول على بيانات النموذج
   */
  async getFormData() {
    const title = this.elements.quickReplyTitleInput?.value?.trim() || "";
    const messageType =
      this.elements.quickReplyFileInput?.files[0] !== null ? 1 : 0;
    const content = this.elements.quickReplyContentInput?.value?.trim() || "";
    const file = this.elements.quickReplyFileInput?.files[0];

    const formData = {
      title,
      messageType,
      content,
    };

    // معالجة الملفات للأنواع المختلفة
    if (file && [1, 2, 3, 4, 8, 9].includes(messageType)) {
      formData.fileName = file.name;
      formData.fileSize = file.size;
      formData.mimeType = file.type;
      // تحويل الملف إلى Base64
      formData.fileData = await this.quickRepliesService.fileToBase64(file);

      // إضافة مدة التسجيل للملفات الصوتية
      if (messageType === 3 || messageType === 8) {
        formData.seconds = this.getAudioDuration(file) || 0;
      }
    }

    // معالجة المحتوى المركب للموقع وجهة الاتصال
    if (messageType === 5 || messageType === 6) {
      try {
        // التحقق من صحة JSON
        const parsedContent = JSON.parse(content);

        if (messageType === 6) {
          // التحقق من وجود الحقول المطلوبة للموقع
          if (!parsedContent.latitude || !parsedContent.longitude) {
            throw new Error("يجب توفير latitude و longitude للموقع");
          }
          formData.location = parsedContent;
        } else if (messageType === 5) {
          // التحقق من وجود الحقول المطلوبة لجهة الاتصال
          if (!parsedContent.name || !parsedContent.phoneNumber) {
            throw new Error("يجب توفير name و phoneNumber لجهة الاتصال");
          }
          formData.contactCard = parsedContent;
        }
      } catch (error) {
        throw new Error(`خطأ في تحليل بيانات JSON: ${error.message}`);
      }
    }

    // معالجة بيانات الاستطلاع
    if (messageType === 7) {
      try {
        const parsedContent = JSON.parse(content);

        if (!parsedContent.question && !parsedContent.title) {
          throw new Error("يجب توفير عنوان أو سؤال للاستطلاع");
        }

        if (
          !parsedContent.options ||
          !Array.isArray(parsedContent.options) ||
          parsedContent.options.length < 2
        ) {
          throw new Error("يجب توفير خيارين على الأقل للاستطلاع");
        }

        formData.poll = {
          title: parsedContent.question || parsedContent.title,
          expirationDate:
            parsedContent.expirationDate ||
            new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          pollType: parsedContent.pollType || 0,
          options: parsedContent.options.map((option) => ({
            text: typeof option === "string" ? option : option.text,
          })),
        };
      } catch (error) {
        throw new Error(`خطأ في تحليل بيانات الاستطلاع: ${error.message}`);
      }
    }

    return formData;
  }

  /**
   * استخراج مدة الملف الصوتي (إذا أمكن)
   */
  getAudioDuration(file) {
    return new Promise((resolve) => {
      if (!file.type.startsWith("audio/") && !file.type.startsWith("video/")) {
        resolve(0);
        return;
      }

      const audio = document.createElement("audio");
      const url = URL.createObjectURL(file);

      audio.addEventListener("loadedmetadata", () => {
        URL.revokeObjectURL(url);
        resolve(Math.round(audio.duration) || 0);
      });

      audio.addEventListener("error", () => {
        URL.revokeObjectURL(url);
        resolve(0);
      });

      audio.src = url;
    });
  }

  /**
   * التحقق من صحة النموذج
   */
  validateForm(formData) {
    if (!formData.title) {
      alert("يرجى إدخال عنوان للرد السريع");
      this.elements.quickReplyTitleInput?.focus();
      return false;
    }

    if (isNaN(formData.messageType)) {
      alert("يرجى اختيار نوع الرسالة");
      this.elements.quickReplyTypeSelect?.focus();
      return false;
    }

    // التحقق من المحتوى حسب نوع الرسالة
    switch (formData.messageType) {
      case 0: // نص
        if (!formData.content) {
          alert("يرجى إدخال نص الرسالة");
          this.elements.quickReplyContentInput?.focus();
          return false;
        }
        break;

      case 1: // صورة
      case 2: // فيديو
      case 3: // صوت
      case 4: // مستند
      case 8: // تسجيل صوتي
      case 9: // ملفات أخرى
        if (!formData.fileName || !formData.fileData) {
          alert("يرجى اختيار ملف لهذا النوع من الرسائل");
          this.elements.quickReplyFileInput?.focus();
          return false;
        }
        break;

      case 5: // جهة اتصال
        if (!formData.contactCard) {
          alert("يرجى إدخال بيانات جهة الاتصال بتنسيق JSON صحيح");
          this.elements.quickReplyContentInput?.focus();
          return false;
        }
        break;

      case 6: // موقع
        if (!formData.location) {
          alert("يرجى إدخال بيانات الموقع بتنسيق JSON صحيح");
          this.elements.quickReplyContentInput?.focus();
          return false;
        }
        break;

      case 7: // استطلاع
        if (!formData.poll) {
          alert("يرجى إدخال بيانات الاستطلاع بتنسيق JSON صحيح");
          this.elements.quickReplyContentInput?.focus();
          return false;
        }
        break;
    }

    return true;
  }

  /**
   * مسح النموذج
   */
  clearForm() {
    if (this.elements.quickReplyTitleInput) {
      this.elements.quickReplyTitleInput.value = "";
    }
    if (this.elements.quickReplyTypeSelect) {
      this.elements.quickReplyTypeSelect.value = "0";
    }
    if (this.elements.quickReplyContentInput) {
      this.elements.quickReplyContentInput.value = "";
    }
    if (this.elements.quickReplyFileInput) {
      this.elements.quickReplyFileInput.value = "";
    }
    if (this.elements.quickReplyFilePreview) {
      this.elements.quickReplyFilePreview.style.display = "none";
    }
  }

  /**
   * ملء النموذج بالبيانات
   */
  fillForm(quickReply) {
    if (this.elements.quickReplyTitleInput) {
      this.elements.quickReplyTitleInput.value = quickReply.title || "";
    }
    if (this.elements.quickReplyTypeSelect) {
      this.elements.quickReplyTypeSelect.value = quickReply.messageType || 0;
    }
    if (this.elements.quickReplyContentInput) {
      this.elements.quickReplyContentInput.value = quickReply.content || "";
    }

    // معالجة نوع الرسالة
    this.handleMessageTypeChange(quickReply.messageType);
  }

  /**
   * معالجة تغيير نوع الرسالة
   */
  handleMessageTypeChange(messageType) {
    const contentGroup = document.getElementById("quickReplyContentGroup");
    const fileGroup = document.getElementById("quickReplyFileGroup");

    console.log("messageType:", messageType);
    if (!contentGroup || !fileGroup) return;

    // إظهار/إخفاء الحقول حسب نوع الرسالة
    switch (messageType) {
      case 0: // نص
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder = "أدخل نص الرسالة";
        this.hideHelpText();
        break;

      case 1: // صورة
      case 2: // فيديو
      case 3: // صوت
      case 4: // مستند
      case 9: // ملفات أخرى
        contentGroup.style.display = "block";
        fileGroup.style.display = "block";
        this.elements.quickReplyContentInput.placeholder =
          "أدخل تعليق (اختياري)";
        this.hideHelpText();
        break;

      case 5: // جهة اتصال
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder =
          "أدخل بيانات جهة الاتصال بتنسيق JSON";
        this.showHelpText(
          '{\n  "name": "الاسم",\n  "phoneNumber": "+123456789"\n}'
        );
        break;

      case 6: // موقع
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder =
          "أدخل بيانات الموقع بتنسيق JSON";
        this.showHelpText(
          '{\n  "latitude": 24.7136,\n  "longitude": 46.6753,\n  "name": "اسم المكان",\n  "address": "عنوان المكان"\n}'
        );
        break;

      case 7: // استطلاع
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder =
          "أدخل بيانات الاستطلاع بتنسيق JSON";
        this.showHelpText(
          '{\n  "question": "ما هو لونك المفضل؟",\n  "options": ["أحمر", "أزرق", "أخضر"],\n  "pollType": 0\n}'
        );
        break;

      case 8: // تسجيل صوتي
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder =
          "أدخل وصف للرسالة الصوتية";
        this.hideHelpText();
        break;
    }
  }

  /**
   * معالجة اختيار الملف
   */
  async handleFileSelection(file) {
    if (!file) return;

    try {
      // عرض معاينة الملف
      if (this.elements.quickReplyFilePreview) {
        const fileInfo = `
          <div class="file-info">
            <i class="fe fe-file"></i>
            <span>${file.name}</span>
            <small>${this.quickRepliesService.formatFileSize(file.size)}</small>
          </div>
        `;
        this.elements.quickReplyFilePreview.innerHTML = fileInfo;
        this.elements.quickReplyFilePreview.style.display = "block";
      }
    } catch (error) {
      console.error("خطأ في معالجة الملف:", error);
    }
  }

  /**
   * عرض نص مساعدة للأنواع المعقدة
   */
  showHelpText(exampleText) {
    // إنشاء عنصر المساعدة إذا لم يكن موجوداً
    let helpElement = document.getElementById("contentHelp");
    if (!helpElement) {
      helpElement = document.createElement("div");
      helpElement.id = "contentHelp";
      helpElement.className = "content-help";

      const contentGroup = document.getElementById("quickReplyContentGroup");
      if (contentGroup) {
        contentGroup.appendChild(helpElement);
      }
    }

    helpElement.innerHTML = `
      <div class="help-text">
        <strong>مثال على التنسيق:</strong>
        <pre>${exampleText}</pre>
      </div>
    `;

    helpElement.style.display = "block";
  }

  /**
   * إخفاء نص المساعدة
   */
  hideHelpText() {
    const helpElement = document.getElementById("contentHelp");
    if (helpElement) {
      helpElement.style.display = "none";
    }
  }

  /**
   * تعيين حالة التحميل
   */
  setLoading(loading) {
    this.isLoading = loading;

    if (this.elements.createQuickReplyBtnText) {
      this.elements.createQuickReplyBtnText.style.display = loading
        ? "none"
        : "block";
    }

    if (this.elements.createQuickReplyBtnLoader) {
      this.elements.createQuickReplyBtnLoader.style.display = loading
        ? "block"
        : "none";
    }

    if (this.elements.createQuickReplyConfirmBtn) {
      this.elements.createQuickReplyConfirmBtn.disabled = loading;
    }
  }
}
