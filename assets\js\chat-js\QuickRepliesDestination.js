/**
 * فئة إدارة واجهة الردود السريعة - تتبع نمط GroupDestination
 */
class QuickRepliesDestination {
  constructor(quickRepliesService) {
    this.quickRepliesService = quickRepliesService;
    this.currentQuickReplies = [];
    this.filteredQuickReplies = [];
    this.currentEditingId = null;
    this.currentStep = "list"; // 'list', 'create', 'edit'
    this.isLoading = false;

    this.elements = {
      // الشاشات
      mainScreen: document.getElementById("leftSid"),
      quickRepliesListScreen: document.getElementById("QuickReplyList"),
      createQuickReplyScreen: document.getElementById(
        "createQuickReplyDetails"
      ),

      // أزرار التنقل والتحكم
      closeQuickRepliesBtn: document.querySelector(
        "#QuickReplyList .header-Chat .icons"
      ),
      backToListBtn: document.getElementById("backToQuickRepliesListBtn"),
      createQuickReplyConfirmBtn: document.getElementById(
        "createQuickReplyConfirmBtn"
      ),
      createQuickReplyBtnText: document.querySelector(
        "#createQuickReplyConfirmBtn .btn-text"
      ),
      createQuickReplyBtnLoader: document.querySelector(
        "#createQuickReplyConfirmBtn .loader"
      ),

      // عناصر شاشة قائمة الردود السريعة
      quickRepliesContainer: document.querySelector("#QuickReply-elementList"),
      quickRepliesSearchInput: document.querySelector("#QuickReply-search"),
      addQuickReplyBtn: null, // سيتم إنشاؤه ديناميكيًا

      // عناصر شاشة إنشاء/تعديل الرد السريع
      quickReplyTitleInput: document.getElementById("quickReplyTitleInput"),
      quickReplyTypeSelect: document.getElementById("quickReplyTypeSelect"),
      quickReplyContentInput: document.getElementById("quickReplyContentInput"),
      quickReplyFileInput: document.getElementById("quickReplyFileInput"),
      quickReplyFilePreview: document.getElementById("quickReplyFilePreview"),
    };

    if (this.elements.quickRepliesListScreen) {
      this.initializeEventListeners();
      this.createAddButton();
    }

    // Initialize slash commands for inline dropdown
    this.initializeSlashCommands();
  }

  /**
   * تهيئة مستمعي الأحداث
   */
  initializeEventListeners() {
    // زر إغلاق قائمة الردود السريعة
    if (this.elements.closeQuickRepliesBtn) {
      this.elements.closeQuickRepliesBtn.addEventListener("click", () => {
        this.closeQuickRepliesList();
      });
    }

    // زر الرجوع من شاشة الإنشاء
    if (this.elements.backToListBtn) {
      this.elements.backToListBtn.addEventListener("click", () => {
        this.showQuickRepliesList();
      });
    }

    // زر تأكيد إنشاء الرد السريع
    if (this.elements.createQuickReplyConfirmBtn) {
      this.elements.createQuickReplyConfirmBtn.addEventListener("click", () => {
        this.handleCreateQuickReply();
      });
    }

    // البحث في الردود السريعة
    if (this.elements.quickRepliesSearchInput) {
      this.elements.quickRepliesSearchInput.addEventListener("input", (e) => {
        this.handleSearch(e.target.value);
      });
    }

    // تغيير نوع الرسالة
    if (this.elements.quickReplyTypeSelect) {
      this.elements.quickReplyTypeSelect.addEventListener("change", (e) => {
        this.handleMessageTypeChange(parseInt(e.target.value));
      });
    }

    // اختيار ملف
    if (this.elements.quickReplyFileInput) {
      this.elements.quickReplyFileInput.addEventListener("change", (e) => {
        this.handleFileSelection(e.target.files[0]);
      });
    }
  }

  /**
   * إنشاء زر إضافة رد سريع جديد
   */
  createAddButton() {
    if (!this.elements.quickRepliesContainer) return;

    const addButtonHtml = `
      <div class="add-quick-reply-btn" onclick="quickRepliesDestination.showCreateQuickReply()">
        <div class="add-btn-content">
          <div class="add-btn-icon">
            <i class="fe fe-plus"></i>
          </div>
          <div class="add-btn-text">
            <h4>إضافة رد سريع جديد</h4>
            <p>إنشاء رد سريع لاستخدامه في المحادثات</p>
          </div>
        </div>
      </div>
    `;

    this.elements.quickRepliesContainer.insertAdjacentHTML(
      "afterbegin",
      addButtonHtml
    );
  }

  /**
   * عرض قائمة الردود السريعة
   */
  async showQuickRepliesList() {
    try {
      this.isLoading = true;
      this.currentStep = "list";

      // إخفاء الشاشات الأخرى
      if (this.elements.createQuickReplyScreen) {
        this.elements.createQuickReplyScreen.style.display = "none";
      }

      // عرض شاشة القائمة
      if (this.elements.quickRepliesListScreen) {
        this.elements.quickRepliesListScreen.style.display = "block";
      }

      // تحميل الردود السريعة
      this.currentQuickReplies =
        await this.quickRepliesService.loadQuickReplies();
      this.filteredQuickReplies = [...this.currentQuickReplies];

      // عرض الردود السريعة
      this.renderQuickRepliesList();
    } catch (error) {
      console.error("خطأ في عرض قائمة الردود السريعة:", error);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * عرض شاشة إنشاء رد سريع جديد
   */
  showCreateQuickReply() {
    this.currentStep = "create";
    this.currentEditingId = null;

    // إخفاء شاشة القائمة
    if (this.elements.quickRepliesListScreen) {
      this.elements.quickRepliesListScreen.style.display = "none";
    }

    // عرض شاشة الإنشاء
    if (this.elements.createQuickReplyScreen) {
      this.elements.createQuickReplyScreen.style.display = "block";
    }

    // مسح النموذج
    this.clearForm();

    // تحديث عنوان الشاشة
    const titleElement =
      this.elements.createQuickReplyScreen.querySelector(".newText h2");
    if (titleElement) {
      titleElement.textContent = "إنشاء رد سريع جديد";
    }

    // تحديث نص الزر
    if (this.elements.createQuickReplyBtnText) {
      this.elements.createQuickReplyBtnText.textContent = "إنشاء الرد السريع";
    }
  }

  /**
   * عرض شاشة تعديل رد سريع
   */
  showEditQuickReply(quickReplyId) {
    const quickReply = this.quickRepliesService.getQuickReplyById(quickReplyId);
    if (!quickReply) {
      console.error("الرد السريع غير موجود:", quickReplyId);
      return;
    }

    this.currentStep = "edit";
    this.currentEditingId = quickReplyId;

    // إخفاء شاشة القائمة
    if (this.elements.quickRepliesListScreen) {
      this.elements.quickRepliesListScreen.style.display = "none";
    }

    // عرض شاشة التعديل
    if (this.elements.createQuickReplyScreen) {
      this.elements.createQuickReplyScreen.style.display = "block";
    }

    // ملء النموذج بالبيانات الحالية
    this.fillForm(quickReply);

    // تحديث عنوان الشاشة
    const titleElement =
      this.elements.createQuickReplyScreen.querySelector(".newText h2");
    if (titleElement) {
      titleElement.textContent = "تعديل الرد السريع";
    }

    // تحديث نص الزر
    if (this.elements.createQuickReplyBtnText) {
      this.elements.createQuickReplyBtnText.textContent = "حفظ التغييرات";
    }
  }

  /**
   * إغلاق قائمة الردود السريعة
   */
  closeQuickRepliesList() {
    if (this.elements.quickRepliesListScreen) {
      this.elements.quickRepliesListScreen.style.display = "none";
    }
    if (this.elements.createQuickReplyScreen) {
      this.elements.createQuickReplyScreen.style.display = "none";
    }
    if (this.elements.mainScreen) {
      this.elements.mainScreen.style.display = "block";
    }
  }

  /**
   * عرض الردود السريعة في القائمة
   */
  renderQuickRepliesList() {
    if (!this.elements.quickRepliesContainer) return;

    // الاحتفاظ بزر الإضافة
    const addButton = this.elements.quickRepliesContainer.querySelector(
      ".add-quick-reply-btn"
    );

    if (this.filteredQuickReplies.length === 0) {
      this.elements.quickRepliesContainer.innerHTML = "";
      if (addButton) {
        this.elements.quickRepliesContainer.appendChild(addButton);
      }

      const emptyStateHtml = `
        <div class="empty-state">
          <div class="empty-icon">
            <i class="fe fe-message-circle"></i>
          </div>
          <h3>لا توجد ردود سريعة</h3>
          <p>قم بإنشاء أول رد سريع لك</p>
        </div>
      `;
      this.elements.quickRepliesContainer.insertAdjacentHTML(
        "beforeend",
        emptyStateHtml
      );
      return;
    }

    const quickRepliesHtml = this.filteredQuickReplies
      .map((reply) => {
        const typeInfo = this.quickRepliesService.getMessageTypeInfo(
          reply.messageType
        );
        const previewText = this.quickRepliesService.getPreviewText(reply);

        return `
        <div class="block quick-reply-item" data-id="${reply.id}">
          <div class="imgBox quick-reply-type-icon" style="background-color: ${typeInfo.color}">
            <i class="fe fe-${typeInfo.icon}"></i>
          </div>
          <div class="h-text">
            <div class="head">
              <h4 title="${reply.title}" aria-label="${reply.title}">
                ${reply.title}
              </h4>
              <div class="quick-reply-actions">
                <button class="action-btn edit-btn" onclick="quickRepliesDestination.showEditQuickReply('${reply.id}')" title="تعديل">
                  <i class="fe fe-edit-2"></i>
                </button>
                <button class="action-btn delete-btn" onclick="quickRepliesDestination.deleteQuickReply('${reply.id}')" title="حذف">
                  <i class="fe fe-trash-2"></i>
                </button>
              </div>
            </div>
            <div class="message">
              <p title="${previewText}" aria-label="${previewText}">
                ${previewText}
              </p>
            </div>
          </div>
        </div>
      `;
      })
      .join("");

    this.elements.quickRepliesContainer.innerHTML = "";
    if (addButton) {
      this.elements.quickRepliesContainer.appendChild(addButton);
    }
    this.elements.quickRepliesContainer.insertAdjacentHTML(
      "beforeend",
      quickRepliesHtml
    );
  }

  /**
   * معالجة البحث
   */
  handleSearch(searchTerm) {
    this.filteredQuickReplies =
      this.quickRepliesService.searchQuickReplies(searchTerm);
    this.renderQuickRepliesList();
  }

  /**
   * معالجة إنشاء/تحديث الرد السريع
   */
  async handleCreateQuickReply() {
    try {
      if (this.isLoading) return;

      const formData = await this.getFormData();
      if (!this.validateForm(formData)) {
        return;
      }

      this.setLoading(true);

      if (this.currentStep === "create") {
        await this.quickRepliesService.createQuickReply(formData);
      } else if (this.currentStep === "edit") {
        await this.quickRepliesService.updateQuickReply(
          this.currentEditingId,
          formData
        );
      }

      // العودة إلى قائمة الردود السريعة
      await this.showQuickRepliesList();
    } catch (error) {
      console.error("خطأ في حفظ الرد السريع:", error);
      alert("حدث خطأ أثناء حفظ الرد السريع. يرجى المحاولة مرة أخرى.");
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * حذف رد سريع
   */
  async deleteQuickReply(quickReplyId) {
    if (!confirm("هل أنت متأكد من حذف هذا الرد السريع؟")) {
      return;
    }

    try {
      await this.quickRepliesService.deleteQuickReply(quickReplyId);
      await this.showQuickRepliesList();
    } catch (error) {
      console.error("خطأ في حذف الرد السريع:", error);
      alert("حدث خطأ أثناء حذف الرد السريع. يرجى المحاولة مرة أخرى.");
    }
  }

  /**
   * الحصول على بيانات النموذج
   */
  async getFormData() {
    const title = this.elements.quickReplyTitleInput?.value?.trim() || "";
    const messageType =
      parseInt(this.elements.quickReplyTypeSelect?.value) || 0;
    const content = this.elements.quickReplyContentInput?.value?.trim() || "";
    const file = this.elements.quickReplyFileInput?.files[0];

    const formData = {
      title,
      messageType,
      content,
    };

    // معالجة الملفات للأنواع المختلفة
    if (file && [1, 2, 3, 4, 8, 9].includes(messageType)) {
      formData.fileName = file.name;
      formData.fileSize = file.size;
      formData.mimeType = file.type;
      // تحويل الملف إلى Base64
      formData.fileData = await this.quickRepliesService.fileToBase64(file);

      // إضافة مدة التسجيل للملفات الصوتية
      if (messageType === 3 || messageType === 8) {
        formData.seconds = this.getAudioDuration(file) || 0;
      }
    }

    // معالجة المحتوى المركب للموقع وجهة الاتصال
    if (messageType === 5 || messageType === 6) {
      try {
        // التحقق من صحة JSON
        const parsedContent = JSON.parse(content);

        if (messageType === 6) {
          // التحقق من وجود الحقول المطلوبة للموقع
          if (!parsedContent.latitude || !parsedContent.longitude) {
            throw new Error("يجب توفير latitude و longitude للموقع");
          }
          formData.location = parsedContent;
        } else if (messageType === 5) {
          // التحقق من وجود الحقول المطلوبة لجهة الاتصال
          if (!parsedContent.name || !parsedContent.phoneNumber) {
            throw new Error("يجب توفير name و phoneNumber لجهة الاتصال");
          }
          formData.contactCard = parsedContent;
        }
      } catch (error) {
        throw new Error(`خطأ في تحليل بيانات JSON: ${error.message}`);
      }
    }

    // معالجة بيانات الاستطلاع
    if (messageType === 7) {
      try {
        const parsedContent = JSON.parse(content);

        if (!parsedContent.question && !parsedContent.title) {
          throw new Error("يجب توفير عنوان أو سؤال للاستطلاع");
        }

        if (
          !parsedContent.options ||
          !Array.isArray(parsedContent.options) ||
          parsedContent.options.length < 2
        ) {
          throw new Error("يجب توفير خيارين على الأقل للاستطلاع");
        }

        formData.poll = {
          title: parsedContent.question || parsedContent.title,
          expirationDate:
            parsedContent.expirationDate ||
            new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          pollType: parsedContent.pollType || 0,
          options: parsedContent.options.map((option) => ({
            text: typeof option === "string" ? option : option.text,
          })),
        };
      } catch (error) {
        throw new Error(`خطأ في تحليل بيانات الاستطلاع: ${error.message}`);
      }
    }

    return formData;
  }

  /**
   * استخراج مدة الملف الصوتي (إذا أمكن)
   */
  getAudioDuration(file) {
    return new Promise((resolve) => {
      if (!file.type.startsWith("audio/") && !file.type.startsWith("video/")) {
        resolve(0);
        return;
      }

      const audio = document.createElement("audio");
      const url = URL.createObjectURL(file);

      audio.addEventListener("loadedmetadata", () => {
        URL.revokeObjectURL(url);
        resolve(Math.round(audio.duration) || 0);
      });

      audio.addEventListener("error", () => {
        URL.revokeObjectURL(url);
        resolve(0);
      });

      audio.src = url;
    });
  }

  /**
   * التحقق من صحة النموذج
   */
  validateForm(formData) {
    if (!formData.title) {
      alert("يرجى إدخال عنوان للرد السريع");
      this.elements.quickReplyTitleInput?.focus();
      return false;
    }

    if (isNaN(formData.messageType)) {
      alert("يرجى اختيار نوع الرسالة");
      this.elements.quickReplyTypeSelect?.focus();
      return false;
    }

    // التحقق من المحتوى حسب نوع الرسالة
    switch (formData.messageType) {
      case 0: // نص
        if (!formData.content) {
          alert("يرجى إدخال نص الرسالة");
          this.elements.quickReplyContentInput?.focus();
          return false;
        }
        break;

      case 1: // صورة
      case 2: // فيديو
      case 3: // صوت
      case 4: // مستند
      case 8: // تسجيل صوتي
      case 9: // ملفات أخرى
        if (!formData.fileName || !formData.fileData) {
          alert("يرجى اختيار ملف لهذا النوع من الرسائل");
          this.elements.quickReplyFileInput?.focus();
          return false;
        }
        break;

      case 5: // جهة اتصال
        if (!formData.contactCard) {
          alert("يرجى إدخال بيانات جهة الاتصال بتنسيق JSON صحيح");
          this.elements.quickReplyContentInput?.focus();
          return false;
        }
        break;

      case 6: // موقع
        if (!formData.location) {
          alert("يرجى إدخال بيانات الموقع بتنسيق JSON صحيح");
          this.elements.quickReplyContentInput?.focus();
          return false;
        }
        break;

      case 7: // استطلاع
        if (!formData.poll) {
          alert("يرجى إدخال بيانات الاستطلاع بتنسيق JSON صحيح");
          this.elements.quickReplyContentInput?.focus();
          return false;
        }
        break;
    }

    return true;
  }

  /**
   * مسح النموذج
   */
  clearForm() {
    if (this.elements.quickReplyTitleInput) {
      this.elements.quickReplyTitleInput.value = "";
    }
    if (this.elements.quickReplyTypeSelect) {
      this.elements.quickReplyTypeSelect.value = "0";
    }
    if (this.elements.quickReplyContentInput) {
      this.elements.quickReplyContentInput.value = "";
    }
    if (this.elements.quickReplyFileInput) {
      this.elements.quickReplyFileInput.value = "";
    }
    if (this.elements.quickReplyFilePreview) {
      this.elements.quickReplyFilePreview.style.display = "none";
    }
  }

  /**
   * ملء النموذج بالبيانات
   */
  fillForm(quickReply) {
    if (this.elements.quickReplyTitleInput) {
      this.elements.quickReplyTitleInput.value = quickReply.title || "";
    }
    if (this.elements.quickReplyTypeSelect) {
      this.elements.quickReplyTypeSelect.value = quickReply.messageType || 0;
    }
    if (this.elements.quickReplyContentInput) {
      this.elements.quickReplyContentInput.value = quickReply.content || "";
    }

    // معالجة نوع الرسالة
    this.handleMessageTypeChange(quickReply.messageType);
  }

  /**
   * معالجة تغيير نوع الرسالة
   */
  handleMessageTypeChange(messageType) {
    const contentGroup = document.getElementById("quickReplyContentGroup");
    const fileGroup = document.getElementById("quickReplyFileGroup");

    if (!contentGroup || !fileGroup) return;

    // إظهار/إخفاء الحقول حسب نوع الرسالة
    switch (messageType) {
      case 0: // نص
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder = "أدخل نص الرسالة";
        this.hideHelpText();
        break;

      case 1: // صورة
      case 2: // فيديو
      case 3: // صوت
      case 4: // مستند
      case 9: // ملفات أخرى
        contentGroup.style.display = "block";
        fileGroup.style.display = "block";
        this.elements.quickReplyContentInput.placeholder =
          "أدخل تعليق (اختياري)";
        this.hideHelpText();
        break;

      case 5: // جهة اتصال
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder =
          "أدخل بيانات جهة الاتصال بتنسيق JSON";
        this.showHelpText(
          '{\n  "name": "الاسم",\n  "phoneNumber": "+123456789"\n}'
        );
        break;

      case 6: // موقع
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder =
          "أدخل بيانات الموقع بتنسيق JSON";
        this.showHelpText(
          '{\n  "latitude": 24.7136,\n  "longitude": 46.6753,\n  "name": "اسم المكان",\n  "address": "عنوان المكان"\n}'
        );
        break;

      case 7: // استطلاع
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder =
          "أدخل بيانات الاستطلاع بتنسيق JSON";
        this.showHelpText(
          '{\n  "question": "ما هو لونك المفضل؟",\n  "options": ["أحمر", "أزرق", "أخضر"],\n  "pollType": 0\n}'
        );
        break;

      case 8: // تسجيل صوتي
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        this.elements.quickReplyContentInput.placeholder =
          "أدخل وصف للرسالة الصوتية";
        this.hideHelpText();
        break;
    }
  }

  /**
   * معالجة اختيار الملف
   */
  async handleFileSelection(file) {
    if (!file) return;

    try {
      // عرض معاينة الملف
      if (this.elements.quickReplyFilePreview) {
        const fileInfo = `
          <div class="file-info">
            <i class="fe fe-file"></i>
            <span>${file.name}</span>
            <small>${this.quickRepliesService.formatFileSize(file.size)}</small>
          </div>
        `;
        this.elements.quickReplyFilePreview.innerHTML = fileInfo;
        this.elements.quickReplyFilePreview.style.display = "block";
      }
    } catch (error) {
      console.error("خطأ في معالجة الملف:", error);
    }
  }

  /**
   * عرض نص مساعدة للأنواع المعقدة
   */
  showHelpText(exampleText) {
    // إنشاء عنصر المساعدة إذا لم يكن موجوداً
    let helpElement = document.getElementById("contentHelp");
    if (!helpElement) {
      helpElement = document.createElement("div");
      helpElement.id = "contentHelp";
      helpElement.className = "content-help";

      const contentGroup = document.getElementById("quickReplyContentGroup");
      if (contentGroup) {
        contentGroup.appendChild(helpElement);
      }
    }

    helpElement.innerHTML = `
      <div class="help-text">
        <strong>مثال على التنسيق:</strong>
        <pre>${exampleText}</pre>
      </div>
    `;

    helpElement.style.display = "block";
  }

  /**
   * إخفاء نص المساعدة
   */
  hideHelpText() {
    const helpElement = document.getElementById("contentHelp");
    if (helpElement) {
      helpElement.style.display = "none";
    }
  }

  /**
   * تعيين حالة التحميل
   */
  setLoading(loading) {
    this.isLoading = loading;

    if (this.elements.createQuickReplyBtnText) {
      this.elements.createQuickReplyBtnText.style.display = loading
        ? "none"
        : "block";
    }

    if (this.elements.createQuickReplyBtnLoader) {
      this.elements.createQuickReplyBtnLoader.style.display = loading
        ? "block"
        : "none";
    }

    if (this.elements.createQuickReplyConfirmBtn) {
      this.elements.createQuickReplyConfirmBtn.disabled = loading;
    }
  }

  // ==================== SLASH COMMANDS FUNCTIONALITY ====================

  /**
   * إنشاء قائمة الردود السريعة المنسدلة للبحث بـ "/"
   */
  createQuickRepliesDropdown() {
    const chatInputWrapper = document.querySelector(".chat-input-wrapper");
    if (!chatInputWrapper) return;

    // إزالة القائمة الموجودة إذا كانت موجودة
    const existingDropdown = document.getElementById("quick-replies-dropdown");
    if (existingDropdown) {
      existingDropdown.remove();
    }

    const dropdown = document.createElement("div");
    dropdown.id = "quick-replies-dropdown";
    dropdown.className = "quick-replies-dropdown";
    dropdown.innerHTML = `
      <div class="quick-replies-header">
        <h3 class="quick-replies-title">الردود السريعة</h3>
        <button class="quick-replies-manage-btn" id="manage-quick-replies" onclick="showQuickRepliesManagement()">
          إدارة
        </button>
      </div>
      <div class="quick-replies-search">
        <input type="text" placeholder="البحث عن الردود السريعة..." id="quick-replies-search">
      </div>
      <div class="quick-replies-list" id="quick-replies-list">
        <!-- Quick replies will be populated here -->
      </div>
    `;

    chatInputWrapper.appendChild(dropdown);
  }

  /**
   * تهيئة أوامر الشرطة المائلة
   */
  initializeSlashCommands() {
    const messageInput = document.getElementById("input-send-message");
    if (!messageInput) return;

    // إنشاء القائمة المنسدلة
    this.createQuickRepliesDropdown();

    // الاستماع لتغييرات الإدخال
    messageInput.addEventListener("input", (e) => {
      this.handleInputChange(e.target.value);
    });

    // الاستماع لأحداث لوحة المفاتيح
    messageInput.addEventListener("keydown", (e) => {
      this.handleKeyDown(e);
    });

    // الاستماع لأحداث التركيز/عدم التركيز
    messageInput.addEventListener("blur", () => {
      // إخفاء القائمة بعد تأخير قصير للسماح بالنقرات
      setTimeout(() => {
        this.hideInlineDropdown();
      }, 200);
    });
  }

  /**
   * معالجة تغييرات الإدخال لأوامر الشرطة المائلة
   */
  handleInputChange(value) {
    if (value.startsWith("/")) {
      const searchTerm = value.substring(1); // إزالة الشرطة المائلة
      this.showInlineDropdown(searchTerm);
    } else {
      this.hideInlineDropdown();
    }
  }

  /**
   * معالجة التنقل بلوحة المفاتيح
   */
  handleKeyDown(e) {
    const dropdown = document.getElementById("quick-replies-dropdown");
    if (!dropdown || dropdown.style.display === "none") return;

    const items = dropdown.querySelectorAll(".quick-reply-item");
    if (items.length === 0) return;

    let currentIndex = -1;
    items.forEach((item, index) => {
      if (item.classList.contains("selected")) {
        currentIndex = index;
      }
    });

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        this.selectNextItem(items, currentIndex);
        break;
      case "ArrowUp":
        e.preventDefault();
        this.selectPreviousItem(items, currentIndex);
        break;
      case "Enter":
        e.preventDefault();
        if (currentIndex >= 0 && items[currentIndex]) {
          const replyId = items[currentIndex].getAttribute("data-id");
          this.useQuickReplyFromSlash(replyId);
        }
        break;
      case "Escape":
        e.preventDefault();
        this.hideInlineDropdown();
        break;
    }
  }

  /**
   * اختيار العنصر التالي في القائمة
   */
  selectNextItem(items, currentIndex) {
    items.forEach((item) => item.classList.remove("selected"));
    const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
    items[nextIndex].classList.add("selected");
    items[nextIndex].scrollIntoView({ block: "nearest" });
  }

  /**
   * اختيار العنصر السابق في القائمة
   */
  selectPreviousItem(items, currentIndex) {
    items.forEach((item) => item.classList.remove("selected"));
    const prevIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
    items[prevIndex].classList.add("selected");
    items[prevIndex].scrollIntoView({ block: "nearest" });
  }

  /**
   * عرض القائمة المنسدلة المضمنة
   */
  async showInlineDropdown(searchTerm = "") {
    const dropdown = document.getElementById("quick-replies-dropdown");
    if (!dropdown) return;

    await this.populateInlineQuickRepliesList(searchTerm);
    dropdown.style.display = "block";
  }

  /**
   * إخفاء القائمة المنسدلة المضمنة
   */
  hideInlineDropdown() {
    const dropdown = document.getElementById("quick-replies-dropdown");
    if (dropdown) {
      dropdown.style.display = "none";
    }
  }

  /**
   * ملء قائمة الردود السريعة المضمنة
   */
  async populateInlineQuickRepliesList(searchTerm = "") {
    const listContainer = document.getElementById("quick-replies-list");
    if (!listContainer) return;

    // تحميل الردود السريعة إذا لم تكن محملة
    if (!this.currentQuickReplies || this.currentQuickReplies.length === 0) {
      this.currentQuickReplies =
        await this.quickRepliesService.loadQuickReplies();
    }

    const filteredReplies = this.currentQuickReplies.filter((reply) => {
      const titleMatch = reply.title
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      const contentMatch = reply.content
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      return titleMatch || contentMatch;
    });

    if (filteredReplies.length === 0) {
      listContainer.innerHTML = `
        <div class="quick-replies-empty" style="padding: 16px; text-align: center; color: var(--primary);">
          <div>لم يتم العثور على ردود سريعة</div>
          <div style="font-size: 12px; margin-top: 4px;">جرب مصطلح بحث مختلف</div>
        </div>
      `;
      return;
    }

    listContainer.innerHTML = filteredReplies
      .map(
        (reply, index) => `
      <div class="quick-reply-item ${index === 0 ? "selected" : ""}" data-id="${
          reply.id
        }" onclick="quickRepliesDestination.useQuickReplyFromSlash('${
          reply.id
        }')">
        <div class="quick-reply-icon ${this.quickRepliesService.getMessageTypeClass(
          reply.messageType
        )}">
          ${this.quickRepliesService.getMessageTypeIcon(reply.messageType)}
        </div>
        <div class="quick-reply-content">
          <div class="quick-reply-title">${reply.title}</div>
          <div class="quick-reply-preview">${this.quickRepliesService.getPreviewText(
            reply
          )}</div>
        </div>
      </div>
    `
      )
      .join("");
  }

  /**
   * استخدام الرد السريع من أمر الشرطة المائلة
   */
  async useQuickReplyFromSlash(replyId) {
    const messageInput = document.getElementById("input-send-message");
    if (!messageInput) return;

    // مسح الإدخال أولاً
    messageInput.value = "";

    // استخدام الرد السريع
    await this.useQuickReply(replyId);

    // إخفاء القائمة المنسدلة
    this.hideInlineDropdown();

    // التركيز مرة أخرى على الإدخال
    messageInput.focus();
  }

  /**
   * استخدام الرد السريع
   */
  async useQuickReply(replyId) {
    try {
      const reply = this.currentQuickReplies.find((r) => r.id === replyId);
      if (!reply) return;

      const messageInput = document.getElementById("input-send-message");
      if (!messageInput) return;

      // معالجة أنواع الرسائل المختلفة
      switch (reply.messageType) {
        case 0: // نص
          messageInput.value = reply.content;
          messageInput.focus();
          // إرسال الرسالة النصية تلقائياً
          await this.sendTextMessage(reply.content);
          // مسح الإدخال بعد الإرسال
          messageInput.value = "";
          break;

        case 1: // صورة
        case 2: // فيديو
        case 3: // صوت
        case 4: // مستند
        case 9: // ملفات أخرى
          await this.handleFileQuickReply(reply);
          break;

        case 5: // جهة اتصال
          await this.handleContactQuickReply(reply);
          break;

        case 6: // موقع
          await this.handleLocationQuickReply(reply);
          break;

        case 7: // استطلاع
          await this.handlePollQuickReply(reply);
          break;

        case 8: // تسجيل صوتي
          // الرسائل الصوتية عادة ما يتم تسجيلها مباشرة
          messageInput.value = reply.content || "قالب رسالة صوتية";
          messageInput.focus();
          break;
      }
    } catch (error) {
      console.error("خطأ في استخدام الرد السريع:", error);
    }
  }

  /**
   * إرسال رسالة نصية مباشرة
   */
  async sendTextMessage(messageText) {
    try {
      // الحصول على المحادثة الحالية
      const currentChat = window.chat;
      if (!currentChat) {
        console.error("لا توجد محادثة نشطة");
        return;
      }

      // إنشاء بيانات الرسالة
      const messageData = {
        messageType: 0, // نص
        messageText: messageText,
        chatID: currentChat.id,
        senderID: ChatProcessor.processedData.currentUser?.id,
        source: 1,
        deviceInfo: "web",
      };

      // إرسال الرسالة باستخدام الدالة الموجودة
      if (window.saveSendingMessage) {
        await window.saveSendingMessage(messageData);
      } else {
        console.error("دالة saveSendingMessage غير موجودة");
      }
    } catch (error) {
      console.error("خطأ في إرسال الرسالة النصية:", error);
    }
  }

  /**
   * معالجة الرد السريع للملفات
   */
  async handleFileQuickReply(reply) {
    try {
      if (reply.fileData) {
        // إذا كانت بيانات الملف مخزنة، إنشاء كائن ملف وتشغيل رفع الملف
        const fileUploadManager = window.fileUploadManager;
        if (fileUploadManager) {
          // تحويل base64 إلى blob إذا لزم الأمر
          const blob = this.quickRepliesService.base64ToBlob(
            reply.fileData,
            reply.mimeType
          );
          const file = new File([blob], reply.fileName || "quick-reply-file", {
            type: reply.mimeType,
          });

          // تشغيل عملية رفع الملف
          fileUploadManager.handleFileSelection([file], reply.content);
        }
      } else {
        // إذا لم توجد بيانات ملف، فقط تعيين التعليق
        const messageInput = document.getElementById("input-send-message");
        messageInput.value = reply.content || "";
        messageInput.focus();
      }
    } catch (error) {
      console.error("خطأ في معالجة الرد السريع للملف:", error);
    }
  }

  /**
   * معالجة الرد السريع لجهة الاتصال
   */
  async handleContactQuickReply(reply) {
    try {
      // تحليل بيانات جهة الاتصال
      const contactData = JSON.parse(reply.content);

      // إنشاء رسالة جهة اتصال
      const messageData = {
        messageType: 5,
        messageText: "",
        otherContent: {
          contentType: "ContactCard",
          contactCard: {
            name: contactData.name,
            phoneNumber: contactData.phoneNumber,
          },
        },
      };

      // إرسال رسالة جهة الاتصال
      await this.sendQuickReplyMessage(messageData);
    } catch (error) {
      console.error("خطأ في معالجة الرد السريع لجهة الاتصال:", error);
    }
  }

  /**
   * معالجة الرد السريع للموقع
   */
  async handleLocationQuickReply(reply) {
    try {
      // تحليل بيانات الموقع
      const locationData = JSON.parse(reply.content);

      // إنشاء رسالة موقع
      const messageData = {
        messageType: 6,
        messageText: locationData.description || "",
        otherContent: {
          contentType: "Location",
          location: {
            latitude: locationData.latitude,
            longitude: locationData.longitude,
            name: locationData.name,
          },
        },
      };

      // إرسال رسالة الموقع
      await this.sendQuickReplyMessage(messageData);
    } catch (error) {
      console.error("خطأ في معالجة الرد السريع للموقع:", error);
    }
  }

  /**
   * معالجة الرد السريع للاستطلاع
   */
  async handlePollQuickReply(reply) {
    try {
      // تحليل بيانات الاستطلاع
      const pollData = JSON.parse(reply.content);

      // إنشاء رسالة استطلاع
      const messageData = {
        messageType: 7,
        messageText: pollData.question,
        poll: {
          question: pollData.question,
          options: pollData.options.map((option) => ({
            optionText: option,
            votes: 0,
          })),
        },
      };

      // إرسال رسالة الاستطلاع
      await this.sendQuickReplyMessage(messageData);
    } catch (error) {
      console.error("خطأ في معالجة الرد السريع للاستطلاع:", error);
    }
  }

  /**
   * إرسال رسالة الرد السريع
   */
  async sendQuickReplyMessage(messageData) {
    try {
      // الحصول على المحادثة الحالية
      const currentChat = window.chat;
      if (!currentChat) {
        console.error("لا توجد محادثة نشطة");
        return;
      }

      // إضافة معرف المحادثة ومعلومات المرسل
      messageData.chatID = currentChat.id;
      messageData.senderID = ChatProcessor.processedData.currentUser?.id;
      messageData.source = 1;
      messageData.deviceInfo = "web";

      // حفظ وإرسال الرسالة
      if (window.saveSendingMessage) {
        await window.saveSendingMessage(messageData);
      } else {
        console.error("دالة saveSendingMessage غير موجودة");
      }
    } catch (error) {
      console.error("خطأ في إرسال رسالة الرد السريع:", error);
    }
  }
}
