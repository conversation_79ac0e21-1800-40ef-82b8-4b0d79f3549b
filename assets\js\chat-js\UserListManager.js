﻿//import userProcessor from './userProcessor.js';

class UserListManager {
  constructor() {
    this.users = [];
    this.currentPage = 1;
    this.pageSize = 20;
    this.hasMore = false;
    this.searchTerm = "";
    this.isLoading = false;
    this.userProcessor = null;
    this.activeUserTypeId = "3"; // منصة (افتراضي)
    this.scrollThrottleTimeout = null;
  }

  async initialize() {
    try {
      this.userProcessor = new UserProcessor();
      // Initialize user data
      await this.userProcessor.initializeUserData();

      // Fetch initial users for default tab (platform)
      await this.fetchUsers();

      // Set up event listeners
      this.setupEventListeners();
      this.setupInfiniteScroll();

      //////console.log('user list manager initialized successfully');
    } catch (error) {
      console.error("Error initializing user list manager:", error);
      throw error;
    }
  }

  setupInfiniteScroll() {
    const userList = document.getElementById("contact-list");
    if (!userList) return;

    userList.addEventListener("scroll", () => {
      // Clear any existing throttle timeout
      if (this.scrollThrottleTimeout) {
        clearTimeout(this.scrollThrottleTimeout);
      }

      // Throttle the scroll event
      this.scrollThrottleTimeout = setTimeout(() => {
        // Don't load more if we're already loading or there's no more data
        if (this.isLoading || !this.hasMore) return;

        const scrollPosition = userList.scrollTop + userList.clientHeight;
        const scrollHeight = userList.scrollHeight;

        // Load more when user scrolls to 80% of the container
        if (scrollPosition >= scrollHeight * 0.8) {
          this.currentPage++;
          this.fetchUsers(true);
        }
      }, 200); // Throttle to 200ms
    });
  }
  setupEventListeners() {
    // Search input handler
    const searchInput = document.getElementById("user-search");
    if (searchInput) {
      searchInput.addEventListener("input", this.handleSearch.bind(this));
    }

    // Scroll handler for infinite loading
    const userList = document.getElementById("contact-list");
    if (userList) {
      userList.addEventListener("scroll", this.handleScroll.bind(this));
    }

    // Tab click handlers
    this.setupTabEventListeners();
  }

  setupTabEventListeners() {
    const tabLinks = document.querySelectorAll("[data-user-type-id]");
    tabLinks.forEach((tab) => {
      tab.addEventListener("click", (event) => {
        event.preventDefault();

        // تحديث التبويب النشط
        const userTypeId = tab.getAttribute("data-user-type-id");
        this.setActiveTab(userTypeId);

        // إعادة تعيين الصفحة وجلب المستخدمين بناءً على النوع المحدد
        this.currentPage = 1;
        this.activeUserTypeId = userTypeId;
        this.users = this.userProcessor.getUsersByType(parseInt(userTypeId));
        this.renderUsers();
      });
    });
  }

  setActiveTab(userTypeId) {
    // إزالة الكلاس النشط من جميع علامات التبويب
    const tabLinks = document.querySelectorAll("[data-user-type-id]");
    tabLinks.forEach((tab) => {
      tab.classList.remove("active");

      // إزالة aria-selected من جميع التبويبات
      tab.setAttribute("aria-selected", "false");

      // الحصول على معرف المحتوى المرتبط
      const tabContentId = tab.getAttribute("href").substring(1);
      const tabContent = document.getElementById(tabContentId);

      if (tabContent) {
        tabContent.classList.remove("show", "active");
      }
    });

    // تعيين التبويب المحدد كنشط
    const activeTab = document.querySelector(
      `[data-user-type-id="${userTypeId}"]`
    );
    if (activeTab) {
      activeTab.classList.add("active");
      activeTab.setAttribute("aria-selected", "true");

      // تعيين محتوى التبويب المرتبط كنشط
      const tabContentId = activeTab.getAttribute("href").substring(1);
      const tabContent = document.getElementById(tabContentId);

      if (tabContent) {
        tabContent.classList.add("show", "active");
      }
    }
  }

  async handleSearch(event) {
    const searchTerm = event.target.value;
    this.searchTerm = searchTerm;

    // If search term is empty, fetch from server
    if (!searchTerm.trim()) {
      this.currentPage = 1;
      await this.fetchUsers();
      return;
    }

    //console.log("handleSearch", this.activeUserTypeId);
    // For non-empty search terms, use local search for immediate results
    const searchResults = await this.userProcessor.getUsersBySearch(
      searchTerm,
      this.activeUserTypeId
    );
    //console.log("searchResults", searchResults);
    this.users = searchResults;
    this.renderUsers();

    // Show a message if no results found
    const userList = document.getElementById("contact-list");
    if (userList && searchResults.length === 0) {
      userList.innerHTML = `
                <div class="no-results">
                    <p>لم يتم العثور على مستخدمين يطابقون "${searchTerm}"</p>
                </div>
            `;
    }
    // Also fetch from server to ensure we have all matching users
    // Only if the search term is at least 2 characters to avoid too many requests
    //if (searchTerm.trim().length >= 2 && searchResults.length !== 0) {
    //    this.currentPage = 1;
    //   //console.log("fetch from server to ensure we have all matching users")
    //    await this.fetchUsers();
    //}
    //await this.fetchUsers();
  }

  async handleScroll(event) {
    const element = event.target;
    const isNearBottom =
      element.scrollHeight - element.scrollTop <= element.clientHeight + 50;

    if (isNearBottom && this.hasMore && !this.isLoading) {
      this.currentPage++;
      await this.fetchUsers(true);
    }
  }

  async fetchUsers(isLoadMore = false) {
    try {
      this.isLoading = true;

      // إظهار مؤشر التحميل
      this.showLoader();

      //console.log("fetchUsers searchTerm", this.searchTerm);
      //console.log("fetchUsers this.activeUserTypeId", this.activeUserTypeId);
      const result = await this.userProcessor.fetchUsers(
        this.searchTerm,
        this.currentPage,
        this.pageSize,
        "Desc",
        this.activeUserTypeId
      );

      //console.log("fetchUsers fetchUsers", result, this.activeUserTypeId);

      // If we're doing a local search, don't overwrite the results
      if (this.searchTerm.trim() && !isLoadMore) {
        // Merge server results with local results to ensure we have all matches
        const localuserIds = new Set(
          this.users.map((c) => {
            //////console.log("c",c)
            return c.userName;
          })
        );
        //////console.log("localuserIds", localuserIds)
        // Add any server results that aren't already in our local results
        result.users.forEach((user) => {
          if (!localuserIds.has(user.userName)) {
            //////console.log("this.users.push", user)

            this.users.push(user);
          }
        });
      } else if (!isLoadMore) {
        // For non-search or empty search, replace users
        this.users = result.users;
      } else {
        // For load more, append new users
        this.users = [...this.users, ...result.users];
      }

      this.hasMore = result.pagination.hasMore;

      // Update UI
      this.renderUsers();

      // إخفاء مؤشر التحميل
      this.hideLoader();

      //////console.log('users fetched successfully');
    } catch (error) {
      console.error("Error fetching users:", error);

      // عرض رسالة الخطأ
      const contactList = document.getElementById("contact-list");
      if (contactList) {
        contactList.innerHTML += `
                    <div class="error-message">
                        <p>حدث خطأ أثناء تحميل المستخدمين. يرجى المحاولة مرة أخرى.</p>
                    </div>
                `;
      }

      // إخفاء مؤشر التحميل
      this.hideLoader();

      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  showLoader() {
    const contactList = document.getElementById("contact-list");
    if (contactList) {
      // تحقق مما إذا كان مؤشر التحميل موجودًا بالفعل
      if (!contactList.querySelector(".loader-container")) {
        const loaderHTML = `
                    <div class="loader-container">
                        <div class="loader"></div>
                        <p>جاري التحميل...</p>
                    </div>
                `;
        contactList.insertAdjacentHTML("beforeend", loaderHTML);
      }
    }
  }

  hideLoader() {
    const contactList = document.getElementById("contact-list");
    if (contactList) {
      const loader = contactList.querySelector(".loader-container");
      if (loader) {
        loader.remove();
      }
    }
  }

  renderUsers() {
    const userList = document.getElementById("contact-list");
    if (!userList) return;
    // If no users, show a message
    if (this.users.length === 0) {
      userList.innerHTML = `
                <div class="no-results">
                    <p>لم يتم العثور على مستخدمين</p>
                </div>
            `;
      return;
    }
    // Group users by first letter
    const groupedUsers = this.groupUsersByLetter();

    // Generate HTML
    let html = "";
    for (const [letter, users] of Object.entries(groupedUsers)) {
      html += `
                <div class="a">
                    <h3>${letter}</h3>
                </div>
            `;

      for (const user of users) {
        html += this.generateUserHTML(user);
      }
    }

    userList.innerHTML = html;
  }

  groupUsersByLetter() {
    const grouped = {};
    this.users.forEach((user) => {
      if (!user.userName) return;

      const letter = user.userName.charAt(0).toUpperCase();
      if (!grouped[letter]) {
        grouped[letter] = [];
      }
      grouped[letter].push(user);
    });
    return grouped;
  }

  generateUserHTML(user) {
    //const user = user[0]?.user;
    // //////console.log("user[0]?.user", user)
    return `
            <div class="block top" data-contact-id="${
              user.id
            }" onclick="generateMessageAreaForContact(this, ${user.id})" >
                <div class="imgBox">
                    <img src="${
                      user?.picture || "/assets/avatars/imageGrey.jpg"
                    }" class="cover" alt="${user.userName}">
                </div>
                <div class="h-text">
                    <div class="head">
                        <h4 title="${user.userName}" aria-label="${
      user.userName
    }">${user.userName}</h4>
                    </div>
                    <div class="message">
                        <p title="${user?.phoneNumber || ""}" aria-label="${
      user?.phoneNumber || ""
    }">
                            ${user?.phoneNumber || ""}
                        </p>
                    </div>
                </div>
            </div>
        `;
  }

  async deleteUser(userId) {
    try {
      await this.userProcessor.deleteUser(userId);
      this.users = this.users.filter((c) => c.id !== userId);
      this.renderUsers();
      //////console.log('user deleted successfully');
    } catch (error) {
      console.error("Error deleting user:", error);
      throw error;
    }
  }

  showDeleteConfirmation(userId) {
    if (confirm("هل أنت متأكد أنك تريد حذف جهة الاتصال هذه؟")) {
      this.deleteUser(userId);
    }
  }
}
