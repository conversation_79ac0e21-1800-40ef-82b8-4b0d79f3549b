﻿class UserProcessor {
  constructor() {
    this.processedData = {
      users: [],
      currentPage: 1,
      pageSize: 20,
      totalPages: 0,
      hasMore: false,
    };
    this.ajaxManager = new AjaxManager();
  }

  async initializeUserData() {
    try {
      // Initialize database if not already initialized
      if (!DBManager.isInitialized) {
        await DBManager.initializeDB();
      }

      // Load users from IndexedDB
      await this.loadUsersFromDB();

      // //////console.log('Contact data initialized successfully');
    } catch (error) {
      //console.error('Error initializing contact data:', error);
      throw error;
    }
  }

  async loadUsersFromDB() {
    try {
      const users = await DBManager.getUsers();

      this.processedData.users = users || [];
      // //////console.log('users loaded from DB:', this.processedData.users.length);
    } catch (error) {
      //console.error('Error loading users from DB:', error);
      throw error;
    }
  }
  async getUsersFromDB() {
    try {
      const users = await DBManager.getUsers();
      //console.log("users", users);
      return users;
    } catch (error) {
      //console.error('Error loading users from DB:', error);
      throw error;
    }
  }
  async fetchUsers(
    search = "",
    page = 1,
    pageSize = 20,
    sort = "Desc",
    userTypeId = null
  ) {
    try {
      // يمكن إضافة userTypeId كمعلمة للطلب إذا تم توفيره
      // let url = `Contact/GetAllContacts`;
      let url = `api/v1/User/GetAllUsersDelivery?currentPage=1&pageSize=0`;
      //   if (userTypeId) {
      //     url += `?userTypeId=${userTypeId}`;
      //   }

      const data = await this.ajaxManager.get(url);
      //const data = await response.json();
      //console.log("datcontacta", data);
      if (!data || !data.resObject) {
        throw new Error("Invalid response format from server");
      }
      if (data.resCode === 200) {
        // Process and store users
        //const users = data.resObject.pageData.map(item => ContactModels.Contact.fromResponse(item.contact));
        const users = data.resObject.pageData.map((item) => {
          //////console.log("item", item)

          return new UsersAllModels.UserData(item);
        });

        // Update processed data
        // Update processed data
        //this.processedData.users = users;
        this.processedData.users = users;
        this.processedData.currentPage = data.resObject.currentPage;
        this.processedData.pageSize = data.resObject.pageSize;
        this.processedData.totalPages = data.resObject.totalPages;
        this.processedData.hasMore = data.resObject.hasNextPage;

        // Save to IndexedDB
        await this.saveUsersToDB(users);
        const usersFliter =
          userTypeId !== null
            ? users.filter((user) => user.userTypeId == userTypeId)
            : users;
        return {
          users: usersFliter,
          pagination: {
            currentPage: data.resObject.currentPage,
            pageSize: data.resObject.pageSize,
            totalPages: data.resObject.totalPages,
            hasMore: data.resObject.hasNextPage,
          },
        };
      } else {
        throw new Error(data.resMeg || "Failed to fetch users");
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      // Return empty data instead of throwing error
      return {
        //users: [],
        users: [],
        pagination: {
          currentPage: 1,
          pageSize: pageSize,
          totalPages: 0,
          hasMore: false,
        },
      };
    }
  }

  async saveUsersToDB(users) {
    try {
      // Clear existing data
      //await DBManager.clearStore('users');

      await DBManager.clearStore("users");

      // Save new data
      //for (const contact of users) {
      //    await DBManager.saveData('users', contact);
      //}
      for (const user of users) {
        await DBManager.saveData("users", user);
      }

      //// //////console.log('users saved to DB successfully');
    } catch (error) {
      //console.error('Error saving users to DB:', error);
      throw error;
    }
  }

  async deleteContact(userId) {
    try {
      // Remove from IndexedDB
      await DBManager.delete("users", userId);

      // Update processed data
      this.processedData.users = this.processedData.users.filter(
        (c) => c.id !== userId
      );

      // //////console.log('Contact deleted successfully');
    } catch (error) {
      //console.error('Error deleting contact:', error);
      throw error;
    }
  }

  getUserById(Id) {
    return this.processedData.users.find((c) => c.id === Id);
  }

  //getContactMembershipById(Id) {
  //    return this.processedData.users.find(m => m.id === Id);
  //}

  async getUsersBySearch(searchTerm, userTypeId = null) {
    if (!this.processedData || !Array.isArray(this.processedData.users)) {
      //console.warn("No users data to search.");
      return [];
    }

    // فلترة المستخدمين أولاً حسب النوع إذا تم تحديده
    let filteredUsers = this.processedData.users;

    if (userTypeId) {
      filteredUsers = filteredUsers.filter(
        (user) => user.userTypeId == userTypeId
      );
    }

    // في حالة عدم وجود مصطلح بحث، إرجاع القائمة المفلترة بالنوع فقط
    if (!searchTerm || searchTerm.trim().length < 1) {
      return filteredUsers;
    }

    const term = searchTerm?.toLowerCase()?.trim();

    // البحث في القائمة المفلترة حسب النوع
    const results = await DBManager.findUsersBySearch(
      term,
      parseInt(userTypeId)
    );
    //const res =  filteredUsers.filter(user => {
    //    const name = user?.userName?.toLowerCase();
    //    const phoneNumber = user?.phoneNumber?.toLowerCase();
    //    return name && name.includes(term) || phoneNumber && phoneNumber.includes(term);
    //});

    ////console.log("getusersBySearch results:", results);
    //////console.log("getusersBySearch res:", res);
    return results;
  }

  getUsersByType(userTypeId) {
    if (!this.processedData || !Array.isArray(this.processedData.users)) {
      //console.warn("No users data to filter.");
      return [];
    }

    if (!userTypeId) {
      return this.processedData.users;
    }
    //////console.log("this.processedData.users.filter(user => user.userTypeId == userTypeId)", this.processedData.users.filter(user => user.userTypeId == userTypeId))
    return this.processedData.users.filter(
      (user) => user.userTypeId == userTypeId
    );
  }
}
