/**
 * App<PERSON>oa<PERSON> - Manages the loading screen and transition to the main app
 */
class AppLoader {
  constructor() {
    // Initialize DOM elements
    this.loadingContainer = document.getElementById("loading-container");
    this.chatContainer = document.getElementById("chat-container");
    this.loadingState = document.getElementById("loadingState");
    this.errorState = document.getElementById("errorState");
    this.offlineState = document.getElementById("offlineState");
    this.progressFill = document.getElementById("progressFill");
    this.progressText = document.getElementById("progressText");
    this.loadingDetails = document.getElementById("loadingDetails");
    this.retryButton = document.getElementById("retryButton");
    this.retryOfflineButton = document.getElementById("retryOfflineButton");

    //////console.log("this.chatContainer", this.chatContainer)
    // Set initial state
    this.chatContainer.style.display = "none";
    this.loadingContainer.style.display = "flex";

    // Bind event listeners
    this.retryButton.addEventListener("click", async () => {
      await ChatProcessor.initializeChatData();
    });
    this.retryOfflineButton.addEventListener("click", async () => {
      await ChatProcessor.initializeChatData();
    });

    // Start loading process
    this.startLoading();
  }

  /**
   * Start the loading process
   */
  startLoading() {
    this.showLoadingState();
    this.simulateLoading();
  }

  /**
   * Simulate loading process with progress updates
   */
  simulateLoading() {
    const steps = [
      { progress: 20, message: "جاري تحميل قائمة المحادثات..." },
      { progress: 40, message: "جاري تحميل الرسائل السابقة..." },
      { progress: 60, message: "جاري تحميل الوسائط..." },
      { progress: 80, message: "جاري تحميل الملفات..." },
      { progress: 100, message: "اكتمل التحميل!" },
    ];

    let currentStep = 0;
    const interval = setInterval(() => {
      if (currentStep < steps.length) {
        const step = steps[currentStep];
        this.updateProgress(step.progress);
        this.updateLoadingMessage(step.message);
        currentStep++;
      } else {
        //clearInterval(interval);
        //setTimeout(() => this.showMainApp(), 500);
      }
    }, 1000);
  }

  /**
   * Update the progress bar
   * @param {number} progress - Current progress percentage
   */
  updateProgress(progress) {
    this.progressFill.style.width = `${progress}%`;
    this.progressText.textContent = `${progress}%`;
  }

  /**
   * Update the loading message
   * @param {string} message - Message to display
   */
  updateLoadingMessage(message) {
    this.loadingDetails.textContent = message;
  }

  /**
   * Show the loading state
   */
  showLoadingState() {
    this.loadingState.style.display = "flex";
    this.errorState.style.display = "none";
    this.offlineState.style.display = "none";
  }

  /**
   * Show the error state
   * @param {string} message - Error message to display
   */
  showErrorState(message = "حدث خطأ أثناء تحميل المحادثات") {
    this.loadingState.style.display = "none";
    this.errorState.style.display = "flex";
    this.offlineState.style.display = "none";
    document.getElementById("errorMessage").textContent = message;
  }

  /**
   * Show the offline state
   */
  showOfflineState() {
    this.loadingState.style.display = "none";
    this.errorState.style.display = "none";
    this.offlineState.style.display = "flex";
  }

  /**
   * Retry loading
   */
  retryLoading() {
    this.startLoading();
  }

  /**
   * Show the main app and hide the loading screen
   */
  showMainApp() {
    // Hide loading screen
    this.loadingContainer.style.display = "none";

    // Show chat container
    this.chatContainer.removeAttribute("style");
    //.style.display = 'flex';

    // Initialize chat list if it exists
    if (window.chatListManager) {
      window.chatListManager.initializeChatList();
    }
  }
}

// Initialize AppLoader when DOM is loaded
//document.addEventListener('DOMContentLoaded', () => {
//    window.appLoader = new AppLoader();

//});
