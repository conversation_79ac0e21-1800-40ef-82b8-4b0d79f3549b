/**
 * ChatListManager - Manages the chat list with fragments and loading functionality
 */
class ChatListManager {
  constructor() {
    this.chatListContainer = document.querySelector("#chat-list");
    this.chatsPerPage = 10;
    this.currentPage = 1;
    this.isLoading = false;
    this.hasMoreChats = true;
    this.chats = [];
    this.loadingIndicator = this.createLoadingIndicator();
    this.activeChatId = null;
    this.activeChatLocId = null;
    this.searchTerm = "";
    // Chat item management
    this.chatItems = new Map(); // Map of chat ID to DOM element
    this.statusIndicators = new Map(); // Map of chat ID to status indicator element
    this.messageStatusIcons = new Map(); // Map of chat ID to message status icon element
    this.unreadCounters = new Map(); // Map of chat ID to unread counter element

    this.setupEventListeners();
    // Initialize scroll event listener
    this.setupInfiniteScroll();
  }

  /**
   * Creates a loading indicator element
   */
  createLoadingIndicator() {
    const loadingDiv = document.createElement("div");
    loadingDiv.className = "chat-loading-indicator";
    loadingDiv.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner"></div>
            </div>
            <div class="loading-text">جاري تحميل المزيد من المحادثات...</div>
        `;
    return loadingDiv;
  }

  /**
   * Sets up infinite scroll functionality
   */
  setupInfiniteScroll() {
    this.chatListContainer.addEventListener("scroll", () => {
      if (this.isLoading || !this.hasMoreChats) return;

      const scrollPosition =
        this.chatListContainer.scrollTop + this.chatListContainer.clientHeight;
      const scrollHeight = this.chatListContainer.scrollHeight;

      // Load more when user scrolls to 80% of the container
      if (scrollPosition >= scrollHeight * 0.6) {
        console.log("Loading more chats...");
        this.loadMoreChats();
      }
    });
  }

  setupEventListeners() {
    // Search input handler
    const searchInput = document.getElementById("search-chats");
    if (searchInput) {
      searchInput.addEventListener("input", this.handleSearch.bind(this));
    }

    // Scroll handler for infinite loading
    const userList = document.getElementById("chat-list");
    if (userList) {
      userList.addEventListener("scroll", this.setupInfiniteScroll.bind(this));
    }
  }

  async handleSearch(event) {
    const searchTerm = event.target.value;
    this.searchTerm = searchTerm;

    // If search term is empty, fetch from server
    if (!searchTerm.trim()) {
      this.currentPage = 1;
      await this.initializeChatList();
      return;
    }
    // For non-empty search terms, use local search for immediate results
    const { chats: searchResults, hasMore } =
      await ChatProcessor.getAllChatsAndSearchWithPag(
        1,
        this.chatsPerPage,
        this.searchTerm
      );
    console.log("searchResults", searchResults);
    if (searchResults) {
      this.chatListContainer.innerHTML = "";
      // Clear existing items
      this.chatItems.clear();
      this.statusIndicators.clear();
      this.messageStatusIcons.clear();
      this.unreadCounters.clear();
      this.chats = searchResults;
      this.appendChatsToFragment(searchResults);
    }

    // Show a message if no results found
    if (this.chatListContainer && searchResults.length === 0) {
      this.renderEmptyState();
    }
  }

  /**
   * Loads more chats when scrolling
   */
  async loadMoreChats() {
    if (this.isLoading) return;

    this.isLoading = true;
    this.showLoadingIndicator();

    try {
      const { chats, hasMore } =
        await ChatProcessor.getAllChatsAndSearchWithPag(
          this.currentPage + 1,
          this.chatsPerPage,
          this.searchTerm
        );

      if (chats.length > 0) {
        this.appendChatsToFragment(chats);
        this.currentPage++;
        this.hasMoreChats = hasMore;
      } else {
        this.hasMoreChats = false;
      }
    } catch (error) {
      console.error("Error loading more chats:", error);
    } finally {
      this.isLoading = false;
      this.hideLoadingIndicator();
    }
  }

  /**
   * Shows the loading indicator
   */
  showLoadingIndicator() {
    this.chatListContainer.appendChild(this.loadingIndicator);
  }

  /**
   * Hides the loading indicator
   */
  hideLoadingIndicator() {
    if (this.chatListContainer.contains(this.loadingIndicator)) {
      this.chatListContainer.removeChild(this.loadingIndicator);
    }
  }

  /**
   * Initializes the chat list with initial data
   * @param {Array} chats - Array of chat objects
   */
  async initializeChatList() {
    this.currentPage = 1;
    this.hasMoreChats = true;
    console.log("initializeChatList");
    // Clear existing content
    this.chatListContainer.innerHTML = "";
    // Clear existing items
    this.chatItems.clear();
    this.statusIndicators.clear();
    this.messageStatusIcons.clear();
    this.unreadCounters.clear();
    // Load first batch
    const { chats, hasMore } = await ChatProcessor.getAllChatsAndSearchWithPag(
      1,
      this.chatsPerPage,
      this.searchTerm
    );
    this.hasMoreChats = hasMore;
    if (chats.length === 0) {
      this.renderEmptyState();
    } else {
      this.appendChatsToFragment(chats);
    }
    // Initialize click event listener for active state
    await this.setupActiveStateHandler();
  }

  renderEmptyState() {
    this.chatListContainer.innerHTML = "";

    if (this.searchTerm) {
      this.chatListContainer.innerHTML = `
                <div class="no-results">
                    <p>لم يتم العثور على أي محادثات "${this.searchTerm}"</p>
                </div>
            `;
    } else {
      this.chatListContainer.innerHTML = `
                <div class="no-chats">
                    <p>لا توجد محادثات</p>
                    <p>قم بإنشاء محادثة جديدة للبدء</p>
                </div>
            `;
    }
  }
  /**
   * Appends chats to the container using a document fragment
   * @param {Array} chats - Array of chat objects to append
   */
  appendChatsToFragment(chats) {
    const fragment = document.createDocumentFragment();
    chats.forEach((chat) => {
      const chatElement = document.createElement("div");
      chatElement.innerHTML = this.generateChatItemHTML(chat);
      // Find and store reference to the status indicator
      const chatElementActive = chatElement.querySelector(".item-chat-list");
      if (chat.id === this.getActiveChatId()) {
        chatElementActive.classList.add("active");
      }

      if (chat.type === "Individual") {
        let userState = chat.members?.[0].user.statusConnection;
        const statusIndicator = chatElement.querySelector(".status-indicator");
        this.statusIndicators.set(chat.id, statusIndicator);

        if (statusIndicator && userState && userState.statusConnection) {
          // Set initial status
          this.updateStatusIndicator(chat.id, userState.statusConnection);
        }
      }
      fragment.appendChild(chatElement.firstElementChild);
    });
    // Insert before the loading indicator if it exists
    if (this.chatListContainer.contains(this.loadingIndicator)) {
      this.chatListContainer.insertBefore(fragment, this.loadingIndicator);
    } else {
      this.chatListContainer.appendChild(fragment);
    }
  }

  /**
   * Generates HTML for a chat item
   * @param {Object} chat - Chat object
   * @returns {string} HTML string for the chat item
   */
  generateChatItemHTML(chat) {
    const messageTime = chat.lastMessages?.[0]
      ? new Date(chat.lastMessages?.[0].createdDate).toLocaleTimeString(
          "En-EG",
          {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          }
        )
      : "";

    const getTickIcon = (message) => {
      if (!message) return "";
      if (
        message.messageStatus === "Pending" &&
        message.senderID === ChatProcessor.processedData.currentUser?.id
      ) {
        return '<i  class="fe fe-clock  ml-1 fe-16"></i>';
      } else if (
        message.messageStatus === "Sent" &&
        message.senderID === ChatProcessor.processedData.currentUser?.id
      ) {
        return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20"
              class="white-tick">
              <path fill="currentColor"
                  d="m10.91 3.316-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z">
              </path>
          </svg>`;
      } else if (
        message.messageStatus === "Delivered" &&
        message.senderID === ChatProcessor.processedData.currentUser?.id
      ) {
        return '<i class="fe fe-message-circle ml-1 fe-16"></i>';
      } else if (
        message.messageStatus === "Read" &&
        message.senderID === ChatProcessor.processedData.currentUser?.id
      ) {
        return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20"
              aria-label="read" class="blue-tick">
              <path fill="currentColor"
                  d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.879a.32.32 0 0 1-.484.033l-.358-.325a.319.319 0 0 0-.484.032l-.378.483a.418.418 0 0 0 .036.541l1.32 1.266c.143.14.361.125.484-.033l6.272-8.048a.366.366 0 0 0-.064-.512zm-4.1 0l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z">
              </path>
          </svg>`;
      } else if (
        message.messageStatus === "Failed" &&
        message.senderID === ChatProcessor.processedData.currentUser?.id
      ) {
        return '<i class="fe fe-x-circle ml-1 fe-16"></i>';
      } else {
        return "";
      }
    };

    // Generate the status indicator HTML
    let statusIndicatorHTML = "";
    statusIndicatorHTML = `
                <div class="status-indicator "></div>
            `;
    return `
      <div class="block item-chat-list ${
        chat.unreadCount ? "unread" : ""
      }"   data-chat-id="${chat.id}" data-last-message-date="${
      chat.lastMessageDate || 0
    }" data-chat-locid="${chat.locId}" >
         
             

            <div class="avatar-wrapper">
  <div class="imgBox">
 <img src="${
   chat.picture || "/assets/avatars/imageGrey.jpg"
 }" class="cover" alt="${chat.name}">
            </div>
            ${statusIndicatorHTML}
</div>
          
          <div class="h-text">
              <div class="head">
                  <h4 title="${chat.name}" aria-label="${chat.name}">${
      chat.name
    }</h4>
                  <p class="time">${messageTime}</p>
              </div>
              <div class="last-message-chat">
                  <div class="tick-icon">
                      ${getTickIcon(chat.lastMessages?.[0])}
                  </div>

                  <div class="chat-text-icon">
                     
                     <span class="text-last-message">${this.getMessagePreview(
                       chat
                     )}</span>

                      ${
                        chat.unreadCount > 0
                          ? `
                          <div class="unread" id="unread-count">
                              <span class="numb">${chat.unreadCount}</span>
                          </div>
                      `
                          : ""
                      }
                  </div>
              </div>
          </div>
         <button class="btn btn-sm  more-horizontal" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <svg viewBox="0 0 24 24" width="24" height="24" class="">
                            <path fill="currentColor" d="M12 7a2 2 0 1 0-.001-4.001A2 2 0 0 0 12 7zm0 2a2 2 0 1 0-.001 3.999A2 2 0 0 0 12 9zm0 6a2 2 0 1 0-.001 3.999A2 2 0 0 0 12 15z">
                            </path>
                        </svg>                   
                     </button>
                        <div class="dropdown-menu dropdown-menu-right">
                        <a class="dropdown-item" href="/RejectReason/Edit/{rowdata}">حظر</a>
                    </div>
  `;
  }

  /**
   * الحصول على معاينة الرسالة
   * @param {ChatModel} chat - نموذج المحادثة
   * @returns {string} - نص المعاينة
   */
  getMessagePreview(chat) {
    if (!chat.lastMessages || chat.lastMessages.length === 0) {
      return "";
    }

    const lastMessage = chat.lastMessages[0];
    let mt = lastMessage.messageType;
    if (typeof mt === "string") {
      if (MessageTypes.hasOwnProperty(mt)) {
        mt = MessageTypes[mt];
      } else {
        const n = parseInt(mt, 10);
        mt = isNaN(n) ? undefined : n;
      }
    }
    console.log("mt", mt);
    // const prefix =
    //   chat.type === "Group" &&
    //   lastMessage.sender?.id !== ChatProcessor.getCurrentUser()?.id
    //     ? lastMessage.sender.userName
    //     : "";

    // التحقق من نوع الرسالة
    switch (mt) {
      case MessageTypes.Text:
        return (
          lastMessage.messageText.slice(0, 30) +
          (lastMessage.messageText.length > 30 ? "..." : "")
        );
      case MessageTypes.Image:
        return "🖼️ صورة";
      case MessageTypes.Video:
        return "🎬 فيديو";
      case MessageTypes.Audio:
        return "🎤 رسالة صوتية";
      case MessageTypes.RecordVoice:
        return `<span class="icon-voice"></span> Voice`;
      case MessageTypes.Document:
        return "📄 ملف";
      case MessageTypes.Location:
        return "🌍 موقع";
      case MessageTypes.ContactCard:
        return "📞 جهة اتصال";
      case MessageTypes.Poll:
        return "🗳️ استطلاع";
      case MessageTypes.OtherFiles:
        return `📎 File`;
      default:
        return (
          lastMessage.messageText.slice(0, 30) +
          (lastMessage.messageText.length > 30 ? "..." : "")
        );
    }
  }

  async setupActiveStateHandler() {
    if (!this.chatListContainer) {
      console.error("chatListContainer element not found.");
      return;
    }

    this.chatListContainer.addEventListener("click", async (event) => {
      const item = event.target.closest(".item-chat-list");
      if (item) {
        const chatId = item.getAttribute("data-chat-id");
        const chatLocId = item.getAttribute("data-chat-locid");
        await generateMessageArea(item, parseInt(chatLocId));
        this.setActiveChat(chatId, chatLocId);
      }
    });
  }

  /**
   * Sets the active chat and updates UI
   * @param {Object} chat - The chat object to set as active
   */
  setActiveChat(chatId, chatLocId) {
    // Update active state in chatItems array
    this.chats.forEach((item) => {
      item.isActive = item.id === chatId || item.locId === chatLocId;
    });
    // Update UI
    const allItems = this.chatListContainer.querySelectorAll(".item-chat-list");
    allItems.forEach((el) => {
      const itemId = el.getAttribute("data-chat-id");
      const itemLocId = el.getAttribute("data-chat-locid");

      if (itemId === chatId && chatLocId === itemLocId) {
        el.classList.add("active");
      } else {
        el.classList.remove("active");
      }
    });

    // Store the active chat ID
    this.activeChatId = chatId;

    // Scroll the active chat into view if needed
    const activeElement = this.chatListContainer.querySelector(
      ".item-chat-list.active"
    );
    if (activeElement) {
      activeElement.scrollIntoView({ behavior: "smooth", block: "nearest" });
    }
  }
  /**
   * Gets the currently active chat ID
   * @returns {string|null} - The active chat ID or null if no chat is active
   */
  getActiveChatId() {
    return parseInt(this.activeChatId);
  }

  /**
   * Updates a specific chat in the list
   * @param {Object} updatedChat - Updated chat object
   */

  updateChat(updatedChat) {
    const newElement = this.updateChatUI(updatedChat);

    // Find the correct position to insert based on lastMessageDate
    const insertPosition = this.findInsertPosition(updatedChat.lastMessageDate);

    // Insert at the correct position
    if (insertPosition === 0) {
      // Insert at the beginning
      this.chatListContainer.insertBefore(
        newElement,
        this.chatListContainer.firstChild
      );
    } else if (insertPosition >= this.chatListContainer.children.length) {
      // Append at the end
      this.chatListContainer.appendChild(newElement);
    } else {
      // Insert at specific position
      this.chatListContainer.insertBefore(
        newElement,
        this.chatListContainer.children[insertPosition]
      );
    }
    this.sortChats();
  }

  /**
   * Find the correct position to insert a chat based on its lastMessageDate
   * @param {number} lastMessageDate - Timestamp of the last message
   * @returns {number} - Index where the chat should be inserted
   */
  findInsertPosition(lastMessageDate) {
    const children = this.chatListContainer.children;
    for (let i = 0; i < children.length; i++) {
      const chatDate = parseInt(
        children[i].getAttribute("data-last-message-date") || "0"
      );
      if (lastMessageDate > chatDate) {
        return i;
      }
    }
    return children.length;
  }

  /**
   * Sort the chats array by lastMessageDate
   */
  sortChats() {
    this.chats.sort((a, b) => {
      const dateA = a.lastMessageDate || 0;
      const dateB = b.lastMessageDate || 0;
      return dateB - dateA; // Sort in descending order (newest first)
    });
  }
  /**
   * Updates a chat with new messages
   * @param {number} chatId - ID of the chat to update
   * @param {Array} messages - Array of new messages
   */
  async updateChatWithMessages(chatId, messages) {
    try {
      const updatedChat = await DBManager.updateChatMessages(chatId, messages, {
        prepend: true,
        updateUnreadCount: true,
        currentUserId: ChatProcessor.processedData.currentUser?.id,
      });

      if (updatedChat) {
        this.updateChat(updatedChat);
        return updatedChat;
      }
    } catch (error) {
      console.error("Error updating chat with messages:", error);
    }
    return null;
  }

  /**
   * Update chat UI element
   * @param {Object} chat - Chat object to update
   */
  updateChatUI(chat) {
    const chatElement = this.chatListContainer.querySelector(
      `[data-chat-id="${chat.id}"]`
    );
    //////console.log("chatElement", chatElement)
    if (chatElement) {
      const newChatElement = document.createElement("div");
      newChatElement.innerHTML = this.generateChatItemHTML(chat);
      const newElement = newChatElement.firstElementChild;

      // Preserve active state if needed
      if (chatElement.classList.contains("active")) {
        newElement.classList.add("active");
      }
      const statusIndicator = newChatElement.querySelector(".status-indicator");
      if (statusIndicator) {
        this.statusIndicators.set(chat.id, statusIndicator);
      }
      if (
        chat.type === "Individual" &&
        chat.members[0].user.statusConnection.statusConnection
      ) {
        this.updateStatusIndicator(
          chat.id,
          chat.members[0].user.statusConnection.statusConnection
        );
        //newElement.classList.add("online");
      } else if (
        chat.type === "Individual" &&
        !chat.members[0].user.statusConnection.statusConnection
      ) {
        this.updateStatusIndicator(
          chat.id,
          chat.members[0].user.statusConnection.statusConnection
        );
      }

      chatElement.replaceWith(newElement);
      return newElement;
    }
  }
  /**
   * Adds a new chat to the beginning of the list
   * @param {Object} newChat - New chat object
   */
  addNewChat(newChat) {
    const chatElement = document.createElement("div");
    chatElement.innerHTML = this.generateChatItemHTML(newChat);
    this.chatListContainer.insertBefore(
      chatElement.firstElementChild,
      this.chatListContainer.firstChild
    );
    // Update the chats array
    this.chats.unshift(newChat);
  }

  /**
   * Update the online status of users in the chat list
   * @param {Array} userStatuses - List of UserConnectionStatus objects
   */
  async updateUsersOnlineStatus(userStatuses) {
    if (!userStatuses || !Array.isArray(userStatuses)) return;

    try {
      // Get all user IDs that need updating
      const userIds = userStatuses.map((status) => status.id);
      // Find all chats containing these users
      const chats = await DBManager.findChatsWithUsers(userIds);
      // Update each chat
      for (const chat of chats) {
        let isUpdated = false;
        for (const userStatus of userStatuses) {
          const memberIndex = chat.members.findIndex(
            (member) => member.userId === userStatus.userId
          );
          if (
            memberIndex !== -1 &&
            !chat.members[memberIndex].user.statusConnection.statusConnection
          ) {
            chat.members[memberIndex].user.statusConnection.statusConnection =
              userStatus.statusConnection;
            chat.members[memberIndex].user.statusConnection.lastSeenDatetime =
              userStatus.lastSeenDatetime;
          }

          if (chat.type === "Individual") {
            this.updateStatusIndicator(chat.id, userStatus.statusConnection);
            isUpdated = true;
          }
          if (
            chat.type === "Individual" &&
            this.getActiveChatId() === chat.id
          ) {
            this.updateChatDetailsUI(chat, userStatus);
          }
        }

        if (isUpdated) {
          const updatedChat = await DBManager.updateChat(chat);
        }
      }
    } catch (error) {
      console.error("Error updating users online status:", error);
    }
  }

  /**
   * Update chat details UI with new online status
   * @param {Object} chat - Current chat object
   * @param {Object} userStatus - User connection status object
   */
  updateChatDetailsUI(chat, userStatus) {
    // Find the details element in the chat UI
    const detailsElement = document.querySelector(
      "#message-area #navbar #details"
    );
    if (!detailsElement) return;

    // Update the details text based on online status
    if (userStatus.statusConnection) {
      detailsElement.textContent = "متصل";
    } else {
      const lastSeen = new Date(userStatus.lastSeenDatetime);
      const formattedDate = this.formatLastSeen(lastSeen);
      detailsElement.textContent = `آخر ظهور ${formattedDate}`;
    }
  }

  /**
   * Format last seen date
   * @param {Date} date - Last seen date
   * @returns {string} - Formatted date string
   */
  formatLastSeen(date) {
    const now = new Date();
    const diff = now - date;

    // Less than a minute
    if (diff < 60000) {
      return "الآن";
    }

    // Less than an hour
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return `منذ ${minutes} دقيقة`;
    }

    // Less than a day
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000);
      return `منذ ${hours} ساعة`;
    }

    // More than a day
    return date.toLocaleDateString("ar-EG", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  /**
   * Update message statuses in the chat list
   * @param {string} messageId - ID of the message
   * @param {Array} statuses - List of message status objects
   */
  async updateMessageStatuses(messageId, statuses) {
    if (!messageId || !statuses || !Array.isArray(statuses)) return;
    try {
      // Determine the highest status
      let highestStatus = "Sent";
      for (const status of statuses) {
        if (status.status === "Read") {
          highestStatus = "Read";
          break;
        } else if (status.status === "Delivered") {
          highestStatus = "Delivered";
        }
      }
      const { message, chat } = await ChatProcessor.updateMessageStatus(
        messageId,
        highestStatus
      );
      MessageGenerator.updateMessageStatus(message.locId, highestStatus);
      // Update UI for each affected chat
      this.updateChatUI(chat);
    } catch (error) {
      console.error("Error updating message statuses:", error);
    }
  }

  async updateMessageStatuse(messageId, statuses) {
    try {
      const { message, chat } = await ChatProcessor.updateMessageStatus(
        messageId,
        statuses
      );
      MessageGenerator.updateMessageStatus(message.locId, statuses);
      // Update UI for each affected chat
      this.updateChatUI(chat);
    } catch (error) {
      console.error("Error updating message statuses:", error);
    }
  }

  /**
   * Update the status indicator for a chat
   * @param {number} chatId - Chat ID
   * @param {string} status - Online status ('online', 'offline', 'typing')
   */
  updateStatusIndicator(chatId, status) {
    const statusIndicator = this.statusIndicators.get(chatId);
    if (!statusIndicator) return;
    // Add the appropriate class
    if (status) {
      statusIndicator.classList.add("online");
    } else {
      statusIndicator.classList.remove("online");
    }
  }
}
