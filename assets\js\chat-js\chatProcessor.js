/* وحدة معالجة بيانات المحادثات - تقوم بتحميل ومعالجة بيانات المحادثات من ملف JSON */

// معالج البيانات الرئيسي
window.ChatProcessor = {
  /* بيانات المحادثات المعالجة */
  processedData: {
    currentUser: {
      //id: 104,
    },
    chatList: [],
  },

  /* تحميل وتهيئة بيانات المحادثات */
  async initializeChatData() {
    try {
      const appLoader = new AppLoader();
      await DBManager.removeDatabase();
      // التحقق من وجود قاعدة البيانات
      const dbExists = await DBManager.isDatabaseExists();
      //    // تهيئة قاعدة البيانات
      await DBManager.initializeDB();
      // التأكد من تهيئة قاعدة البيانات
      if (!DBManager.isInitialized) {
        console.error("Database initialization failed");
        return false;
      }

      // إذا لم تكن قاعدة البيانات موجودة، قم بتحميل البيانات الأولية
      if (!dbExists) {
        try {
          // استخدام AjaxManager للحصول على المحادثات
          const ajaxManager = new AjaxManager();
          // const chatData = await ajaxManager.post('Chat/GetAllChats');
          const chatData = await ajaxManager.get(
            "api/v1/User/Chats?currentPage=1&pageSize=0"
          );
          if (chatData.resCode !== 200) {
            appLoader.showErrorState();

            throw new Error("Failed to fetch chat data from server");
          }

          const root = new ChatModels.Root(chatData);
          // const rootApi = new ApiResponse(
          //   chatData,
          //   // wrap PagedApiResponse in a small factory so it gets ChatModels.PageData too:
          //   (data) => new PagedApiResponse(data, ChatModels.PageData)
          // );
          // console.log("rootApi", rootApi);

          const rootApis = new ApiResponse(
            chatData,
            // wrap PagedApiResponse in a small factory so it gets ChatModels.PageData too:
            (data) => new PagedApiResponse(data, ChatModel.toJSON)
          );
          console.log("rootApis", rootApis);
          await this.processChatData(root.resObject);
          appLoader.showMainApp();
        } catch (error) {
          appLoader.showErrorState();

          console.error("Error loading initial data:", error);
          return false;
        }
      }

      // Access the user's name
      // تحميل المستخدم الحالي من IndexedDB
      await DBManager.removeCurrentUser();
      const currentUser = await DBManager.getCurrentUser();
      if (currentUser) {
        this.processedData.currentUser = currentUser;
      } else {
        const ajaxManager = new AjaxManager();
        // const responseData = await ajaxManager.get(
        //   "Authentication/UserChatProfile"
        // );
        const responseData = await ajaxManager.get("api/v1/User/Profile");
        const userData = new UserModels.UserData(responseData.resObject);
        userData.tokenInfo = new UserModels.TokenInfo(getAccessToken());
        this.setCurrentUser(userData);
      }
      appLoader.showMainApp();

      return true;
    } catch (error) {
      appLoader.showErrorState();

      console.error("Error loading chat data:", error);
      return false;
    }
  },
  /* معالجة بيانات المحادثات الخام */
  async processChatData(data) {
    if (!data || !data.pageData) return;

    // استخراج المستخدمين من أعضاء المحادثات
    const users = new Set();
    data.pageData.forEach((page) => {
      page.chatMembers.forEach((member) => {
        if (member.user) {
          users.add(JSON.stringify(member.user));
        }
      });
    });
    const processedUsers = Array.from(users).map((u) => JSON.parse(u));
    // معالجة المحادثات
    const processedChats = data.pageData.map((page) => ({
      id: page.id,
      name: page.chatName,
      type: page.chatType,
      picture: page.chatPicture,
      chatDescription: page.chatDescription,
      unreadCount: page.unreadCount,
      members: page.chatMembers,
      lastMessages: page.lastMessages,
      createdDate: page.createdDate,
      company: page.company,
    }));
    // ////////console.log("processedChats", processedChats);
    // معالجة الرسائل
    const processedMessages = data.pageData.flatMap(
      (page) => page.lastMessages || []
    );
    // حفظ البيانات في قاعدة البيانات
    await Promise.all([
      DBManager.saveUsers(processedUsers),
      DBManager.saveChats(processedChats),
      DBManager.saveMessages(processedMessages),
      DBManager.savePageData(data.pageData),
    ]);
    this.processedData.chatList = processedChats;
  },
  /* تعيين المستخدم الحالي */
  async setCurrentUser(userData) {
    try {
      // حفظ المستخدم في IndexedDB
      await DBManager.saveCurrentUser(userData);
      // تحديث المستخدم في الذاكرة
      this.processedData.currentUser = userData;
      return userData;
    } catch (error) {
      console.error("Error setting current user:", error);
      throw error;
    }
  },
  /* حذف المستخدم الحالي */
  async removeCurrentUser() {
    try {
      // حذف المستخدم من IndexedDB
      await DBManager.removeCurrentUser();
      // حذف المستخدم من الذاكرة
      this.processedData.currentUser = null;
    } catch (error) {
      console.error("Error removing current user:", error);
      throw error;
    }
  },

  async getAllChatsAndSearchWithPag(
    currentPage = 1,
    chatsPerPage = 10,
    searchTerm = ""
  ) {
    try {
      let chats = await DBManager.getChatsAndSearch(
        currentPage,
        chatsPerPage,
        searchTerm
      );
      return chats;
    } catch (error) {
      console.error("Error removing current user:", error);
      throw error;
    }
  },

  /* الحصول على المستخدم الحالي */
  getCurrentUser() {
    return this.processedData.currentUser;
  },
  /* الحصول على محادثات مستخدم معين */
  async getUserChats(userId) {
    try {
      // Get all chats sorted by last message date
      const allChats = await DBManager.getChatsSortedByLastMessage();
      // Filter chats for the specific user and enhance with additional information
      const userChats = allChats
        .filter(
          (chat) =>
            chat.type !== "Group" &&
            chat.members.some((member) => member.userID === userId)
        )
        .map((chat) => {
          // Calculate unread count for the specific user
          const unreadCount =
            chat.lastMessages?.filter(
              (msg) => msg.messageStatus !== "Read" && msg.senderID !== userId
            ).length || 0;

          // Get the last message if available
          const lastMessage = chat.lastMessages?.[0];

          return {
            ...chat,
            unreadCount,
            lastMessage,
            lastMessageDate: lastMessage
              ? new Date(lastMessage.createdDate).getTime()
              : 0,
          };
        });

      return userChats;
    } catch (error) {
      console.error("Error getting user chats:", error);
      return [];
    }
  },

  //-----
  /* الحصول على رسائل محادثة معينة */
  async getChatMessages(chatId, page = 1, pageSize = 10) {
    try {
      const result = await DBManager.getMessagesByChatId(
        chatId,
        page,
        pageSize
      );
      return {
        messages: result.messages,
        totalMessages: result.totalMessages,
        currentPage: result.currentPage,
        totalPages: result.totalPages,
        hasMore: result.hasMore,
      };
    } catch (error) {
      console.error("Error getting chat messages:", error);
      return {
        messages: [],
        totalMessages: 0,
        currentPage: 1,
        totalPages: 1,
        hasMore: false,
      };
    }
  },
  /* إضافة رسالة جديدة */
  //-----
  async addMessage(messageData) {
    try {
      // Create new message
      const newMessage = new ChatModels.LastMessage({
        id: messageData.id || null,
        createdDate: messageData.messageData || new Date().toISOString(),
        messageStatus: messageData.messageStatus || "Sent",
        senderID: messageData.senderID,
        chatID: messageData.chatID,
        messageType: messageData.messageType,
        messageText: messageData.messageText,
        deviceInfo: messageData.deviceInfo,

        isDeleted: messageData.isDeleted || false,
        edited: messageData.edited || false,
        sender: messageData.sender
          ? new ChatModels.Sender(messageData.sender)
          : null,
        messageStatuses: messageData.messageStatuses
          ? new ChatModels.MessageStatuses(messageData.messageStatuses)
          : null,
        file: messageData.file ? new ChatModels.File(messageData.file) : null,
        modifyDate: messageData.modifyDate
          ? new Date(messageData.modifyDate)
          : null,
        replyToMessageID: messageData.replyToMessageID || null,
        replyToMessage: messageData.replyToMessage
          ? new ChatModels.ReplyToMessage(messageData.replyToMessage)
          : null,
        otherContent: messageData.otherContent
          ? new ChatModels.OtherContent(messageData.otherContent)
          : null,
        poll: messageData.poll ? new ChatModels.Poll(messageData.poll) : null,
      });
      // Save message to database
      const savedMessage = await DBManager.saveMessages(newMessage);
      // Get the chat and update it
      const chat = await this.getChatById(messageData.chatID);
      if (chat) {
        // Initialize lastMessages array if it doesn't exist
        if (!chat.lastMessages) {
          chat.lastMessages = [];
        }
        // Add the new message to the beginning of the array (newest first)
        chat.lastMessages.unshift(savedMessage[0]);
        // Update lastMessageDate
        chat.lastMessageDate = new Date(savedMessage[0].createdDate).getTime();
        // Save the updated chat
        await DBManager.saveChats([chat]);
      }

      return savedMessage[0];
    } catch (error) {
      console.error("Error adding message:", error);
      throw error;
    }
  },
  //-----
  async updateOnlyMessage(messageLocId, updates) {
    try {
      // Get the current message
      const messages = await DBManager.getMessages();
      const messageIndex = messages.findIndex((m) => m.locId === messageLocId);

      const messagesLoacl = await DBManager.getMessageById(
        parseInt(messageLocId),
        "locId"
      );

      if (messageIndex === -1) {
        console.error(`Message with ID ${messageLocId} not found`);
        throw new Error(`Message with ID ${messageLocId} not found`);
      }

      // Update the message
      const updatedMessage = {
        ...messages[messageIndex],
        ...updates,
      };

      await DBManager._updateMessageRecord(updatedMessage);

      // Update the chat's lastMessages array
      const chat = await this.getChatById(updatedMessage.chatID);

      if (chat && chat.lastMessages) {
        const chatMessageIndex = chat.lastMessages.findIndex(
          (m) => m.locId === messageLocId
        );
        if (chatMessageIndex !== -1) {
          chat.lastMessages.splice(chatMessageIndex, 1, updatedMessage);
          await DBManager.saveChats([chat]);
        }
      }
      return updatedMessage;
    } catch (error) {
      console.error("Error updating message:", error);
      throw error;
    }
  },
  //-----
  /* الحصول على رسالة بواسطة معرفها */
  async getMessageById(messageId, type) {
    try {
      const message = await DBManager.getMessageById(messageId, type);
      if (!message) {
        console.warn(`Message with ID ${messageId} not found`);
        return null;
      }

      return message;
    } catch (error) {
      console.error("Error getting message by ID:", error);
      throw error;
    }
  },
  /* تحديث حالة قراءة الرسائل في محادثة معينة */
  async updateChatReadStatus(chatId) {
    try {
      const ajaxManagers = new AjaxManager();
      const chatMakeReadData = await ajaxManagers.post(
        `api/v1/MessageStatus/${chatId}`
      );
      // const chatMakeReadData = await ajaxManagers.post(
      //   `Chat/MakeRead/${chatId}`
      // );
      if (chatMakeReadData.resCode === 200) {
        console.log("updateChatReadStatus chatMakeReadData", chatMakeReadData);
      }
      // جلب جميع الرسائل في المحادثة
      const messages = await DBManager.getMessagesByChatId(chatId, -1);

      // تحديث حالة الرسائل غير المقروءة
      const updatedMessages = messages.messages.map((msg) => {
        if (
          msg.senderID !== this.processedData.currentUser?.id &&
          msg.messageStatus !== "Read"
        ) {
          return {
            ...msg,
            messageStatus: "Read",
            locId: msg.locId,
          };
        }
        return msg;
      });

      // حفظ الرسائل المحدثة
      await DBManager.saveMessages2(updatedMessages);

      // تحديث المحادثة
      const chat = await this.getChatById(chatId);
      if (chat) {
        // تحديث عدد الرسائل غير المقروءة
        chat.unreadCount = 0;
        // حفظ المحادثة المحدثة
        await DBManager.saveChats([chat]);
      }

      // تحديث قائمة المحادثات في الواجهة
      return true;
    } catch (error) {
      console.error("Error updating chat read status:", error);
      return false;
    }
  },

  async addChats(chats) {
    const processedChats = chats.map((page) => ({
      id: page.id,
      name: page.chatName,
      type: page.chatType,
      picture: page.chatPicture,
      chatDescription: page.chatDescription,
      unreadCount: page.unreadCount,
      members: page.chatMembers,
      lastMessages: page.lastMessages,
      createdDate: page.createdDate,
      company: page.company,
    }));
    const nChats = await DBManager.saveChats(processedChats);
    return nChats;
  },

  /* الحصول على المحادثة الحالية */
  async getCurrentChat(chatId) {
    // Get the current chat ID from the URL or from the active chat element
    //
    if (chatId) {
      return await this.getChatById(chatIds);
    }

    const activeChatElement = document.querySelector(".item-chat-list.active");
    if (!activeChatElement) return null;
    const chatIds = activeChatElement.getAttribute("data-chat-id");
    if (!chatIds) return null;
    return await this.getChatById(chatIds);
    // this.processedData.chatList.find(
    // (chat) => chat.id === parseInt(chatIds)
    // );
  },

  /* تحديث المحادثة الحالية */
  async updateCurrentChat(updatedChat) {
    if (!updatedChat) return;

    await DBManager.updateChat(updatedChat);
  },

  /* تحديث المحادثة الحالية */
  async refreshCurrentChat() {
    const currentChat = await this.getCurrentChat();
    if (!currentChat) return;

    // Get the updated chat data
    const updatedChat = await this.getChatById(currentChat.id);
    if (updatedChat) {
      await this.updateCurrentChat(updatedChat);
    }
  },

  /* الحصول على محادثة بواسطة معرفها */
  async getChatById(chatId) {
    try {
      return await DBManager.getChatById(chatId);
    } catch (error) {
      console.error("Error getting chat by ID:", error);
      return null;
    }
  },
  async getChatByLocId(chatLocId) {
    try {
      return await DBManager.getChatByLocId(chatLocId);
    } catch (error) {
      console.error("Error getting chat by ID:", error);
      return null;
    }
  },
  /* إنشاء محادثة فردية جديدة */
  async createNewIndividualChat(user) {
    try {
      // التحقق من وجود محادثة سابقة
      const existingChat = await this.findExistingChat(user.id);
      if (existingChat) {
        ////////console.log('Existing chat found:', existingChat);
        return existingChat;
      }

      // إنشاء محادثة جديدة
      const newChat = {
        id: Number(user.phoneNumber), // معرف مؤقت
        name: user.userName,
        type: "individual",
        picture: user.picture || "",
        chatDescription: user?.about || "",
        unreadCount: 0,
        //locId: null,
        members: [
          {
            chatID: null,
            id: null,
            userID: user.id, // استخدام رقم الهاتف كمؤقت
            userRoleInChat: "member",
            dateJoined: new Date().toISOString(),
            user: user,
          },
        ],
        lastMessages: [],
        createdDate: new Date().toISOString(),
        lastMessageDate: new Date().toISOString(),
        isOffline: true, // محادثة غير متزامنة
      };

      // حفظ المحادثة محليًا
      const nChat = await DBManager.saveChats([newChat]);

      return nChat[0];
    } catch (error) {
      console.error("Error creating new individual chat:", error);
      throw error;
    }
  },
  /* البحث عن محادثة موجودة */
  async findExistingChat(recipientPhoneNumber) {
    try {
      const chats = await DBManager.getChats();
      return chats.find(
        (chat) =>
          chat.type === "individual" &&
          chat.members.some((member) => member.userID === recipientPhoneNumber)
      );
    } catch (error) {
      console.error("Error finding existing chat:", error);
      return null;
    }
  },

  /* معالجة الرسالة الأولى في محادثة جديدة */
  /* تحديث معرف المحادثة */
  async updateChatId(oldChatId, newChatId) {
    try {
      const chat = await this.getChatById(oldChatId);
      if (!chat) {
        throw new Error("Chat not found");
      }

      // تحديث معرف المحادثة
      chat.id = newChatId;
      chat.isOffline = false;

      chat.lastMessages.forEach((msg) => {
        msg.chatID = newChatId;
      });
      // حفظ التغييرات
      var chatsss = await DBManager.saveChats([chat]);

      // تحديث الرسائل المرتبطة
      const messages = await DBManager.getMessagesByChatId(oldChatId);
      for (const message of messages.messages) {
        message.chatID = newChatId;
        await DBManager._updateMessageRecord(message);
      }
      return chat;
    } catch (error) {
      console.error("Error updating chat ID:", error);
      throw error;
    }
  },
  async updateMessage(messageId, updates) {
    try {
      ////console.log(`Updating message ${messageId} with:`, updates);

      // Get the current message
      const messages = await DBManager.getMessages();
      const messageIndex = messages.findIndex((m) => m.locId === messageId);

      const messagesLoacl = await DBManager.getMessageById(messageId, "locId");
      //console.log("updateMessage messages[messageIndex]", messages[messageIndex])
      //console.log("updateMessage updateMessage", messagesLoacl)
      if (messageIndex === -1) {
        console.error(`Message with ID ${messageId} not found`);
        throw new Error(`Message with ID ${messageId} not found`);
      }

      // Update the message
      const updatedMessage = {
        ...messages[messageIndex],
        ...updates,
      };
      //await this.deleteMessage(messageId)
      // Save the updated message
      //console.log("updatedMessage updatedMessage", updatedMessage)
      await DBManager._updateMessageRecord(updatedMessage);
      //await DBManager.updateItem("messages", updatedMessage)
      //    .then((result) => {
      //        ////console.log("Update completed:", result);
      //    })
      //    .catch((error) => {
      //        console.error("Error updating the message:", error);
      //    });
      console.log(
        "updatedMessage",
        "Message updated successfully:",
        updatedMessage
      );

      // Update the chat's lastMessages array
      const chat = await DBManager.getChatById(updatedMessage.chatID);
      //console.log("Message updated successfully chat:", chat);

      chat.lastMessageDate = new Date(updatedMessage.createdDate).getTime();
      if (chat && chat.lastMessages) {
        const chatMessageIndex = chat.lastMessages.findIndex(
          (m) => m.locId === messageId
        );
        ////console.log("chatMessageIndex", chatMessageIndex);

        if (chatMessageIndex !== -1) {
          //chat.lastMessages[0] = updatedMessage;
          // Add the new message to the beginning of the array (newest first)
          chat.lastMessages.unshift(updatedMessage);
          await DBManager.saveChats([chat]);
          ////console.log("Updated message in chat's lastMessages",chat);
        }
      }
      //await this.populateChatList();
      return updatedMessage;
    } catch (error) {
      console.error("Error updating message:", error);
      throw error;
    }
  },
  /**
   * Remove a chat from the chat list and local storage
   * @param {string} chatId - ID of the chat to remove
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async removeChat(chatId) {
    try {
      if (!chatId) {
        console.error("Invalid chat ID provided to removeChat");
        return false;
      }

      // Remove from DOM
      const chatElement = document.querySelector(
        `.chats-group .block[data-chat-id="${chatId}"]`
      );
      if (chatElement) {
        chatElement.remove();
      }

      // Remove from local storage
      const chats = await this.getChatsFromStorage();
      const updatedChats = chats.filter((chat) => chat.id !== chatId);

      // Update local storage
      localStorage.setItem("chats", JSON.stringify(updatedChats));

      // If this was the active chat, clear the chat area
      const activeChat = this.getActiveChatId();
      if (activeChat === chatId) {
        // Clear chat area
        const chatArea = document.querySelector(".chat-box");
        if (chatArea) {
          chatArea.innerHTML = "";
        }

        // Reset active chat
        this.setActiveChatId(null);
      }
      return true;
    } catch (error) {
      console.error("Error removing chat:", error);
      return false;
    }
  },
  async removeChat(chatId) {
    try {
      // 1. Get the chat from the database
      const chat = await this.getChatById(chatId);
      if (!chat) {
        console.warn(`Chat with ID ${chatId} not found.`);
        return false;
      }

      // 2. Remove the chat from the database by its locId (primary key)
      await DBManager.deleteChat(chat.locId);

      // 3. Remove the chat from the in-memory list
      const chatIndex = this.processedData.chatList.findIndex(
        (c) => c.id === chatId
      );
      if (chatIndex !== -1) {
        this.processedData.chatList.splice(chatIndex, 1);
      }

      // 4. Remove related messages to keep database clean
      const messagesResult = await DBManager.getMessagesByChatId(chatId);
      if (
        messagesResult &&
        messagesResult.messages &&
        messagesResult.messages.length > 0
      ) {
        for (const message of messagesResult.messages) {
          await DBManager.deleteMessage(message.locId);
        }
      }
      return true;
    } catch (error) {
      console.error(`Error removing chat ${chatId}:`, error);
      throw error;
    }
  },
};
