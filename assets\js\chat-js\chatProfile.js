﻿/**
 * ChatProfile - إدارة وظائف ملف تعريف المحادثة
 * يتعامل مع عرض وإدارة معلومات ملف تعريف المحادثة للمحادثات الفردية والمجموعات وجهات الاتصال
 */
class ChatProfile {
  /**
   * إنشاء كائن جديد من ChatProfile
   */
  constructor() {
    // تهيئة العناصر الأساسية
    this.container = document.getElementById("ChatAbout");
    this.currentChat = null;
    this.currentContact = null;
    this.mediaItems = [];
    this.groupMembers = [];
    this.currentPage = 1;
    this.hasMoreMembers = false;
    this.ajaxManager = new AjaxManager();
    this.chatType = null; // 'Individual', 'Group', أو 'Contact'
    this.userProcessor = null;
    this.selectedContacts = []; // جهات الاتصال المحددة للإضافة إلى المجموعة
    this.groupModalElement = null; // عنصر النافذة المنبثقة
    this.count = 1;

    this.initialize();
  }

  /**
   * تهيئة الفئة
   */
  initialize() {
    this.userProcessor = new UserProcessor();
    this.initializeEventListeners();
  }

  /**
   * تهيئة مستمعي الأحداث
   */
  initializeEventListeners() {
    // زر الإغلاق
    const closeButton = this.container.querySelector(".ICON button");
    if (closeButton) {
      closeButton.addEventListener("click", () => this.close());
    }

    // زر الحظر/مغادرة المجموعة
    const blockButton = this.container.querySelector(".Block-head:first-child");
    if (blockButton) {
      blockButton.addEventListener("click", () =>
        this.handleBlockOrLeaveGroup()
      );
    }

    // زر حذف المحادثة
    const deleteButton = this.container.querySelector(".Block-head:last-child");
    if (deleteButton) {
      deleteButton.addEventListener("click", () => this.handleDeleteChat());
    }

    // زر إضافة أعضاء (للمجموعات)
    const addMembersButton = this.container.querySelector(".add-members-btn");
    if (addMembersButton) {
      addMembersButton.addEventListener("click", () => this.handleAddMembers());
    }

    // زر إظهار المزيد من الأعضاء (للمجموعات)
    const showMoreButton = this.container.querySelector(
      ".show-more-members-btn"
    );
    if (showMoreButton) {
      showMoreButton.addEventListener("click", () => this.loadMoreMembers());
    }

    // مربع البحث عن الأعضاء (للمجموعات)
    const searchInput = this.container.querySelector(".search-members-input");
    if (searchInput) {
      searchInput.addEventListener("input", (e) =>
        this.searchMembers(e.target.value)
      );
    }
  }

  /**
   * فتح ملف تعريف المحادثة
   * @param {Object} chat - كائن المحادثة الذي يحتوي على المعلومات
   * @param {string} type - نوع الملف الشخصي ('chat', 'group', أو 'contact')
   */
  open(chat, type) {
    //console.log("Opening chat profile:", chat, type);

    // تحديد نوع المحادثة الفعلي
    if (type === "chat" && chat.type === "Group") {
      this.chatType = "Group";
    } else if (type === "chat") {
      this.chatType = "Individual";
    } else if (type === "group") {
      this.chatType = "Group";
    } else {
      this.chatType = "Contact";
    }

    this.currentChat = type === "chat" || type === "group" ? chat : null;
    this.currentContact = type === "contact" ? chat : null;

    // إعادة تعيين الحالة
    this.mediaItems = [];
    this.groupMembers = [];
    this.currentPage = 1;
    this.hasMoreMembers = false;

    // تحديث واجهة المستخدم بناءً على نوع المحادثة
    this.updateUI(chat, type);

    // عرض الحاوية مع الرسوم المتحركة
    this.container.classList.add("animate__fadeInRight");
    this.container.style.display = "block";

    // تحميل البيانات الإضافية
    if (type === "chat" || type === "group") {
      // تحميل الوسائط لجميع أنواع المحادثات
      //this.loadMediaItems(chat.id);

      // تحميل أعضاء المجموعة فقط للمحادثات الجماعية
      if (this.chatType === "Group") {
        this.loadGroupMembers(chat.id);
      }
    }
  }

  /**
   * إغلاق ملف تعريف المحادثة
   */
  close() {
    // إغلاق بسيط بدون رسوم متحركة لتوافق أفضل
    this.container.style.display = "none";

    // بديل مع الرسوم المتحركة إذا لزم الأمر
    /*
        this.container.classList.remove("animate__fadeInRight");
        this.container.classList.add("animate__fadeOutRight");
        setTimeout(() => {
          this.container.style.display = "none";
          this.container.classList.remove("animate__fadeOutRight");
        }, 300);
        */
  }

  /**
   * تحديث واجهة المستخدم بمعلومات المحادثة
   * @param {Object} chat - كائن المحادثة الذي يحتوي على المعلومات
   * @param {string} type - نوع الملف الشخصي
   */
  updateUI(chat, type) {
    //console.log("Updating UI for chat type:", this.chatType);

    // تحديث رأس الملف الشخصي
    this.updateProfileHeader(chat, type);

    // تحديث رؤية الأقسام بناءً على نوع المحادثة
    if (this.chatType === "Group") {
      this.showGroupProfileSections();
    } else {
      this.showIndividualProfileSections();
    }

    // تحديث نص الحظر/التقرير بناءً على نوع المحادثة
    this.updateActionButtons(chat, type);
  }

  /**
   * تحديث رأس الملف الشخصي بالمعلومات الأساسية
   * @param {Object} chat - كائن المحادثة
   * @param {string} type - نوع الملف الشخصي
   */
  updateProfileHeader(chat, type) {
    // تحديث صورة الملف الشخصي
    const profileImage = this.container.querySelector(".img-animated");
    if (profileImage) {
      profileImage.src =
        type === "chat" || type === "group"
          ? chat.picture || "/assets/avatars/imageGrey.jpg"
          : chat.picture || "/assets/avatars/imageGrey.jpg";

      profileImage.alt =
        type === "chat" || type === "group"
          ? chat.name || "Profile"
          : chat.userName || "Profile";
    }

    // تحديث الاسم
    const nameElement = this.container.querySelector(".text-Ani h3");
    if (nameElement) {
      nameElement.textContent =
        type === "chat" || type === "group"
          ? chat.name || ""
          : chat.userName || "";
    }

    // تحديث الهاتف / عدد الأعضاء
    const detailsElement = this.container.querySelector(".text-Ani p");
    if (detailsElement) {
      if (this.chatType === "Individual" || this.chatType === "Contact") {
        detailsElement.textContent =
          type === "chat"
            ? chat.members && chat.members[0] && chat.members[0].user
              ? chat.members[0].user.phoneNumber || ""
              : ""
            : chat.phoneNumber || "";
      } else if (this.chatType === "Group") {
        detailsElement.textContent = `${
          chat.members ? chat.members.length : 0
        } أعضاء`;
      }
    }

    // تحديث حول / الوصف
    const aboutElement = this.container.querySelector(".bio .text-inner h4");
    if (aboutElement) {
      aboutElement.textContent =
        type === "chat" || type === "group"
          ? chat.chatDescription || ""
          : chat.About || "";
    }

    // تحديث عدد الأعضاء للمحادثات الجماعية
    if (this.chatType === "Group") {
      const membersCountElement =
        this.container.querySelector(".members-count h6");
      if (membersCountElement) {
        membersCountElement.textContent = chat.members
          ? chat.members.length.toString()
          : "0";
      }
    }
  }

  /**
   * تحديث أزرار الإجراء (حظر / مغادرة / حذف) بناءً على نوع المحادثة
   * @param {Object} chat - كائن المحادثة
   * @param {string} type - نوع الملف الشخصي
   */
  updateActionButtons(chat, type) {
    // تحديث نص الحظر / المغادرة
    const blockText = this.container.querySelector(
      ".Block-head:first-child .star-text"
    );
    if (blockText) {
      if (this.chatType === "Individual" || this.chatType === "Contact") {
        blockText.textContent =
          type === "chat" || type === "group"
            ? `حظر ${chat.name}`
            : `حظر ${chat.userName}`;
      } else if (this.chatType === "Group") {
        blockText.textContent = "مغادرة المجموعة";
      }
    }

    // تحديث نص الحذف
    const deleteText = this.container.querySelector(
      ".Block-head:last-child .star-text"
    );
    if (deleteText) {
      deleteText.textContent = "حذف";
    }
  }

  /**
   * عرض الأقسام ذات الصلة بملفات تعريف المجموعة
   */
  showGroupProfileSections() {
    // عرض عناصر محددة للمجموعة
    const groupElements = this.container.querySelectorAll(".group-only");
    groupElements.forEach((el) => el.classList.remove("d-none"));

    // إخفاء العناصر المخصصة للأفراد
    const individualElements =
      this.container.querySelectorAll(".individual-only");
    if (individualElements.length > 0) {
      individualElements.forEach((el) => el.classList.add("d-none"));
    }
  }

  /**
   * عرض الأقسام ذات الصلة بالملفات الشخصية الفردية
   */
  showIndividualProfileSections() {
    // إخفاء العناصر المخصصة للمجموعة
    const groupElements = this.container.querySelectorAll(".group-only");
    groupElements.forEach((el) => el.classList.add("d-none"));

    // عرض العناصر المخصصة للأفراد إن وجدت
    const individualElements =
      this.container.querySelectorAll(".individual-only");
    if (individualElements.length > 0) {
      individualElements.forEach((el) => el.classList.remove("d-none"));
    }
  }

  /**
   * عرض إشعار للمستخدم
   * @param {string} message - الرسالة المراد عرضها
   * @param {string} type - نوع الإشعار (success, error, warning)
   * @param {number} duration - المدة بالمللي ثانية
   */
  showToast(message, type = "success", duration = 3000) {
    // إنشاء عنصر الإشعار
    const toast = document.createElement("div");
    toast.className = `chat-toast toast-${type} animate__animated animate__fadeIn`;
    toast.style.position = "fixed";
    toast.style.bottom = "20px";
    toast.style.right = "20px";
    toast.style.padding = "10px 20px";
    toast.style.borderRadius = "4px";
    toast.style.color = "#fff";
    toast.style.zIndex = "9999";
    toast.style.minWidth = "250px";
    toast.style.boxShadow = "0 2px 8px rgba(0,0,0,0.2)";
    toast.style.direction = "rtl";
    toast.style.textAlign = "right";

    // تعيين لون الخلفية بناءً على النوع
    switch (type) {
      case "error":
        toast.style.backgroundColor = "#dc3545";
        break;
      case "warning":
        toast.style.backgroundColor = "#ffc107";
        toast.style.color = "#333";
        break;
      default: // success
        toast.style.backgroundColor = "#28a745";
    }

    // إضافة رمز بناءً على النوع
    let icon = "";
    switch (type) {
      case "error":
        icon = "✕";
        break;
      case "warning":
        icon = "⚠";
        break;
      default: // success
        icon = "✓";
    }

    toast.innerHTML = `
      <div style="display:flex;align-items:center;">
        <span style="background-color:rgba(255,255,255,0.2);border-radius:50%;width:24px;height:24px;display:flex;align-items:center;justify-content:center;margin-left:10px;">${icon}</span>
        <span>${message}</span>
      </div>
    `;

    // إضافة إلى DOM
    document.body.appendChild(toast);

    // إزالة بعد المدة المحددة
    setTimeout(() => {
      toast.classList.replace("animate__fadeIn", "animate__fadeOut");
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 300);
    }, duration);
  }

  /**
   * تحميل أعضاء المجموعة
   * @param {number} chatId - معرف المحادثة
   */
  async loadGroupMembers(chatId) {
    try {
      // أولاً محاولة التحميل من التخزين المحلي
      const localMembers = await this.loadMembersFromLocalStorage(chatId);

      if (localMembers && localMembers.length > 0) {
        this.groupMembers = localMembers;
        this.updateMembersSection();
      }

      // التحقق مما إذا كنا بحاجة إلى التحديث من الخادم
      const shouldRefresh = await this.shouldRefreshMembers(
        chatId,
        localMembers.length
      );

      if (shouldRefresh) {
        // جلب من الخادم للتأكد من أن لدينا أحدث البيانات
        const response = await this.ajaxManager.get(
          `Chat/Group/Memebers/${chatId}`
        );
        //console.log("response", response);
        if (response && response.pageData) {
          this.groupMembers = response.pageData;
          this.updateMembersSection();

          // حفظ في التخزين المحلي للاستخدام المستقبلي
          await this.saveMembersToLocalStorage(chatId, response.pageData);
        }
      }
    } catch (error) {
      console.error("Error loading group members:", error);
      // إذا فشل جلب الخادم، لا يزال لدينا البيانات المحلية المعروضة
    }
  }

  /**
   * التحقق مما إذا كان يجب تحديث الأعضاء من الخادم
   * @param {number} chatId - معرف المحادثة
   * @param {number} localCount - عدد الأعضاء في التخزين المحلي
   * @returns {Promise<boolean>} - ما إذا كان يجب التحديث من الخادم
   */
  async shouldRefreshMembers(chatId, localCount) {
    try {
      // الحصول على عدد الأعضاء من الخادم
      const response = await this.loadMembersFromLocalStorage(chatId);

      if (response) {
        // إذا كان عدد الخادم يختلف عن العدد المحلي، يجب أن نقوم بالتحديث
        return response.length !== localCount;
      }

      return true; // التحديث بشكل افتراضي إذا لم نتمكن من التحديد
    } catch (error) {
      console.error("Error checking member count:", error);
      return false; // لا تقم بالتحديث إذا كان هناك خطأ
    }
  }

  /**
   * تحميل الأعضاء من التخزين المحلي
   * @param {number} chatId - معرف المحادثة
   * @returns {Promise<Array>} - أعضاء المجموعة
   */
  async loadMembersFromLocalStorage(chatId) {
    try {
      // الحصول على المحادثة من التخزين المحلي
      const chat = await DBManager.getChatById(chatId);

      if (chat && chat.members) {
        return chat.members;
      }

      return [];
    } catch (error) {
      console.error("Error loading members from local storage:", error);
      return [];
    }
  }

  /**
   * حفظ الأعضاء في التخزين المحلي
   * @param {number} chatId - معرف المحادثة
   * @param {Array} members - الأعضاء المراد حفظهم
   */
  async saveMembersToLocalStorage(chatId, members) {
    try {
      // الحصول على المحادثة من التخزين المحلي
      const chat = await DBManager.getChatById(chatId);

      if (chat) {
        // تحديث الأعضاء
        chat.members = members;

        // حفظ المحادثة المحدثة
        await DBManager.updateChat(chat);
        //console.log("Members saved to local storage");
      }
    } catch (error) {
      console.error("Error saving members to local storage:", error);
    }
  }

  /**
   * تحديث قسم الأعضاء مع الأعضاء المحملين
   */
  updateMembersSection() {
    const membersContainer = this.container.querySelector(".members-container");
    if (!membersContainer) return;

    if (this.groupMembers.length === 0) {
      membersContainer.innerHTML =
        '<div class="no-members-message">لا يوجد أعضاء</div>';
      return;
    }

    // مسح المحتوى الحالي
    membersContainer.innerHTML = "";

    // ترتيب الأعضاء: المستخدم الحالي أولاً، ثم المالك، ثم المشرفين، ثم الأعضاء العاديين
    const sortedMembers = this.sortGroupMembers();

    // إضافة أول 10 أعضاء (أو الكل إذا كان أقل من 10)
    const initialMembers = sortedMembers.slice(0, 10);
    initialMembers.forEach((member) => {
      const memberElement = this.createMemberElement(member);
      membersContainer.appendChild(memberElement);
    });

    // إظهار/إخفاء زر "عرض المزيد"
    const showMoreButton = this.container.querySelector(
      ".show-more-members-btn"
    );
    if (showMoreButton) {
      if (sortedMembers.length > 10) {
        showMoreButton.classList.remove("d-none");
        this.hasMoreMembers = true;
      } else {
        showMoreButton.classList.add("d-none");
        this.hasMoreMembers = false;
      }
    }
  }

  /**
   * ترتيب أعضاء المجموعة حسب الدور
   * @returns {Array} - الأعضاء المرتبين
   */
  sortGroupMembers() {
    // الحصول على معرف المستخدم الحالي
    const currentUserId = ChatProcessor.processedData.currentUser?.id;

    return [...this.groupMembers].sort((a, b) => {
      // المستخدم الحالي أولاً
      if (a.userID === currentUserId) return -1;
      if (b.userID === currentUserId) return 1;

      // ثم مالك المجموعة
      if (a.userRoleInChat === "Owner") return -1;
      if (b.userRoleInChat === "Owner") return 1;

      // ثم المشرفين
      if (a.userRoleInChat === "Admin" && b.userRoleInChat !== "Admin")
        return -1;
      if (a.userRoleInChat !== "Admin" && b.userRoleInChat === "Admin")
        return 1;

      // ثم حسب الاسم
      return (a.user?.userName || "").localeCompare(b.user?.userName || "");
    });
  }

  /**
   * إنشاء عنصر عضو للعرض
   * @param {Object} member - كائن العضو
   * @returns {HTMLElement} - عنصر العضو
   */
  createMemberElement(member) {
    const element = document.createElement("div");
    element.className = "member-item";
    element.dataset.memberId = member.id;
    element.dataset.userId = member.userID;

    // الحصول على تسمية الدور
    const roleLabel = this.getMemberRoleLabel(member.userRoleInChat);

    // الحصول على أيقونة الدور
    const roleIcon = this.getMemberRoleIcon(member.userRoleInChat);

    // التحقق مما إذا كان هذا هو المستخدم الحالي
    const isCurrentUser =
      member.userID === ChatProcessor.processedData.currentUser?.id;

    // إنشاء HTML لعنصر العضو
    element.innerHTML = `
            <div class="member-avatar">
                <img src="${
                  member.user?.picture || "/assets/avatars/imageGrey.jpg"
                }" alt="${member.user?.userName || "Member"}">
            </div>
            <div class="member-info">
                <div class="member-info-name">
                   <h6>${member.user?.userName || "Unknown Member"}
                    ${
                      isCurrentUser
                        ? '<span class="current-user-label">(أنت)</span>'
                        : ""
                    }
                    </h6>
                   ${
                     member.memberType === "TeamMember"
                       ? '<div class="member-type team-member">عضو فريق </div>'
                       : ""
                   }
                </div>
                <div class="status-role-member">
                    <h5 class="member-status">
                        ${
                          member.user.statusConnection?.statusConnection
                            ? "متصل"
                            : `اخر ظهور ${mDate(
                                chat.members?.[0].user.statusConnection
                                  .lastSeenDatetime
                              ).lastSeenFormat()}`
                        }
  </h5>
 <div class="member-role">
                    ${roleIcon}
                    <span>${roleLabel}</span>
                </div>
</div>
            </div>
        `;

    // إضافة حدث النقر لإظهار إجراءات العضو
    element.addEventListener("click", () =>
      this.handleMemberClick(member, element)
    );

    return element;
  }

  /**
   * الحصول على تسمية لدور العضو
   * @param {string} role - دور العضو
   * @returns {string} - تسمية الدور
   */
  getMemberRoleLabel(role) {
    switch (role) {
      case "Owner":
        return "مالك المجموعة";
      case "Admin":
        return "مشرف";
      default:
        return "عضو";
    }
  }

  /**
   * الحصول على أيقونة لدور العضو
   * @param {string} role - دور العضو
   * @returns {string} - HTML لأيقونة الدور
   */
  getMemberRoleIcon(role) {
    switch (role) {
      case "Owner":
        return '<i class="fe fe-crown role-icon owner-icon"></i>';
      case "Admin":
        return '<i class="fe fe-star role-icon admin-icon"></i>';
      default:
        return "";
    }
  }

  /**
   * الحصول على دور المستخدم الحالي في المجموعة
   * @returns {string} - دور المستخدم
   */
  getCurrentUserRole() {
    const currentUserId = ChatProcessor.processedData.currentUser?.id;
    const currentMember = this.groupMembers.find(
      (m) => m.userID === currentUserId
    );
    return currentMember ? currentMember.userRoleInChat : "Member";
  }

  /**
   * معالجة نقر العضو (ضغط قصير)
   * @param {Object} member - كائن العضو
   * @param {HTMLElement} element - العنصر الذي تم النقر عليه (اختياري)
   */
  async handleMemberClick(member, element = null) {
    // لا تفعل شيئًا للمستخدم الحالي
    if (member.userID === ChatProcessor.processedData.currentUser?.id) {
      return;
    }

    try {
      // التحقق مما إذا كان العضو في جهات الاتصال
      const isContact = true;

      // التحقق مما إذا كانت هناك محادثة موجودة
      const existingChat = await ChatProcessor.getUserChats(member.userID);
      const hasChat = Array.isArray(existingChat) && existingChat.length > 0;

      // عرض نافذة منبثقة للإجراء مع جميع الخيارات (كلاً من خيارات المراسلة والإدارة)
      this.showMemberActionPopup(member, isContact, hasChat, element);
    } catch (error) {
      console.error("Error handling member click:", error);
    }
  }

  /**
   * تحميل المزيد من الأعضاء (عند النقر على زر "عرض المزيد")
   */
  loadMoreMembers() {
    const membersContainer = this.container.querySelector(".members-container");
    if (!membersContainer) return;

    // ترتيب الأعضاء
    const sortedMembers = this.sortGroupMembers();

    // حساب الأعضاء المراد عرضهم بعد ذلك
    const startIndex = 10 + (this.currentPage - 1) * 10;
    const endIndex = startIndex + 10;
    const nextMembers = sortedMembers.slice(startIndex, endIndex);

    // إضافة الأعضاء إلى الحاوية
    nextMembers.forEach((member) => {
      const memberElement = this.createMemberElement(member);
      membersContainer.appendChild(memberElement);
    });

    // تحديث عداد الصفحة
    this.currentPage++;

    // إخفاء زر "عرض المزيد" إذا لم يكن هناك المزيد من الأعضاء
    if (endIndex >= sortedMembers.length) {
      const showMoreButton = this.container.querySelector(
        ".show-more-members-btn"
      );
      if (showMoreButton) {
        showMoreButton.classList.add("d-none");
      }
      this.hasMoreMembers = false;
    }
  }

  /**
   * البحث عن الأعضاء بالاسم
   * @param {string} query - استعلام البحث
   */
  searchMembers(query) {
    const membersContainer = this.container.querySelector(".members-container");
    if (!membersContainer) return;
    //console.log("filteredMembers", query);

    // إذا كان الاستعلام فارغًا، عرض جميع الأعضاء
    if (!query.trim()) {
      this.updateMembersSection();
      return;
    }

    // تصفية الأعضاء حسب الاسم
    const filteredMembers = this.groupMembers.filter((member) =>
      member.user?.userName?.toLowerCase().includes(query.toLowerCase())
    );
    // مسح الحاوية
    membersContainer.innerHTML = "";

    if (filteredMembers.length === 0) {
      membersContainer.innerHTML =
        '<div class="no-members-message">لا توجد نتائج</div>';
      return;
    }

    // ترتيب الأعضاء المصفاة
    const sortedMembers = this.sortGroupMembers(filteredMembers);

    // إضافة الأعضاء المصفاة إلى الحاوية
    sortedMembers.forEach((member) => {
      const memberElement = this.createMemberElement(member);
      membersContainer.appendChild(memberElement);
    });

    // إخفاء زر "عرض المزيد" أثناء البحث
    const showMoreButton = this.container.querySelector(
      ".show-more-members-btn"
    );
    if (showMoreButton) {
      showMoreButton.classList.add("d-none");
    }
  }

  /**
   * عرض نافذة منبثقة للإجراءات للعضو مع جميع الخيارات
   * @param {Object} member - كائن العضو
   * @param {boolean} isContact - ما إذا كان العضو جهة اتصال
   * @param {Object|null} existingChat - المحادثة الموجودة إن وجدت
   * @param {HTMLElement} element - العنصر المراد وضعه بالقرب منه (اختياري)
   */
  showMemberActionPopup(member, isContact, existingChat, element = null) {
    // إنشاء عنصر النافذة المنبثقة
    const popup = document.createElement("div");
    popup.className = "member-action-popup";

    // الحصول على دور المستخدم الحالي للإجراءات الإدارية
    const currentUserRole = this.getCurrentUserRole();
    const isCurrentUser =
      member.userID === ChatProcessor.processedData.currentUser?.id;

    // إضافة خيارات بناءً على الشروط
    let options = "";

    // إضافة خيارات الإدارة إذا كان المستخدم مشرفًا ولا يتصرف على نفسه
    if (
      (currentUserRole === "Owner" || currentUserRole === "Admin") &&
      !isCurrentUser
    ) {
      // خيار إزالة العضو (للمشرفين والمالكين)
      if (
        currentUserRole === "Owner" ||
        (currentUserRole === "Admin" && member.userRoleInChat !== "Owner")
      ) {
        options += `
          <div class="popup-option remove-option" data-action="remove">
            <i class="fe fe-user-x"></i>
            <span>إخراج العضو</span>
          </div>
        `;
      }

      // خيار تغيير الدور (فقط للمالكين)
      if (currentUserRole === "Owner" || currentUserRole === "Admin") {
        const newRole = member.userRoleInChat === "Admin" ? "Member" : "Admin";
        const actionLabel =
          member.userRoleInChat === "Admin"
            ? "إزالة صلاحية المشرف"
            : "تعيين كمشرف";

        options += `
          <div class="popup-option role-option" data-action="change-role" data-new-role="${newRole}">
                        <i class="fe fe-${
                          member.userRoleInChat === "Admin"
                            ? "user-minus"
                            : "user-plus"
                        }"></i>
            <span>${actionLabel}</span>
          </div>
        `;
      }
    }

    // إذا لم تكن هناك خيارات متاحة، لا تعرض النافذة المنبثقة
    if (!options.trim()) {
      return;
    }

    popup.innerHTML = `
      <div class="popup-header">
                <img src="${
                  member.user?.picture || "/assets/avatars/imageGrey.jpg"
                }" alt="${member.user?.userName || "Member"}">
        <div class="popup-title">
          <h6>${member.user?.userName || "Unknown Member"}</h6>
                    <span>${member.user?.phoneNumber || "Unknown Member"}</span>
        </div>
      </div>
      <div class="popup-options">
        ${options}
      </div>
    `;

    // إضافة إلى DOM
    document.body.appendChild(popup);

    // تحديد موقع النافذة المنبثقة
    this.positionPopup(popup);

    // إضافة مستمعي الأحداث
    popup.querySelectorAll(".popup-option").forEach((option) => {
      option.addEventListener("click", () => {
        const action = option.dataset.action;

        // التعامل مع الإجراءات المختلفة
        if (
          action === "message" ||
          action === "new-message" ||
          action === "profile" ||
          action === "add-contact"
        ) {
          this.handleMemberAction(action, member, existingChat);
        } else if (action === "remove") {
          this.handleRemoveMember(member);
        } else if (action === "change-role") {
          const newRole = option.dataset.newRole;
          this.handleChangeRole(member, newRole);
        }

        popup.remove();
      });
    });

    // إغلاق النافذة المنبثقة عند النقر خارجها
    document.addEventListener("click", function closePopup(e) {
      if (!popup.contains(e.target)) {
        popup.remove();
        document.removeEventListener("click", closePopup);
      }
    });
  }

  /**
   * تحديد موقع النافذة المنبثقة
   * @param {HTMLElement} popup - عنصر النافذة المنبثقة
   */
  positionPopup(popup) {
    // الحصول على أبعاد النافذة
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // توسيط النافذة المنبثقة
    const popupWidth = 300; // العرض التقريبي
    const popupHeight = 200; // الارتفاع التقريبي

    popup.style.left = `${(windowWidth - popupWidth) / 2}px`;
    popup.style.top = `${(windowHeight - popupHeight) / 2}px`;
  }

  /**
   * معالجة إجراء العضو من النافذة المنبثقة
   * @param {string} action - الإجراء المراد تنفيذه
   * @param {Object} member - كائن العضو
   * @param {Object|null} existingChat - المحادثة الموجودة إن وجدت
   */
  async handleMemberAction(action, member, existingChat) {
    switch (action) {
      case "message":
        // فتح المحادثة الموجودة
        if (existingChat) {
          this.close(); // إغلاق الملف الشخصي
          const chatElement = document.querySelector(
            `[data-chat-id="${existingChat.id}"]`
          );
          if (chatElement) {
            generateMessageArea(chatElement, existingChat.id);
          }
        }
        break;

      case "new-message":
        // إنشاء محادثة جديدة
        this.close(); // إغلاق الملف الشخصي
        try {
          const newChat = await ChatProcessor.createNewIndividualChat(
            member.user
          );
          if (newChat) {
            const chatElement = document.querySelector(
              `[data-chat-locid="${newChat.locId}"]`
            );
            if (chatElement) {
              generateMessageArea(chatElement, newChat.locId);
            }
          }
        } catch (error) {
          console.error("Error creating new chat:", error);
        }
        break;

      case "profile":
        // فتح الملف الشخصي للمحادثة الفردية
        this.close(); // إغلاق ملف تعريف المجموعة
        if (existingChat) {
          // استخدام نسخة chatProfile العالمية بدلاً من إنشاء ChatAbout جديد
          if (window.chatProfile) {
            window.chatProfile.open(member.user, "contact");
          }
        }
        break;
    }
  }

  /**
   * معالجة إزالة عضو
   * @param {Object} member - العضو المراد إزالته
   */
  async handleRemoveMember(member) {
    // إنشاء مربع حوار تأكيدي متقدم
    const confirmDialog = document.createElement("div");
    confirmDialog.className =
      "confirmation-dialog animate__animated animate__fadeIn";
    confirmDialog.style.position = "fixed";
    confirmDialog.style.top = "50%";
    confirmDialog.style.left = "50%";
    confirmDialog.style.transform = "translate(-50%, -50%)";
    confirmDialog.style.backgroundColor = "#fff";
    confirmDialog.style.borderRadius = "8px";
    confirmDialog.style.boxShadow = "0 4px 20px rgba(0,0,0,0.15)";
    confirmDialog.style.padding = "20px";
    confirmDialog.style.zIndex = "9999";
    confirmDialog.style.minWidth = "300px";
    confirmDialog.style.maxWidth = "400px";
    confirmDialog.style.textAlign = "center";
    confirmDialog.style.direction = "rtl";

    const memberName = member.user?.userName || "هذا العضو";

    confirmDialog.innerHTML = `
      <h3 style="margin-top:0;color:#333;font-size:18px;">تأكيد الإزالة</h3>
      <p style="color:#666;margin-bottom:20px;">هل أنت متأكد أنك تريد إزالة ${memberName} من المجموعة؟</p>
      <div style="display:flex;justify-content:center;gap:10px;">
        <button class="confirm-remove-btn" style="background-color:#dc3545;color:white;border:none;padding:8px 16px;border-radius:4px;cursor:pointer;">إزالة</button>
        <button class="cancel-remove-btn" style="background-color:#f8f9fa;border:1px solid #ddd;padding:8px 16px;border-radius:4px;cursor:pointer;">إلغاء</button>
      </div>
    `;

    // إضافة مربع الحوار للصفحة
    document.body.appendChild(confirmDialog);

    // إضافة overlay لخلفية معتمة
    const overlay = document.createElement("div");
    overlay.className = "dialog-overlay";
    overlay.style.position = "fixed";
    overlay.style.top = "0";
    overlay.style.left = "0";
    overlay.style.right = "0";
    overlay.style.bottom = "0";
    overlay.style.backgroundColor = "rgba(0,0,0,0.5)";
    overlay.style.zIndex = "9998";
    document.body.appendChild(overlay);

    // إعداد الأحداث للأزرار
    return new Promise((resolve) => {
      const confirmBtn = confirmDialog.querySelector(".confirm-remove-btn");
      const cancelBtn = confirmDialog.querySelector(".cancel-remove-btn");

      // عند النقر على زر الإلغاء
      cancelBtn.addEventListener("click", () => {
        confirmDialog.classList.replace("animate__fadeIn", "animate__fadeOut");
        overlay.style.opacity = "0";
        setTimeout(() => {
          document.body.removeChild(confirmDialog);
          document.body.removeChild(overlay);
        }, 300);
        resolve(false);
      });

      // عند النقر على زر التأكيد
      confirmBtn.addEventListener("click", async () => {
        try {
          // تغيير نص الزر وتعطيله
          confirmBtn.textContent = "جاري الإزالة...";
          confirmBtn.disabled = true;
          cancelBtn.disabled = true;

          // استدعاء API وفقاً للمواصفات الجديدة
          const response = await this.ajaxManager.post(
            `Chat/Group/RemoveGroupMember`,
            {
              ChatID: this.currentChat.id,
              UserId: member.userID,
              IsLeave: true, // إزالة بواسطة المشرف وليس مغادرة ذاتية
            }
          );

          confirmDialog.classList.replace(
            "animate__fadeIn",
            "animate__fadeOut"
          );
          overlay.style.opacity = "0";
          setTimeout(() => {
            document.body.removeChild(confirmDialog);
            document.body.removeChild(overlay);
          }, 300);

          if (
            response &&
            (response.success ||
              response.resCode === 200 ||
              response.resCode === 201)
          ) {
            // إزالة العضو من القائمة المحلية
            this.groupMembers = this.groupMembers.filter(
              (m) => m.userID !== member.userID
            );

            // تحديث واجهة المستخدم
            this.updateMembersSection();

            // تحديث التخزين المحلي
            await this.saveMembersToLocalStorage(
              this.currentChat.id,
              this.groupMembers
            );

            // إظهار رسالة نجاح
            this.showToast(`تمت إزالة ${memberName} بنجاح`);
            resolve(true);
          } else {
            this.showToast("لا يمكن إزالة هذا العضو", "error");
            resolve(false);
          }
        } catch (error) {
          console.error("Error removing member:", error);
          this.showToast("فشل في إزالة العضو", "error");
          confirmDialog.classList.replace(
            "animate__fadeIn",
            "animate__fadeOut"
          );
          overlay.style.opacity = "0";
          setTimeout(() => {
            document.body.removeChild(confirmDialog);
            document.body.removeChild(overlay);
          }, 300);
          resolve(false);
        }
      });

      // إغلاق عند النقر خارج مربع الحوار
      overlay.addEventListener("click", () => {
        confirmDialog.classList.replace("animate__fadeIn", "animate__fadeOut");
        overlay.style.opacity = "0";
        setTimeout(() => {
          document.body.removeChild(confirmDialog);
          document.body.removeChild(overlay);
        }, 300);
        resolve(false);
      });
    });
  }

  /**
   * معالجة تغيير دور العضو
   * @param {Object} member - العضو المراد تغيير دوره
   * @param {string} newRole - الدور الجديد
   */
  async handleChangeRole(member, newRole) {
    // تحديد تسمية الدور للعرض
    const roleLabel = newRole === "Admin" ? "مشرف" : "عضو عادي";
    const userRoleValue = newRole === "Admin" ? 0 : 1; // Admin = 2, Member = 1
    const memberName = member.user?.userName || "هذا العضو";

    // إنشاء مربع حوار تأكيد متقدم
    const confirmDialog = document.createElement("div");
    confirmDialog.className =
      "confirmation-dialog animate__animated animate__fadeIn";
    confirmDialog.style.position = "fixed";
    confirmDialog.style.top = "50%";
    confirmDialog.style.left = "50%";
    confirmDialog.style.transform = "translate(-50%, -50%)";
    confirmDialog.style.backgroundColor = "#fff";
    confirmDialog.style.borderRadius = "8px";
    confirmDialog.style.boxShadow = "0 4px 20px rgba(0,0,0,0.15)";
    confirmDialog.style.padding = "20px";
    confirmDialog.style.zIndex = "9999";
    confirmDialog.style.minWidth = "300px";
    confirmDialog.style.maxWidth = "400px";
    confirmDialog.style.textAlign = "center";
    confirmDialog.style.direction = "rtl";

    // تخصيص محتوى مربع الحوار حسب نوع التغيير (ترقية أو تخفيض)
    const dialogTitle =
      newRole === "Admin" ? "ترقية عضو" : "تخفيض صلاحيات مشرف";
    const actionText = newRole === "Admin" ? "ترقية" : "تخفيض";
    const actionColor = newRole === "Admin" ? "#28a745" : "#ffc107"; // أخضر للترقية، أصفر للتخفيض

    confirmDialog.innerHTML = `
      <h3 style="margin-top:0;color:#333;font-size:18px;">${dialogTitle}</h3>
      <div style="display:flex;align-items:center;justify-content:center;margin:15px 0;">
        <img src="${member.user?.picture || "/assets/avatars/imageGrey.jpg"}" 
             style="width:50px;height:50px;border-radius:50%;margin-left:10px;" 
             alt="${memberName}">
        <div style="text-align:right;">
          <strong>${memberName}</strong>
          <div style="font-size:13px;color:#666;">${
            member.userRoleInChat === "Admin" ? "مشرف" : "عضو"
          } حالياً</div>
        </div>
      </div>
      <p style="color:#666;margin-bottom:20px;">
        هل أنت متأكد أنك تريد ${actionText} ${memberName} ليصبح ${roleLabel}؟
      </p>
      <div style="display:flex;justify-content:center;gap:10px;">
        <button class="confirm-role-btn" 
                style="background-color:${actionColor};color:white;border:none;padding:8px 16px;border-radius:4px;cursor:pointer;">
          ${actionText}
        </button>
        <button class="cancel-role-btn" 
                style="background-color:#f8f9fa;border:1px solid #ddd;padding:8px 16px;border-radius:4px;cursor:pointer;">
          إلغاء
        </button>
      </div>
    `;

    // إضافة مربع الحوار للصفحة
    document.body.appendChild(confirmDialog);

    // إضافة overlay للخلفية المعتمة
    const overlay = document.createElement("div");
    overlay.className = "dialog-overlay";
    overlay.style.position = "fixed";
    overlay.style.top = "0";
    overlay.style.left = "0";
    overlay.style.right = "0";
    overlay.style.bottom = "0";
    overlay.style.backgroundColor = "rgba(0,0,0,0.5)";
    overlay.style.zIndex = "9998";
    document.body.appendChild(overlay);

    // إعداد الأحداث
    return new Promise((resolve) => {
      const confirmBtn = confirmDialog.querySelector(".confirm-role-btn");
      const cancelBtn = confirmDialog.querySelector(".cancel-role-btn");

      // عند النقر على زر الإلغاء
      cancelBtn.addEventListener("click", () => {
        confirmDialog.classList.replace("animate__fadeIn", "animate__fadeOut");
        overlay.style.opacity = "0";
        setTimeout(() => {
          document.body.removeChild(confirmDialog);
          document.body.removeChild(overlay);
        }, 300);
        resolve(false);
      });

      // عند النقر على زر التأكيد
      confirmBtn.addEventListener("click", async () => {
        try {
          // تغيير حالة الأزرار لإظهار التحميل
          confirmBtn.innerHTML = `<span>جاري التغيير...</span>`;
          confirmBtn.disabled = true;
          cancelBtn.disabled = true;

          // استدعاء API حسب المتطلبات الجديدة
          const response = await this.ajaxManager.post(
            `Chat/Group/RoleGroupMember`,
            {
              Id: member.id,
              ChatID: this.currentChat.id,
              UserID: member.userID,
              UserRoleInChat: userRoleValue, // استخدام القيمة الرقمية المناسبة (Admin=2, Member=1)
            }
          );

          // إغلاق مربع الحوار بتأثير متلاشي
          confirmDialog.classList.replace(
            "animate__fadeIn",
            "animate__fadeOut"
          );
          overlay.style.opacity = "0";
          setTimeout(() => {
            document.body.removeChild(confirmDialog);
            document.body.removeChild(overlay);
          }, 300);

          if (
            response &&
            (response.success ||
              response.resCode === 200 ||
              response.resCode === 201)
          ) {
            // تحديث العضو في القائمة المحلية
            const updatedMember = this.groupMembers.find(
              (m) => m.userID === member.userID
            );
            if (updatedMember) {
              updatedMember.userRoleInChat = newRole;
            }

            // تحديث واجهة المستخدم
            this.updateMembersSection();

            // تحديث التخزين المحلي
            await this.saveMembersToLocalStorage(
              this.currentChat.id,
              this.groupMembers
            );

            // عرض رسالة نجاح
            this.showToast(`تم تغيير دور ${memberName} إلى ${roleLabel} بنجاح`);
            resolve(true);
          } else {
            this.showToast("لا يمكن تغيير دور هذا العضو", "error");
            resolve(false);
          }
        } catch (error) {
          console.error("Error changing member role:", error);
          this.showToast("فشل في تغيير دور العضو", "error");
          resolve(false);
        }
      });

      // إغلاق عند النقر خارج مربع الحوار
      overlay.addEventListener("click", () => {
        confirmDialog.classList.replace("animate__fadeIn", "animate__fadeOut");
        overlay.style.opacity = "0";
        setTimeout(() => {
          document.body.removeChild(confirmDialog);
          document.body.removeChild(overlay);
        }, 300);
        resolve(false);
      });
    });
  }

  /**
   * معالجة إضافة أعضاء جدد للمجموعة
   */
  async handleAddMembers() {
    try {
      // التحقق من وجود نافذة منبثقة مفتوحة بالفعل
      if (
        this.groupModalElement &&
        this.groupModalElement.style.display === "block"
      ) {
        return; // النافذة مفتوحة بالفعل
      }

      // إزالة أي نوافذ منبثقة سابقة
      const existingModal = document.querySelector(".contact-selection-modal");
      if (existingModal) {
        existingModal.remove();
      }

      // إنشاء نافذة اختيار الأعضاء الجديدة
      this.createContactSelectionModal();

      // إضافة مستمع حدث للإغلاق
      const closeBtn = this.groupModalElement.querySelector(".modal-close-btn");
      if (closeBtn) {
        closeBtn.addEventListener("click", () => {
          this.closeContactSelectionModal();
        });
      }

      // إضافة مستمع حدث لزر الإلغاء
      const cancelBtn = this.groupModalElement.querySelector(".cancel-btn");
      if (cancelBtn) {
        cancelBtn.addEventListener("click", () => {
          this.closeContactSelectionModal();
        });
      }
    } catch (error) {
      console.error("Error handling add members:", error);
      this.showToast("فشل في تحميل جهات الاتصال", "error");
    }
  }

  /**
   * الحصول على جهات الاتصال غير الموجودة بالمجموعة
   * @returns {Promise<Array>} - جهات الاتصال المتاحة
   */
  async getContactsNotInGroup() {
    try {
      // الحصول على جميع جهات الاتصال
      const response = await this.userProcessor.getUsersFromDB();
      if (!response) {
        return [];
      }

      // تصفية جهات الاتصال الموجودة بالفعل في المجموعة
      const groupMemberIds = this.groupMembers.map((member) => member.userID);

      return response.filter((contact) => !groupMemberIds.includes(contact.id));
    } catch (error) {
      console.error("Error getting contacts not in group:", error);
      return [];
    }
  }

  /**
   * إنشاء نافذة منبثقة لاختيار جهات الاتصال
   * @returns {HTMLElement} - عنصر النافذة المنبثقة
   */
  createContactSelectionModal() {
    // إنشاء حاوية النافذة المنبثقة
    const modal = document.createElement("div");
    modal.className =
      "contact-selection-modal animate__animated animate__fadeIn";

    // إنشاء محتوى النافذة المنبثقة
    modal.innerHTML = `
    <div class="modal-content">
      <!-- Header -->
      <div class="header-Chat">
        <!-- Back/Close button -->
        <div class="ICON">
          <button role="button" class="icons modal-close-btn">
            <i class="fe fe-arrow-right fe-24"></i>
          </button>
        </div>
        <div class="newText">
          <h2>إضافة أعضاء للمجموعة</h2>
        </div>
      </div>
      
      <!-- Search Bar -->
      <div class="search-bar">
        <div>
          <button class="search">
            <span>
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" d="M15.009 13.805h-.636l-.22-.219a5.184 5.184 0 0 0 1.256-3.386 5.207 5.207 0 1 0-5.207 5.208 5.183 5.183 0 0 0 3.385-1.255l.221.22v.635l4.004 3.999 1.194-1.195-3.997-4.007zm-4.808 0a3.605 3.605 0 1 1 0-7.21 3.605 3.605 0 0 1 0 7.21z"></path>
              </svg>
            </span>
          </button>
          <span class="go-back">
            <svg viewBox="0 0 24 24" width="24" height="24">
              <path fill="currentColor" d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"></path>
            </svg>
          </span>
          <input type="text" class="contact-search-input" title="البحث عن مستخدم" aria-label="البحث عن مستخدم" placeholder="البحث عن مستخدم">
        </div>
      </div>

       <!-- Selected Participants Area -->
      <div class="selected-participants-container">
        <!-- Selected participants will be added here dynamically -->
      </div>
      <!-- Contacts List -->
      <div class="contacts-list-container">
        <!-- Contacts will be added here dynamically -->
      </div>
      
      <!-- Action Buttons -->
      <div class="modal-actions">
        <button class="cancel-btn">إلغاء</button>
        <button class="add-members-confirm-btn" disabled>إضافة (<span class="selected-count">0</span>)</button>
      </div>
    </div>
  `;

    // إضافة النافذة المنبثقة إلى DOM
    document.body.appendChild(modal);

    if (!this.groupModalElement) {
      // تخزين مرجع للنافذة المنبثقة
      this.groupModalElement = modal;
    }

    // تهيئة وظائف النافذة المنبثقة
    this.initializeContactSelectionModal(modal);

    return modal;
  }

  /**
   * تهيئة وظائف نافذة اختيار جهات الاتصال
   * @param {HTMLElement} modal - عنصر النافذة المنبثقة
   */
  initializeContactSelectionModal(modal) {
    // الحصول على مراجع لعناصر النافذة المنبثقة
    const closeBtn = modal.querySelector(".modal-close-btn");
    const searchInput = modal.querySelector(".contact-search-input");
    const goBackBtn = modal.querySelector(".go-back");
    const selectedContainer = modal.querySelector(
      ".selected-participants-container"
    );
    const contactsContainer = modal.querySelector(".contacts-list-container");
    const cancelBtn = modal.querySelector(".cancel-btn");
    const confirmBtn = modal.querySelector(".add-members-confirm-btn");
    const selectedCountElement = modal.querySelector(".selected-count");

    // إعادة تعيين جهات الاتصال المحددة
    this.selectedContacts = [];

    // إغلاق الزر الحدث
    closeBtn.addEventListener("click", () => {
      this.closeContactSelectionModal();
    });

    // زر إلغاء الحدث
    cancelBtn.addEventListener("click", () => {
      this.closeContactSelectionModal();
    });

    // زر تأكيد الحدث
    confirmBtn.addEventListener("click", () => {
      this.confirmContactSelection();
    });

    // حدث مدخل البحث
    searchInput.addEventListener("input", (e) => {
      this.searchContacts(e.target.value, contactsContainer);
    });

    // زر العودة (للبحث)
    goBackBtn.addEventListener("click", () => {
      searchInput.value = "";
      searchInput.focus();
      this.searchContacts("", contactsContainer);
    });

    // تحميل جهات الاتصال
    this.loadContactsForModal(
      contactsContainer,
      selectedContainer,
      selectedCountElement,
      confirmBtn
    );
  }

  /**
   * تأكيد اختيار جهات الاتصال
   */
  async confirmContactSelection() {
    if (this.selectedContacts.length === 0) {
      alert("الرجاء تحديد جهة اتصال واحدة على الأقل");
      return;
    }

    try {
      // تحضير البيانات بالتنسيق المطلوب للـ API
      const groupMembers = this.selectedContacts.map((contact) => ({
        userID: contact.id,
        UserRoleInChat: 1,
      }));

      // عرض مؤشر التحميل
      const confirmButton = document.querySelector(".add-members-confirm-btn");
      const originalText = confirmButton.textContent;
      confirmButton.textContent = "جاري الإضافة...";
      confirmButton.disabled = true;

      // استدعاء API لإضافة الأعضاء بالتنسيق الجديد
      const response = await this.ajaxManager.post(`Chat/Group/AddMembers`, {
        ChatID: this.currentChat.id,
        GroupMembers: groupMembers,
      });

      if ((response && response.resCode === 201) || response.resCode === 200) {
        // إغلاق واجهة الاختيار
        this.closeContactSelectionModal();
        this.saveMembersToLocalStorage(this.currentChat.id, response.data);
        //console.log("response.data", response.data);
        // تحديث قائمة الأعضاء من الخادم
        await this.loadGroupMembers(this.currentChat.id);
        this.updateMembersSection();

        // عرض رسالة نجاح
        this.showToast(`تمت إضافة ${this.selectedContacts.length} أعضاء بنجاح`);
      } else {
        this.showToast(`"فشل في إضافة الأعضاء"`, "error");
        confirmButton.textContent = originalText;
        confirmButton.disabled = false;
      }
    } catch (error) {
      console.error("Error adding members:", error);
      this.showToast(`"فشل في إضافة الأعضاء"`, "error");

      // إعادة ضبط الزر
      const confirmButton = document.querySelector(".add-members-confirm-btn");
      confirmButton.textContent = `إضافة المحددين (${this.selectedContacts.length})`;
      confirmButton.disabled = false;
    }
  }

  /**
   * إغلاق نافذة اختيار جهات الاتصال
   */
  closeContactSelectionModal() {
    if (this.groupModalElement) {
      // إضافة تأثير الإغلاق
      this.groupModalElement.classList.remove("animate__fadeIn");
      this.groupModalElement.classList.add("animate__fadeOut");

      // إزالة من DOM بعد اكتمال التأثير
      setTimeout(() => {
        if (this.groupModalElement && this.groupModalElement.parentNode) {
          this.groupModalElement.parentNode.removeChild(this.groupModalElement);
        }
        this.groupModalElement = null;
      }, 300);
      this.groupModalElement.style.display = "none";
    }
  }

  /**
   * معالجة حظر مستخدم أو مغادرة مجموعة
   */
  async handleBlockOrLeaveGroup() {
    if (this.chatType === "Group") {
      // معالجة مغادرة المجموعة
      if (confirm("هل أنت متأكد من الخروج من المجموعة؟")) {
        try {
          const response = await this.ajaxManager.post(
            `Chat/Group/RemoveGroupMember`,
            {
              ChatID: this.currentChat.id,
              UserId: ChatProcessor.processedData.currentUser?.id,
              IsLeave: true,
            }
          );
          if (response && response.resCode === 200) {
            // إغلاق الملف الشخصي
            this.close();

            // إزالة المحادثة من القائمة
            await ChatProcessor.removeChat(this.currentChat.id);

            // تحديث قائمة المحادثات
            await generateChatList();

            // عرض رسالة نجاح
            this.showToast("تم الخروج من المجموعة بنجاح", "success");
          } else {
            this.showToast("فشل في الخروج من المجموعة", "error");
          }
        } catch (error) {
          console.error("Error leaving group:", error);
          this.showToast("فشل في الخروج من المجموعة", "error");
        }
      }
    } else {
      // معالجة حظر المستخدم
      const userName = this.currentChat
        ? this.currentChat.name
        : this.currentContact
        ? this.currentContact.userName
        : "المستخدم";
      if (confirm(`هل أنت متأكد من حظر ${userName}؟`)) {
        try {
          const userId = this.currentChat
            ? this.currentChat.id
            : this.currentContact
            ? this.currentContact.id
            : null;
          if (!userId) {
            this.showToast("لا يمكن تحديد المستخدم", "error");
            return;
          }

          const response = await this.ajaxManager.post("/Chat/BlockUser", {
            userId: userId,
          });

          if (response && response.success) {
            // إغلاق الملف الشخصي
            this.close();

            // تحديث قائمة المحادثات
            await generateChatList();

            // عرض رسالة نجاح
            this.showToast(`تم حظر ${userName} بنجاح`, "success");
          } else {
            this.showToast("فشل في حظر المستخدم", "error");
          }
        } catch (error) {
          console.error("Error blocking user:", error);
          this.showToast("فشل في حظر المستخدم", "error");
        }
      }
    }
  }

  /**
   * معالجة حذف محادثة
   */
  async handleDeleteChat() {
    const chatName = this.currentChat
      ? this.currentChat.name
      : this.currentContact
      ? this.currentContact.userName
      : "المحادثة";
    if (
      confirm(
        `هل أنت متأكد من حذف محادثة ${chatName}؟ لا يمكن التراجع عن هذا الإجراء.`
      )
    ) {
      try {
        const chatId = this.currentChat ? this.currentChat.id : null;
        if (!chatId) {
          this.showToast("لا يمكن تحديد المحادثة", "error");
          return;
        }

        const response = await this.ajaxManager.post("/Chat/DeleteChat", {
          chatId: chatId,
        });

        if (response && response.success) {
          // إغلاق الملف الشخصي
          this.close();

          // إزالة المحادثة من القائمة
          await ChatProcessor.removeChat(chatId);

          // تحديث قائمة المحادثات
          await generateChatList();

          // عرض رسالة نجاح
          this.showToast("تم حذف المحادثة بنجاح", "success");
        } else {
          this.showToast("فشل في حذف المحادثة", "error");
        }
      } catch (error) {
        console.error("Error deleting chat:", error);
        this.showToast("فشل في حذف المحادثة", "error");
      }
    }
  }

  /**
   * تحميل جهات الاتصال للنافذة المنبثقة
   * @param {HTMLElement} contactsContainer - حاوية جهات الاتصال
   * @param {HTMLElement} selectedContainer - حاوية جهات الاتصال المحددة
   * @param {HTMLElement} countElement - عنصر عرض عدد المحددين
   * @param {HTMLElement} confirmBtn - زر التأكيد
   */
  async loadContactsForModal(
    contactsContainer,
    selectedContainer,
    countElement,
    confirmBtn
  ) {
    try {
      // عرض حالة التحميل
      contactsContainer.innerHTML =
        '<div class="loading-contacts">جاري تحميل جهات الاتصال...</div>';

      // الحصول على جهات الاتصال
      const contacts = await this.getContactsNotInGroup();

      if (!contacts || contacts.length === 0) {
        contactsContainer.innerHTML =
          '<div class="no-contacts-message">لا توجد جهات اتصال</div>';
        return;
      }

      // تجميع جهات الاتصال حسب الحرف الأول
      const groupedContacts = this.groupContactsByFirstLetter(contacts);

      // مسح الحاوية
      contactsContainer.innerHTML = "";

      // إضافة جهات الاتصال إلى الحاوية حسب المجموعة
      Object.keys(groupedContacts)
        .sort()
        .forEach((letter) => {
          const letterGroup = document.createElement("div");
          letterGroup.className = "contact-letter-group";

          // إضافة رأس الحرف
          const letterHeader = document.createElement("div");
          letterHeader.className = "letter-header";
          letterHeader.innerHTML = `<h3>${letter}</h3>`;
          letterGroup.appendChild(letterHeader);

          // إضافة جهات الاتصال لهذا الحرف
          groupedContacts[letter].forEach((contact) => {
            const contactElement = this.createContactElement(
              contact,
              selectedContainer,
              countElement,
              confirmBtn
            );
            letterGroup.appendChild(contactElement);
          });

          contactsContainer.appendChild(letterGroup);
        });
    } catch (error) {
      console.error("Error loading contacts for modal:", error);
      contactsContainer.innerHTML =
        '<div class="error-message">حدث خطأ أثناء تحميل جهات الاتصال</div>';
    }
  }

  /**
   * تجميع جهات الاتصال حسب الحرف الأول من الاسم
   * @param {Array} contacts - قائمة جهات الاتصال
   * @returns {Object} - جهات الاتصال مجمعة حسب الحرف الأول
   */
  groupContactsByFirstLetter(contacts) {
    const grouped = {};

    contacts.forEach((contact) => {
      const name = contact.userName || contact.name || "Unknown";
      const firstLetter = name.charAt(0).toUpperCase();

      if (!grouped[firstLetter]) {
        grouped[firstLetter] = [];
      }

      grouped[firstLetter].push(contact);
    });

    return grouped;
  }

  /**
   * إنشاء عنصر جهة اتصال لقائمة الاختيار
   * @param {Object} contact - كائن جهة الاتصال
   * @param {HTMLElement} selectedContainer - حاوية جهات الاتصال المحددة
   * @param {HTMLElement} countElement - عنصر عرض عدد المحددين
   * @param {HTMLElement} confirmBtn - زر التأكيد
   * @returns {HTMLElement} - عنصر جهة الاتصال
   */
  createContactElement(contact, selectedContainer, countElement, confirmBtn) {
    const element = document.createElement("div");
    element.className = "contact-item";
    element.dataset.contactId = contact.id;
    element.dataset.userId = contact.id;

    element.innerHTML = `
    <div class="imgBox">
      <img src="${
        contact.picture || "/assets/avatars/imageGrey.jpg"
      }" class="cover" alt="${contact.userName || "Contact"}">
    </div>
    <div class="h-text">
      <div class="head">
        <h4 title="${contact.userName || ""}" aria-label="${
      contact.userName || ""
    }">${contact.userName || "Unknown"}</h4>
      </div>
      <div class="message">
        <p title="${contact.about || ""}" aria-label="${contact.about || ""}">${
      contact.about || ""
    }</p>
      </div>
    </div>
  `;

    // إضافة حدث النقر لتحديد/إلغاء تحديد جهة الاتصال
    element.addEventListener("click", () => {
      this.toggleContactSelection(
        contact,
        element,
        selectedContainer,
        countElement,
        confirmBtn
      );
    });

    return element;
  }

  /**
   * تبديل اختيار جهة اتصال
   * @param {Object} contact - كائن جهة الاتصال
   * @param {HTMLElement} element - عنصر جهة الاتصال
   * @param {HTMLElement} selectedContainer - حاوية جهات الاتصال المحددة
   * @param {HTMLElement} countElement - عنصر عرض عدد المحددين
   * @param {HTMLElement} confirmBtn - زر التأكيد
   */
  toggleContactSelection(
    contact,
    element,
    selectedContainer,
    countElement,
    confirmBtn
  ) {
    const isSelected = element.classList.contains("selected");

    if (isSelected) {
      // إلغاء تحديد جهة الاتصال
      element.classList.remove("selected");

      // إزالة من مصفوفة جهات الاتصال المحددة
      this.selectedContacts = this.selectedContacts.filter(
        (c) => c.id !== contact.id
      );

      // إزالة من حاوية المحددين
      const selectedElement = selectedContainer.querySelector(
        `[data-contact-id="${contact.id}"]`
      );
      if (selectedElement) {
        selectedElement.remove();
      }
    } else {
      // تحديد جهة الاتصال
      element.classList.add("selected");

      // إضافة إلى مصفوفة جهات الاتصال المحددة
      this.selectedContacts.push(contact);

      // إضافة إلى حاوية المحددين
      const selectedElement = this.createSelectedContactElement(
        contact,
        element,
        selectedContainer,
        countElement,
        confirmBtn
      );
      selectedContainer.appendChild(selectedElement);
    }

    // تحديث العدد وحالة الزر
    this.updateSelectedContactsUI(selectedContainer, countElement, confirmBtn);
  }

  /**
   * إنشاء عنصر جهة اتصال محددة لحاوية المحددين
   * @param {Object} contact - كائن جهة الاتصال
   * @param {HTMLElement} originalElement - عنصر جهة الاتصال الأصلي
   * @param {HTMLElement} selectedContainer - حاوية جهات الاتصال المحددة
   * @param {HTMLElement} countElement - عنصر عرض عدد المحددين
   * @param {HTMLElement} confirmBtn - زر التأكيد
   * @returns {HTMLElement} - عنصر جهة الاتصال المحددة
   */
  createSelectedContactElement(
    contact,
    originalElement,
    selectedContainer,
    countElement,
    confirmBtn
  ) {
    const element = document.createElement("div");
    element.className = "selected-participant-chip";
    element.dataset.contactId = contact.id;

    element.innerHTML = `
      <img src="${contact.picture || "/assets/avatars/imageGrey.jpg"}" alt="${
      contact.userName || "Contact"
    }">
    <span class="selected-contact-name">${contact.userName || "Unknown"}</span>
    <span class="remove-participant">×</span>
  `;

    // إضافة حدث النقر لزر الإزالة
    const removeButton = element.querySelector(".remove-participant");
    removeButton.addEventListener("click", (e) => {
      e.stopPropagation(); // منع انتشار الحدث

      // إلغاء التحديد في القائمة
      originalElement.classList.remove("selected");

      // إزالة من مصفوفة جهات الاتصال المحددة
      this.selectedContacts = this.selectedContacts.filter(
        (c) => c.id !== contact.id
      );

      // إزالة من حاوية المحددين
      element.remove();

      // تحديث العدد وحالة الزر
      this.updateSelectedContactsUI(
        selectedContainer,
        countElement,
        confirmBtn
      );
    });

    return element;
  }

  /**
   * تحديث عناصر واجهة المستخدم المتعلقة بجهات الاتصال المحددة
   * @param {HTMLElement} selectedContainer - حاوية جهات الاتصال المحددة
   * @param {HTMLElement} countElement - عنصر عرض عدد المحددين
   * @param {HTMLElement} confirmBtn - زر التأكيد
   */
  updateSelectedContactsUI(selectedContainer, countElement, confirmBtn) {
    // تحديث العدد
    const count = this.selectedContacts.length;
    countElement.textContent = count.toString();

    // تحديث حالة الزر
    if (count > 0) {
      confirmBtn.removeAttribute("disabled");
    } else {
      confirmBtn.setAttribute("disabled", "disabled");
    }

    // إظهار/إخفاء حاوية المحددين بناءً على ما إذا كانت هناك اختيارات
    if (count > 0) {
      selectedContainer.style.display = "flex";
      selectedContainer.classList.add("has-selections");
    } else {
      selectedContainer.style.display = "none";
      selectedContainer.classList.remove("has-selections");
    }
  }

  /**
   * البحث عن جهات الاتصال في النافذة المنبثقة
   * @param {string} query - استعلام البحث
   * @param {HTMLElement} contactsContainer - حاوية قائمة جهات الاتصال
   */
  searchContacts(query, contactsContainer) {
    const contactItems = contactsContainer.querySelectorAll(".contact-item");
    const letterHeaders = contactsContainer.querySelectorAll(".letter-header");
    const letterGroups = contactsContainer.querySelectorAll(
      ".contact-letter-group"
    );
    //console.log("query", query);

    if (!query) {
      // عرض جميع جهات الاتصال إذا كان الاستعلام فارغًا
      contactItems.forEach((item) => (item.style.display = "flex"));
      letterHeaders.forEach((header) => (header.style.display = "block"));
      letterGroups.forEach((group) => (group.style.display = "block"));

      // إزالة رسالة "لا توجد نتائج" إذا كانت موجودة
      const noResultsMessage = contactsContainer.querySelector(
        ".no-results-message"
      );
      if (noResultsMessage) {
        noResultsMessage.remove();
      }
      return;
    }

    // تطبيع الاستعلام للبحث غير الحساس لحالة الأحرف
    const normalizedQuery = query.toLowerCase();

    // تتبع المجموعات التي بها جهات اتصال مرئية
    const visibleGroups = new Set();

    // عداد لجهات الاتصال المرئية
    let visibleContactsCount = 0;

    // تصفية جهات الاتصال
    contactItems.forEach((item) => {
      const name = item.querySelector(".head h4").textContent.toLowerCase();
      const about = item.querySelector(".message p").textContent.toLowerCase();

      if (name.includes(normalizedQuery) || about.includes(normalizedQuery)) {
        item.style.display = "flex";
        visibleContactsCount++;
        // البحث عن مجموعة الحرف الأم وإضافتها إلى المجموعات المرئية
        const parentGroup = item.closest(".contact-letter-group");
        if (parentGroup) {
          visibleGroups.add(parentGroup);
        }
      } else {
        item.style.display = "none";
      }
    });

    // إظهار/إخفاء مجموعات الأحرف بناءً على ما إذا كان لديها جهات اتصال مرئية
    letterGroups.forEach((group) => {
      if (visibleGroups.has(group)) {
        group.style.display = "block";
      } else {
        group.style.display = "none";
      }
    });

    // التحقق مما إذا لم تكن هناك نتائج للبحث
    if (visibleContactsCount === 0) {
      // إزالة رسالة "لا توجد نتائج" السابقة إن وجدت
      const existingNoResults = contactsContainer.querySelector(
        ".no-results-message"
      );
      if (existingNoResults) {
        existingNoResults.remove();
      }

      // إنشاء عنصر رسالة "لا توجد نتائج"
      const noResultsMessage = document.createElement("div");
      noResultsMessage.className = "no-results-message";
      noResultsMessage.style.textAlign = "center";
      noResultsMessage.style.padding = "20px";
      noResultsMessage.style.color = "#666";
      noResultsMessage.style.fontWeight = "bold";
      noResultsMessage.style.width = "100%";
      noResultsMessage.style.height = "100%";
      noResultsMessage.textContent = "لا توجد نتائج";

      // إضافة الرسالة إلى الحاوية
      contactsContainer.appendChild(noResultsMessage);
    } else {
      // إزالة رسالة "لا توجد نتائج" إذا كانت موجودة وكانت هناك نتائج
      const noResultsMessage = contactsContainer.querySelector(
        ".no-results-message"
      );
      if (noResultsMessage) {
        noResultsMessage.remove();
      }
    }
  }
}

/**
 * دالة عامة لإغلاق شاشة اختيار المجموعة
 */
function closeGroup() {
  if (window.chatProfile) {
    window.chatProfile.closeContactSelection();
  }
}
