﻿
export const CSS_CLASSES = {
 ACTIVE_CHAT: "active",
 UNREAD_CHAT: "unread",
 MESSAGE_MY: "my-chat",
 MESSAGE_FRIEND: "frnd-chat",
 LOADING: "loading",
 ERROR: "error",
 // Add other frequently used CSS classes
};

export const EVENT_NAMES = {
 SIGNALR_CONNECTING: "signalRConnecting",
 SIGNALR_CONNECTED: "signalRConnected",
 SIGNALR_RECONNECTING: "signalRReconnecting",
 SIGNALR_RECONNECTED: "signalRReconnected",
 SIGNALR_DISCONNECTED: "signalRDisconnected",
 SIGNA<PERSON>_CONNECTION_FAILURE: "signalRConnectionFailure",
 NEW_MESSAGE_RECEIVED: "newMessageReceived",
 CHAT_LIST_UPDATED: "chatListUpdated",
 // Add other custom event names
};

export const STORAGE_KEYS = {
   CURRENT_USER: "currentUser",
   AUTH_TOKEN: "authToken",
   // Add other local/session storage keys
};

export const MESSAGE_STATUS = {
 PENDING: "Pending",
 SENT: "Sent",
 DELIVERED: "Delivered",
 READ: "Read",
 FAILED: "Failed",
};

export const CHAT_TYPE = {
 INDIVIDUAL: "Individual",
 GROUP: "Group",
};

export const USER_ROLE = {
 ADMIN: 0, // Or 1 depending on your backend
 MEMBER: 1, // Or 2 depending on your backend
};

/**
* Application Constants
*/

// API Endpoints (adjust base URL as needed)
export const API_BASE_URL = "/"; // Assuming API is hosted at the root
export const AUTH_PROFILE_URL = "Authentication/profile";
export const AUTH_LOGIN_URL = "Authentication/Login"; // Example
export const CHAT_GET_ALL_URL = "Chat/GetAllChats";
export const CHAT_GET_CHAT_URL = (chatId) => `Chat/GetChat/${chatId}`; // Example with parameter
export const CHAT_MARK_READ_URL = (chatId) => `Chat/MakeRead/${chatId}`;
export const MESSAGE_SEND_TEXT_URL = "Message/CreateTextMessage";
export const CONTACT_GET_ALL_URL = "Contact/GetAllContacts"; // Example
// Add other API endpoints...

// SignalR Hub URL
export const SIGNALR_HUB_URL = "http://localhost:5236"; // Adjust host/port if needed

// DOM Element IDs (use sparingly, prefer class selectors or component-local refs)
export const CHAT_LIST_ID = "chat-list";
export const MESSAGE_AREA_ID = "message-area";
export const MESSAGES_CONTAINER_ID = "messages";
export const MESSAGE_INPUT_ID = "input-send-message";
export const USER_PROFILE_IMAGE_ID = "user-profile-image"; // Example
export const CONNECTION_STATUS_ID = "connection-status";
// Add other critical element IDs...

// CSS Classes
export const ACTIVE_CHAT_ITEM_CLASS = "active";
export const UNREAD_CHAT_ITEM_CLASS = "unread";
export const MESSAGE_SENT_CLASS = "message-sent";
export const MESSAGE_RECEIVED_CLASS = "message-received"; // Example if needed
export const MESSAGE_PENDING_CLASS = "message-pending";
export const MESSAGE_FAILED_CLASS = "message-failed";
export const LOADING_INDICATOR_CLASS = "loading";
export const ONLINE_STATUS_CLASS = "online";
export const OFFLINE_STATUS_CLASS = "offline";
// Add other common CSS classes...

// Event Names (for custom events)
export const EVENT_CHAT_SELECTED = "chatSelected";
export const EVENT_MESSAGE_SENT = "messageSent";
export const EVENT_MESSAGE_RECEIVED = "messageReceived";
export const EVENT_USER_STATUS_CHANGED = "userStatusChanged";
export const EVENT_CONNECTION_STATE_CHANGED = "connectionStateChanged";
// Add other custom event names...

// Local Storage / IndexedDB Keys
export const DB_NAME = "ChatAppCloneDB";
export const DB_VERSION = 1; // Keep in sync with dbManager.js
export const CURRENT_USER_STORE = "currentUser";
export const CHATS_STORE = "chats";
export const MESSAGES_STORE = "messages";
export const CONTACTS_STORE = "contacts";
export const USERS_STORE = "users";
// Add other storage keys...

// Default Settings
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_RECONNECT_ATTEMPTS = 5;
export const RECONNECT_DELAY_MS = 3000;

// Message Types (Numeric representation used in backend/DB)
export const MESSAGE_TYPE_TEXT = 0;
export const MESSAGE_TYPE_IMAGE = 1;
export const MESSAGE_TYPE_VIDEO = 2;
export const MESSAGE_TYPE_AUDIO = 3;
export const MESSAGE_TYPE_DOCUMENT = 4;
export const MESSAGE_TYPE_CONTACT = 5; // Assuming Contact Card maps to this
export const MESSAGE_TYPE_LOCATION = 6;
export const MESSAGE_TYPE_POLL = 7;
// Add other message types...

// Message Status
export const MESSAGE_STATUS_PENDING = "Pending";
export const MESSAGE_STATUS_SENT = "Sent";
export const MESSAGE_STATUS_DELIVERED = "Delivered";
export const MESSAGE_STATUS_READ = "Read";
export const MESSAGE_STATUS_FAILED = "Failed";

// Other constants
export const DEFAULT_AVATAR_URL = "/assets/avatars/imageGrey.jpg";

