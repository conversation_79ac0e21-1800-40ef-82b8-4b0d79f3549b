/**
 * ConnectionStatusManager - Manages the UI for connection status
 * Handles displaying connection status, errors, and reconnection attempts
 */
class ConnectionStatusManager {
    constructor() {
        this.statusContainer = document.getElementById('connection-status');
        this.statusIndicator = document.getElementById('connection-indicator');
        this.statusText = document.getElementById('connection-text');
        this.reconnectButton = document.getElementById('reconnect-button');

        // Create elements if they don't exist
        this.initializeUI();

        // Bind event handlers
        this.reconnectButton.addEventListener('click', () => this.handleReconnect());

        // Listen for SignalR connection events
        document.addEventListener('signalRConnectionFailure', (event) => this.handleConnectionFailure(event));
        document.addEventListener('signalRReconnecting', () => this.handleReconnecting());
        document.addEventListener('signalRReconnected', () => this.handleReconnected());
        document.addEventListener('signalRConnected', () => this.handleConnected());
    }

    /**
     * Initialize UI elements if they don't exist
     */
    initializeUI() {
        if (!this.statusContainer) {
            this.statusContainer = document.createElement('div');
            this.statusContainer.id = 'connection-status';
            this.statusContainer.className = 'connection-status';

            this.statusIndicator = document.createElement('div');
            this.statusIndicator.id = 'connection-indicator';
            this.statusIndicator.className = 'connection-indicator';

            this.statusText = document.createElement('span');
            this.statusText.id = 'connection-text';
            this.statusText.className = 'connection-text';
            this.statusText.textContent = 'متصل';

            this.reconnectButton = document.createElement('button');
            this.reconnectButton.id = 'reconnect-button';
            this.reconnectButton.className = 'reconnect-button';
            this.reconnectButton.textContent = 'إعادة الاتصال';
            this.reconnectButton.style.display = 'none';

            this.statusContainer.appendChild(this.statusIndicator);
            this.statusContainer.appendChild(this.statusText);
            this.statusContainer.appendChild(this.reconnectButton);

            // Add to the DOM (assuming there's a header or appropriate container)
            const header = document.querySelector('.header');
            if (header) {
                header.appendChild(this.statusContainer);
            } else {
                document.body.appendChild(this.statusContainer);
            }
        }
    }

    /**
     * Handle connection failure
     * @param {CustomEvent} event - The connection failure event
     */
    handleConnectionFailure(event) {
        this.statusIndicator.className = 'connection-indicator disconnected';
        this.statusText.textContent = 'انقطع الاتصال';
        this.reconnectButton.style.display = 'block';

        // Show error message if provided
        if (event.detail && event.detail.message) {
            this.showErrorNotification(event.detail.message);
        }
    }

    /**
     * Handle reconnecting state
     */
    handleReconnecting() {
        this.statusIndicator.className = 'connection-indicator reconnecting';
        this.statusText.textContent = 'جاري إعادة الاتصال...';
        this.reconnectButton.style.display = 'none';
    }

    /**
     * Handle reconnected state
     */
    handleReconnected() {
        this.statusIndicator.className = 'connection-indicator connected';
        this.statusText.textContent = 'متصل';
        this.reconnectButton.style.display = 'none';

        // Show success notification
        this.showSuccessNotification('تم إعادة الاتصال بنجاح');
    }

    /**
     * Handle connected state
     */
    handleConnected() {
        this.statusIndicator.className = 'connection-indicator connected';
        this.statusText.textContent = 'متصل';
        this.reconnectButton.style.display = 'none';
    }

    /**
     * Handle reconnect button click
     */
    handleReconnect() {
        // Dispatch a custom event that the SignalR manager can listen for
        const event = new CustomEvent('signalRReconnectRequested');
        document.dispatchEvent(event);

        // Update UI to show reconnecting state
        this.handleReconnecting();
    }

    /**
     * Show error notification
     * @param {string} message - Error message to display
     */
    showErrorNotification(message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'connection-notification error';
        notification.textContent = message;

        // Add to DOM
        document.body.appendChild(notification);

        // Remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 500);
        }, 5000);
    }

    /**
     * Show success notification
     * @param {string} message - Success message to display
     */
    showSuccessNotification(message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'connection-notification success';
        notification.textContent = message;

        // Add to DOM
        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 500);
        }, 3000);
    }
}

// Export the ConnectionStatusManager class
//export default ConnectionStatusManager; 