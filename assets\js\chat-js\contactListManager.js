﻿//import ContactProcessor from './contactProcessor.js';

class ContactListManager {
  constructor() {
    this.contacts = [];
    this.currentPage = 1;
    this.pageSize = 20;
    this.hasMore = false;
    this.searchTerm = "";
    this.isLoading = false;
    this.contactProcessor = null;
  }

  async initialize() {
    try {
      this.contactProcessor = new ContactProcessor();
      // Initialize contact data
      await this.contactProcessor.initializeContactData();

      // Fetch initial contacts
      await this.fetchContacts();

      // Set up event listeners
      this.setupEventListeners();

      //////console.log('Contact list manager initialized successfully');
    } catch (error) {
      console.error("Error initializing contact list manager:", error);
      throw error;
    }
  }

  setupEventListeners() {
    // Search input handler
    const searchInput = document.getElementById("contact-search");
    if (searchInput) {
      searchInput.addEventListener("input", this.handleSearch.bind(this));
    }

    // Scroll handler for infinite loading
    const contactList = document.getElementById("contact-list");
    if (contactList) {
      contactList.addEventListener("scroll", this.handleScroll.bind(this));
    }
  }

  async handleSearch(event) {
    const searchTerm = event.target.value;
    this.searchTerm = searchTerm;

    // If search term is empty, fetch from server
    if (!searchTerm.trim()) {
      this.currentPage = 1;
      await this.fetchContacts();
      return;
    }

    // For non-empty search terms, use local search for immediate results
    const searchResults = this.contactProcessor.getContactsBySearch(searchTerm);
    //////console.log("searchResults", searchResults)
    this.contacts = searchResults;
    this.renderContacts();

    // Show a message if no results found
    const contactList = document.getElementById("contact-list");
    if (contactList && searchResults.length === 0) {
      contactList.innerHTML = `
                <div class="no-results">
                    <p>No contacts found matching "${searchTerm}"</p>
                </div>
            `;
    }
    // Also fetch from server to ensure we have all matching contacts
    // Only if the search term is at least 2 characters to avoid too many requests
    if (searchTerm.trim().length >= 2 && searchResults.length !== 0) {
      this.currentPage = 1;
      await this.fetchContacts();
    }
    //await this.fetchContacts();
  }

  async handleScroll(event) {
    const element = event.target;
    if (element.scrollHeight - element.scrollTop === element.clientHeight) {
      if (this.hasMore && !this.isLoading) {
        this.currentPage++;
        await this.fetchContacts(true);
      }
    }
  }

  //async fetchContacts(isLoadMore = false) {
  //    try {
  //        this.isLoading = true;

  //        const result = await this.contactProcessor.fetchContacts(
  //            this.searchTerm,
  //            this.currentPage,
  //            this.pageSize
  //        );
  //         //////console.log("fetchContacts", result)
  //        if (!isLoadMore) {
  //            this.contacts = result.memberships;
  //        } else {
  //            this.contacts = [...this.contacts, ...result.contacts];
  //        }

  //        this.hasMore = result.pagination.hasMore;

  //        // Update UI
  //        this.renderContacts();

  //         //////console.log('Contacts fetched successfully');
  //    } catch (error) {
  //        console.error('Error fetching contacts:', error);
  //        throw error;
  //    } finally {
  //        this.isLoading = false;
  //    }
  //}
  async fetchContacts(isLoadMore = false) {
    try {
      this.isLoading = true;

      const result = await this.contactProcessor.fetchContacts(
        this.searchTerm,
        this.currentPage,
        this.pageSize
      );
      //////console.log()
      //console.log("result contact", result);
      // If we're doing a local search, don't overwrite the results
      if (this.searchTerm.trim() && !isLoadMore) {
        // Merge server results with local results to ensure we have all matches
        const localContactIds = new Set(
          this.contacts.map((c) => {
            //////console.log("c",c)
            return c.user.userName;
          })
        );
        //////console.log("localContactIds", localContactIds)
        // Add any server results that aren't already in our local results
        result.memberships.forEach((contact) => {
          if (!localContactIds.has(contact.user.userName)) {
            //////console.log("this.contacts.push", contact)

            this.contacts.push(contact);
          }
        });
      } else if (!isLoadMore) {
        // For non-search or empty search, replace contacts
        this.contacts = result.memberships;
      } else {
        // For load more, append new contacts
        this.contacts = [...this.contacts, ...result.memberships];
      }

      this.hasMore = result.pagination.hasMore;

      // Update UI
      this.renderContacts();

      //////console.log('Contacts fetched successfully');
    } catch (error) {
      console.error("Error fetching contacts:", error);
      throw error;
    } finally {
      this.isLoading = false;
    }
  }
  renderContacts() {
    const contactList = document.getElementById("contact-list");
    if (!contactList) return;
    // If no contacts, show a message
    if (this.contacts.length === 0) {
      contactList.innerHTML = `
                <div class="no-results">
                    <p>No contacts found</p>
                </div>
            `;
      return;
    }
    // Group contacts by first letter
    const groupedContacts = this.groupContactsByLetter();

    // Generate HTML
    let html = "";
    for (const [letter, contacts] of Object.entries(groupedContacts)) {
      html += `
                <div class="a">
                    <h3>${letter}</h3>
                </div>
            `;

      for (const contact of contacts) {
        html += this.generateContactHTML(contact);
      }
    }

    contactList.innerHTML = html;
  }

  groupContactsByLetter() {
    const grouped = {};
    this.contacts.forEach((contact) => {
      const letter = contact.user.userName.charAt(0).toUpperCase();
      if (!grouped[letter]) {
        grouped[letter] = [];
      }
      grouped[letter].push(contact);
    });
    return grouped;
  }

  generateContactHTML(contact) {
    //const user = contact[0]?.user;
    // //////console.log("contact[0]?.user", contact)
    return `
            <div class="block top" data-contact-id="${
              contact.userId
            }" onclick="generateMessageAreaForContact(this, ${contact.id})" >
                <div class="imgBox">
                    <img src="${
                      contact.user?.picture || "/assets/avatars/imageGrey.jpg"
                    }" class="cover" alt="${contact.user.userName}">
                </div>
                <div class="h-text">
                    <div class="head">
                        <h4 title="${contact.user.userName}" aria-label="${
      contact.user.userName
    }">${contact.user.userName}</h4>
                    </div>
                    <div class="message">
                        <p title="${
                          contact.user?.userName || ""
                        }" aria-label="${contact.user?.userName || ""}">
                            ${contact.user?.userName || ""}
                        </p>
                    </div>
                </div>
            </div>
        `;
  }

  async deleteContact(contactId) {
    try {
      await this.contactProcessor.deleteContact(contactId);
      this.contacts = this.contacts.filter((c) => c.id !== contactId);
      this.renderContacts();
      //////console.log('Contact deleted successfully');
    } catch (error) {
      console.error("Error deleting contact:", error);
      throw error;
    }
  }

  showDeleteConfirmation(contactId) {
    if (confirm("هل أنت متأكد أنك تريد حذف جهة الاتصال هذه؟")) {
      this.deleteContact(contactId);
    }
  }
}

//export default new ContactListManager();
