﻿//import { DBManager } from './dbManager.js';
//import { Models } from './models.js';

class ContactProcessor {
  constructor() {
    this.processedData = {
      contacts: [],
      contactMemberships: [],
      currentPage: 1,
      pageSize: 20,
      totalPages: 0,
      hasMore: false,
    };
    this.ajaxManager = new AjaxManager();
  }

  async initializeContactData() {
    try {
      // Initialize database if not already initialized
      if (!DBManager.isInitialized) {
        await DBManager.initializeDB();
      }

      // Load contacts from IndexedDB
      await this.loadContactsFromDB();

      // //////console.log('Contact data initialized successfully');
    } catch (error) {
      //console.error('Error initializing contact data:', error);
      throw error;
    }
  }

  async loadContactsFromDB() {
    try {
      const contacts = await DBManager.getAllData("contacts");
      const memberships = await DBManager.getAllData("contactMemberships");

      this.processedData.contacts = contacts || [];
      this.processedData.contactMemberships = memberships || [];

      // //////console.log('Contacts loaded from DB:', this.processedData.contacts.length);
    } catch (error) {
      //console.error('Error loading contacts from DB:', error);
      throw error;
    }
  }

  async fetchContacts(search = "", page = 1, pageSize = 20, sort = "Desc") {
    try {
      const data = await this.ajaxManager.get(`Contact/GetAllContacts`);
      //const data = await response.json();
      //console.log("datcontacta", data);
      if (!data || !data.resObject) {
        throw new Error("Invalid response format from server");
      }
      if (data.resCode === 200) {
        // Process and store contacts
        //const contacts = data.resObject.pageData.map(item => ContactModels.Contact.fromResponse(item.contact));
        const memberships = data.resObject.pageData.map((item) => {
          //////console.log("item", item)

          return ContactModels.ContactMembership.fromResponse(item);
        });

        // Update processed data
        // Update processed data
        //this.processedData.contacts = contacts;
        this.processedData.contactMemberships = memberships;
        this.processedData.currentPage = data.resObject.currentPage;
        this.processedData.pageSize = data.resObject.pageSize;
        this.processedData.totalPages = data.resObject.totalPages;
        this.processedData.hasMore = data.resObject.hasNextPage;

        // Save to IndexedDB
        await this.saveContactsToDB(null, memberships);

        return {
          //contacts,
          memberships,
          pagination: {
            currentPage: data.resObject.currentPage,
            pageSize: data.resObject.pageSize,
            totalPages: data.resObject.totalPages,
            hasMore: data.resObject.hasNextPage,
          },
        };
      } else {
        throw new Error(data.resMeg || "Failed to fetch contacts");
      }
    } catch (error) {
      console.error("Error fetching contacts:", error);
      // Return empty data instead of throwing error
      return {
        //contacts: [],
        memberships: [],
        pagination: {
          currentPage: 1,
          pageSize: pageSize,
          totalPages: 0,
          hasMore: false,
        },
      };
    }
  }

  async saveContactsToDB(contacts, memberships) {
    try {
      // Clear existing data
      //await DBManager.clearStore('contacts');
      await DBManager.clearStore("contactMemberships");

      // Save new data
      //for (const contact of contacts) {
      //    await DBManager.saveData('contacts', contact);
      //}
      for (const membership of memberships) {
        await DBManager.saveData("contactMemberships", membership);
      }

      //// //////console.log('Contacts saved to DB successfully');
    } catch (error) {
      //console.error('Error saving contacts to DB:', error);
      throw error;
    }
  }

  async deleteContact(contactId) {
    try {
      // Remove from IndexedDB
      await DBManager.delete("contacts", contactId);
      await DBManager.delete("contactMemberships", contactId);

      // Update processed data
      this.processedData.contacts = this.processedData.contacts.filter(
        (c) => c.id !== contactId
      );
      this.processedData.contactMemberships =
        this.processedData.contactMemberships.filter(
          (m) => m.contactId !== contactId
        );

      // //////console.log('Contact deleted successfully');
    } catch (error) {
      //console.error('Error deleting contact:', error);
      throw error;
    }
  }

  getContactById(contactId) {
    return this.processedData.contacts.find((c) => c.contactId === contactId);
  }

  getContactMembershipById(Id) {
    return this.processedData.contactMemberships.find((m) => m.id === Id);
  }

  getContactsBySearch(searchTerm) {
    if (!searchTerm || searchTerm.trim().length < 1) {
      return this.processedData.contactMemberships;
    }
    const term = searchTerm?.toLowerCase()?.trim();
    if (
      !this.processedData ||
      !Array.isArray(this.processedData.contactMemberships)
    ) {
      //console.warn("No contacts data to search.");
      return [];
    }

    const results = this.processedData.contactMemberships.filter((contact) => {
      const name = contact?.user?.userName?.toLowerCase();
      return name && name.includes(term);
    });

    // //////console.log("getContactsBySearch results:", results);
    return results;
  }
}
