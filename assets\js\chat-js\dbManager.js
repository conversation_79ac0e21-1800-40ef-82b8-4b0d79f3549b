/* مدير قاعدة البيانات - يتعامل مع عمليات IndexedDB */

window.DBManager = {
  dbName: "ChatAppCloneDB",
  dbVersion: 1,
  db: null,
  isInitialized: false,

  /* تهيئة قاعدة البيانات */
  async initializeDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        console.error("Error opening database:", request.error);
        reject(request.error);
      };

      request.onsuccess = (event) => {
        this.db = event.target.result;
        this.isInitialized = true;

        ////////console.log("Database initialized successfully");
        resolve();
      };

      request.onupgradeneeded = (event) => {
        //////console.log('Database upgrade needed - creating object stores');

        const db = event.target.result;

        // إنشاء مخزن للمحادثات
        if (!db.objectStoreNames.contains("chats")) {
          //////console.log('Creating chats store');

          const chatStore = db.createObjectStore("chats", {
            keyPath: "locId", // id is the key property in each record
            autoIncrement: true,
          });
          // إنشاء فهرس للبحث السريع عن المحادثات حسب آخر رسالة
          chatStore.createIndex("lastMessageDate", "lastMessageDate", {
            unique: false,
          });
          // Create an index on the "id" property so you can search by it if needed.
          // The "id" field in your records can be null.
          chatStore.createIndex("id", "id", { unique: false });
        }

        // إنشاء مخزن للرسائل
        if (!db.objectStoreNames.contains("messages")) {
          //////console.log('Creating messages store');

          const messageStore = db.createObjectStore("messages", {
            keyPath: "locId", // id is the key property in each record
            autoIncrement: true,
          });
          messageStore.createIndex("id", "id", { unique: false });

          messageStore.createIndex("chatId", "chatID", { unique: false });
          messageStore.createIndex("createdDate", "createdDate", {
            unique: false,
          });
        }

        // إنشاء مخزن للمستخدمين
        if (!db.objectStoreNames.contains("users")) {
          //////console.log('Creating users store');

          const store = db.createObjectStore("users", { keyPath: "id" });
          store.createIndex("by_userTypeId", "userTypeId", { unique: false });
        }

        // إنشاء مخزن للبيانات العامة
        if (!db.objectStoreNames.contains("pageData")) {
          //////console.log('Creating pageData store');

          db.createObjectStore("pageData", { keyPath: "id" });
        }
        // إنشاء مخزن للمستخدم الحالي
        if (!db.objectStoreNames.contains("currentUser")) {
          //////console.log('Creating currentUser store');
          db.createObjectStore("currentUser", { keyPath: "id" });
        }
        if (!db.objectStoreNames.contains("contacts")) {
          db.createObjectStore("contacts", { keyPath: "id" });
        }
        if (!db.objectStoreNames.contains("contactMemberships")) {
          db.createObjectStore("contactMemberships", { keyPath: "id" });
        }
        if (!db.objectStoreNames.contains("team")) {
          db.createObjectStore("team", { keyPath: "id" });
        }
      };
    });
  },

  /* التأكد من تهيئة قاعدة البيانات قبل تنفيذ أي عملية */
  async ensureInitialized() {
    if (!this.isInitialized || !this.db) {
      await this.initializeDB();
    }
  },
  /* التحقق من وجود قاعدة البيانات */
  async isDatabaseExists() {
    return new Promise((resolve) => {
      // Modern browsers support the databases() method
      indexedDB
        .databases()
        .then((dbs) => {
          const dbExists = dbs.some(
            (db) => db.name === this.dbName && db.version === this.dbVersion
          );

          if (dbExists) {
            //////console.log('Database exists with correct version');
            resolve(true);
          } else {
            //////console.log('Database does not exist or has different version');
            resolve(false);
          }
        })
        .catch((error) => {
          console.error("Error checking databases:", error);
          resolve(false);
        });
      //return indexedDB.databases().then((databases) => {
      //    return databases.some(db => db.name === this.dbName &&
      //        db.version === this.dbVersion);
      //});
    });
  },

  /* حذف قاعدة البيانات */
  async removeDatabase() {
    return new Promise((resolve, reject) => {
      // إغلاق الاتصال الحالي إذا كان مفتوحًا
      if (this.db) {
        this.db.close();
        this.db = null;
        this.isInitialized = false;
      }
      const request = indexedDB.deleteDatabase(this.dbName, this.dbVersion);

      request.onerror = () => {
        console.error("Error deleting database:", request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        //////console.log('Database deleted successfully');
        resolve();
      };
    });
  },
  /* حفظ المحادثات */
  async saveChats(chats) {
    await this.ensureInitialized();

    try {
      // تحديث lastMessageDate لكل محادثة قبل الحفظ
      let processedChats = chats.map((chat) => {
        // Get the most recent message from lastMessages array
        const lastMessage = chat.lastMessages?.[0];
        // Set lastMessageDate to the timestamp of the last message, or 0 if no messages
        chat.lastMessageDate = lastMessage
          ? new Date(lastMessage.createdDate).getTime()
          : 0;
        return chat;
      });

      // Save the processed chats
      processedChats = await this.saveData("chats", processedChats);
      //console.log("Chats saved successfully:", processedChats);
      return processedChats;
    } catch (error) {
      console.error("Error saving chats:", error);
      throw error;
    }
  },
  /* الحصول على محادثة بواسطة معرفها */
  async getChatById(chatId) {
    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(["chats"], "readonly");
        const store = transaction.objectStore("chats");

        // Use the secondary index "id" to retrieve the chat.
        const index = store.index("id");
        const request = index.get(chatId);

        request.onsuccess = async (event) => {
          let chat = event.target.result;
          if (chat) {
            // If a chat is found, attempt to retrieve its messages.
            try {
              const messages = await this.getMessagesByChatId(chat.id, -1);
              // Sort messages by createdDate in descending order (newest first)
              chat.lastMessages = messages.messages.sort((a, b) => {
                const dateA = new Date(a.createdDate).getTime();
                const dateB = new Date(b.createdDate).getTime();
                return dateB - dateA;
              });
            } catch (msgError) {
              console.error("Error retrieving messages for chat:", msgError);
              // Optionally, set lastMessages to an empty array if retrieval fails.
              chat.lastMessages = [];
            }
            resolve(chat);
          } else {
            console.error(
              `Chat with ID ${chatId} not found (using index 'id')`
            );
            resolve(null);
          }
        };

        request.onerror = () => {
          console.error(
            "Error retrieving chat by secondary index:",
            request.error
          );
          reject(request.error);
        };

        transaction.onerror = (event) => {
          console.error(
            "Transaction error in getChatById:",
            event.target.error
          );
          reject(event.target.error);
        };
      } catch (error) {
        console.error("Error in getChatById:", error);
        reject(error);
      }
    });
  },
  async getChatByLocId(chatLocId) {
    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(["chats"], "readonly");
        const store = transaction.objectStore("chats");

        // First, try to get by the primary key ("locId")
        const primaryRequest = store.get(chatLocId);

        primaryRequest.onsuccess = async (event) => {
          let chat = event.target.result;
          //////console.log("primaryRequest 1 ", chat)

          // If found by primary key, fetch its messages and resolve.
          const messages = await this.getMessagesByChatId(chat.id, -1);
          //////console.log("messagesssssss", messages)
          chat.lastMessages = messages.messages.sort((a, b) => {
            const dateA = new Date(a.createdDate).getTime();
            const dateB = new Date(b.createdDate).getTime();
            return dateB - dateA; // Descending order
          });
          resolve(chat);
        };

        primaryRequest.onerror = () => {
          console.error(
            "Error retrieving chat by primary key:",
            primaryRequest.error
          );
          reject(primaryRequest.error);
        };

        transaction.onerror = (event) => {
          console.error(
            "Transaction error in getChatByLocId:",
            event.target.error
          );
          reject(event.target.error);
        };
      } catch (error) {
        console.error("Error in getChatByLocId:", error);
        reject(error);
      }
    });
  },

  /* حفظ الرسائل */
  /* حفظ البيانات في المخزن المحدد */

  async saveData(storeName, data) {
    await this.ensureInitialized();
    return new Promise((resolve, reject) => {
      try {
        const tx = this.db.transaction([storeName], "readwrite");
        const store = tx.objectStore(storeName);

        // Normalize to array
        const items = Array.isArray(data) ? data : [data];

        // Create promises for each put operation that resolve to the saved item
        const putPromises = items.map((item) => {
          // Clone so we don't mutate original
          const toSave = { ...item };
          return new Promise((res, rej) => {
            const req = store.put(toSave);
            req.onsuccess = (event) => {
              // If keyPath is auto-generated, assign it back
              const keyPath = store.keyPath;
              if (keyPath && event.target.result !== undefined) {
                toSave[keyPath] = event.target.result;
              }
              res(toSave);
            };
            req.onerror = (event) => rej(event.target.error);
          });
        });

        // Wait for all puts, then resolve with array of saved items
        Promise.all(putPromises)
          .then((savedItems) => {
            //////console.log(`Data saved successfully to ${storeName}`, savedItems);
            // If original input was a single object, return that, otherwise return array
            resolve(savedItems);
          })
          .catch((error) => {
            console.error(`Error saving data to ${storeName}:`, error);
            reject(error);
          });
      } catch (error) {
        console.error(`Error in saveData for ${storeName}:`, error);
        reject(error);
      }
    });
  },
  /* حفظ الرسائل */

  async saveMessages(messages) {
    //////console.log("Messages saved successfully:result", messages);

    return new Promise((resolve, reject) => {
      try {
        // Open a readwrite transaction on the "messages" store.
        const transaction = this.db.transaction(["messages"], "readwrite");
        const store = transaction.objectStore("messages");

        // Prepare an array of promises that update each message and capture the new locId.
        let promises = [];

        if (Array.isArray(messages)) {
          ////////console.log("Array.isArray(messages)",messages)
          messages.forEach((message) => {
            // Remove locId so the autoIncrement key is generated
            if (message.hasOwnProperty("locId")) {
              delete message.locId;
            }
            // Create a promise for the store.put request
            const putPromise = new Promise((res, rej) => {
              const request = store.put(message);
              request.onsuccess = (event) => {
                // Capture the auto-generated key and assign it back to message.locId
                message.locId = event.target.result;
                ////////console.log("message.locId", messages)

                res(message);
              };
              request.onerror = (event) => {
                rej(event.target.error);
              };
            });

            promises.push(putPromise);
          });
        } else {
          // Process a single message object similarly.
          if (messages.hasOwnProperty("locId")) {
            delete messages.locId;
          }
          promises.push(
            new Promise((res, rej) => {
              const request = store.put(messages);
              request.onsuccess = (event) => {
                messages.locId = event.target.result;
                ////////console.log("messages.locId promises.push", messages)

                res(messages);
              };
              request.onerror = (event) => {
                rej(event.target.error);
              };
            })
          );
        }

        // Wait until all put() requests complete successfully.
        Promise.all(promises)
          .then((result) => {
            //////console.log("Messages saved successfully:result", result);
            resolve(result); // Return the updated messages, which now include locId
          })
          .catch((error) => {
            console.error("Error saving messages:", error);
            reject(error);
          });
      } catch (error) {
        console.error("Error in saveMessages:", error);
        reject(error);
      }
    });
  },

  async saveMessages2(messages) {
    return new Promise((resolve, reject) => {
      try {
        const tx = this.db.transaction(["messages"], "readwrite");
        const store = tx.objectStore("messages");
        const promises = [];

        for (let msg of Array.isArray(messages) ? messages : [messages]) {
          // make a shallow clone so we don’t mutate the original
          const toSave = { ...msg };

          // only drop locId if this really is a brand‑new message:
          if (!toSave.locId) {
            delete toSave.locId;
          }

          promises.push(
            new Promise((res, rej) => {
              const req = store.put(toSave);
              req.onsuccess = (e) => {
                toSave.locId = e.target.result;
                res(toSave);
              };
              req.onerror = (e) => rej(e.target.error);
            })
          );
        }

        Promise.all(promises).then(resolve).catch(reject);
      } catch (err) {
        reject(err);
      }
    });
  },

  async updateMessage(message, keyToUpdate) {
    return new Promise((resolve, reject) => {
      try {
        // Open a transaction on the "messages" store with readwrite permissions.
        const transaction = this.db.transaction(["messages"], "readwrite");
        const store = transaction.objectStore("messages");

        // Use the put() method to update the message.
        // The put() method will insert the record if it doesn't exist or update it if it does.
        const request = store.put({ ...message, key: keyToUpdate });

        // When the update is successful, resolve the promise.
        request.onsuccess = () => {
          //////console.log("Message updated successfully:", message);
          resolve(message);
        };

        // In case of error, reject the promise.
        request.onerror = (event) => {
          console.error("Error updating message:", event.target.error);
          reject(event.target.error);
        };

        // Optional: If you want to catch transaction errors.
        transaction.onerror = (event) => {
          console.error(
            "Transaction error while updating message:",
            event.target.error
          );
          reject(event.target.error);
        };
      } catch (error) {
        console.error("Error in updateMessage:", error);
        reject(error);
      }
    });
  },
  async updateItem(storeName, updatedItem) {
    return new Promise((resolve, reject) => {
      try {
        // Open a transaction in "readwrite" mode on the specified store.
        const transaction = this.db.transaction([storeName], "readwrite");
        const store = transaction.objectStore(storeName);

        // Call put() with the updated item. put() will update the record if
        // its key (as defined in the store's keyPath) already exists.
        const request = store.put(updatedItem);

        // On successful update, resolve the promise with the updated item.
        request.onsuccess = (event) => {
          //////console.log("Item updated successfully:", updatedItem);
          resolve(updatedItem);
        };

        // Handle errors in the request.
        request.onerror = (event) => {
          console.error("Error updating item:", event.target.error);
          reject(event.target.error);
        };

        // Optionally catch errors at the transaction level.
        transaction.onerror = (event) => {
          console.error(
            "Transaction error while updating item:",
            event.target.error
          );
          reject(event.target.error);
        };
      } catch (error) {
        console.error("Error in updateItem:", error);
        reject(error);
      }
    });
  },
  async _updateMessageRecord(updatedMessage) {
    return new Promise((resolve, reject) => {
      try {
        // Open transaction on "messages" store in readwrite mode.
        const transaction = this.db.transaction(["messages"], "readwrite");
        const store = transaction.objectStore("messages");

        // Make sure to include the original id in the object
        const request = store.put(updatedMessage);

        request.onsuccess = () => {
          //////console.log("Message updated successfully:", updatedMessage);
          resolve(updatedMessage);
        };

        request.onerror = (event) => {
          console.error("Error updating message:", event.target.error);
          reject(event.target.error);
        };

        transaction.onerror = (event) => {
          console.error(
            "Transaction error while updating message:",
            event.target.error
          );
          reject(event.target.error);
        };
      } catch (error) {
        console.error("Error in _updateMessageRecord:", error);
        reject(error);
      }
    });
  },

  /* حفظ أو تحديث رسالة واحدة */
  async saveOrUpdateMessage(message) {
    //await this.ensureInitialized();
    return new Promise((resolve, reject) => {
      try {
        // Validate message
        if (!message || !message.id) {
          throw new Error(
            "Invalid message data provided to saveOrUpdateMessage"
          );
        }

        const transaction = this.db.transaction(["messages"], "readwrite");
        const store = transaction.objectStore("messages");
        const request = store.put(message); // Use put() for add or update

        request.onsuccess = () => {
          //////console.log(`Message ${message.id} saved/updated successfully.`);
          resolve(message);
        };

        request.onerror = () => {
          console.error("Error saving/updating message:", request.error);
          reject(request.error);
        };

        transaction.onerror = (event) => {
          console.error(
            "Transaction error in saveOrUpdateMessage:",
            event.target.error
          );
          reject(event.target.error);
        };
      } catch (error) {
        console.error("Error in saveOrUpdateMessage setup:", error);
        reject(error);
      }
    });
  },


  /* الحصول على آخر معرف رسالة */
  async getLastMessageId() {
    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(["messages"], "readonly");
        const store = transaction.objectStore("messages");
        const request = store.openCursor(null, "prev");

        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor) {
            // Get the last message ID
            const lastMessageId = cursor.value.id;
            // //////console.log('Last message ID:', lastMessageId);
            resolve(lastMessageId);
          } else {
            // If no messages exist, return 0
            //////console.log("No messages found, returning 0");
            resolve(0);
          }
        };

        request.onerror = () => {
          console.error("Error getting last message ID:", request.error);
          reject(request.error);
        };

        transaction.onerror = (event) => {
          console.error("Transaction error:", event.target.error);
          reject(event.target.error);
        };
      } catch (error) {
        console.error("Error in getLastMessageId:", error);
        reject(error);
      }
    });
  },
  /* حفظ المستخدمين */
  async saveUsers(users) {
    return this.saveData("users", users);
  },

  /* حفظ البيانات العامة */
  async savePageData(pageData) {
    return this.saveData("pageData", pageData);
  },

  /* استرجاع المحادثات */

  async getChatss() {
    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(["chats"], "readonly");
        const store = transaction.objectStore("chats");
        const index = store.index("lastMessageDate");
        const request = index.openCursor(null, "prev");
        const results = [];

        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor) {
            results.push(cursor.value);
            cursor.continue();
          } else {
            resolve(results);
          }
        };

        request.onerror = (event) => {
          console.error("Error in cursor:", event.target.error);
          reject(event.target.error);
        };

        transaction.oncomplete = () => {};

        transaction.onerror = (event) => {
          console.error("Transaction error:", event.target.error);
          reject(event.target.error);
        };
      } catch (error) {
        console.error("Error in getChatsSortedByLastMessage:", error);
        reject(error);
      }
    });
  },

  /**
   * Get chats with pagination
   * @param {number} page - Page number (1-based)
   * @param {number} limit - Number of chats per page
   * @returns {Promise<{chats: Array, hasMore: boolean}>}
   */
  async getChatsAndSearch(page = 1, limit = 10) {
    try {
      //const db = await this.db.getDatabase();
      const transaction = this.db.transaction(["chats"], "readonly");
      const store = transaction.objectStore("chats");

      // Create an index on lastMessageDate if it doesn't exist
      if (!store.indexNames.contains("lastMessageDate")) {
        db.close();
        await this.createLastMessageDateIndex();
        return this.getChats(page, limit);
      }

      const indexStore = store.index("lastMessageDate");

      // Calculate skip and limit
      const skip = (page - 1) * limit;
      let count = 0;

      let chats = [];
      let hasMore = false;

      // Use a cursor to implement pagination
      return await new Promise((resolve, reject) => {
        const request = indexStore.openCursor(null, "prev"); // Sort by lastMessageDate descending

        request.onsuccess = (event) => {
          const cursor = event.target.result;

          if (cursor) {
            if (count < skip) {
              // Skip records before the desired page
              count++;
              cursor.continue();
            } else if (chats.length < limit) {
              // Collect records for the current page
              chats.push(cursor.value);
              cursor.continue();
            } else {
              // We have one more record, so there are more pages
              hasMore = true;
              resolve();
            }
          } else {
            resolve();
          }
        };

        request.onerror = () => reject(request.error);
      });

      return { chats, hasMore };
    } catch (error) {
      console.error("Error getting chats:", error);
      throw error;
    }
  },
  async getChatsAndSearch(page = 1, limit = 10, searchTerm = "") {
    try {
      //const db = await this.db.getDatabase();
      const transaction = this.db.transaction(["chats"], "readonly");
      const store = transaction.objectStore("chats");

      // Create an index on lastMessageDate if it doesn't exist
      if (!store.indexNames.contains("lastMessageDate")) {
        db.close();
        await this.createLastMessageDateIndex();
        return this.getChats(page, limit);
      }

      const indexStore = store.index("lastMessageDate");
      const term = searchTerm.trim().toLowerCase();

      // Calculate skip and limit
      const skip = (page - 1) * limit;
      let count = 0;
      let index = 0; // ترقيم المحادثات الفعلي (بعد الفلترة)

      let chats = [];
      let hasMore = false;

      // Use a cursor to implement pagination
      return await new Promise((resolve, reject) => {
        const request = indexStore.openCursor(null, "prev"); // Sort by lastMessageDate descending

        // request.onsuccess = (event) => {
        //   const cursor = event.target.result;

        //   if (cursor) {
        //     if (count < skip) {
        //       // Skip records before the desired page
        //       count++;
        //       cursor.continue();
        //     } else if (chats.length < limit) {
        //       // Collect records for the current page
        //       chats.push(cursor.value);
        //       cursor.continue();
        //     } else {
        //       // We have one more record, so there are more pages
        //       hasMore = true;
        //       resolve();
        //     }
        //   } else {
        //     resolve();
        //   }
        // };
        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (!cursor) {
            // انتهت السجلات
            return resolve({
              chats: chats,
              hasMore: false,
              total: chats.length,
            });
          }

          const chat = cursor.value;
          let matchesFilter = true;

          // تطبيق الفلتر بالبحث إن وُجد
          if (term) {
            const nameMatch = chat.name?.toLowerCase().includes(term) ?? false;
            let phoneMatch = false;
            if (chat.type === "Individual" && Array.isArray(chat.members)) {
              phoneMatch = chat.members.some((m) =>
                (m.user?.phoneNumber || "").includes(term)
              );
            }
            matchesFilter = nameMatch || phoneMatch;
          }

          if (matchesFilter) {
            index++;
            // إذا limit=0 نُرجع كل المحادثات المفلترة
            if (limit === 0) {
              chats.push({ index, ...chat });
            } else {
              // تفادى العناصر قبل الصفحة المطلوبة
              if (count < skip) {
                count++;
              } else if (chats.length < limit) {
                chats.push({ index, ...chat });
              } else {
                // وجدنا أكثر من limit عنصر => هناك صفحات لاحقة
                hasMore = true;
                return resolve({
                  chats: chats,
                  hasMore,
                  total: chats.length,
                });
              }
            }
          }

          cursor.continue();
        };

        request.onerror = () => reject(request.error);
      });

      // return { chats, hasMore };
    } catch (error) {
      console.error("Error getting chats:", error);
      throw error;
    }
  },

  async findChats(searchTerm = "") {
    // normalize for case‐insensitive matching
    const term = searchTerm.trim().toLowerCase();

    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(["chats"], "readonly");
        const store = transaction.objectStore("chats");
        const index = store.index("lastMessageDate");
        const request = index.openCursor(null, "prev");
        const results = [];

        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (!cursor) {
            // no more records
            resolve(results);
            return;
          }

          const chat = cursor.value;
          let keep = true;

          if (term) {
            // match on chat.name
            const nameMatch =
              chat.name && chat.name.toLowerCase().includes(term);

            // if Individual, also match on phoneNumber
            let phoneMatch = false;
            if (chat.type === "Individual" && Array.isArray(chat.members)) {
              phoneMatch = chat.members.some((m) => {
                const phone = m.user?.phoneNumber ?? "";
                return phone.includes(term);
              });
            }

            keep = nameMatch || phoneMatch;
          }

          if (keep) {
            results.push(chat);
          }

          cursor.continue();
        };

        request.onerror = (event) => {
          console.error("Error in cursor:", event.target.error);
          reject(event.target.error);
        };

        transaction.oncomplete = () => {
          // optional://console.log("Transaction complete");
        };
        transaction.onerror = (event) => {
          console.error("Transaction error:", event.target.error);
          reject(event.target.error);
        };
      } catch (err) {
        console.error("Error in getChatsSortedByLastMessage:", err);
        reject(err);
      }
    });
  },

  /**
   * Find chats containing specific users
   */
  async findChatsWithUsers(userIds) {
    try {
      //const db = await this.dbManager.getDatabase();
      const transaction = this.db.transaction(["chats"], "readonly");
      const store = transaction.objectStore("chats");

      const chats = [];
      await new Promise((resolve, reject) => {
        const request = store.openCursor();

        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor) {
            const chat = cursor.value;
            if (
              chat.members &&
              chat.members.some((member) => userIds.includes(member.userID))
            ) {
              chats.push(chat);
            }
            cursor.continue();
          } else {
            resolve();
          }
        };

        request.onerror = () => reject(request.error);
      });

      return chats;
    } catch (error) {
      console.error("Error finding chats with users:", error);
      throw error;
    }
  },
  /**
   * Create lastMessageDate index
   */
  async createLastMessageDateIndex() {
    try {
      //const db = await this.dbManager.getDatabase();
      const version = this.db.version + 1;
      db.close();

      return new Promise((resolve, reject) => {
        const request = indexedDB.open(this.dbManager.dbName, version);

        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          const store = db.transaction.objectStore("chats");

          // Create the index if it doesn't exist
          if (!store.indexNames.contains("lastMessageDate")) {
            store.createIndex("lastMessageDate", "lastMessageDate", {
              unique: false,
            });
          }
        };

        request.onsuccess = () => {
          request.result.close();
          resolve();
        };

        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error("Error creating lastMessageDate index:", error);
      throw error;
    }
  },
  async getChatssssss() {
    return this.getAllData("chats");
  },
  /* استرجاع الرسائل */
  async getMessages() {
    return this.getAllData("messages");
  },
  /**
   * Update chat messages
   */
  async updateChatMessages(chatId, messages, options = {}) {
    try {
      const chat = await this.getChatById(chatId);
      if (!chat) {
        throw new Error(`Chat with ID ${chatId} not found`);
      }

      // Initialize lastMessages array if it doesn't exist
      if (!chat.lastMessages) {
        chat.lastMessages = [];
      }

      // Add new messages
      if (options.prepend) {
        chat.lastMessages = [...messages, ...chat.lastMessages];
      } else {
        chat.lastMessages = [...chat.lastMessages, ...messages];
      }

      // Update unread count if specified
      if (options.updateUnreadCount) {
        const newUnreadCount = messages.filter(
          (msg) => msg.senderID !== options.currentUserId
        ).length;
        chat.unreadCount = (chat.unreadCount || 0) + newUnreadCount;
      }

      // Update lastMessageDate if there are new messages
      if (messages.length > 0) {
        chat.lastMessageDate = new Date(messages[0].createdDate).getTime();
      }

      // Save updated chat
      return await this.updateChat(chat);
    } catch (error) {
      console.error("Error updating chat messages:", error);
      throw error;
    }
  },
  /**
   * Update chat
   */
  async updateChat(chat) {
    try {
      const transaction = this.db.transaction(["chats"], "readwrite");
      const store = transaction.objectStore("chats");

      return new Promise((resolve, reject) => {
        const request = store.put(chat);
        request.onsuccess = () => resolve(chat);
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error("Error updating chat:", error);
      throw error;
    }
  },
  /* استرجاع المستخدمين */
  async getUsers() {
    return this.getAllData("users");
  },
  async getUserById(userId) {
    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(["users"], "readonly");
        const store = transaction.objectStore("users");
        const request = store.get(userId);

        request.onsuccess = () => {
          resolve(request.result || null);
        };

        request.onerror = () => {
          console.error("Error getting user by ID:", request.error);
          reject(request.error);
        };
      } catch (error) {
        console.error("Error in getUserById:", error);
        resolve(null);
      }
    });
  },
  /* استرجاع البيانات العامة */
  async getPageData() {
    return this.getAllData("pageData");
  },

  /* استرجاع جميع البيانات من المخزن المحدد */
  async getAllData(storeName) {
    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction([storeName], "readonly");
        const store = transaction.objectStore(storeName);
        const request = store.getAll();

        request.onsuccess = () => {
          ////////console.log(`Retrieved all data from ${storeName}:`, request.result);
          resolve(request.result);
        };
        request.onerror = () => {
          console.error(
            `Error retrieving data from ${storeName}:`,
            request.error
          );
          reject(request.error);
        };
      } catch (error) {
        console.error(`Error in getAllData for ${storeName}:`, error);
        reject(error);
      }
    });
  },

  /* استرجاع المحادثات مرتبة حسب آخر رسالة */
  async getChatsSortedByLastMessage() {
    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(["chats"], "readonly");
        const store = transaction.objectStore("chats");
        const index = store.index("lastMessageDate");
        const request = index.openCursor(null, "prev");
        const results = [];

        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor) {
            results.push(cursor.value);
            // //////console.log("cursor", cursor.value);

            cursor.continue();
          } else {
            ////////console.log("Retrieved sorted chats:", results);
            resolve(results);
          }
        };

        request.onerror = (event) => {
          console.error("Error in cursor:", event.target.error);
          reject(event.target.error);
        };

        transaction.oncomplete = () => {
          ////////console.log("Transaction completed");
        };

        transaction.onerror = (event) => {
          console.error("Transaction error:", event.target.error);
          reject(event.target.error);
        };
      } catch (error) {
        console.error("Error in getChatsSortedByLastMessage:", error);
        reject(error);
      }
    });
  },

  /* استرجاع رسائل محادثة معينة مع الصفحات */
  async getMessagesByChatId(chatId, page = 1, pageSize = 10) {
    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(["messages"], "readonly");
        const store = transaction.objectStore("messages");
        const index = store.index("chatId");
        const request = index.getAll(chatId);

        request.onsuccess = () => {
          try {
            // Sort messages by date in descending order (newest first)
            // Filter out deleted messages.
            const activeMessages = request.result.filter(
              (msg) => msg.isDeleted !== true
            );
            // Sort messages by date in descending order (newest first)
            const allMessages = activeMessages.sort((a, b) => {
              const dateA = new Date(a.createdDate).getTime();
              const dateB = new Date(b.createdDate).getTime();
              return dateB - dateA; // Descending order
            });

            // If page is -1, return all messages without pagination
            if (page === -1) {
              resolve({
                messages: allMessages,
                totalMessages: allMessages.length,
                currentPage: -1,
                totalPages: 1,
                hasMore: false,
                firstMessageDate: allMessages[0]?.createdDate,
                lastMessageDate:
                  allMessages[allMessages.length - 1]?.createdDate,
              });
              return;
            }
            // Calculate pagination for normal cases
            const startIndex = (page - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const paginatedMessages = allMessages.slice(startIndex, endIndex);
            const totalMessages = allMessages.length;
            //////console.log("paginatedMessages", paginatedMessages)

            resolve({
              messages: paginatedMessages.sort((a, b) => {
                const dateA = new Date(a.createdDate).getTime();
                const dateB = new Date(b.createdDate).getTime();
                return dateA - dateB; // Descending order
              }),
              totalMessages,
              currentPage: page,
              totalPages: Math.ceil(totalMessages / pageSize),
              hasMore: endIndex < totalMessages,
              firstMessageDate: paginatedMessages[0]?.createdDate,
              lastMessageDate:
                paginatedMessages[paginatedMessages.length - 1]?.createdDate,
            });
          } catch (sortError) {
            console.error("Error processing messages:", sortError);
            reject(sortError);
          }
        };

        request.onerror = () => {
          console.error("Error retrieving messages:", request.error);
          reject(request.error);
        };

        transaction.onerror = (event) => {
          console.error("Transaction error:", event.target.error);
          reject(event.target.error);
        };
      } catch (error) {
        console.error("Error in getMessagesByChatId:", error);
        reject(error);
      }
    });
  },

  /* حذف جميع البيانات */
  async clearAllData() {
    const stores = ["chats", "messages", "users", "pageData", "currentUser"];
    const promises = stores.map(
      (storeName) =>
        new Promise((resolve, reject) => {
          const transaction = this.db.transaction([storeName], "readwrite");
          const store = transaction.objectStore(storeName);
          const request = store.clear();

          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        })
    );

    return Promise.all(promises);
  },

  /* حذف جميع البيانات */
  async clearStore(storeName) {
    await this.ensureInitialized();
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], "readwrite");
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  },

  /* الحصول على رسالة بواسطة معرفها */
  async getMessageById(messageId, type = "Id") {
    try {
      const transaction = this.db.transaction(["messages"], "readonly");
      const store = transaction.objectStore("messages");
      //const message = await store.get(messageId);
      /// First attempt: get via primary key (locId)
      const message = await this.requestToPromise(store.get(messageId));

      // If not found using primary key, try the "id" index.
      if (type === "locId") {
        const message = await this.requestToPromise(store.get(messageId));

        return message;
      } else {
        const index = store.index("id");
        return await this.requestToPromise(index.get(messageId));
      }
    } catch (error) {
      console.error("Error getting message by ID:", error);
      return null;
    }
  },

  async getMessageByIdsss(messageId) {
    try {
      const transaction = this.db.transaction(["messages"], "readonly");
      const store = transaction.objectStore("messages");
      //const message = await store.get(messageId);
      /// First attempt: get via primary key (locId)
      //const message = await this.requestToPromise(store.get(messageId));
      const index = store.index("id");
      return await this.requestToPromise(index.get(messageId));
      // If not found using primary key, try the "id" index.
      //if (message) {
      //    return message;
      //} else {
      //    const index = store.index("id");
      //    return await this.requestToPromise(index.get(messageId));
      //}
    } catch (error) {
      console.error("Error getting message by ID:", error);
      return null;
    }
  },

  requestToPromise(request) {
    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  },
  /**
   * Mark chat as read
   */
  async markChatAsRead(chatId) {
    try {
      const chat = await this.getChatById(chatId);
      if (chat) {
        chat.unreadCount = 0;
        return await this.updateChat(chat);
      }
      return null;
    } catch (error) {
      console.error("Error marking chat as read:", error);
      throw error;
    }
  },
  /* حذف رسالة */
  async deleteMessage(messageId) {
    try {
      const transaction = this.db.transaction(["messages"], "readwrite");
      const store = transaction.objectStore("messages");
      await store.delete(messageId);
      //////console.log(`Message ${messageId} deleted successfully`);
      return true;
    } catch (error) {
      console.error("Error deleting message:", error);
      throw error;
    }
  },

  async updateMessageStatus(messageId, newStatus) {
    try {
      const chats = await this.findChatsWithMessage(messageId);
      const updatedChats = [];

      for (const chat of chats) {
        const messageIndex = chat.lastMessages.findIndex(
          (msg) => msg.id === messageId
        );
        //////console.log("updateMessageStatus", messageIndex)
        if (messageIndex !== -1) {
          chat.lastMessages[messageIndex].messageStatus = newStatus;

          //await this.saveMessages(chat.lastMessages[messageIndex]);
          const updatedChat = await this.updateChat(chat);
          updatedChats.push(updatedChat);
        }
      }

      return updatedChats;
    } catch (error) {
      console.error("Error updating message status:", error);
      throw error;
    }
  },

  /**
   * Find chats containing a specific message
   */
  async findChatsWithMessage(messageId) {
    try {
      //const db = await this.dbManager.getDatabase();
      const transaction = this.db.transaction(["chats"], "readonly");
      const store = transaction.objectStore("chats");

      const chats = [];
      await new Promise((resolve, reject) => {
        const request = store.openCursor();

        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor) {
            const chat = cursor.value;
            if (
              chat.lastMessages &&
              chat.lastMessages.some((msg) => msg.id === messageId)
            ) {
              chats.push(chat);
            }
            cursor.continue();
          } else {
            resolve();
          }
        };

        request.onerror = () => reject(request.error);
      });

      return chats;
    } catch (error) {
      console.error("Error finding chats with message:", error);
      throw error;
    }
  },
  /* حفظ المستخدم الحالي */
  async saveCurrentUser(userData) {
    //await this.ensureInitialized();
    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(["currentUser"], "readwrite");
        const store = transaction.objectStore("currentUser");

        // Clear existing data first (we only want one current user)
        store.clear();

        // Add the new user data
        const request = store.add(userData);

        request.onsuccess = () => {
          //////console.log('Current user saved successfully:', userData);
          resolve(userData);
        };

        request.onerror = () => {
          console.error("Error saving current user:", request.error);
          reject(request.error);
        };
      } catch (error) {
        console.error("Error in saveCurrentUser:", error);
        reject(error);
      }
    });
  },

  /* استرجاع المستخدم الحالي */
  async getCurrentUser() {
    await this.ensureInitialized();
    return new Promise((resolve, reject) => {
      try {
        // إنشاء مخزن للمستخدم الحالي
        if (!this.db.objectStoreNames.contains("currentUser")) {
          //////console.log('Creating currentUser store');
          this.db.createObjectStore("currentUser", { keyPath: "id" });
        }
        const transaction = this.db.transaction(["currentUser"], "readonly");
        const store = transaction.objectStore("currentUser");
        const request = store.getAll();

        request.onsuccess = () => {
          // Return the first user (there should only be one)
          const currentUser =
            request.result.length > 0 ? request.result[0] : null;
          //////console.log('Current user retrieved:', currentUser);
          resolve(currentUser);
        };

        request.onerror = () => {
          console.error("Error retrieving current user:", request.error);
          reject(request.error);
        };
      } catch (error) {
        console.error("Error in getCurrentUser:", error);
        reject(error);
      }
    });
  },

  /* حذف المستخدم الحالي */
  async removeCurrentUser() {
    await this.ensureInitialized();
    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(["currentUser"], "readwrite");
        const store = transaction.objectStore("currentUser");
        const request = store.clear();

        request.onsuccess = () => {
          //////console.log('Current user removed successfully');
          resolve();
        };

        request.onerror = () => {
          console.error("Error removing current user:", request.error);
          reject(request.error);
        };
      } catch (error) {
        console.error("Error in removeCurrentUser:", error);
        reject(error);
      }
    });
  },

  async findUsersBySearch(searchTerm = "", userTypeId = null) {
    const term = searchTerm.trim().toLowerCase();

    return new Promise((resolve, reject) => {
      const tx = this.db.transaction("users", "readonly");
      const store = tx.objectStore("users");
      let req;

      if (userTypeId !== null) {
        // only walk records where userTypeId === given value
        const idx = store.index("by_userTypeId");
        const range = IDBKeyRange.only(userTypeId);
        req = idx.openCursor(range);
      } else {
        // no type filter → scan whole store
        req = store.openCursor();
      }

      const results = [];

      req.onsuccess = (event) => {
        const cursor = event.target.result;
        if (!cursor) {
          resolve(results);
          return;
        }

        const user = cursor.value;
        // now only need to apply text‐search
        if (
          !term ||
          user.userName?.toLowerCase().includes(term) ||
          user.phoneNumber?.includes(term)
        ) {
          results.push(user);
        }
        cursor.continue();
      };

      req.onerror = (event) => reject(event.target.error);
      tx.onerror = (event) => reject(event.target.error);
    });
  },

  async deleteChat(ChatId) {
    try {
      const transaction = this.db.transaction(["chats"], "readwrite");
      const store = transaction.objectStore("chats");
      await store.delete(ChatId);
      //////console.log(`Message ${messageId} deleted successfully`);
      return true;
    } catch (error) {
      console.error("Error deleting chat:", error);
      throw error;
    }
  },
};
