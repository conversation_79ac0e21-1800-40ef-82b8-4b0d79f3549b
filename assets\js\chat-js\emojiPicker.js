import { Picker } from "https://esm.sh/emoji-picker-element@1.18.2";

class EmojiPickerManager {
  constructor() {
    this.picker = null;
    this.emojiButton = null;
    this.messageInput = null;
    this.emojiWrapper = null;
    this.isOpen = false;
  }

  init() {
    // Initialize elements
    // this.emojiButton = document.querySelector("#sticker-icon");
    this.emojiButtons = document.querySelectorAll(".sticker-icon");

    //console.log("emoji-button", this.emojiButton);
    this.messageInput = document.querySelector(".message-input");
    this.emojiWrapper = document.querySelector(".emoji-wrapper");

    // Create emoji picker
    this.picker = new Picker({
      theme: "dark",
      visibleRows: 6,
      styleProperties: {
        "--background": "#2a2a2a",
        "--input-border-color": "#3a3a3a",
        "--input-font-color": "#ffffff",
        "--input-placeholder-color": "#999999",
        "--input-font-size": "14px",
        "--category-label-background-color": "#2a2a2a",
        "--category-label-font-color": "#ffffff",
        "--category-label-font-size": "13px",
        "--category-label-padding": "8px 12px",
        "--outline-color": "#3a3a3a",
      },
    });

    // Add picker to wrapper
    this.emojiWrapper.appendChild(this.picker);

    // Add event listeners
    // this.emojiButton.addEventListener("click", () => this.togglePicker());
    // 3. Wire up each button to toggle the picker
    this.emojiButtons.forEach((btn) =>
      btn.addEventListener("click", () => this.togglePicker())
    );
    this.picker.addEventListener("emoji-click", (event) =>
      this.handleEmojiSelect(event)
    );
    document.addEventListener("click", (event) =>
      this.handleClickOutside(event)
    );
  }

  togglePicker() {
    //console.log("togglePicker");
    this.isOpen = !this.isOpen;
    this.emojiWrapper.style.display = this.isOpen ? "block" : "none";
  }

  handleEmojiSelect(event) {
    const emoji = event.detail.unicode;
    const cursorPos = this.messageInput.selectionStart;
    const textBefore = this.messageInput.value.substring(0, cursorPos);
    const textAfter = this.messageInput.value.substring(cursorPos);
    this.messageInput.value = textBefore + emoji + textAfter;
    this.messageInput.focus();
    this.togglePicker();
  }

  handleClickOutside(event) {
    if (
      this.isOpen &&
      !this.emojiWrapper.contains(event.target) &&
      !this.emojiButton.contains(event.target)
    ) {
      this.togglePicker();
    }
  }
}

// Initialize the emoji picker
const emojiPicker = new EmojiPickerManager();
document.addEventListener("DOMContentLoaded", () => emojiPicker.init());
