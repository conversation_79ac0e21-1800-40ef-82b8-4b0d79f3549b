﻿/**
 * File Interactions
 * This file contains functions for interacting with files in the chat interface
 */

/**
 * View a full-size image
 * @param {HTMLElement} imgElement - The image element to view
 */
function viewFullImage(imgElement) {
  // Create overlay
  const overlay = document.createElement("div");
  overlay.className = "file-preview-overlay";
  overlay.style.display = "flex";
  overlay.style.alignItems = "center";
  overlay.style.justifyContent = "center";

  // Create image container
  const container = document.createElement("div");
  container.style.position = "relative";
  container.style.maxWidth = "90%";
  container.style.maxHeight = "90%";

  // Create image
  const img = document.createElement("img");
  img.src = imgElement.src;
  img.style.maxWidth = "100%";
  img.style.maxHeight = "90vh";
  img.style.objectFit = "contain";

  // Create close button
  const closeButton = document.createElement("button");
  closeButton.className = "file-preview-close";
  closeButton.innerHTML = "&times;";
  closeButton.style.position = "absolute";
  closeButton.style.top = "10px";
  closeButton.style.right = "10px";
  closeButton.style.background = "rgba(0, 0, 0, 0.5)";
  closeButton.style.color = "white";
  closeButton.style.border = "none";
  closeButton.style.borderRadius = "50%";
  closeButton.style.width = "40px";
  closeButton.style.height = "40px";
  closeButton.style.fontSize = "24px";
  closeButton.style.cursor = "pointer";

  // Add event listener to close button
  closeButton.addEventListener("click", () => {
    document.body.removeChild(overlay);
  });

  // Add elements to DOM
  container.appendChild(img);
  container.appendChild(closeButton);
  overlay.appendChild(container);
  document.body.appendChild(overlay);

  // Add event listener to close on overlay click
  overlay.addEventListener("click", (e) => {
    if (e.target === overlay) {
      document.body.removeChild(overlay);
    }
  });
}

/**
 * Play a video in a modal
 * @param {string} videoUrl - The URL of the video to play
 */

//function playVideo(videoUrl) {
//   ////console.log("videoUrl", videoUrl)
//    // Create overlay
//    const overlay = document.createElement('div');
//    overlay.className = 'file-preview-overlay';
//    overlay.style.display = 'flex';
//    overlay.style.alignItems = 'center';
//    overlay.style.justifyContent = 'center';

//    // Create video container
//    const container = document.createElement('div');
//    container.style.position = 'relative';
//    container.style.maxWidth = '90%';
//    container.style.maxHeight = '90%';

//    // Create video element
//    const video = document.createElement('video');
//    video.src = videoUrl;
//    video.controls = true;
//    video.autoplay = true;
//    video.style.maxWidth = '100%';
//    video.style.maxHeight = '90vh';
//    video.style.backgroundColor = 'black';

//    // Create close button
//    const closeButton = document.createElement('button');
//    closeButton.className = 'file-preview-close';
//    closeButton.innerHTML = '&times;';
//    closeButton.style.position = 'absolute';
//    closeButton.style.top = '10px';
//    closeButton.style.right = '10px';
//    closeButton.style.background = 'rgba(0, 0, 0, 0.5)';
//    closeButton.style.color = 'white';
//    closeButton.style.border = 'none';
//    closeButton.style.borderRadius = '50%';
//    closeButton.style.width = '40px';
//    closeButton.style.height = '40px';
//    closeButton.style.fontSize = '24px';
//    closeButton.style.cursor = 'pointer';

//    // Add event listener to close button
//    closeButton.addEventListener('click', () => {
//        video.pause();
//        document.body.removeChild(overlay);
//    });

//    // Add elements to DOM
//    container.appendChild(video);
//    container.appendChild(closeButton);
//    overlay.appendChild(container);
//    document.body.appendChild(overlay);

//    // Add event listener to close on overlay click
//    overlay.addEventListener('click', (e) => {
//        if (e.target === overlay) {
//            video.pause();
//            document.body.removeChild(overlay);
//        }
//    });
//}

/**
 * Open a document in a new tab or download it
 * @param {string} documentUrl - The URL of the document to open
 */
function openDocument(documentUrl) {
  window.open(documentUrl, "_blank");
}

/**
 * Open a file in a new tab or download it
 * @param {string} fileUrl - The URL of the file to open
 */
function openFile(fileUrl) {
  window.open(fileUrl, "_blank");
}

/**
 * Download a file from a message
 * @param {string} messageId - ID of the message containing the file to download
 * @param {boolean} forceDownload - Whether to force download even if Wi-Fi is required
 */
function downloadFile(messageId, forceDownload = false) {
  // Variable to store the progress interval timer for this download session
  let progressInterval;

  ChatProcessor.getMessageById(parseInt(messageId), "locId").then((message) => {
    if (!message || !message.file) return;

    ////console.log("Starting download for message:", message);

    // Check if the file needs WiFi and we're not forcing the download
    if (message.file.requiresWifi && !forceDownload) {
      // Check network connection (this is a mockup - in a real app you'd use the Network Information API)
      const isConnectedToWiFi = checkWifiConnection();

      if (!isConnectedToWiFi) {
        ////console.log("File requires WiFi connection, waiting...");
        // Update status in the message object
        message.file.downloadStatus = "WaitingForWifi";
        // Update the message in storage
        ChatProcessor.updateOnlyMessage(message.locId, message);
        // Update UI to show waiting for WiFi
        updateDownloadUI(message);
        return;
      }
    }

    // Check for available storage (mockup - in a real app you'd use the Storage API)
    const hasEnoughStorage = checkStorageAvailability(message.file.fileSize);
    if (!hasEnoughStorage) {
      console.error("Not enough storage to download file");
      message.file.downloadStatus = "Failed";
      message.file.downloadError = "Not enough storage space";

      // Update the message in storage
      ChatProcessor.updateOnlyMessage(message.locId, message);

      // Update UI to show error
      updateDownloadUI(message);
      return;
    }

    // Update the message to show download in progress
    message.file.downloadStatus = "Downloading";
    message.file.downloadProgress = 0;
    message.file.lastAttempt = new Date().toISOString();
    message.file.retryCount = (message.file.retryCount || 0) + 1;

    // Update the message in storage
    ChatProcessor.updateOnlyMessage(message.locId, message);

    // Update the UI immediately to show download starting
    updateDownloadUI(message);

    // Simulate download progress
    const simulateProgress = () => {
      // Set a realistic speed for different file sizes
      const fileSize = message.file.fileSize || 1000000; // Default to 1MB if unknown
      const totalTime = Math.min(Math.max(fileSize / 100000, 2), 15) * 1000; // Between 2-15 seconds
      const interval = 100; // Update every 100ms
      const steps = totalTime / interval;
      const incrementPerStep = 100 / steps;

      let progress = 0;
      // Clear any existing interval first to prevent memory leaks
      if (progressInterval) {
        clearInterval(progressInterval);
      }

      progressInterval = setInterval(() => {
        progress += incrementPerStep;
        if (progress >= 99) {
          // Cap at 99%, 100% is set when complete
          clearInterval(progressInterval);
          progress = 99;
        }

        // Update message
        message.file.downloadProgress = progress;

        // Update UI
        updateDownloadUI(message);
      }, interval);

      return progressInterval;
    };

    // Start progress simulation
    progressInterval = simulateProgress();

    // In a real implementation, actually fetch the file
    fetch(`${message.file.fileContent}`)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        ////console.log("File downloaded successfully:", response);
        return response.blob();
      })
      .then((blob) => {
        ////console.log("File downloaded successfully:", blob);
        // Clear progress simulation
        if (progressInterval) {
          clearInterval(progressInterval);
          progressInterval = null;
        }

        // Create a local URL for the downloaded file
        const url = URL.createObjectURL(blob);

        // Update the message with download complete status
        message.file.downloadStatus = "Downloaded";
        message.file.downloadProgress = 100;
        message.file.isDownloaded = true;
        message.file.localUrl = url;

        const downloadLink = document.createElement("a");
        downloadLink.href = url;
        downloadLink.download = message.file.fileName;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
        // Update the message in storage
        ChatProcessor.updateOnlyMessage(message.locId, message);

        // Update UI to show completed download
        updateDownloadUI(message);

        // Reload the message to show updated content
        setTimeout(async () => {
          const messageElement = document.querySelector(
            `[data-message-id="${message.locId}"]`
          );
          if (messageElement) {
            // Force reload the message UI (this would be better handled with a proper UI update mechanism)
            const chat = await ChatProcessor.getChatById(message.chatID);
            // In a real app, you'd use a proper state management system
            messageElement.innerHTML = MessageGenerator.generateMessageHTML(
              message,
              chat
            );
          }
        }, 100);
      })
      .catch((error) => {
        console.error("Error downloading file:", error);
        // Clear progress simulation
        if (progressInterval) {
          clearInterval(progressInterval);
          progressInterval = null;
        }

        // Update message with failure status
        message.file.downloadStatus = "Failed";
        message.file.downloadError = error.message || "Failed to download file";

        // Update the message in storage
        ChatProcessor.updateOnlyMessage(message.locId, message);

        // Update UI to show download failure
        updateDownloadUI(message);
      });
  });
}

// Note: The simulateDownloadProgress function has been moved inside the downloadFile function
// to properly scope the progressInterval variable and prevent ReferenceError

/**
 * Update the UI based on download status
 * @param {Object} message - The message object with file data
 */
function updateDownloadUI(message) {
  const messageElement = document.querySelector(
    `[data-message-id="${message.locId}"]`
  );
  if (!messageElement) return;

  // Get the message container
  const container = messageElement.querySelector(".file-message-container");
  if (!container) return;

  // Remove any existing overlay elements
  const existingOverlay = container.querySelector(
    ".download-button, .download-progress-overlay, .download-error-overlay, .wifi-waiting-overlay"
  );
  if (existingOverlay) {
    existingOverlay.remove();
  }

  // Get file element based on type
  const fileElement = container.querySelector(
    ".image-message, .video-message, .file-message, .audio-message, .document-message, .voice-message"
  );
  if (!fileElement) return;

  // Add appropriate class based on download status
  // fileElement.classList.remove('downloading', 'download-failed', 'downloaded', 'not-downloaded', 'wifi-waiting');

  // Generate the new overlay HTML based on download status
  let overlayHTML = "";

  if (message.file.requiresWifi) {
    // Case: Waiting for WiFi
    overlayHTML = `
            <div class="wifi-waiting-overlay">
                <div class="wifi-icon">
                    <i class="fe fe-wifi"></i>
                </div>
                <div class="wifi-text">Waiting for Wi-Fi</div>
                <div class="download-now-button" onclick="downloadFile('${message.locId}', true)">
                    Download now
                </div>
            </div>
        `;
    fileElement.classList.add("wifi-waiting");
  } else if (
    message.file.downloadStatus === "NotStarted" ||
    message.file.downloadStatus === undefined
  ) {
    // Case 1: File ready for download (not started)
    overlayHTML = `
            <div class="download-button" onclick="downloadFile('${message.locId}')">
                <i class="fe fe-download"></i>
            </div>
        `;
    fileElement.classList.add("not-downloaded");

    // For image/video: blur the thumbnail to indicate it needs download
    const img = container.querySelector("img");
    if (img) {
      img.classList.add("blurred-image");
    }
  } else if (message.file.downloadStatus === "Downloading") {
    // Case 2: File is downloading
    // Calculate stroke-dashoffset for circular progress
    const progress = message.file.downloadProgress / 100;
    const circumference = 2 * Math.PI * 16; // For a circle with r=16
    const dashOffset = circumference * (1 - progress);

    overlayHTML = `
            <div class="download-progress-overlay">
                <div class="progress-container">
                    <svg class="progress-ring" width="40" height="40">
                        <circle class="progress-ring-circle-bg" stroke="rgba(0,0,0,0.2)" stroke-width="3" fill="transparent" r="16" cx="20" cy="20"/>
                        <circle class="progress-ring-circle" stroke="#25D366" stroke-width="3" fill="transparent" r="16" cx="20" cy="20" 
                            stroke-dasharray="${circumference}" stroke-dashoffset="${dashOffset}"/>
                    </svg>
                    <div class="progress-percentage">${Math.round(
                      progress * 100
                    )}%</div>
                    <div class="cancel-button" onclick="cancelDownload('${
                      message.locId
                    }')">
                        <i class="fe fe-x"></i>
                    </div>
                </div>
            </div>
        `;
    fileElement.classList.add("downloading");
  } else if (message.file.downloadStatus === "Failed") {
    // Case 5.1: Download failed
    let errorMessage = message.file.downloadError || "Download failed";
    let errorIcon = "fe-alert-circle";

    // Case 5.2: Check for storage error
    if (
      message.file.downloadError &&
      message.file.downloadError.includes("storage")
    ) {
      errorMessage = "Not enough storage";
      errorIcon = "fe-hard-drive";
    }

    overlayHTML = `
            <div class="download-error-overlay">
                <i class="fe ${errorIcon}"></i>
                <span>${errorMessage}</span>
                <button class="retry-button" onclick="retryDownload('${message.locId}')">Retry</button>
            </div>
        `;
    fileElement.classList.add("download-failed");
  } else if (message.file.downloadStatus === "Downloaded") {
    // Case 3: Successfully downloaded
    fileElement.classList.add("downloaded");

    // Update image source if it's an image message
    const img = container.querySelector("img.blurred-image");
    if (img && message.file.localUrl) {
      img.src = message.file.localUrl;
      img.classList.remove("blurred-image");
    }

    // Update video thumbnail if it's a video message
    const videoThumbnail = container.querySelector(
      ".video-thumbnail.blurred-thumbnail"
    );
    if (videoThumbnail) {
      videoThumbnail.classList.remove("blurred-thumbnail");
    }

    // For document/audio: Add visual indicator of downloaded state
    const fileIcon = container.querySelector(".file-icon i");
    if (fileIcon) {
      // Add a small indicator dot or change the icon color to indicate downloaded state
      fileIcon.classList.add("downloaded-icon");
    }

    // Make the appropriate clickable actions available based on file type
    if (container.querySelector(".video-message")) {
      // Enable video play button
      const playButton = container.querySelector(".video-play-button");
      if (!playButton) {
        const videoContainer = container.querySelector(".video-container");
        if (videoContainer) {
          const playButtonHTML = `
                        <button class="video-play-button" data-video-url="${
                          message.file.localUrl || message.file.fileContent
                        }" 
                                data-mime-type="${
                                  message.file.mimeFileType || "video/mp4"
                                }">
                            <i class="fe fe-play"></i>
                        </button>
                    `;
          videoContainer.insertAdjacentHTML("beforeend", playButtonHTML);
        }
      }
    } else if (
      container.querySelector(".audio-message") ||
      container.querySelector(".voice-message")
    ) {
      // For audio/voice: Enable play controls
      const audioElement = container.querySelector("audio");
      if (audioElement) {
        audioElement.removeAttribute("disabled");
      }
    }
  }

  // Add the overlay to the container
  if (overlayHTML) {
    container.insertAdjacentHTML("beforeend", overlayHTML);
  }
}

/**
 * Cancel an in-progress download
 * @param {string} locId - The ID of the message containing the file
 */
function cancelDownload(locId) {
  ChatProcessor.getMessageById(parseInt(locId), "locId").then((message) => {
    if (!message || !message.file) return;

    // Reset download status
    message.file.downloadStatus = "NotStarted";
    message.file.downloadProgress = 0;

    // Update the message in storage
    ChatProcessor.updateOnlyMessage(message.locId, message);

    // Update UI
    updateDownloadUI(message);
  });
}

/**
 * Retry a failed download
 * @param {string} messageId - The ID of the message containing the file
 */
function retryDownload(messageId) {
  // Simply call downloadFile again, force parameter is false
  downloadFile(messageId, false);
}

/**
 * Check if the device is connected to WiFi
 * This is a mockup function - in a real app, you would use the Network Information API
 * @returns {boolean} Whether the device is connected to WiFi
 */
function checkWifiConnection() {
  // Mockup - in a real app, you would detect actual network connection
  // For demo purposes, we'll return true 80% of the time
  return Math.random() > 0.2;
}

/**
 * Check if there is enough storage available to download a file
 * This is a mockup function - in a real app, you would use the Storage API
 * @param {number} fileSize - Size of the file in bytes
 * @returns {boolean} Whether there is enough storage available
 */
function checkStorageAvailability(fileSize) {
  // Mockup - in a real app, you would check actual device storage
  // For demo purposes, we'll simulate storage issues for files larger than 50MB (randomly)
  const isBigFile = fileSize > 50 * 1024 * 1024;
  return !(isBigFile && Math.random() < 0.3); // 30% chance of storage issues for big files
}

/**
 * Set a file to require WiFi for download
 * @param {string} messageId - ID of the message containing the file
 */
function setRequireWifi(messageId) {
  ChatProcessor.getMessageById(messageId).then((message) => {
    if (!message || !message.file) return;

    message.file.requiresWifi = true;

    // Update the message in storage
    ChatProcessor.updateMessageInStorage(message);

    // Update UI to show WiFi waiting state
    updateDownloadUI(message);
  });
}

// Add CSS keyframes for spin animation and video player modal styling
// const style = document.createElement('style');
// style.textContent = `

// `;
// document.head.appendChild(style);
document.addEventListener("DOMContentLoaded", function () {
  // Create an enhanced WhatsApp-style video player modal
  const videoPlayerModal = document.createElement("div");
  videoPlayerModal.className = "video-player-modal";
  videoPlayerModal.innerHTML = `
        <div class="video-player-container">
            <div class="video-player-header">
                <div class="video-title"></div>
                <button class="close-video-btn"><i class="fe fe-x"></i></button>
            </div>
            <div class="video-player-content">
                <div class="video-loading">
                    <div class="spinner"></div>
                </div>
                <video class="fullscreen-video"></video>
                <div class="video-error-message">Failed to load video</div>
                <div class="video-controls">
                    <button class="play-pause-btn">
                        <i class="fe fe-play"></i>
                    </button>
                    <div class="video-time current-time">0:00</div>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-filled"></div>
                            <div class="progress-handle"></div>
                        </div>
                        <div class="buffer-bar"></div>
                    </div>
                    <div class="video-time total-duration">0:00</div>
                    <div class="volume-container">
                        <button class="volume-btn">
                            <i class="fe fe-volume-2"></i>
                        </button>
                        <div class="volume-slider">
                            <div class="volume-progress">
                                <div class="volume-filled"></div>
                                <div class="volume-handle"></div>
                            </div>
                        </div>
                    </div>
                    <button class="fullscreen-btn">
                        <i class="fe fe-maximize"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
  document.body.appendChild(videoPlayerModal);

  // Get all required elements
  const videoElement = videoPlayerModal.querySelector("video");
  const playPauseBtn = videoPlayerModal.querySelector(".play-pause-btn");
  const volumeBtn = videoPlayerModal.querySelector(".volume-btn");
  const fullscreenBtn = videoPlayerModal.querySelector(".fullscreen-btn");
  const progressBar = videoPlayerModal.querySelector(".progress-bar");
  const progressFilled = videoPlayerModal.querySelector(".progress-filled");
  const progressHandle = videoPlayerModal.querySelector(".progress-handle");
  const bufferBar = videoPlayerModal.querySelector(".buffer-bar");
  const currentTimeDisplay = videoPlayerModal.querySelector(".current-time");
  const durationDisplay = videoPlayerModal.querySelector(".total-duration");
  const videoTitle = videoPlayerModal.querySelector(".video-title");
  const loadingIndicator = videoPlayerModal.querySelector(".video-loading");
  const errorMessage = videoPlayerModal.querySelector(".video-error-message");
  const volumeSlider = videoPlayerModal.querySelector(".volume-slider");
  const volumeFilled = videoPlayerModal.querySelector(".volume-filled");
  const volumeHandle = videoPlayerModal.querySelector(".volume-handle");

  // Close button handler
  const closeBtn = videoPlayerModal.querySelector(".close-video-btn");
  closeBtn.addEventListener("click", closeVideoPlayer);

  // Function to close the video player
  function closeVideoPlayer() {
    videoPlayerModal.classList.remove("active");
    videoElement.pause();
    videoElement.src = "";
    // Reset UI elements
    resetVideoPlayerUI();
  }

  // Function to reset video player UI
  function resetVideoPlayerUI() {
    progressFilled.style.width = "0%";
    progressHandle.style.left = "0%";
    currentTimeDisplay.textContent = "0:00";
    durationDisplay.textContent = "0:00";
    playPauseBtn.innerHTML = '<i class="fe fe-play"></i>';
    loadingIndicator.style.display = "none";
    errorMessage.style.display = "none";
    videoTitle.textContent = "";
  }

  // Format time function (converts seconds to mm:ss format)
  function formatTime(timeInSeconds) {
    if (!timeInSeconds || isNaN(timeInSeconds)) return "0:00";

    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  }

  // Play/Pause function
  function togglePlay() {
    if (videoElement.paused) {
      videoElement.play().catch((error) => {
        console.error("Error playing video:", error);
        errorMessage.style.display = "flex";
      });
    } else {
      videoElement.pause();
    }
  }

  // Toggle volume mute
  function toggleMute() {
    videoElement.muted = !videoElement.muted;
    updateVolumeUI();
  }

  // Update volume UI
  function updateVolumeUI() {
    const volume = videoElement.muted ? 0 : videoElement.volume;
    volumeFilled.style.width = `${volume * 100}%`;
    volumeHandle.style.left = `${volume * 100}%`;

    // Update volume icon
    if (videoElement.muted || volume === 0) {
      volumeBtn.innerHTML = '<i class="fe fe-volume-x"></i>';
    } else if (volume < 0.5) {
      volumeBtn.innerHTML = '<i class="fe fe-volume-1"></i>';
    } else {
      volumeBtn.innerHTML = '<i class="fe fe-volume-2"></i>';
    }
  }

  // Toggle fullscreen
  function toggleFullscreen() {
    if (!document.fullscreenElement) {
      if (videoPlayerModal.requestFullscreen) {
        videoPlayerModal.requestFullscreen();
      } else if (videoPlayerModal.webkitRequestFullscreen) {
        videoPlayerModal.webkitRequestFullscreen();
      } else if (videoPlayerModal.msRequestFullscreen) {
        videoPlayerModal.msRequestFullscreen();
      }
      fullscreenBtn.innerHTML = '<i class="fe fe-minimize"></i>';
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
      fullscreenBtn.innerHTML = '<i class="fe fe-maximize"></i>';
    }
  }

  // Skip to a specific time in the video
  function skipTo(event) {
    const progressBarRect = progressBar.getBoundingClientRect();
    const clickPosition =
      (event.clientX - progressBarRect.left) / progressBarRect.width;
    const newTime = clickPosition * videoElement.duration;
    videoElement.currentTime = newTime;
  }

  // Set volume
  function setVolume(event) {
    const volumeSliderRect = volumeSlider.getBoundingClientRect();
    let clickPosition =
      (event.clientX - volumeSliderRect.left) / volumeSliderRect.width;

    // Clamp value between 0 and 1
    clickPosition = Math.max(0, Math.min(1, clickPosition));

    // Set volume
    videoElement.volume = clickPosition;
    videoElement.muted = false;
    updateVolumeUI();
  }

  // Update UI based on video state
  function updateVideoProgress() {
    if (!videoElement.duration) return;

    const percent = (videoElement.currentTime / videoElement.duration) * 100;
    progressFilled.style.width = `${percent}%`;
    progressHandle.style.left = `${percent}%`;
    currentTimeDisplay.textContent = formatTime(videoElement.currentTime);
  }

  // Update buffered progress
  function updateBufferProgress() {
    if (!videoElement.buffered.length) return;

    const bufferedEnd = videoElement.buffered.end(
      videoElement.buffered.length - 1
    );
    const duration = videoElement.duration;
    const bufferedPercent = (bufferedEnd / duration) * 100;

    bufferBar.style.width = `${bufferedPercent}%`;
  }

  // Add all event listeners
  playPauseBtn.addEventListener("click", togglePlay);
  volumeBtn.addEventListener("click", toggleMute);
  fullscreenBtn.addEventListener("click", toggleFullscreen);
  progressBar.addEventListener("click", skipTo);
  volumeSlider.addEventListener("click", setVolume);

  // Progress bar dragging
  let isDraggingProgress = false;
  progressHandle.addEventListener("mousedown", () => {
    isDraggingProgress = true;
  });

  // Volume slider dragging
  let isDraggingVolume = false;
  volumeHandle.addEventListener("mousedown", () => {
    isDraggingVolume = true;
  });

  // Global mouse move and up events for dragging
  document.addEventListener("mousemove", (e) => {
    if (isDraggingProgress) {
      skipTo(e);
    } else if (isDraggingVolume) {
      setVolume(e);
    }
  });

  document.addEventListener("mouseup", () => {
    isDraggingProgress = false;
    isDraggingVolume = false;
  });

  // Video event listeners
  videoElement.addEventListener("loadstart", () => {
    loadingIndicator.style.display = "flex";
    errorMessage.style.display = "none";
  });

  videoElement.addEventListener("canplay", () => {
    loadingIndicator.style.display = "none";
    durationDisplay.textContent = formatTime(videoElement.duration);
  });

  videoElement.addEventListener("timeupdate", updateVideoProgress);
  videoElement.addEventListener("progress", updateBufferProgress);

  videoElement.addEventListener("play", () => {
    playPauseBtn.innerHTML = '<i class="fe fe-pause"></i>';
  });

  videoElement.addEventListener("pause", () => {
    playPauseBtn.innerHTML = '<i class="fe fe-play"></i>';
  });

  videoElement.addEventListener("ended", () => {
    playPauseBtn.innerHTML = '<i class="fe fe-rotate-cw"></i>';
    progressFilled.style.width = "100%";
    progressHandle.style.left = "100%";
  });

  videoElement.addEventListener("error", () => {
    loadingIndicator.style.display = "none";
    errorMessage.style.display = "flex";
    console.error("Video error:", videoElement.error);
  });

  // Keyboard shortcuts
  document.addEventListener("keydown", (e) => {
    // Only process shortcuts when video player is active
    if (!videoPlayerModal.classList.contains("active")) return;

    switch (e.key.toLowerCase()) {
      case " ":
      case "k": // YouTube-style shortcuts
        togglePlay();
        e.preventDefault();
        break;
      case "f":
        toggleFullscreen();
        e.preventDefault();
        break;
      case "m":
        toggleMute();
        e.preventDefault();
        break;
      case "arrowleft":
        videoElement.currentTime = Math.max(0, videoElement.currentTime - 5);
        e.preventDefault();
        break;
      case "arrowright":
        videoElement.currentTime = Math.min(
          videoElement.duration,
          videoElement.currentTime + 5
        );
        e.preventDefault();
        break;
      case "arrowup":
        videoElement.volume = Math.min(1, videoElement.volume + 0.1);
        videoElement.muted = false;
        updateVolumeUI();
        e.preventDefault();
        break;
      case "arrowdown":
        videoElement.volume = Math.max(0, videoElement.volume - 0.1);
        updateVolumeUI();
        e.preventDefault();
        break;
      case "escape":
        closeVideoPlayer();
        e.preventDefault();
        break;
    }
  });

  // Click on the modal background to close
  videoPlayerModal.addEventListener("click", (e) => {
    if (e.target === videoPlayerModal) {
      closeVideoPlayer();
    }
  });

  // Enhanced video handler
  function handleVideoPlay(button) {
    // Get video URL, mime type, and title from data attributes
    const videoUrl = button.getAttribute("data-video-url");
    const mimeType = button.getAttribute("data-mime-type");
    const title = button.getAttribute("data-title") || "Video";

    if (!videoUrl) {
      console.error("No video URL found");
      return;
    }

    ////console.log("Playing video:", videoUrl, mimeType);

    // Set title
    videoTitle.textContent = title;

    // Reset UI
    resetVideoPlayerUI();

    // Set up the video modal
    videoElement.src = videoUrl;
    videoElement.type = mimeType || "video/mp4";

    // Show the modal
    videoPlayerModal.classList.add("active");

    // Set initial volume
    videoElement.volume = 0.8;
    updateVolumeUI();

    // Auto-play the video after showing the modal
    videoElement.play().catch((err) => {
      console.error("Error playing video:", err);
      errorMessage.style.display = "flex";
    });
  }

  // Static elements - for any that exist on page load
  document.querySelectorAll(".video-play-button").forEach((button) => {
    button.addEventListener("click", () => handleVideoPlay(button));
  });

  // Dynamic elements delegation - for elements added after page load
  document.addEventListener("click", function (e) {
    const target = e.target.closest(".video-play-button");
    if (target) {
      handleVideoPlay(target);
    }
  });
});

/**
 * Download and save a file to the user's device
 * @param {string} messageId - The ID of the message containing the file
 */
function downloadAndSaveFile(messageId) {
  ChatProcessor.getMessageById(parseInt(messageId), "locId")
    .then((message) => {
      if (!message || !message.file) return;

      ////console.log("Downloading and saving file:", message.file.fileName);

      // If file is already downloaded and has a local URL
      if (message.file.isDownloaded && message.file.localUrl) {
        // Create a download link
        const downloadLink = document.createElement("a");
        downloadLink.href = message.file.localUrl;
        downloadLink.download = message.file.fileName;
        downloadLink.style.display = "none";
        document.body.appendChild(downloadLink);

        // Trigger the download
        downloadLink.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(downloadLink);
        }, 100);
        return;
      }

      // If file isn't downloaded yet, download it first
      const originalStatus = message.file.downloadStatus;

      // Create a callback to handle the file after download
      const afterDownload = (downloadedMessage) => {
        if (!downloadedMessage.file.isDownloaded) {
          console.error("Failed to download file for saving");
          return;
        }

        // Create a download link
        const downloadLink = document.createElement("a");
        downloadLink.href = downloadedMessage.file.localUrl;
        downloadLink.download = downloadedMessage.file.fileName;
        downloadLink.style.display = "none";
        document.body.appendChild(downloadLink);

        // Trigger the download
        downloadLink.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(downloadLink);
        }, 100);
      };

      // Download the file with our callback
      downloadFile(messageId, true, afterDownload);
    })
    .catch((error) => {
      console.error("Error in downloadAndSaveFile:", error);
    });
}
// Audio manager to handle multiple audio instances
const voiceMessagePlayer = {
  currentAudio: null,
  currentMessageId: null,
  progressIntervals: {},

  init() {
    document.addEventListener("click", (e) => {
      if (e.target.closest(".voice-play-btn")) {
        const button = e.target.closest(".voice-play-btn");
        const container = button.closest(".voice-message");
        this.handlePlayback(container);
      }
    });
  },

  handlePlayback(container) {
    const messageId = container.dataset.messageId;
    const audioSrc = container.dataset.audioSrc;

    if (this.currentMessageId === messageId) {
      this.togglePlayback();
    } else {
      this.stopCurrentPlayback();
      this.startNewPlayback(container, audioSrc, messageId);
    }

    this.updateUI(container);
  },

  startNewPlayback(container, src, messageId) {
    this.currentAudio = new Audio(src);
    this.currentMessageId = messageId;

    this.currentAudio.addEventListener("timeupdate", () => {
      this.updateProgress(container);
    });

    this.currentAudio.addEventListener("ended", () => {
      this.resetPlayback(container);
    });

    this.currentAudio.play();
    this.startWaveformAnimation(container);
  },

  togglePlayback() {
    if (this.currentAudio.paused) {
      this.currentAudio.play();
      this.resumeWaveformAnimation(this.currentMessageId);
    } else {
      this.currentAudio.pause();
      this.pauseWaveformAnimation(this.currentMessageId);
    }
  },

  updateProgress(container) {
    if (!this.currentAudio) return;

    // Ensure duration is available
    const duration = this.currentAudio.duration || 1; // Fallback to 1 to prevent division by zero
    const progress = (this.currentAudio.currentTime / duration) * 100;

    // Update progress bar
    const progressBar = container.querySelector(".voice-progress");
    if (progressBar) {
      progressBar.style.width = `${progress}%`;
    }

    // Update time display
    const timeElement = container.querySelector(".voice-duration");
    if (timeElement) {
      timeElement.textContent = this.formatTime(
        this.currentAudio.currentTime || 0
      );
    }
  },

  resetPlayback(container) {
    // Safely handle icon toggle
    const button = container?.querySelector(".voice-play-btn");
    const icon = button?.querySelector(".fe");
    if (icon) {
      icon.classList.remove("fe-pause");
      icon.classList.add("fe-play");
    }

    // Reset progress
    const progressBar = container?.querySelector(".voice-progress");
    if (progressBar) {
      progressBar.style.width = "0%";
    }

    // Reset time display
    const timeElement = container?.querySelector(".voice-duration");
    if (timeElement) {
      timeElement.textContent = this.formatTime(
        this.currentAudio?.duration || 0
      );
    }

    this.stopWaveformAnimation(this.currentMessageId);
    this.currentAudio = null;
    this.currentMessageId = null;
  },

  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  },

  startNewPlayback(container, src, messageId) {
    this.currentAudio = new Audio(src);
    this.currentMessageId = messageId;

    // Wait for metadata to load
    this.currentAudio.addEventListener("loadedmetadata", () => {
      const timeElement = container.querySelector(".voice-duration");
      if (timeElement) {
        timeElement.textContent = this.formatTime(
          this.currentAudio.duration || 0
        );
      }
    });

    this.currentAudio.addEventListener("timeupdate", () => {
      this.updateProgress(container);
    });

    this.currentAudio.addEventListener("ended", () => {
      this.resetPlayback(container);
    });

    this.currentAudio.play();
    this.startWaveformAnimation(container);
  },
  startWaveformAnimation(container) {
    const waveformBars = container.querySelectorAll(".enhanced-waveform span");
    let currentBar = 0;

    this.progressIntervals[this.currentMessageId] = setInterval(() => {
      waveformBars.forEach(
        (bar) => (bar.style.height = `${Math.random() * 80 + 20}%`)
      );
      currentBar = (currentBar + 1) % waveformBars.length;
    }, 100);
  },

  pauseWaveformAnimation(messageId) {
    clearInterval(this.progressIntervals[messageId]);
  },

  resumeWaveformAnimation(messageId) {
    this.startWaveformAnimation(
      document.querySelector(`[data-message-id="${messageId}"]`)
    );
  },

  stopWaveformAnimation(messageId) {
    clearInterval(this.progressIntervals[messageId]);
    delete this.progressIntervals[messageId];
  },

  stopCurrentPlayback() {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.stopWaveformAnimation(this.currentMessageId);
    }
  },

  updateUI(container) {
    const icon = container.querySelector(".fe");
    icon.classList.toggle("fe-play");
    icon.classList.toggle("fe-pause");
  },
};

// Initialize when DOM is ready
document.addEventListener("DOMContentLoaded", () => voiceMessagePlayer.init());
