/**
 * مدير رفع الملفات
 * هذا الملف مسؤول عن معالجة رفع الملفات في تطبيق الدردشة
 */

class FileUploadManager {
  constructor() {
    this.supportedImageTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
    ];
    this.supportedVideoTypes = [
      "video/mp4",
      "video/quicktime",
      "video/mpeg",
      "video/webm",
    ];
    this.supportedAudioTypes = [
      "audio/mpeg",
      "audio/mp3",
      "audio/wav",
      "audio/ogg",
    ];
    this.supportedDocumentTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.ms-powerpoint",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      "text/plain",
    ];
    this.maxFileSizeInBytes = 50 * 1024 * 1024; // 50 ميجابايت كحد أقصى
    this.chatListManager = new ChatListManager();

    // تهيئة مؤشرات العناصر
    this.initializeElements();

    // تعيين مستمعي الأحداث
    this.setupEventListeners();
  }

  /**
   * تهيئة مؤشرات العناصر في واجهة المستخدم
   */
  initializeElements() {
    // أزرار إرفاق الملفات
    this.documentsButton = document.querySelector('button[title="Document"]');
    this.cameraButton = document.querySelector('button[title="Camera"]');
    this.imagesButton = document.querySelector(
      'button[title="Photos & Videos"]'
    );

    // إنشاء عناصر معاينة الملفات
    this.createFilePreviewElements();
  }

  /**
   * إنشاء عناصر معاينة الملفات
   */
  createFilePreviewElements() {
    // إنشاء العنصر الرئيسي للمعاينة
    this.overlayElement = document.createElement("div");
    this.overlayElement.className = "file-upload-overlay";
    this.overlayElement.style.display = "none";

    // إنشاء حاوية المعاينة
    this.previewContainer = document.createElement("div");
    this.previewContainer.className = "file-preview-container";

    // إنشاء رأس المعاينة
    this.previewHeader = document.createElement("div");
    this.previewHeader.className = "file-preview-header";

    this.previewTitle = document.createElement("h3");
    this.previewTitle.textContent = "إرسال ملف";

    this.previewCloseButton = document.createElement("button");
    this.previewCloseButton.className = "file-preview-close";
    this.previewCloseButton.innerHTML = "&times;";

    this.previewHeader.appendChild(this.previewTitle);
    this.previewHeader.appendChild(this.previewCloseButton);

    // إنشاء محتوى المعاينة
    this.previewContent = document.createElement("div");
    this.previewContent.className = "file-preview-content";

    // إنشاء تذييل المعاينة
    this.previewFooter = document.createElement("div");
    this.previewFooter.className = "file-preview-footer";

    this.sendButton = document.createElement("button");
    this.sendButton.className = "send-file-btn";
    this.sendButton.textContent = "إرسال";

    this.previewFooter.appendChild(this.sendButton);

    // تجميع العناصر
    this.previewContainer.appendChild(this.previewHeader);
    this.previewContainer.appendChild(this.previewContent);
    this.previewContainer.appendChild(this.previewFooter);

    this.overlayElement.appendChild(this.previewContainer);

    // إضافة العنصر إلى الصفحة
    document.body.appendChild(this.overlayElement);
  }

  /**
   * إعداد مستمعي الأحداث
   */
  setupEventListeners() {
    if (this.documentsButton) {
      this.documentsButton.addEventListener("click", () =>
        this.openFilePicker("document")
      );
    }

    if (this.cameraButton) {
      this.cameraButton.addEventListener("click", () =>
        this.openFilePicker("camera")
      );
    }

    if (this.imagesButton) {
      this.imagesButton.addEventListener("click", () =>
        this.openFilePicker("media")
      );
    }

    // مستمع إغلاق المعاينة
    this.previewCloseButton.addEventListener("click", () =>
      this.closeFilePreview()
    );

    // مستمع زر الإرسال
    this.sendButton.addEventListener("click", () => this.sendFile());
    // 2. Enter Key Press Listener
    // Attach the keydown listener to the document, but only trigger if the modal is visible
    document.addEventListener("keydown", this.handleKeyDown.bind(this));

    // You might also want to close the modal if Escape is pressed
    document.addEventListener("keydown", (event) => {
      if (event.key === "Escape") {
        event.preventDefault();

        this.closeFilePreview(); // Assuming you have a method to close the modal
      }
    });
  }

  handleKeyDown(event) {
    // Check if the Enter key was pressed (key code 13 for older browsers, 'Enter' for modern)
    // AND ensure the modal is currently open/visible
    // AND ensure the user isn't holding Shift (for new line in textareas, if applicable)
    if ((event.key === "Enter" || event.keyCode === 13) && !event.shiftKey) {
      event.preventDefault(); // Prevent default browser action (e.g., submitting a form, new line)
      this.sendFile(); // Call your file sending method
      //$("#chat-popup .popup").toggle(700);
      $(".chat-attach .popup").fadeOut();
    }
  }
  /**
   * فتح منتقي الملفات
   * @param {string} type - نوع الملف (document, camera, media)
   */
  openFilePicker(type) {
    // إنشاء عنصر إدخال ملف مؤقت
    const fileInput = document.createElement("input");
    fileInput.type = "file";

    switch (type) {
      case "document":
        fileInput.accept = ".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt";
        break;
      case "camera":
        fileInput.accept = "image/*";
        fileInput.capture = "camera";
        break;
      case "media":
        fileInput.accept = "image/*,video/*";
        fileInput.multiple = true;
        break;
    }

    // إضافة مستمع تغيير ملف
    fileInput.addEventListener("change", (event) => {
      const files = event.target.files;
      if (files && files.length > 0) {
        this.handleFileSelection(files);
      }
    });

    // فتح منتقي الملفات
    fileInput.click();
  }

  /**
   * معالجة اختيار الملفات
   * @param {FileList} files - قائمة الملفات المختارة
   */
  handleFileSelection(files) {
    this.selectedFiles = Array.from(files);

    // التحقق من حجم الملفات
    for (const file of this.selectedFiles) {
      if (file.size > this.maxFileSizeInBytes) {
        alert(`الملف ${file.name} كبير جدًا. الحد الأقصى هو 50 ميجابايت.`);
        return;
      }
    }

    // إظهار معاينة الملفات
    this.showFilePreview();
  }

  /**
   * عرض معاينة الملفات
   */
  showFilePreview() {
    // مسح المحتوى السابق
    this.previewContent.innerHTML = "";

    // تحديث العنوان بناءً على عدد الملفات
    this.previewTitle.textContent =
      this.selectedFiles.length > 1
        ? `إرسال ${this.selectedFiles.length} ملفات`
        : "إرسال ملف";

    // إنشاء معاينة لكل ملف
    for (const file of this.selectedFiles) {
      const previewElement = this.createFilePreviewElement(file);
      this.previewContent.appendChild(previewElement);
    }

    // إنشاء حقل التعليق
    const captionContainer = document.createElement("div");
    captionContainer.className = "file-preview-caption";

    const captionInput = document.createElement("textarea");
    captionInput.className = "file-caption-input";
    captionInput.placeholder = "أضف تعليقًا (اختياري)...";
    captionInput.rows = 3;

    captionContainer.appendChild(captionInput);
    this.previewContent.appendChild(captionContainer);
    this.captionInput = captionInput;

    // عرض المعاينة
    this.overlayElement.style.display = "flex";
  }

  /**
   * إنشاء عنصر معاينة الملف
   * @param {File} file - ملف للمعاينة
   * @returns {HTMLElement} عنصر المعاينة
   */
  createFilePreviewElement(file) {
    const previewElement = document.createElement("div");
    //console.log("file", file);
    if (this.isImageFile(file)) {
      // معاينة الصور
      const image = document.createElement("img");
      image.className = "file-preview-image";

      // إنشاء URL للصورة
      const imageUrl = URL.createObjectURL(file);
      image.src = imageUrl;

      previewElement.appendChild(image);

      // تخزين URL لتحريره لاحقًا
      file.previewUrl = imageUrl;
    } else {
      // معاينة الملفات الأخرى
      const fileDetails = document.createElement("div");
      fileDetails.className = "file-preview-details";

      const fileIcon = document.createElement("div");
      fileIcon.className = "file-icon";
      fileIcon.innerHTML = this.getFileIcon(file);

      const fileInfo = document.createElement("div");
      fileInfo.className = "file-info";

      const fileName = document.createElement("div");
      fileName.className = "file-name";
      fileName.textContent = file.name;

      const fileSize = document.createElement("div");
      fileSize.className = "file-size";
      fileSize.textContent = this.formatFileSize(file.size);

      fileInfo.appendChild(fileName);
      fileInfo.appendChild(fileSize);

      fileDetails.appendChild(fileIcon);
      fileDetails.appendChild(fileInfo);

      previewElement.appendChild(fileDetails);
    }

    return previewElement;
  }

  getFileDocumentIcon(file) {
    const name = file.name || "";
    const ext = name.split(".").pop().toLowerCase();

    const ICONS = {
      pdf: "/assets/images/icon_docu/pdf-icon.svg",
      doc: "/assets/images/icon_docu/word-icon.svg",
      docx: "/assets/images/icon_docu/word-icon.svg",
      xls: "/assets/images/icon_docu/excel-icon.svg",
      xlsx: "/assets/images/icon_docu/excel-icon.svg",
      ppt: "/assets/images/icon_docu/google-slides-icon.svg",
      pptx: "/assets/images/icon_docu/google-slides-icon.svg",
      // …
    };

    const iconUrl =
      ICONS[ext] || "/assets/images/icon_docu/default-file-icon.svg";

    // return an <img> string you can insert into innerHTML
    return `<img src="${iconUrl}" alt="${ext} file" class="file-icon-img" width="24" height="24">`;
  }

  /**
   * الحصول على أيقونة الملف بناءً على نوعه
   * @param {File} file - الملف
   * @returns {string} رمز HTML للأيقونة
   */
  getFileIcon(file) {
    const mimeType = file.type;

    if (this.isImageFile(file)) {
      return '<i class="fe fe-image"></i>';
    } else if (this.isVideoFile(file)) {
      return '<i class="fe fe-video"></i>';
    } else if (this.isAudioFile(file)) {
      return '<i class="fe fe-music"></i>';
    } else if (this.isPdfFile(file)) {
      return '<i class="fe fe-file-text"></i>';
    } else if (this.isDocumentFile(file)) {
      return this.getFileDocumentIcon(file);
    } else {
      return '<i class="fe fe-file"></i>';
    }
  }

  /**
   * تنسيق حجم الملف
   * @param {number} bytes - حجم الملف بالبايت
   * @returns {string} حجم الملف المنسق
   */
  formatFileSize(bytes) {
    if (bytes === 0) return "0 بايت";

    const sizes = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت", "تيرابايت"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));

    return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * إغلاق معاينة الملفات
   */
  closeFilePreview() {
    // إخفاء المعاينة
    this.overlayElement.style.display = "none";

    // تحرير موارد URL
    if (this.selectedFiles) {
      for (const file of this.selectedFiles) {
        if (file.previewUrl) {
          URL.revokeObjectURL(file.previewUrl);
        }
      }
    }

    // إعادة تعيين المتغيرات
    this.selectedFiles = null;
    this.captionInput = null;
  }

  /**
   * إرسال الملف
   */
  async sendFile() {
    if (!this.selectedFiles || this.selectedFiles.length === 0) {
      return;
    }

    // الحصول على نص التعليق
    const caption = this.captionInput ? this.captionInput.value.trim() : "";

    // نحتفظ بنسخة من الملفات المحددة قبل إغلاق المعاينة
    const filesToSend = [...this.selectedFiles];

    // إغلاق المعاينة
    this.closeFilePreview();

    // إرسال كل ملف على حدة
    for (const file of filesToSend) {
      await this.uploadAndSendFile(file, caption);
    }
  }

  /**
   * رفع وإرسال ملف
   * @param {File} file - الملف المراد رفعه
   * @param {string} caption - التعليق المصاحب للملف
   */
  async uploadAndSendFile(file, caption) {
    //console.log("uploadAndSendFile", file);
    try {
      // Get video duration first
      const getVideoDuration = (file) => {
        return new Promise((resolve, reject) => {
          // --- 1. Input Validation ---
          if (!file instanceof File) {
            // Reject if the input is not a valid File object
            return reject(new Error("Invalid input: Expected a File object."));
          }

          // Basic check for video type based on MIME type
          if (!file.type.startsWith("video/")) {
            // Reject if the file is not identified as a video
            return reject(
              new Error("Invalid file type: Please select a video file.")
            );
          }

          const video = document.createElement("video");
          video.preload = "metadata"; // Load just enough to get metadata (like duration)

          // --- 2. Event-based Error Handling ---

          // Handle errors during video loading (e.g., corrupted file, unsupported format)
          video.onerror = () => {
            // Revoke the URL to free up memory
            URL.revokeObjectURL(video.src);
            // Provide a more specific error message based on the video element's error object
            const error = video.error;
            let errorMessage = "Failed to load video metadata.";
            switch (error.code) {
              case error.MEDIA_ERR_ABORTED:
                errorMessage = "Video loading was aborted.";
                break;
              case error.MEDIA_ERR_NETWORK:
                errorMessage = "Network error prevented video loading.";
                break;
              case error.MEDIA_ERR_DECODE:
                errorMessage =
                  "Video could not be decoded (corrupted or unsupported format).";
                break;
              case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                errorMessage =
                  "Video source not supported (invalid URL or format).";
                break;
              default:
                errorMessage =
                  "An unknown error occurred during video loading.";
            }
            console.error("Video loading error:", errorMessage, error);
            reject(new Error(errorMessage));
          };

          // Handle successful metadata loading
          video.onloadedmetadata = () => {
            URL.revokeObjectURL(video.src); // Clean up the object URL
            resolve(Math.round(video.duration)); // Resolve with the rounded duration
          };

          // Assign the object URL to the video element's src
          try {
            video.src = URL.createObjectURL(file);
          } catch (e) {
            // Catch synchronous errors if URL.createObjectURL fails (though rare for valid File objects)
            console.error("Error creating object URL:", e);
            reject(new Error("Could not create URL for the video file."));
          }
        });
      };

      // إنشاء رسالة محلية بحالة "قيد الانتظار"
      const messageType = this.getMessageTypeFromFile(file);
      const fileType = this.getFileTypeString(file);

      // التأكد من أن الملف له امتداد صحيح
      let fileName = file.name;
      const extension = this.getProperExtension(file);
      if (!fileName.toLowerCase().endsWith(extension.toLowerCase())) {
        fileName = `${fileName}${extension}`;
      }

      // التأكد من MIME type صحيح
      const mimeType = this.getProperMimeType(file);

      const msg = {
        chatID: chat.id,
        senderID: ChatProcessor.processedData.currentUser?.id,
        messageType: messageType.toString(),
        source: 1,
        messageText: caption,
        deviceInfo: "string",
        messageStatus: "Pending",
        file: {
          fileName: fileName,
          mimeFileType: mimeType,
          fileSize: file.size,
          isUploadComplete: false,
          fileContent: URL.createObjectURL(file),
          thumbnailContent: URL.createObjectURL(file),
          localUrl: URL.createObjectURL(file),
          seconds: file.type.startsWith("video/")
            ? await getVideoDuration(file)
            : null,
        },
      };

      // حفظ الرسالة محليًا
      const localMessage = await ChatProcessor.addMessage(msg);

      // إضافة الرسالة إلى واجهة المستخدم مع مؤشر تقدم الرفع
      addMessageToMessageArea(localMessage);

      // تحديث قائمة الدردشات
      await generateChatList();

      // التحقق من الاتصال بالإنترنت
      if (!navigator.onLine) {
        // تحديث حالة الرسالة إلى "غير مرسلة"
        MessageGenerator.updateMessageStatus(localMessage.locId, "Failed");

        // إظهار رسالة خطأ
        showMessageError(
          localMessage.locId,
          "فشل الإرسال، أنت غير متصل بالإنترنت"
        );

        return;
      }

      // إنشاء نسخة من الملف مع الاسم الصحيح إذا تم تغييره
      const fileToUpload =
        file.name !== fileName
          ? new File([file], fileName, { type: mimeType })
          : file;

      // إنشاء كائن FormData لرفع الملف
      const formData = new FormData();
      formData.append("File.MultipartFile", fileToUpload);
      formData.append("File.FileName", fileName);
      formData.append("File.MimeFileType", mimeType);
      formData.append("File.FileSize", file.size);
      formData.append("File.StorageSourseType", "LoaclStorage");
      formData.append("File.FileType", fileType);

      if (file.type.startsWith("video/") || file.type.startsWith("audio/")) {
        const seconds = file.type.startsWith("video/")
          ? await getVideoDuration(file)
          : 0;
        formData.append("File.Seconds", seconds);
      }

      formData.append("ChatID", chat.id);
      formData.append("MessageType", messageType);
      formData.append("Source", 1);
      formData.append("MessageText", caption);
      formData.append("DeviceInfo", "web");
      formData.append("senderID", ChatProcessor.processedData.currentUser?.id);

      try {
        // إرسال الطلب إلى الخادم
        const response = await fetch("/Message/Create/FileMultipart", {
          method: "POST",
          body: formData,
          headers: {
            Accept: "application/json",
          },
        });

        if (!response.ok) {
          throw new Error(`Server responded with status: ${response.status}`);
          //throw new Error(`Server responded with status: ${response}`);
        }

        const data = await response.json();

        // تحديث الرسالة المحلية بمعلومات الاستجابة
        if (data && data.resObject) {
          const messageContainer = document.querySelector(
            `.containerMessage[data-message-id="${localMessage.locId}"]`
          );
          if (messageContainer) {
            const overlay = messageContainer.querySelector(
              ".upload-progress-overlay"
            );
            if (overlay) {
              overlay.remove();
            }
          }
          //console.log("updatedMessage", data);
          const updatedMessage = await ChatProcessor.updateMessage(
            localMessage.locId,
            {
              id: data.resObject.id,
              messageStatus: "Sent",
              createdDate: data.resObject.createdDate,
            }
          );
          //MessageGenerator.updateMessageStatus(localMessage.locId, "Sent");
          if (this.chatListManager) {
            await this.chatListManager.updateMessageStatuse(
              parseInt(data.resObject.id),
              "Sent"
            );
          }
        }

        return data;
      } catch (error) {
        console.error("خطأ في رفع الملف:", error);
        MessageGenerator.updateMessageStatus(localMessage.locId, "Failed");
        showMessageError(
          localMessage.locId,
          "فشل الإرسال. انقر لإعادة المحاولة."
        );
        throw error;
      }
    } catch (error) {
      console.error("خطأ في رفع الملف:", error);
      throw error;
    }
  }

  /**
   * الحصول على امتداد مناسب للملف
   * @param {File} file - الملف
   * @returns {string} - امتداد الملف مع النقطة
   */
  getProperExtension(file) {
    // التحقق من نوع MIME
    const mimeType = file.type.toLowerCase();

    // الخريطة من MIME types إلى امتدادات الملفات
    const mimeToExtension = {
      // Images
      "image/jpeg": ".jpg",
      "image/jpg": ".jpg",
      "image/png": ".png",
      "image/gif": ".gif",
      "image/webp": ".webp",

      // Audio
      "audio/mpeg": ".mp3",
      "audio/mp3": ".mp3",
      "audio/wav": ".wav",
      "audio/ogg": ".ogg",
      "audio/webm": ".webm",
      "audio/aac": ".aac",
      "audio/m4a": ".m4a",

      // Video
      "video/mp4": ".mp4",
      "video/quicktime": ".mov",
      "video/webm": ".webm",
      "video/ogg": ".ogv",

      // Documents
      "application/pdf": ".pdf",
      "application/msword": ".doc",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        ".docx",
      "application/vnd.ms-excel": ".xls",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        ".xlsx",
      "application/vnd.ms-powerpoint": ".ppt",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation":
        ".pptx",
      "text/plain": ".txt",
    };

    // البحث عن الامتداد المناسب
    if (mimeToExtension[mimeType]) {
      return mimeToExtension[mimeType];
    }

    // استخراج الامتداد من اسم الملف إذا كان موجودًا
    const fileName = file.name;
    const lastDotIndex = fileName.lastIndexOf(".");

    if (lastDotIndex !== -1) {
      return fileName.substring(lastDotIndex);
    }

    // تقديم امتداد افتراضي بناءً على نوع الملف
    if (mimeType.startsWith("image/")) return ".jpg";
    if (mimeType.startsWith("video/")) return ".mp4";
    if (mimeType.startsWith("audio/")) return ".mp3";
    if (mimeType.includes("pdf") || mimeType.includes("document"))
      return ".pdf";

    return ""; // إرجاع سلسلة فارغة إذا لم يتم العثور على امتداد مناسب
  }

  /**
   * الحصول على نوع MIME الصحيح للملف
   * @param {File} file - الملف
   * @returns {string} - نوع MIME
   */
  getProperMimeType(file) {
    const mimeType = file.type.toLowerCase();

    // التصحيح لأنواع MIME الشائعة غير الصحيحة
    const mimeCorrections = {
      "audio/mp3": "audio/mpeg",
      "audio/mp4": "audio/mpeg",
      "video/quicktime": "video/mp4",
    };

    // تصحيح نوع MIME إذا كان ضروريًا
    if (mimeCorrections[mimeType]) {
      return mimeCorrections[mimeType];
    }

    // التحقق من نوع الملف إذا كان MIME فارغًا
    if (!mimeType || mimeType === "application/octet-stream") {
      const extension = this.getFileExtension(file.name).toLowerCase();

      // خريطة من الامتدادات إلى أنواع MIME
      const extensionToMime = {
        ".jpg": "image/jpeg",
        ".jpeg": "image/jpeg",
        ".png": "image/png",
        ".gif": "image/gif",
        ".webp": "image/webp",

        ".mp3": "audio/mpeg",
        ".wav": "audio/wav",
        ".ogg": "audio/ogg",
        ".m4a": "audio/m4a",

        ".mp4": "video/mp4",
        ".mov": "video/quicktime",
        ".webm": "video/webm",

        ".pdf": "application/pdf",
        ".doc": "application/msword",
        ".docx":
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ".xls": "application/vnd.ms-excel",
        ".xlsx":
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ".ppt": "application/vnd.ms-powerpoint",
        ".pptx":
          "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        ".txt": "text/plain",
      };

      if (extensionToMime[extension]) {
        return extensionToMime[extension];
      }

      // تخمين نوع MIME بناءً على اسم الملف
      if (file.name.includes("voice") || file.name.includes("audio")) {
        return "audio/mpeg";
      }
    }

    return mimeType || "application/octet-stream";
  }

  /**
   * استخراج امتداد الملف من اسمه
   * @param {string} fileName - اسم الملف
   * @returns {string} - امتداد الملف مع النقطة
   */
  getFileExtension(fileName) {
    const lastDotIndex = fileName.lastIndexOf(".");

    if (lastDotIndex !== -1) {
      return fileName.substring(lastDotIndex);
    }

    return "";
  }

  /**
   * تحديد نوع الملف كسلسلة نصية للإرسال
   * @param {File} file - الملف
   * @returns {string} - نوع الملف كسلسلة
   */
  getFileTypeString(file) {
    if (this.isImageFile(file)) return "image";
    if (this.isVideoFile(file)) return "video";
    if (this.isAudioFile(file)) return "audio";
    if (this.isPdfFile(file)) return "document";
    if (this.isDocumentFile(file)) return "document";
    return "file";
  }

  /**
   * الحصول على نوع الرسالة من الملف
   * @param {File} file - الملف
   * @returns {number} نوع الرسالة
   */
  getMessageTypeFromFile(file) {
    if (this.isImageFile(file)) {
      return MessageTypes.Image;
    } else if (this.isVideoFile(file)) {
      return MessageTypes.Video;
    } else if (this.isAudioFile(file)) {
      return MessageTypes.Audio;
    } else if (this.isDocumentFile(file)) {
      return MessageTypes.Document;
    } else {
      return MessageTypes.OtherFiles;
    }
  }

  /**
   * التحقق مما إذا كان الملف صورة
   * @param {File} file - الملف للتحقق
   * @returns {boolean} صحيح إذا كان صورة
   */
  isImageFile(file) {
    return this.supportedImageTypes.includes(file.type);
  }

  /**
   * التحقق مما إذا كان الملف فيديو
   * @param {File} file - الملف للتحقق
   * @returns {boolean} صحيح إذا كان فيديو
   */
  isVideoFile(file) {
    return this.supportedVideoTypes.includes(file.type);
  }

  /**
   * التحقق مما إذا كان الملف صوتيًا
   * @param {File} file - الملف للتحقق
   * @returns {boolean} صحيح إذا كان صوتيًا
   */
  isAudioFile(file) {
    return this.supportedAudioTypes.includes(file.type);
  }

  /**
   * التحقق مما إذا كان الملف مستندًا
   * @param {File} file - الملف للتحقق
   * @returns {boolean} صحيح إذا كان مستندًا
   */
  isDocumentFile(file) {
    return this.supportedDocumentTypes.includes(file.type);
  }

  /**
   * التحقق مما إذا كان الملف PDF
   * @param {File} file - الملف للتحقق
   * @returns {boolean} صحيح إذا كان PDF
   */
  isPdfFile(file) {
    return file.type === "application/pdf";
  }
}

// إنشاء مثيل لمدير رفع الملفات عند تحميل الصفحة
let fileUploadManager;

document.addEventListener("DOMContentLoaded", () => {
  fileUploadManager = new FileUploadManager();
});

// تحديث دالة retryMessage لدعم إعادة محاولة رفع الملفات
// نتحقق أولاً إذا كانت الدالة موجودة قبل محاولة تعديلها
if (typeof retryMessage === "function") {
  // إذا كانت الدالة موجودة بالفعل، قم بتخزينها ثم استبدالها
  const originalRetryMessage = retryMessage;
  retryMessage = async function (messageId) {
    // الحصول على الرسالة من قاعدة البيانات
    const message = await ChatProcessor.getMessageById(messageId);

    // إذا كانت رسالة ملف، تعامل معها بشكل مختلف
    if (
      message &&
      (message.messageType == MessageTypes.Image ||
        message.messageType == MessageTypes.Video ||
        message.messageType == MessageTypes.Audio ||
        message.messageType == MessageTypes.Document ||
        message.messageType == MessageTypes.OtherFiles)
    ) {
      // تحديث حالة الرسالة
      MessageGenerator.updateMessageStatus(messageId, "Pending");

      // هنا يمكن استدعاء API لإعادة الرفع
      // ...

      return;
    }

    // لغير رسائل الملفات، استخدم الدالة الأصلية
    return originalRetryMessage(messageId);
  };
} else {
  // إذا لم تكن الدالة موجودة بعد، نقوم بتعريف نسخة مؤقتة ستُستبدَل لاحقاً
  window.retryMessage = async function (messageId) {
    // الحصول على الرسالة من قاعدة البيانات
    const message = await ChatProcessor.getMessageById(messageId);

    // إذا كانت رسالة ملف، تعامل معها بشكل مختلف
    if (
      message &&
      (message.messageType == MessageTypes.Image ||
        message.messageType == MessageTypes.Video ||
        message.messageType == MessageTypes.Audio ||
        message.messageType == MessageTypes.Document ||
        message.messageType == MessageTypes.OtherFiles)
    ) {
      // تحديث حالة الرسالة
      MessageGenerator.updateMessageStatus(messageId, "Pending");
      //console.log("إعادة محاولة رفع الملف:", messageId);
      // هنا يمكن استدعاء API لإعادة الرفع
      // ...

      return;
    }

    //console.log("طلب إعادة إرسال رسالة:", messageId);
  };

  // نضيف مستمع حدث لاستبدال الدالة المؤقتة بالدالة الحقيقية عندما تصبح متاحة
  const checkRetryMessageInterval = setInterval(() => {
    if (typeof window.originalRetryMessage === "function") {
      clearInterval(checkRetryMessageInterval);
    }
  }, 1000);
}
