const MessageTypes = {
  Text: 0,
  Image: 1,
  Video: 2,
  Audio: 3,
  Document: 4,
  ContactCard: 5,
  Location: 6,
  Poll: 7,
  RecordVoice: 8,
  OtherFiles: 9,
  // Helper to get name from value
  getName: function (value) {
    return Object.keys(this).find((key) => this[key] === value);
  },
};

class MessageGenerator {
  static formatTime(date) {
    return new Date(date).toLocaleTimeString("En-EG", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  }

  static formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  static getMessageStatusIcon(status) {
    switch (status) {
      case "Read":
        return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20" aria-label="read" class="blue-tick" style="
    color: var(--tick-icons);
">
              <path fill="currentColor" d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.879a.32.32 0 0 1-.484.033l-.358-.325a.319.319 0 0 0-.484.032l-.378.483a.418.418 0 0 0 .036.541l1.32 1.266c.143.14.361.125.484-.033l6.272-8.048a.366.366 0 0 0-.064-.512zm-4.1 0l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z">
              </path>
          </svg>`;
      case "Sent":
        return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 15" width="16" height="15" class="chat-icon--gray">
                    <path fill="currentColor" d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.879a.32.32 0 0 1-.484.033l-.358-.325a.319.319 0 0 0-.484.032l-.378.483a.418.418 0 0 0 .036.541l1.32 1.266c.143.14.361.125.484-.033l6.272-8.048a.366.366 0 0 0-.064-.512z"></path>
                </svg>`;
      default:
        return "";
    }
  }

  /* دالة لعرض أيقونة الحالة المناسبة */
  static getStatusIcon(status) {
    ////////console.log("status", status)
    switch (status) {
      case "Pending":
        return '<i  class="fe fe-clock  ml-1 fe-16"></i>';
      //< svg class="status-icon pending" viewBox = "0 0 24 24" ><circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/><path d="M12 6v6l4 2" fill="none" stroke="currentColor" stroke-width="2"/></svg > ';
      case "Sent":
        return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20"
              class="white-tick">
              <path fill="currentColor"
                  d="m10.91 3.316-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z">
              </path>
          </svg>`;
      case "Delivered":
        return '<i class="fe fe-message-circle ml-1 fe-16"></i>';
      case "Read":
        return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20"
              aria-label="read" class="blue-tick">
              <path fill="currentColor"
                  d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.879a.32.32 0 0 1-.484.033l-.358-.325a.319.319 0 0 0-.484.032l-.378.483a.418.418 0 0 0 .036.541l1.32 1.266c.143.14.361.125.484-.033l6.272-8.048a.366.366 0 0 0-.064-.512zm-4.1 0l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z">
              </path>
          </svg>`;
      case "Failed":
        return '<i class="fe fe-x-circle ml-1 fe-16"></i>';
      default:
        return "";
    }
  }
  /* دالة لتحديث حالة الرسالة في واجهة المستخدم */
  static updateMessageStatus(messageId, status) {
    const messageElement = document.querySelector(
      `[data-message-id="${messageId}"]`
    );
    //console.log(
    //   "messageElement",
    //   status,
    //   messageId,
    //   document.querySelector(`[data-message-id="${messageId}"]`)
    // );
    if (!messageElement) return;

    // Remove existing status classes
    messageElement.classList.remove(
      "message-pending",
      "message-sent",
      "message-delivered",
      "message-read",
      "message-failed"
    );

    // Add new status class
    messageElement.classList.add(`message-${status.toLowerCase()}`);

    // Update status icon
    const statusIcon = messageElement.querySelector(".msg-status");
    if (statusIcon) {
      ////////console.log("statusIcon", statusIcon)
      //////////console.log("statusIcon", this.getStatusIcon(status))
      statusIcon.innerHTML = this.getStatusIcon(status);
    }
  }

  static generateMessageHTML(msg, chat) {
    ////////console.log("msg, chat", msg, chat);
    const isMyMessage =
      msg.senderID === ChatProcessor.processedData.currentUser?.id;
    const messageTime = this.formatTime(msg.createdDate);
    const messageStatus = this.getStatusIcon(msg.messageStatus);

    ////////console.log("msg.id,msg.messageStatus", msg.locId, msg.messageStatus)
    let messageContent = "";
    let messageClass = isMyMessage ? "my-chat" : "frnd-chat";

    let messageType = msg.messageType;
    //////console.log("msg.messageType first", messageType);

    if (typeof messageType === "string") {
      // Check if the string is a key in MessageTypes (e.g., "Text")
      if (MessageTypes.hasOwnProperty(messageType)) {
        messageType = MessageTypes[messageType];
      } else {
        // Attempt to parse the string as a number (e.g., "0" → 0)
        const parsed = parseInt(messageType, 10);
        if (!isNaN(parsed)) {
          messageType = parsed;
        } else {
          // Handle invalid values (optional)
          messageType = undefined; // or throw an error
        }
      }
    }
    console.log("msg.messageType sssss", msg.messageType);
    console.log("msg.messageType sssss", messageType);

    // Generate content based on message type
    switch (messageType) {
      case MessageTypes.Text: // Text
        messageContent = this.generateTextMessage(msg);
        break;
      case MessageTypes.Image: // Image
        console.log("image");
        messageContent = this.generateImageMessage(msg);
        break;
      case MessageTypes.Video: // Video
        messageContent = this.generateVideoMessage(msg);
        break;
      case MessageTypes.Audio: // Audio
        messageContent = this.generateAudioMessage(msg);
        break;
      case MessageTypes.Document: // Document
        messageContent = this.generateDocumentMessage(msg);
        break;
      case MessageTypes.ContactCard && msg.otherContent?.contactCard: // Contact Card
        messageContent = this.generateContactCardMessage(msg);
        break;
      case MessageTypes.Location && msg.otherContent?.location: // Location
        messageContent = this.generateLocationMessage(msg);
        break;
      case MessageTypes.Poll && msg.poll: // Poll
        messageContent = this.generatePollMessage(msg);
        break;
      case MessageTypes.RecordVoice: // Record Voice
        messageContent = this.generateVoiceMessage(msg);
        break;
      case MessageTypes.OtherFiles: // Other Files
        messageContent = this.generateOtherFileMessage(msg);
        break;
    }

    console.log("messageContent", messageContent);

    // Generate reply context if exists
    const replyContext = msg.replyToMessageID
      ? this.generateReplyContext(msg)
      : "";

    // Generate message actions menu
    const messageActions = this.generateMessageActions(msg, isMyMessage);

    let profilePic = null;
    if (chat) {
      // Generate profile picture for group chats

      profilePic = this.generateProfilePic(msg, chat);
    }
    let username = "";
    if (chat.type === "Group") {
      username = !isMyMessage
        ? `<div class="userName">
          <span>${msg.sender.userName}</span>
          </div>`
        : "";
    }

    return `
            <div class="containerMessage ${
              isMyMessage ? "d-flex justify-content-end" : ""
            }"  data-message-id="${msg.locId}">
                <div class="row align-items-center">
                    ${!isMyMessage ? profilePic : ""}
                    ${isMyMessage ? messageActions : ""}


                    <div class="col-auto" >
                        <div class="chatMessage ${messageClass}">
                            ${username}
                            ${replyContext}
                            ${messageContent}
                            <span class="chat__msg-filler${
                              isMyMessage ? "" : "2"
                            }"> </span>
                            <span class="msg-footer">

                                <span class="message-time">${messageTime}</span>
                                 <div class="msg-status">
                                ${isMyMessage ? messageStatus : ""}
                                </div>

                            </span>
                        </div>
                    </div>
                    ${isMyMessage ? profilePic : ""}
                    ${!isMyMessage ? messageActions : ""}

                </div>
            </div>
        `;
  }

  static generateTextMessage(msg) {
    return `<span>${msg.messageText}</span>`;
  }

  /**
   * Generates HTML for image messages with support for WhatsApp-style download states
   * @param {Object} msg - The message object containing file data
   * @returns {string} HTML markup for the image message
   */
  static generateImageMessage(msg) {
    if (!msg.file) return "";

    // Message variables
    const file = msg.file;
    const isMyMessage =
      msg.senderID === ChatProcessor.processedData.currentUser?.id;
    const imgClass = isMyMessage
      ? "image-message sent-image"
      : "image-message received-image";
    const imageSource = file.fileContent || "";
    const thumbnailSource =
      file.thumbnailContent || "assets/img/placeholder-image.jpg";
    const fileName = file.fileName || "Image";
    const fileSize = this.formatFileSize(file.fileSize || 0);

    // WhatsApp-style blurred image for not-yet-downloaded files
    const imageBlurClass =
      !isMyMessage && !file.isDownloaded && file.downloadStatus !== "Downloaded"
        ? "blurred-image"
        : "";

    // Determine which image source to use based on message state
    let displayImageSource = imageSource;

    // For incoming messages that are not downloaded yet, use the thumbnail (blurred)
    if (
      !isMyMessage &&
      !file.isDownloaded &&
      file.downloadStatus !== "Downloaded"
    ) {
      displayImageSource = thumbnailSource;
    }
    // For local image that's being uploaded or has failed to upload
    else if (
      isMyMessage &&
      (msg.messageStatus === "Pending" ||
        msg.messageStatus === "Uploading" ||
        msg.messageStatus === "Failed")
    ) {
      displayImageSource = imageSource || thumbnailSource;
    }
    // For downloaded images, use the local URL if available
    else if (file.isDownloaded && file.localUrl) {
      displayImageSource = file.localUrl;
    }

    // Generate HTML for the image message
    let messageContent = `
      <div class="file-message-container">
        <div class="${imgClass} ${imageBlurClass}">
          <img src="${displayImageSource}" alt="${fileName}" 
               onclick="${
                 file.isDownloaded || isMyMessage
                   ? `viewFullImage(this)`
                   : `downloadFile('${msg.locId}')`
               }">
          <div class="file-info">
            <div class="file-name">${fileName}</div>
            <div class="file-size">${fileSize}</div>
          </div>
        </div>
        ${this.generateUploadStatusOverlay(msg)}
      </div>
    `;

    // Add caption if present
    if (msg.messageText) {
      messageContent += `<div class="mt-2 file-caption">${msg.messageText}</div>`;
    }
    console.log("generateImageMessage", messageContent);
    return messageContent;
  }

  /**
   * Generates HTML for video messages with support for WhatsApp-style download states
   * @param {Object} msg - The message object containing file data
   * @returns {string} HTML markup for the video message
   */
  static generateVideoMessage(msg) {
    if (!msg.file) return "";

    // Message variables
    const file = msg.file;
    const isMyMessage =
      msg.senderID === ChatProcessor.processedData.currentUser?.id;
    const videoUrl = file.fileContent || "";
    const thumbnailUrl =
      file.thumbnailContent || "assets/img/placeholder-video.jpg";
    const fileName = file.fileName || "Video";
    const fileSize = this.formatFileSize(file.fileSize || 0);
    const duration = this.formatDuration(file.seconds || 0);
    const mimeType = file.mimeFileType || "video/mp4";

    // Determine video state for display
    const isDownloaded = file.isDownloaded || isMyMessage;
    const isDownloading = !isMyMessage && file.downloadStatus === "Downloading";
    const needsDownload =
      !isMyMessage &&
      !file.isDownloaded &&
      file.downloadStatus !== "Downloaded";

    // Add blur class for videos that need downloading
    const blurClass = needsDownload ? "blurred-thumbnail" : "";

    // Different content based on download state
    let videoContent = "";
    let actionButton = "";

    //console.log("isDownloaded", msg.file);

    // Case 1: Video is downloaded or is outgoing
    if (isDownloaded) {
      const videoToPlay = file.localUrl || videoUrl;
      videoContent = `<img src="${thumbnailUrl}" class="video-thumbnail" alt="${fileName}">`;
      actionButton = `<button class="video-play-button"  data-video-url="${videoToPlay}" 
                          data-mime-type="${mimeType}">
            <i class="fe fe-play"></i>
        </button>`;
    }
    // Case 2: Video needs to be downloaded (incoming, not downloaded)
    else if (needsDownload) {
      videoContent = `<img src="${thumbnailUrl}" class="video-thumbnail ${blurClass}" alt="${fileName}"   onclick="downloadFile('${msg.locId}')">`;
      // No play button for non-downloaded videos, clicking will initiate download via overlay
    }
    // Case 3: Video is currently downloading (handled by the overlay)
    else if (isDownloading) {
      videoContent = `<img src="${thumbnailUrl}" class="video-thumbnail ${blurClass}" alt="${fileName}">`;
      // Download progress shown in the overlay
    }

    //        videoContent =` <video class="video-thumbnail" controls>
    //    <source src="${ msg.file?.thumbnailContent }" type="${msg.file.mimeFileType}">
    //</video>`;
    // Generate complete message HTML
    let messageContent = `
        <div class="file-message-container">
            <div class="video-message ${
              isMyMessage ? "sent-video" : "received-video"
            }">
                <div class="video-container">
                    ${videoContent}
                    <span class="video-duration">${duration}</span>
                    ${actionButton}
                </div>
                <div class="file-info">
                    <div class="file-size">${fileSize}</div>
                </div>
                ${this.generateUploadStatusOverlay(msg)}
            </div>
        </div>
    `;

    // Add caption if present
    if (msg.messageText) {
      messageContent += `<div class="mt-2 file-caption">${msg.messageText}</div>`;
    }

    return messageContent;
  }

  static generateAudioMessage(msg) {
    if (!msg.file) return "";

    const file = msg.file;
    const isMyMessage =
      msg.senderID === ChatProcessor.processedData.currentUser?.id;
    const duration = file.duration || 0;

    // Convert audio duration from seconds to displayable format
    const formattedDuration = this.formatDuration(duration);

    // Get file size
    const fileSize = this.formatFileSize(file.fileSize || 0);

    // Check if it's a special audio message type
    switch (file.fileName?.toLowerCase()) {
      case "poll":
        return this.generatePollMessage(msg);
      case "voice":
        return this.generateVoiceMessage(msg);
      default:
        return this.generateOtherFileMessage(msg);
    }
  }

  static generatePollMessage(msg) {
    const poll = msg.poll;
    if (!poll) return "";

    return `
        <div class="poll-message">
            <div class="poll-title">${poll.pollTitle}</div>
            <div class="poll-options">
                ${poll.pollOptions
                  .map(
                    (option) => `
                    <div class="poll-option">
                        <div class="option-text">${option.optionText}</div>
                        <div class="votes-count">${option.votesCount} votes</div>
                    </div>
                `
                  )
                  .join("")}
            </div>
        </div>
    `;
  }

  static generateVoiceMessage(msg) {
    if (!msg.file) return "";

    // تحديد مصدر الصوت بناءً على حالة الرسالة والمرسل
    let audioSource = "";

    // إذا كانت الرسالة قيد الرفع أو من المستخدم الحالي واحتوت على ملف صوتي مخزن محليًا
    if (
      msg.messageStatus === "Pending" &&
      msg.senderID === ChatProcessor.processedData.currentUser?.id &&
      msg.file?.fileContent
    ) {
      // استخدام رابط الصوت المحلي مباشرة بدون بادئة
      audioSource = msg.file.fileContent;
    } else {
      // استخدام رابط الخادم للملفات الصوتية المستلمة من مستخدمين آخرين
      audioSource = msg.file.fileContent;
    }

    // تنسيق مدة التسجيل الصوتي
    const duration = this.formatDuration(msg.file?.seconds || 0);

    // إنشاء التحليل البصري المحسن
    let waveformBars = "";
    const barCount = 27; // عدد الأعمدة

    for (let i = 0; i < barCount; i++) {
      // ارتفاع عشوائي بين 20% و100%
      const height = Math.floor(Math.random() * 80 + 20);
      waveformBars += `<span style="height: ${height}%;"></span>`;
    }

    // تحديث لدعم حالة الرفع والخطأ
    let messageContent = `
        <div class="file-message-container">
            <div class="voice-message" data-audio-src="${audioSource}" data-message-id="${
      msg.locId || msg.id
    }">
                <button class="voice-play-btn">
                    <i class="fe fe-play"></i>
                </button>
                <div class="voice-waveform">
                    <div class="voice-progress"></div>
                    <div class="enhanced-waveform">
                        ${waveformBars}
                    </div>
                </div>
                <span class="voice-duration">${duration}</span>
            </div>
        </div>
    `;

    return messageContent;
  }

  static generateDocumentMessage(msg) {
    if (!msg.file) return "";

    const file = msg.file;
    const isMine = msg.senderID === ChatProcessor.processedData.currentUser?.id;
    const fileSize = this.formatFileSize(file.fileSize || 0);

    // 1) Determine file extension
    const name = file.fileName || "";
    const ext = name.split(".").pop().toLowerCase();

    // 2) Map extensions to icon URLs
    const ICONS = {
      pdf: "/assets/images/icon_docu/pdf-icon.svg",
      doc: "/assets/images/icon_docu/word-icon.svg",
      docx: "/assets/images/icon_docu/word-icon.svg",
      xls: "/assets/images/icon_docu/excel-icon.svg",
      xlsx: "/assets/images/icon_docu/excel-icon.svg",
      ppt: "/assets/images/icon_docu/google-slides-icon.svg",
      pptx: "/assets/images/icon_docu/google-slides-icon.svg",
      // add more as needed…
    };

    // 3) Pick icon or fallback
    const iconUrl =
      ICONS[ext] || `<i class="fe fe-file-text"></i>`;

    // download status…
    const needsDownload =
      !isMine && (!file.downloadStatus || file.downloadStatus === "NotStarted");
    const isDownloading = file.downloadStatus === "Downloading";
    const downloadFailed = file.downloadStatus === "Failed";
    const downloadDone = file.downloadStatus === "Downloaded" || isMine;

    const clickHandler = downloadDone
      ? `onclick="openFile('${isMine ? file.fileContent : file.localUrl}')"`
      : "";

    // 4) Build HTML
    let html = `
    <div class="file-message-container">
      <div class="file-message 
                  ${needsDownload ? "not-downloaded" : ""}
                  ${isDownloading ? "downloading" : ""}
                  ${downloadFailed ? "download-failed" : ""}
                  ${downloadDone ? "downloaded" : ""}"
           ${clickHandler}>
        
        <!-- dynamic icon -->
        <div class="file-icon">
          <img src="${iconUrl}" alt="${ext} file" width="24" height="24" />
        </div>

        <div class="file-info">
          <div class="file-name">${name}</div>
          <div class="file-size">${fileSize}</div>
        </div>

        ${
          needsDownload
            ? `<button class="download-file-btn"
                     onclick="downloadAndSaveFile('${msg.locId}')">
               <i class="fe fe-download"></i>
             </button>`
            : ""
        }
      </div>
    </div>
  `;

    // 5) Optional message text
    if (msg.messageText) {
      html += `<div class="mt-2">${msg.messageText}</div>`;
    }

    return html;
  }

  // static generateDocumentMessage(msg) {
  //   if (!msg.file) return "";

  //   const file = msg.file;
  //   const isMyMessage =
  //     msg.senderID === ChatProcessor.processedData.currentUser?.id;
  //   const fileSize = this.formatFileSize(file.fileSize || 0);

  //   // Create HTML based on download status
  //   const needsDownload =
  //     !isMyMessage &&
  //     (file.downloadStatus === "NotStarted" ||
  //       file.downloadStatus === undefined);

  //   const isDownloading = file.downloadStatus === "Downloading";
  //   const downloadFailed = file.downloadStatus === "Failed";
  //   const downloadComplete =
  //     file.downloadStatus === "Downloaded" || isMyMessage;

  //   // Add click handler for downloaded files
  //   const clickHandler = downloadComplete
  //     ? `onclick="openFile('${
  //         isMyMessage ? msg.file.fileContent : msg.file.localUrl
  //       }')"`
  //     : "";

  //   // Generate the message container with file details
  //   let messageContent = `
  //       <div class="file-message-container">
  //           <div class="file-message ${needsDownload ? "not-downloaded" : ""}${
  //     isDownloading ? "downloading" : ""
  //   }${downloadFailed ? "download-failed" : ""}${
  //     downloadComplete ? "downloaded" : ""
  //   }" ${clickHandler}>
  //               <div class="file-icon">
  //                   <i class="fe fe-file-text"></i>
  //               </div>
  //               <div class="file-info">
  //                   <div class="file-size">${fileSize}</div>
  //               </div>
  //                ${
  //                  needsDownload
  //                    ? `<button class="download-file-btn" onclick="downloadAndSaveFile('${msg.locId}')"><i class="fe fe-download"></i></button> `
  //                    : ""
  //                }

  //           </div>
  //       </div>
  //   `;

  //   // Add comment if exists
  //   if (msg.messageText) {
  //     messageContent += `<div class="mt-2">${msg.messageText}</div>`;
  //   }

  //   return messageContent;
  // }

  static generateOtherFileMessage(msg) {
    if (!msg.file) return "";

    // Add click handler for downloaded files
    const clickHandler =
      msg.messageStatus !== "Pending" &&
      msg.messageStatus !== "Failed" &&
      msg.senderID === ChatProcessor.processedData.currentUser?.id
        ? `onclick="openFile('${msg.file?.fileContent}')"`
        : "";

    // Generate file message content
    let messageContent = `
        <div class="file-message-container">
            <div class="file-message" ${clickHandler}>
                <div class="file-icon">
                    <i class="fe fe-file"></i>
                </div>
                <div class="file-info">
                    
                    <div class="file-size">${this.formatFileSize(
                      msg.file?.fileSize || msg.fileInfo?.fileSize || 0
                    )}</div>
                </div>
            </div>
            ${this.generateUploadStatusOverlay(msg)}
        </div>
    `;

    // Add comment if exists
    if (msg.messageText) {
      messageContent += `<div class="mt-2">${msg.messageText}</div>`;
    }

    return messageContent;
  }

  static generateContactCardMessage(msg) {
    const contact = msg.contact;
    if (!contact) return "";

    return `
        <div class="contact-card-message">
            <div class="contact-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="contact-info">
                <div class="contact-name">${contact.name}</div>
                <div class="contact-number">${contact.phoneNumber}</div>
            </div>
        </div>
    `;
    //     return `
    //     <div class="contact-card-message">
    //         <div class="contact-avatar">
    //             <i class="fas fa-user"></i>
    //         </div>
    //         <div class="contact-info">
    //             <div class="contact-name">${contact.name}</div>
    //             <div class="contact-number">${contact.phoneNumber}</div>
    //         </div>
    //     </div>
    // `;
  }

  static generateLocationMessage(msg) {
    return `
        <div class="location-message">
            <div class="location-map">
                <!-- Add map implementation here -->
            </div>
            <div class="location-info">
                <div class="location-name">${msg.messageText}</div>
            </div>
        </div>
    `;
  }

  static generateLocationMessage(msg) {
    return `
            <div class="location-message">
                <div class="location-map">
                    <!-- Add map implementation here -->
                </div>
                <div class="location-info">
                    <div class="location-name">${msg.messageText}</div>
                </div>
            </div>
        `;
  }

  static generatePollMessage(msg) {
    const poll = msg.poll;

    ////////console.log("msg.poll", msg.poll);
    ////////console.log("msg.poll", msg);
    return `
            <div class="poll-message">
                <div class="poll-title">${poll.pollTitle}</div>
                <div class="poll-options">
                    ${poll.pollOptions
                      .map(
                        (option) => `
                        <div class="poll-option">
                            <div class="option-text">${option.optionText}</div>
                            <div class="votes-count">${option.votesCount} votes</div>
                        </div>
                    `
                      )
                      .join("")}
                </div>
            </div>
        `;
  }

  static generateVoiceMessage(msg) {
    // تحديد مصدر الصوت بناءً على حالة الرسالة والمرسل
    let audioSource = "";

    // إذا كانت الرسالة قيد الرفع أو من المستخدم الحالي واحتوت على ملف صوتي مخزن محليًا
    if (
      msg.messageStatus === "Pending" &&
      msg.senderID === ChatProcessor.processedData.currentUser?.id &&
      msg.file?.fileContent
    ) {
      // استخدام رابط الصوت المحلي مباشرة بدون بادئة
      audioSource = msg.file.fileContent;
    } else {
      // استخدام رابط الخادم للملفات الصوتية المستلمة من مستخدمين آخرين
      audioSource = msg.file.fileContent;
    }

    // تنسيق مدة التسجيل الصوتي
    const duration = this.formatDuration(msg.file?.seconds || 0);

    // إنشاء التحليل البصري المحسن
    let waveformBars = "";
    const barCount = 27; // عدد الأعمدة

    for (let i = 0; i < barCount; i++) {
      // ارتفاع عشوائي بين 20% و100%
      const height = Math.floor(Math.random() * 80 + 20);
      waveformBars += `<span style="height: ${height}%;"></span>`;
    }

    // تحديث لدعم حالة الرفع والخطأ
    let messageContent = `
      <div class="file-message-container">
        <div class="voice-message" data-audio-src="${audioSource}" data-message-id="${
      msg.locId || msg.id
    }">
          <button class="voice-play-btn" >
            <i class="fe fe-play"></i>
          </button>
          <div class="voice-waveform">
            <div class="voice-progress"></div>
            <div class="enhanced-waveform">
                ${waveformBars}
            </div>
          </div>
          <span class="voice-duration">${duration}</span>
        </div>
      </div>
    `;

    return messageContent;
  }

  static generateOtherFileMessage(msg) {
    // Add click handler for downloaded files
    const clickHandler =
      msg.messageStatus !== "Pending" &&
      msg.messageStatus !== "Failed" &&
      msg.senderID === ChatProcessor.processedData.currentUser?.id
        ? `onclick="openFile('${msg.file?.fileContent}')"`
        : "";

    // تحديث لدعم حالة الرفع والخطأ
    let messageContent = `
      <div class="file-message-container">
        <div class="file-message" ${clickHandler}>
          <div class="file-icon">
            <i class="fe fe-file"></i>
          </div>
          <div class="file-info">
            <div class="file-name">${
              msg.file?.fileName || msg.fileInfo?.fileName || "ملف"
            }</div>
            <div class="file-size">${this.formatFileSize(
              msg.file?.fileSize || msg.fileInfo?.fileSize || 0
            )}</div>
          </div>
        </div>
        ${this.generateUploadStatusOverlay(msg)}
      </div>
    `;

    // إضافة التعليق إذا وجد
    if (msg.messageText) {
      messageContent += `<div class="mt-2">${msg.messageText}</div>`;
    }

    return messageContent;
  }

  static generateReplyContext(msg) {
    return `
            <div class="reply-context">
                <span class="reply-sender">${
                  msg.sender?.userName || "Unknown"
                }</span>
                <span class="reply-content">${
                  msg.replyToMessage?.replyMessageContent || ""
                }</span>
            </div>
        `;

    // <div class="reply-context">
    //     <span class="reply-sender">Friend's Name</span>
    //     <span class="reply-content">This was the original message they sent...</span>
    // </div>
  }

  static generateMessageActions(msg, isMyMessage = false) {
    const name = msg.sender?.userName === "You" ? "أنت" : msg.sender?.userName;
    //////console.log("name", name)
    //////console.log("msg.sender?.userName", msg.sender?.userName)
    const actions = isMyMessage
      ? `<a class="dropdown-item" onclick="showReply(${msg.id || msg.locId}, '${
          name || "Unknown"
        }', '${msg.messageText}')">رد</a>
             <a class="dropdown-item" href="#">معلومات الرسالة</a>
             <a class="dropdown-item" href="#">تعديل</a>
             <a class="dropdown-item" href="#">حذف</a>`
      : `<a class="dropdown-item" onclick="showReply(${msg.id}, '${
          name || "Unknown"
        }', '${msg.messageText}')">رد</a>
             <a class="dropdown-item" href="#">حذف</a>`;

    return `
     <div class="col-auto">
            <span class="numb">
                <div class="dropdown-icon text-center">
                    <button class="btn btn-sm more-horizontal" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <svg viewBox="0 0 24 24" width="14" height="14" class="">
                            <path fill="currentColor" d="M12 7a2 2 0 1 0-.001-4.001A2 2 0 0 0 12 7zm0 2a2 2 0 1 0-.001 3.999A2 2 0 0 0 12 9zm0 6a2 2 0 1 0-.001 3.999A2 2 0 0 0 12 15z"></path>
                        </svg>
                    </button>
                    <div class="dropdown-menu dropdown-menu-right">
                        ${actions}
                    </div>
                </div>
            </span>
             </div>
            `;
  }

  static generateProfilePic(msg, chat) {
    if (
      chat.type === "Group" &&
      msg.senderID !== ChatProcessor.processedData.currentUser?.id
    ) {
      //const userPic = await DBManager.getUserById(msg.senderID);
      ////////console.log("userPic", userPic)
      //////console.log("userPic", msg.sender)
      const senderPic = msg.sender?.picture || "/assets/avatars/imageGrey.jpg";
      return `
                <div class="col-auto m-1">
                    <img src="${senderPic}" alt="Profile Photo" class="img-fluid rounded-circle mr-2" style="height: 34px;width: 34px;">
                </div>
            `;
    }
    return "";
  }

  static formatDuration(seconds) {
    if (!seconds) return "0:00";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }

  /**
   * Generate the appropriate overlay for file message based on its status
   * @param {Object} msg - The message object
   * @returns {string} HTML for the overlay
   */
  static generateUploadStatusOverlay(msg) {
    // For outgoing messages in uploading state
    if (
      msg.messageStatus === "Pending" ||
      msg.messageStatus === "Uploading" ||
      msg.messageStatus === "Failed"
    ) {
      return `
        <div class="upload-progress-overlay">
          <div class="progress-container">
            <svg class="progress-ring" width="40" height="40">
              <circle class="progress-ring-circle-bg" stroke="rgba(255,255,255,0.3)" stroke-width="3" fill="transparent" r="16" cx="20" cy="20"/>
              <circle class="progress-ring-circle" stroke="#fff" stroke-width="3" fill="transparent" r="16" cx="20" cy="20" 
                stroke-dasharray="${2 * Math.PI * 16}" stroke-dashoffset="${
        2 * Math.PI * 16 * (1 - 0.3)
      }"/>
            </svg>
            <div class="cancel-button" onclick="cancelUpload('${msg.locId}')">
              <i class="fe fe-x"></i>
            </div>
          </div>
        </div>
      `;
    }
    // For outgoing messages that failed to upload
    else if (msg.messageStatus === "Failed") {
      return `
        <div class="upload-error-overlay">
          <i class="fe fe-alert-circle"></i>
          <span>Failed to send</span>
          <button class="retry-button" onclick="retryUpload('${msg.locId}')">Retry</button>
        </div>
      `;
    }
    // For incoming files that haven't been downloaded yet
    else if (
      msg.senderID !== ChatProcessor.processedData.currentUser?.id &&
      msg.file &&
      !msg.file.isDownloaded
    ) {
      if (msg.file.downloadStatus === "NotStarted") {
        return `
                <div class="download-button" onclick="downloadFile('${msg.locId}')">
                    <i class="fe fe-download"></i>
                </div>
            `;
      }
      // For files that are currently downloading
      else if (msg.file.downloadStatus === "Downloading") {
        // Calculate stroke-dashoffset for the progress ring
        const progress = msg.file.downloadProgress / 100;
        const circumference = 2 * Math.PI * 16;
        const dashOffset = circumference * (1 - progress);

        return `
                <div class="download-progress-overlay">
                    <div class="progress-container">
                        <svg class="progress-ring" width="40" height="40">
                            <circle class="progress-ring-circle-bg" stroke="rgba(0,0,0,0.2)" stroke-width="3" fill="transparent" r="16" cx="20" cy="20"/>
                            <circle class="progress-ring-circle" stroke="#25D366" stroke-width="3" fill="transparent" r="16" cx="20" cy="20" 
                                stroke-dasharray="${circumference}" stroke-dashoffset="${dashOffset}"/>
                        </svg>
                        <div class="progress-percentage">${Math.round(
                          progress * 100
                        )}%</div>
                        <div class="cancel-button" onclick="cancelDownload('${
                          msg.locId
                        }')">
                            <i class="fe fe-x"></i>
                        </div>
                    </div>
                </div>
                `;
      }
      // For files that failed to download
      else if (msg.file.downloadStatus === "Failed") {
        return `
                <div class="download-error-overlay">
                    <i class="fe fe-alert-circle"></i>
                    <span>${msg.file.downloadError || "Download failed"}</span>
                    <button class="retry-button" onclick="retryDownload('${
                      msg.locId
                    }')">Retry</button>
                </div>
                `;
      }
    }

    return "";
  }

  /**
   * Update the message status in the UI
   * @param {string} messageId - The message ID
   * @param {string} status - The new status (Pending, Sent, Delivered, Read, Failed)
   */
  static updateMessageStatusAndProgressOverlay(messageId, status) {
    //const messageElement = document.querySelector(`.chatMessage[data-message-id="${messageId}"]`);
    //if (!messageElement) return;

    //// Update the status attribute
    //messageElement.setAttribute('data-status', status);

    //// Update status icons
    //const statusIcons = messageElement.querySelector('.msg-footer .tick-icon');
    //if (statusIcons) {
    //    switch (status) {
    //        case 'Pending':
    //            statusIcons.innerHTML = '<i class="fe fe-clock"></i>';
    //            break;
    //        case 'Sent':
    //            statusIcons.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20" class="white-tick"><path fill="currentColor" d="M4.2 11.8l3.3 3.3 8.4-8.4-1.3-1.3-7.1 7.1-2-2-1.3 1.3z"></path></svg>';
    //            break;
    //        case 'Delivered':
    //            statusIcons.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20" class="white-tick"><path fill="currentColor" d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.879a.32.32 0 0 1-.484.033l-.358-.325a.319.319 0 0 0-.484.032l-.378.483a.418.418 0 0 0 .036.541l1.32 1.266c.143.14.361.125.484-.033l6.272-8.048a.366.366 0 0 0-.064-.512zm-4.1 0l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"></path></svg>';
    //            break;
    //        case 'Read':
    //            statusIcons.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20" class="blue-tick"><path fill="currentColor" d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.879a.32.32 0 0 1-.484.033l-.358-.325a.319.319 0 0 0-.484.032l-.378.483a.418.418 0 0 0 .036.541l1.32 1.266c.143.14.361.125.484-.033l6.272-8.048a.366.366 0 0 0-.064-.512zm-4.1 0l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"></path></svg>';
    //            break;
    //        case 'Failed':
    //            statusIcons.innerHTML = '<i class="fe fe-alert-circle" style="color: #e74c3c;"></i>';
    //            break;
    //    }
    //}

    // Update upload status overlay
    const fileContainer = messageElement.querySelector(
      ".file-message-container"
    );
    if (fileContainer) {
      // Remove existing overlays
      const existingOverlay = fileContainer.querySelector(
        ".upload-progress-overlay, .upload-error-overlay, .download-button"
      );
      if (existingOverlay) {
        existingOverlay.remove();
      }

      // Get the message object
      const messageId = messageElement.getAttribute("data-message-id");
      ChatProcessor.getMessageById(messageId).then((msg) => {
        if (msg) {
          // Update the message status
          msg.messageStatus = status;

          // Add the appropriate overlay
          const overlay = this.generateUploadStatusOverlay(msg);
          if (overlay) {
            fileContainer.insertAdjacentHTML("beforeend", overlay);
          }
        }
      });
    }
  }
}
