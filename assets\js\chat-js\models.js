// /* نماذج البيانات - تعريف هياكل البيانات المستخدمة في التطبيق */

/* نماذج البيانات - تعريف هياكل البيانات المستخدمة في التطبيق */

// Create a global namespace for our models
window.ChatModels = {
  ChatMember: class ChatMember {
    constructor(data = {}) {
      this.id = data.id || null;
      this.chatID = data.chatID || null;
      this.userID = data.userID || null;
      this.userRoleInChat = data.userRoleInChat || "";
      this.dateJoined = data.dateJoined ? new Date(data.dateJoined) : null;
      this.user = data.user ? new ChatModels.User(data.user) : null;
      this.owner = data.owner || "";
      this.memberType = data.memberType || "";
    }
  },

  Company: class Company {
    constructor(data = {}) {
      this.id = data.id || null;
      this.name = data.name || "";
    }
  },

  MessageStatuses: class MessageStatuses {
    constructor(data = {}) {
      this.userChatMemberUnread = (data.userChatMemberUnread || []).map(
        (u) => new ChatModels.UserChatMemberUnread(u)
      );
      this.usersChatMemberReaded = (data.usersChatMemberReaded || []).map(
        (u) => new ChatModels.UsersChatMemberReaded(u)
      );
    }
  },

  LastMessage: class LastMessage {
    constructor(data = {}) {
      this.id = data.id || null;
      this.senderID = data.senderID || null;
      this.locId = data.locId || null;
      this.chatID = data.chatID || null;
      this.messageType = data.messageType || 0;
      this.messageText = data.messageText || "";
      this.messageStatus = data.messageStatus || "";
      this.deviceInfo = data.deviceInfo || "";
      this.isDeleted = data.isDeleted || false;
      this.edited = data.edited || false;
      this.createdDate = data.createdDate ? new Date(data.createdDate) : null;
      this.sender = data.sender ? new ChatModels.Sender(data.sender) : null;
      this.messageStatuses = data.messageStatuses
        ? new ChatModels.MessageStatuses(data.messageStatuses)
        : null;
      this.file = data.file ? new ChatModels.File(data.file) : null;
      this.modifyDate = data.modifyDate ? new Date(data.modifyDate) : null;
      this.replyToMessageID = data.replyToMessageID || null;
      this.replyToMessage = data.replyToMessage
        ? new ChatModels.ReplyToMessage(data.replyToMessage)
        : null;
      this.otherContent = data.otherContent
        ? new ChatModels.OtherContent(data.otherContent)
        : null;
      this.poll = data.poll ? new ChatModels.Poll(data.poll) : null;
    }
  },

  ReplyToMessage: class ReplyToMessage {
    constructor(data = {}) {
      this.replyMessageID = data.replyMessageID || null;
      this.replyMessageType = data.replyMessageType || null;
      this.replyMessageContent = data.replyMessageContent || null;
      this.replySenderID = data.replySenderID || null;
    }
  },
  OtherContent: class OtherContent {
    constructor(data = {}) {
      this.id = data.id || null;
      this.contentType = data.contentType || null;
      this.contactCard = data.contactCard
        ? new ChatModels.ContactCard(data.contactCard)
        : null;
    }
  },
  ContactCard: class ContactCard {
    constructor(data = {}) {
      this.name = data.name || "";
      this.phoneNumber = data.phoneNumber || "";
    }
  },
  File: class File {
    constructor(data = {}) {
      this.id = data.id || null;
      this.fileName = data.fileName || "";
      this.mimeFileType = data.mimeFileType || "";
      this.fileSize = data.fileSize || null;
      this.isUploadComplete = data.isUploadComplete || false;
      this.storageType = data.storageType || "";
      this.thumbnailContent = data.thumbnailContent || "";
      this.thumbnailMimeType = data.thumbnailMimeType || "";
      this.sha256 = data.sha256 || "";
      this.uuid = data.uuid || "";
      this.seconds = data.seconds || null;
      this.fileMimeType = data.fileMimeType || "";
      this.fileContent = data.fileContent || "";
      // Properties for file receiving (download) functionality
      this.isDownloaded = data.isDownloaded || false; // Whether the file has been downloaded
      this.downloadPath = data.downloadPath || ""; // Local path where file is saved
      this.autoDownloaded = data.autoDownloaded || false; // If the file was auto-downloaded based on settings
      this.localUrl = data.localUrl || "";
      // Additional properties for WhatsApp-style file handling
      this.downloadStatus = "NotStarted"; // Possible values: NotStarted, Downloading, Downloaded, Failed
      this.downloadProgress = 0; // Value from 0 to 100
      this.downloadError = null; // Error message if download failed
      this.isDownloaded = false; // Quick check if file is downloaded
      this.requiresWifi = false; // Flag for files that are waiting for WiFi to download
      this.lastAttempt = null; // Timestamp of last download attempt
      this.retryCount = 0; // Number of download attempts
      this.duration = 0; // For audio/video files - duration in seconds
      this.previewGenerated = false; // Whether a preview has been generated for this file
    }
  },
  Poll: class Poll {
    constructor(data = {}) {
      this.id = data.id || null;
      this.pollTitle = data.pollTitle || "";
      this.expirationDate = data.expirationDate
        ? new Date(data.expirationDate)
        : null;
      this.pollType = data.pollType || null;
      this.pollTypeName = data.pollTypeName || "";
      this.pollOptions = (data.pollOptions || []).map(
        (o) => new ChatModels.PollOption(o)
      );
    }
  },

  PollOption: class PollOption {
    constructor(data = {}) {
      this.id = data.id || null;
      this.optionText = data.optionText || "";
      this.votesCount = data.votesCount || 0;
      this.userVotesPollOptions = (data.userVotesPollOptions || []).map(
        (u) => new ChatModels.UserVotesPollOption(u)
      );
    }
  },
  UserVote: class UserVote {
    constructor(data = {}) {
      this.id = data.id || null;
      this.userName = data.userName || "";
      this.phoneNumber = data.phoneNumber || "";
      this.picture = data.picture || "";
    }
  },

  UserVotesPollOption: class UserVotesPollOption {
    constructor(data = {}) {
      this.id = data.id || null;
      this.chatMemberId = data.chatMemberId || null;
      this.voteCreatedDate = data.voteCreatedDate
        ? new Date(data.voteCreatedDate)
        : null;
      this.userVote = data.userVote
        ? new ChatModels.UserVote(data.userVote)
        : null;
    }
  },

  Sender: class Sender {
    constructor(data = {}) {
      this.id = data.id || null;
      this.userName = data.userName || "";
      this.picture = data.picture || "";

      this.phoneNumber = data.phoneNumber || "";
    }
  },
  PageData: class PageData {
    constructor(data = {}) {
      this.id = data.id || null;
      this.chatName = data.chatName || "";
      this.chatPicture = data.chatPicture || "";
      this.chatType = data.chatType || "";
      this.unreadCount = data.unreadCount || 0;
      this.companyID = data.companyID || null;
      this.createdUserId = data.createdUserId || null;
      this.createdDate = data.createdDate ? new Date(data.createdDate) : null;
      this.company = data.company ? new ChatModels.Company(data.company) : null;
      this.chatMembers = (data.chatMembers || []).map(
        (m) => new ChatModels.ChatMember(m)
      );
      this.lastMessages = (data.lastMessages || []).map(
        (m) => new ChatModels.LastMessage(m)
      );
      this.chatDescription = data.chatDescription || "";
    }
  },

  StatusConnection: class StatusConnection {
    constructor(data = {}) {
      this.id = data.id || null;
      this.statusConnection = data.statusConnection || false;
      this.lastSeenDatetime = data.lastSeenDatetime
        ? new Date(data.lastSeenDatetime)
        : null;
    }
  },

  User: class User {
    constructor(data = {}) {
      this.id = data.id || null;
      this.userName = data.userName || "";
      this.phoneNumber = data.phoneNumber || "";
      this.picture = data.picture || "";
      this.userTypeId = data.userTypeId || "";
      this.statusConnection = data.statusConnection
        ? new ChatModels.StatusConnection(data.statusConnection)
        : null;
      this.statusConnectionPlatform = (data.statusConnectionPlatform || []).map(
        (s) => new ChatModels.StatusConnection(s)
      );
    }
  },

  UserChatMember: class UserChatMember {
    constructor(data = {}) {
      this.id = data.id || null;
      this.userName = data.userName || "";
    }
  },

  UserChatMemberUnread: class UserChatMemberUnread {
    constructor(data = {}) {
      this.id = data.id || null;
      this.messageID = data.messageID || null;
      this.chatMemberId = data.chatMemberId || null;
      this.status = data.status || "";
      this.sentAtDate = data.sentAtDate ? new Date(data.sentAtDate) : null;
      this.userChatMember = data.userChatMember
        ? new ChatModels.UserChatMember(data.userChatMember)
        : null;
    }
  },

  UsersChatMemberReaded: class UsersChatMemberReaded {
    constructor(data = {}) {
      this.id = data.id || null;
      this.messageID = data.messageID || null;
      this.chatMemberId = data.chatMemberId || null;
      this.status = data.status || "";
      this.sentAtDate = data.sentAtDate ? new Date(data.sentAtDate) : null;
      this.readAtDate = data.readAtDate ? new Date(data.readAtDate) : null;
      this.userChatMember = data.userChatMember
        ? new ChatModels.UserChatMember(data.userChatMember)
        : null;
    }
  },

  Data: class Data {
    constructor(data = {}) {
      this.pageData = (data.pageData || []).map(
        (p) => new ChatModels.PageData(p)
      );
      this.totalRecords = data.totalRecords || 0;
      this.currentPage = data.currentPage || 1;
      this.pageSize = data.pageSize || 10;
      this.totalPages = data.totalPages || 0;
      this.hasPreviousPage = data.hasPreviousPage || false;
      this.hasNextPage = data.hasNextPage || false;
    }
  },

  Root: class Root {
    constructor(data = {}) {
      this.resObject = data.resObject
        ? new ChatModels.Data(data.resObject)
        : null;
      this.resCode = data.resCode || null;
      this.resMeg = data.resMeg || "";
    }
  },
};

window.ContactModels = {
  Contact: class Contact {
    constructor(data) {
      this.id = data.id || 0;
      this.contactName = data.contactName || "";
      this.contactType = data.contactType || null;
      this.status = data.status || true;
      this.userContacts = data.userContacts || [];
      this.contactMemberships = data.contactMemberships || [];
    }

    static fromResponse(response) {
      return new Contact({
        id: response.id,
        contactName: response.contactName,
        contactType: response.contactType,
        status: response.status,
        userContacts: response.userContacts,
        contactMemberships: response.contactMemberships,
      });
    }
  },
  User: class User {
    constructor(data) {
      this.id = data.id || 0;
      this.userName = data.userName || "";
      this.phoneNumber = data.phoneNumber || "";
      this.picture = data.picture || "";
    }

    static fromResponse(response) {
      return new User({
        id: response.id,
        userName: response.userName,
        phoneNumber: response.phoneNumber,
        picture: response.picture,
      });
    }
  },
  ContactMembership: class ContactMembership {
    constructor(data) {
      this.id = data.id || 0;
      this.userId = data.userId || 0;
      this.contactId = data.contactId || 0;
      this.user = data.user || null;
      this.contact = data.contact || null;
    }

    static fromResponse(response) {
      return new ContactMembership({
        id: response.id,
        userId: response.userId,
        contactId: response.contactId,
        user: response.user
          ? window.ContactModels.User.fromResponse(response.user)
          : null,
        contact: response.contact
          ? window.ContactModels.Contact.fromResponse(response.contact)
          : null,
      });
    }
  },
};
/* نماذج بيانات المستخدم - تعريف هياكل البيانات المستخدمة في التطبيق */

// Create a global namespace for our user models
window.UserModels = {
  // Model for file type settings
  FileTypeSettings: class FileTypeSettings {
    constructor(data = {}) {
      this.extensions = data.extensions || [];
      this.enabled = data.enabled || false;
      this.maxSizeBytes = data.maxSizeBytes || 0;
      this.compression = data.compression || "";
    }
  },

  // Model for supported file types
  SupportedFileTypes: class SupportedFileTypes {
    constructor(data = {}) {
      this.document = data.document
        ? new UserModels.FileTypeSettings(data.document)
        : null;
      this.image = data.image
        ? new UserModels.FileTypeSettings(data.image)
        : null;
      this.video = data.video
        ? new UserModels.FileTypeSettings(data.video)
        : null;
      this.audio = data.audio
        ? new UserModels.FileTypeSettings(data.audio)
        : null;
      this.other = data.other
        ? new UserModels.FileTypeSettings(data.other)
        : null;
    }
  },

  // Model for message settings
  MessageSettings: class MessageSettings {
    constructor(data = {}) {
      this.maxCharactersPerMessage = data.maxCharactersPerMessage || 0;
      this.messageRetentionPolicy = data.messageRetentionPolicy || "";
      this.supportedFileTypes = data.supportedFileTypes
        ? new UserModels.SupportedFileTypes(data.supportedFileTypes)
        : null;
      this.enableUploadFileTypes = data.enableUploadFileTypes || [];
    }
  },

  // Model for file upload settings
  FileUploadSettings: class FileUploadSettings {
    constructor(data = {}) {
      this.enableUploadFileTypes = data.enableUploadFileTypes || [];
    }
  },

  // Model for chat settings
  ChatSettings: class ChatSettings {
    constructor(data = {}) {
      this.maxContactsPerUser = data.maxContactsPerUser || 0;
      this.maxGroupsPerUser = data.maxGroupsPerUser || 0;
      this.maxMembersPerGroup = data.maxMembersPerGroup || 0;
    }
  },

  // Model for contact settings
  ContactSettings: class ContactSettings {
    constructor(data = {}) {
      this.maxContactsPerUser = data.maxContactsPerUser || 0;
      this.maxMembersPerContact = data.maxMembersPerContact || 0;
    }
  },

  // Model for system settings
  SystemSettingValue: class SystemSettingValue {
    constructor(data = {}) {
      this.messageSettings = data.messageSettings
        ? new UserModels.MessageSettings(data.messageSettings)
        : null;
      this.fileUploadSettings = data.fileUploadSettings
        ? new UserModels.FileUploadSettings(data.fileUploadSettings)
        : null;
      this.chatSettings = data.chatSettings
        ? new UserModels.ChatSettings(data.chatSettings)
        : null;
      this.contactSettings = data.contactSettings
        ? new UserModels.ContactSettings(data.contactSettings)
        : null;
    }
  },

  // Model for company
  Company: class Company {
    constructor(data = {}) {
      this.id = data.id || 0;
      this.name = data.name || "";
      this.systemSettingValue = data.systemSettingValue
        ? new UserModels.SystemSettingValue(data.systemSettingValue)
        : null;
    }
  },

  // Model for token info
  TokenInfo: class TokenInfo {
    constructor(data = {}) {
      this.token = data.token || "";
      this.expiresOn = data.expiresOn ? new Date(data.expiresOn) : null;
    }
  },

  // Model for user permission
  UserPermission: class UserPermission {
    constructor(data = {}) {
      this.id = data.id || 0;
      this.name = data.name || "";
      this.value = data.value || "";
      this.status = data.status || false;
    }
  },

  // Model for user data
  UserData: class UserData {
    constructor(data = {}) {
      this.id = data.id || 0;
      this.userName = data.userName || "";
      this.phoneNumber = data.phoneNumber || "";
      this.picture = data.picture || "";
      this.about = data.about || "";
      this.status = data.status || false;
      this.chatsCount = data.chatsCount || 0;
      this.tokenInfo = data.tokenInfo
        ? new UserModels.TokenInfo(data.tokenInfo)
        : null;
      this.company = data.company ? new UserModels.Company(data.company) : null;
      this.usersPermissions = (data.usersPermissions || []).map(
        (p) => new UserModels.UserPermission(p)
      );
      this.changeOn = data.changeOn || "";
    }
  },

  // Model for API response
  ApiResponse: class ApiResponse {
    constructor(data = {}) {
      this.resCode = data.resCode || 0;
      this.resMeg = data.resMeg || "";
      this.resObject = data.resObject
        ? new UserModels.UserData(data.resObject)
        : null;
    }
  },
};

window.UsersAllModels = {
  StatusConnection: class StatusConnection {
    constructor(data = {}) {
      this.id = data.id || null;
      this.statusConnection = data.statusConnection || false;
      this.lastSeenDatetime = data.lastSeenDatetime
        ? new Date(data.lastSeenDatetime)
        : null;
    }
  },

  // Model for user data
  UserData: class UserData {
    constructor(data = {}) {
      this.id = data.id || null;
      this.userName = data.userName || "";
      this.phoneNumber = data.phoneNumber || "";
      this.picture = data.picture || "";
      this.userTypeId = data.userTypeId || "";
      this.statusConnection = data.statusConnection
        ? new ChatModels.StatusConnection(data.statusConnection)
        : null;
      this.statusConnectionPlatform = (data.statusConnectionPlatform || []).map(
        (s) => new ChatModels.StatusConnection(s)
      );
    }
  },

  // Model for API response
  ApiResponse: class ApiResponse {
    constructor(data = {}) {
      this.resCode = data.resCode || 0;
      this.resMeg = data.resMeg || "";
      this.resObject = data.resObject
        ? new UsersAllModels.UserData(data.resObject)
        : null;
    }
  },
};
window.TeamModels = {
  User: class User {
    constructor(data = {}) {
      this.id = data.id || 0;
      this.userName = data.userName || "";
      this.phoneNumber = data.phoneNumber || "";
      this.picture = data.picture || "";
    }
  },

  TeamMember: class TeamMember {
    constructor(data = {}) {
      this.id = data.id || 0;
      this.teamId = data.teamId || 0;
      this.userID = data.userID || 0;
      this.dateJoined = data.dateJoined || "";
      this.isLeave = data.isLeave || false;
      this.userAddedByID = data.userAddedByID || 0;
      this.user = data.user ? new TeamModels.User(data.user) : null;
    }
  },

  Team: class Team {
    constructor(data = {}) {
      this.id = data.id || 0;
      this.name = data.name || "";
      this.description = data.description || "";
      this.status = data.status || false;
      this.picture = data.picture || "";
      this.companyID = data.companyID || 0;
      this.createdDate = data.createdDate || "";
      this.createdUserId = data.createdUserId || 0;
      this.countMemberInTeam = data.countMemberInTeam || 0;
      this.teamMembers = (data.teamMembers || []).map(
        (member) => new TeamModels.TeamMember(member)
      );
    }
  },
  // Model for API response
  ApiResponse: class ApiResponse {
    constructor(data = {}) {
      this.resCode = data.resCode || 0;
      this.resMeg = data.resMeg || "";
      this.resObject = data.resObject
        ? new TeamModels.Team(data.resObject)
        : null;
    }
  },
};
