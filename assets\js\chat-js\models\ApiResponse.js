class ApiResponse {
  /**
   * @param {{resCode, resMeg, resObject}} data
   * @param {Function} [ModelFactory]  either:
   *     • a class (callable with `new`), or
   *     • a factory function (callable without `new`)
   */
  constructor(data = {}, ModelFactory = null) {
    this.resCode = data.resCode || 0;
    this.resMeg = data.resMeg || "";
    const obj = data.resObject ?? null;

    if (ModelFactory && obj != null) {
      // try calling as a constructor; if that fails, treat it as a factory
      try {
        // this.resObject = new ModelFactory(obj);
        this.resObject = ModelFactory(obj);

        console.log("ApiResponse try");
      } catch {
        console.log("ApiResponse catch");
        this.resObject = ModelFactory(obj);
      }
    } else {
      this.resObject = obj;
    }
  }
}

class PagedApiResponse {
  /**
   * @param {Object} data          – جسم الـ JSON من الـ API
   * @param {Function} [ItemClass] – كلاس بناء كل صفحة (مثل ChatModels.PageData)
   */
  constructor(data = {}, ItemClass = null) {
    // إذا مررت كلاس نبنيه، وإلّا نُرجع المصفوفة كما هي
    // this.pageData = (data.pageData || []).map((item) =>
    //   ItemClass ? new ItemClass(item) : item
    // );
    this.pageData = (data.pageData || []).map((item) => {
      if (!ItemClass) return item;
      try {
        return new ItemClass(item);
      } catch {
        return ItemClass(item);
      }
    });

    this.totalRecords = data.totalRecords || 0;
    this.currentPage = data.currentPage || 1;
    this.pageSize = data.pageSize || 10;
    this.totalPages = data.totalPages || 0;
    this.hasPreviousPage = data.hasPreviousPage || false;
    this.hasNextPage = data.hasNextPage || false;
  }
}
/**  
 * exmple
 *  const rootApi = new ApiResponse(
            chatData,
            // wrap PagedApiResponse in a small factory so it gets ChatModels.PageData too:
            (data) => new PagedApiResponse(data, ChatModels.PageData)
          );
          console.log("rootApi", rootApi); */
