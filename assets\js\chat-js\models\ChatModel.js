class ChatModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.locId = data.locId || null;
    this.name = data.name || "";
    this.type = data.type || "Individual"; // Individual أو Group
    this.picture = data.picture || "";
    this.description = data.description || "";
    this.createdDate = data.createdDate
      ? new Date(data.createdDate)
      : new Date();
    this.lastMessageDate = data.lastMessageDate || 0;
    this.unreadCount = data.unreadCount || 0;
    this.members = Array.isArray(data.members)
      ? data.members.map((m) => new ChatMemberModel(m))
      : [];
    this.lastMessages = Array.isArray(data.lastMessages)
      ? data.lastMessages.map((m) => new MessageModel(m))
      : [];
    this.company = data.company ? new CompanyModel(data.company) : null;

    this.companyID = data.companyID || null;
    this.createdUserId = data.createdUserId || null;
    this.createdDate = data.createdDate ? new Date(data.createdDate) : null;
  }

  /**
   * تحويل كائن المحادثة من واجهة برمجة التطبيقات إلى نموذج المحادثة
   * @param {Object} data - بيانات المحادثة من الخادم
   * @returns {ChatModel} - كائن نموذج المحادثة
   */
  static toJSON(data) {
    // console.log("toJSON", data);
    // تعامل مع بنية البيانات القادمة من الخادم
    const chat = new ChatModel({
      id: data.id,
      locId: data.locId,
      name: data.chatName || data.name,
      type: data.chatType || data.type,
      picture: data.chatPicture || data.picture || "",
      description: data.chatDescription || data.description || "",
      createdDate: data.createdDate,
      unreadCount: data.unreadCount || 0,
      members: data.chatMembers || data.members || [],
      lastMessages: data.lastMessages || [],
      company: data.company ? new ChatModels.Company(data.company) : null,
      companyID: data.companyID || null,
      createdUserId: data.createdUserId || null,
      createdDate: data.createdDate ? new Date(data.createdDate) : null,
    });

    // حساب تاريخ آخر رسالة للمحادثة إذا كانت متوفرة
    if (chat.lastMessages && chat.lastMessages.length > 0) {
      chat.lastMessageDate = new Date(
        chat.lastMessages[0].createdDate
      ).getTime();
    }

    return chat;
  }

  /**
   * تحويل قائمة من بيانات المحادثة من واجهة برمجة التطبيقات إلى نماذج محادثة
   * @param {Array} dataList - قائمة بيانات المحادثات من الخادم
   * @returns {Array<ChatModel>} - قائمة نماذج المحادثة
   */
  static toJSONList(dataList) {
    // console.log("toJSONList", dataList);
    return Array.isArray(dataList)
      ? dataList.map((item) => ChatModel.toJSON(item))
      : [];
  }
}

/**
 * ChatMemberModel - نموذج عضو المحادثة
 */
class ChatMemberModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.chatID = data.chatID || null;
    this.userID = data.userID || null;
    this.userRoleInChat = data.userRoleInChat || "";
    this.dateJoined = data.dateJoined ? new Date(data.dateJoined) : null;
    this.user = data.user ? new UserModel(data.user) : null;
    this.owner = data.owner || "";
    this.memberType = data.memberType || "";
  }
}
class CompanyModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.name = data.name || "";
  }
}

class StatusConnectionModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.statusConnection = data.statusConnection || false;
    this.lastSeenDatetime = data.lastSeenDatetime
      ? new Date(data.lastSeenDatetime)
      : null;
  }
}
class UserModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.userName = data.userName || "";
    this.phoneNumber = data.phoneNumber || "";
    this.picture = data.picture || "";
    this.userTypeId = data.userTypeId || "";
    this.statusConnection = data.statusConnection
      ? new StatusConnectionModel(data.statusConnection)
      : null;
    this.statusConnectionPlatform = (data.statusConnectionPlatform || []).map(
      (s) => new StatusConnectionModel(s)
    );
  }
}
/**
 * تصدير النماذج للاستخدام في باقي التطبيق
 */
// window.ChatModel = ChatModel;
// window.ChatMemberModel = ChatMemberModel;
// window.CompanyModel = CompanyModel;
