class MessageModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.senderID = data.senderID || null;
    this.locId = data.locId || null;
    this.chatID = data.chatID || null;
    this.messageType = data.messageType || "";
    this.messageText = data.messageText || "";
    this.messageStatus = data.messageStatus || "";
    this.deviceInfo = data.deviceInfo || "";
    this.isDeleted = data.isDeleted || false;
    this.edited = data.edited || false;
    this.createdDate = data.createdDate ? new Date(data.createdDate) : null;
    this.sender = data.sender ? new SenderModel(data.sender) : null;
    this.messageStatuses = data.messageStatuses
      ? new MessageStatusesModel(data.messageStatuses)
      : null;
    this.file = data.file ? new FileModel(data.file) : null;
    this.modifyDate = data.modifyDate ? new Date(data.modifyDate) : null;
    this.replyToMessageID = data.replyToMessageID || null;
    this.replyToMessage = data.replyToMessage
      ? new ReplyToMessageModel(data.replyToMessage)
      : null;
    this.otherContent = data.otherContent
      ? new OtherContentModel(data.otherContent)
      : null;
    this.poll = data.poll ? new PollModel(data.poll) : null;
  }
}
class MessageStatusesModel {
  constructor(data = {}) {
    this.userChatMemberUnread = (data.userChatMemberUnread || []).map(
      (u) => new UserChatMemberUnreadModel(u)
    );
    this.usersChatMemberReaded = (data.usersChatMemberReaded || []).map(
      (u) => new UsersChatMemberReadedModel(u)
    );
  }
}
class ReplyToMessageModel {
  constructor(data = {}) {
    this.replyMessageID = data.replyMessageID || null;
    this.replyMessageType = data.replyMessageType || null;
    this.replyMessageContent = data.replyMessageContent || null;
    this.replySenderID = data.replySenderID || null;
  }
}
class OtherContentModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.contentType = data.contentType || null;
    this.contactCard = data.contactCard
      ? new ContactCardModel(data.contactCard)
      : null;
  }
}
class ContactCardModel {
  constructor(data = {}) {
    this.name = data.name || "";
    this.phoneNumber = data.phoneNumber || "";
  }
}
class FileModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.fileName = data.fileName || "";
    this.mimeFileType = data.mimeFileType || "";
    this.fileSize = data.fileSize || null;
    this.isUploadComplete = data.isUploadComplete || false;
    this.storageType = data.storageType || "";
    this.thumbnailContent = data.thumbnailContent || "";
    this.thumbnailMimeType = data.thumbnailMimeType || "";
    this.sha256 = data.sha256 || "";
    this.uuid = data.uuid || "";
    this.seconds = data.seconds || null;
    this.fileMimeType = data.fileMimeType || "";
    this.fileContent = data.fileContent || "";
    // Properties for file receiving (download) functionality
    this.isDownloaded = data.isDownloaded || false; // Whether the file has been downloaded
    this.downloadPath = data.downloadPath || ""; // Local path where file is saved
    this.autoDownloaded = data.autoDownloaded || false; // If the file was auto-downloaded based on settings
    this.localUrl = data.localUrl || "";
    // Additional properties for WhatsApp-style file handling
    this.downloadStatus = "NotStarted"; // Possible values: NotStarted, Downloading, Downloaded, Failed
    this.downloadProgress = 0; // Value from 0 to 100
    this.downloadError = null; // Error message if download failed
    this.isDownloaded = false; // Quick check if file is downloaded
    this.requiresWifi = false; // Flag for files that are waiting for WiFi to download
    this.lastAttempt = null; // Timestamp of last download attempt
    this.retryCount = 0; // Number of download attempts
    this.duration = 0; // For audio/video files - duration in seconds
    this.previewGenerated = false; // Whether a preview has been generated for this file
  }
}
class PollModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.pollTitle = data.pollTitle || "";
    this.expirationDate = data.expirationDate
      ? new Date(data.expirationDate)
      : null;
    this.pollType = data.pollType || null;
    this.pollTypeName = data.pollTypeName || "";
    this.pollOptions = (data.pollOptions || []).map(
      (o) => new PollOptionModel(o)
    );
  }
}

class PollOptionModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.optionText = data.optionText || "";
    this.votesCount = data.votesCount || 0;
    this.userVotesPollOptions = (data.userVotesPollOptions || []).map(
      (u) => new UserVotesPollOptionModel(u)
    );
  }
}
class UserVoteModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.userName = data.userName || "";
    this.phoneNumber = data.phoneNumber || "";
    this.picture = data.picture || "";
  }
}

class UserVotesPollOptionModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.chatMemberId = data.chatMemberId || null;
    this.voteCreatedDate = data.voteCreatedDate
      ? new Date(data.voteCreatedDate)
      : null;
    this.userVote = data.userVote ? new UserVoteModel(data.userVote) : null;
  }
}

class SenderModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.userName = data.userName || "";
    this.picture = data.picture || "";

    this.phoneNumber = data.phoneNumber || "";
  }
}

class UserChatMemberModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.userName = data.userName || "";
  }
}

class UserChatMemberUnreadModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.messageID = data.messageID || null;
    this.chatMemberId = data.chatMemberId || null;
    this.status = data.status || "";
    this.sentAtDate = data.sentAtDate ? new Date(data.sentAtDate) : null;
    this.userChatMember = data.userChatMember
      ? new UserChatMemberModel(data.userChatMember)
      : null;
  }
}

class UsersChatMemberReadedModel {
  constructor(data = {}) {
    this.id = data.id || null;
    this.messageID = data.messageID || null;
    this.chatMemberId = data.chatMemberId || null;
    this.status = data.status || "";
    this.sentAtDate = data.sentAtDate ? new Date(data.sentAtDate) : null;
    this.readAtDate = data.readAtDate ? new Date(data.readAtDate) : null;
    this.userChatMember = data.userChatMember
      ? new UserChatMemberModel(data.userChatMember)
      : null;
  }
}

// window.MessageModel = MessageModel;
// window.MessageStatusesModel = MessageStatusesModel;
// window.ReplyToMessageModel = ReplyToMessageModel;
// window.OtherContentModel = OtherContentModel;
// window.ContactCardModel = ContactCardModel;
// window.FileModel = FileModel;
// window.PollModel = PollModel;
// window.PollOptionModel = PollOptionModel;
// window.UserVoteModel = UserVoteModel;
// window.UserVotesPollOptionModel = UserVotesPollOptionModel;
// window.SenderModel = SenderModel;
// window.UserChatMemberModel = UserChatMemberModel;
// window.UserChatMemberUnreadModel = UserChatMemberUnreadModel;
// window.UsersChatMemberReadedModel = UsersChatMemberReadedModel;
