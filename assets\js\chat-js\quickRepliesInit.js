/* Quick Replies Initialization - Sample Data */

// Sample quick replies data for demonstration
const sampleQuickReplies = [
  {
    id: "qr_welcome_001",
    title: "Welcome Message",
    messageType: 0, // Text
    content: "Welcome to our service! How can I help you today?",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_thanks_002",
    title: "Thank You",
    messageType: 0, // Text
    content: "Thank you for contacting us. We appreciate your business!",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_hours_003",
    title: "Business Hours",
    messageType: 0, // Text
    content:
      "Our business hours are Monday to Friday, 9 AM to 6 PM. We are closed on weekends.",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_location_004",
    title: "Office Location",
    messageType: 6, // Location
    content:
      '{"latitude": 31.2001, "longitude": 29.9187, "name": "Alexandria Office", "description": "Our main office location"}',
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_contact_005",
    title: "Support Contact",
    messageType: 5, // ContactCard
    content: '{"name": "Support Team", "phoneNumber": "+201234567890"}',
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_poll_006",
    title: "Satisfaction Survey",
    messageType: 7, // Poll
    content:
      '{"question": "How satisfied are you with our service?", "options": ["Very Satisfied", "Satisfied", "Neutral", "Dissatisfied", "Very Dissatisfied"]}',
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_image_007",
    title: "Company Logo",
    messageType: 1, // Image
    content: "Here is our company logo and branding information.",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_document_008",
    title: "Price List",
    messageType: 4, // Document
    content: "Please find our latest price list attached.",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_goodbye_009",
    title: "Goodbye Message",
    messageType: 0, // Text
    content:
      "Thank you for your time. Have a great day! Feel free to contact us anytime.",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_appointment_010",
    title: "Schedule Appointment",
    messageType: 0, // Text
    content:
      "To schedule an appointment, please call us at +201234567890 or visit our website.",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
];

/* Initialize sample quick replies if none exist */
async function initializeSampleQuickReplies() {
  try {
    // Check if quick replies already exist
    const existingReplies = await DBManager.getQuickReplies();

    if (existingReplies.length === 0) {
      console.log("No quick replies found, initializing sample data...");

      // Save sample quick replies
      await DBManager.saveQuickReplies(sampleQuickReplies);

      console.log("Sample quick replies initialized successfully");
      return true;
    } else {
      console.log(`Found ${existingReplies.length} existing quick replies`);
      return false;
    }
  } catch (error) {
    console.error("Error initializing sample quick replies:", error);
    return false;
  }
}

/* Export for use in other modules */
window.initializeSampleQuickReplies = initializeSampleQuickReplies;
window.sampleQuickReplies = sampleQuickReplies;
