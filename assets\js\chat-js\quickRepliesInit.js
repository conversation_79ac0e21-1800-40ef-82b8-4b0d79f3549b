/* Quick Replies Initialization - Sample Data */

// Sample quick replies data for demonstration
const sampleQuickReplies = [
  {
    id: "qr_welcome_001",
    title: "Welcome Message",
    messageType: 0, // Text
    content: "Welcome to our service! How can I help you today?",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_thanks_002",
    title: "Thank You",
    messageType: 0, // Text
    content: "Thank you for contacting us. We appreciate your business!",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_hours_003",
    title: "Business Hours",
    messageType: 0, // Text
    content:
      "Our business hours are Monday to Friday, 9 AM to 6 PM. We are closed on weekends.",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_location_004",
    title: "Office Location",
    messageType: 6, // Location
    content:
      '{"latitude": 31.2001, "longitude": 29.9187, "name": "Alexandria Office", "description": "Our main office location"}',
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_contact_005",
    title: "Support Contact",
    messageType: 5, // ContactCard
    content: '{"name": "Support Team", "phoneNumber": "+201234567890"}',
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_poll_006",
    title: "Satisfaction Survey",
    messageType: 7, // Poll
    content:
      '{"question": "How satisfied are you with our service?", "options": ["Very Satisfied", "Satisfied", "Neutral", "Dissatisfied", "Very Dissatisfied"]}',
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_image_007",
    title: "Company Logo",
    messageType: 1, // Image
    content: "Here is our company logo and branding information.",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_document_008",
    title: "Price List",
    messageType: 4, // Document
    content: "Please find our latest price list attached.",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_goodbye_009",
    title: "Goodbye Message",
    messageType: 0, // Text
    content:
      "Thank you for your time. Have a great day! Feel free to contact us anytime.",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_appointment_010",
    title: "الاختصار",
    messageType: 1, // Text
    content: "محتوى الرسالة",
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
  },
  {
    id: "qr_test_010",
    title: "عنوان الرد السريع1",
    messageType: 1,
    content: "محتوى الرسالة1",
    fileName: "ads-image.png",
    fileSize: 7412,
    mimeType: "image/png",
    fileData:
      "data:image/png;base64,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",
    id: "qr_1754233726451_gfyi97f",
    createdDate: "2025-08-03T15:08:46.451Z",
    updatedDate: "2025-08-03T15:08:46.451Z",
  },
];

/* Initialize sample quick replies if none exist */
async function initializeSampleQuickReplies() {
  try {
    // Check if quick replies already exist
    const existingReplies = await DBManager.getQuickReplies();

    if (existingReplies.length === 0) {
      console.log("No quick replies found, initializing sample data...");

      // Save sample quick replies
      await DBManager.saveQuickReplies(sampleQuickReplies);

      console.log("Sample quick replies initialized successfully");
      return true;
    } else {
      console.log(`Found ${existingReplies.length} existing quick replies`);
      return false;
    }
  } catch (error) {
    console.error("Error initializing sample quick replies:", error);
    return false;
  }
}

/* Export for use in other modules */
window.initializeSampleQuickReplies = initializeSampleQuickReplies;
window.sampleQuickReplies = sampleQuickReplies;
