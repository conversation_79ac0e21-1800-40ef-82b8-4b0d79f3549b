/* Quick Replies Manager - WhatsApp Business Web Style */

class QuickRepliesManager {
  constructor() {
    this.ajaxManager = new AjaxManager();
    this.quickReplies = [];
    this.isInitialized = false;
    this.currentEditingId = null;

    // Message type mappings
    this.messageTypes = {
      0: { name: "Text", icon: "message-circle", color: "#25d366" },
      1: { name: "Image", icon: "image", color: "#ac44cf" },
      2: { name: "Video", icon: "video", color: "#d3396d" },
      3: { name: "Audio", icon: "headphones", color: "#ff9800" },
      4: { name: "Document", icon: "file-text", color: "#5157ae" },
      5: { name: "ContactCard", icon: "user", color: "#00bcd4" },
      6: { name: "Location", icon: "map-pin", color: "#4caf50" },
      7: { name: "Poll", icon: "bar-chart", color: "#9c27b0" },
      8: { name: "RecordVoice", icon: "mic", color: "#ff5722" },
      9: { name: "OtherFiles", icon: "paperclip", color: "#607d8b" },
    };
  }

  /* Initialize Quick Replies Manager */
  async initialize() {
    try {
      if (this.isInitialized) return;

      // Load quick replies from database
      await this.loadQuickReplies();

      // Initialize UI
      this.initializeUI();

      this.isInitialized = true;
      console.log("Quick Replies Manager initialized successfully");
    } catch (error) {
      console.error("Error initializing Quick Replies Manager:", error);
      throw error;
    }
  }

  /* Load quick replies from database */
  async loadQuickReplies() {
    try {
      // First try to load from IndexedDB
      const localReplies = await DBManager.getQuickReplies();

      if (localReplies && localReplies.length > 0) {
        this.quickReplies = localReplies;
      } else {
        // If no local data, try to fetch from server
        await this.syncWithServer();
      }

      console.log("Quick replies loaded:", this.quickReplies.length);
    } catch (error) {
      console.error("Error loading quick replies:", error);
      this.quickReplies = [];
    }
  }

  /* Sync with server */
  async syncWithServer() {
    try {
      // Fetch quick replies from server
      const response = await this.ajaxManager.get("api/v1/QuickReplies");

      if (response && response.resCode === 200 && response.resObject) {
        this.quickReplies = response.resObject;
        // Save to local database
        await DBManager.saveQuickReplies(this.quickReplies);
      }
    } catch (error) {
      console.error("Error syncing with server:", error);
      // Continue with local data if server sync fails
    }
  }

  /* Initialize UI components */
  initializeUI() {
    this.createQuickRepliesButton();
    this.createQuickRepliesDropdown();
    this.createQuickRepliesModal();
    this.attachEventListeners();
    this.initializeSlashCommands();
  }

  /* Create quick replies button */
  createQuickRepliesButton() {
    const chatInputContainer = document.querySelector(".chat-input-container");
    if (!chatInputContainer) return;

    const quickRepliesBtn = document.createElement("button");
    quickRepliesBtn.id = "quick-replies-btn";
    quickRepliesBtn.className = "quick-replies-btn icons";
    quickRepliesBtn.title = "Quick Replies";
    quickRepliesBtn.innerHTML = `
      <svg viewBox="0 0 24 24" width="24" height="24">
        <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
    `;

    // Insert before the attachment button
    const attachButton = chatInputContainer.querySelector(".chat-attach");
    if (attachButton) {
      chatInputContainer.insertBefore(quickRepliesBtn, attachButton);
    } else {
      chatInputContainer.appendChild(quickRepliesBtn);
    }
  }

  /* Create quick replies dropdown */
  createQuickRepliesDropdown() {
    const chatInputWrapper = document.querySelector(".chat-input-wrapper");
    if (!chatInputWrapper) return;

    const dropdown = document.createElement("div");
    dropdown.id = "quick-replies-dropdown";
    dropdown.className = "quick-replies-dropdown";
    dropdown.innerHTML = `
      <div class="quick-replies-header">
        <h3 class="quick-replies-title">Quick Replies</h3>
        <button class="quick-replies-manage-btn" id="manage-quick-replies">
          Manage
        </button>
      </div>
      <div class="quick-replies-search">
        <input type="text" placeholder="Search quick replies..." id="quick-replies-search">
      </div>
      <div class="quick-replies-list" id="quick-replies-list">
        <!-- Quick replies will be populated here -->
      </div>
    `;

    chatInputWrapper.appendChild(dropdown);
  }

  /* Create quick replies modal */
  createQuickRepliesModal() {
    const modal = document.createElement("div");
    modal.id = "quick-replies-modal";
    modal.className = "quick-replies-modal";
    modal.innerHTML = `
      <div class="quick-replies-modal-content">
        <div class="quick-replies-modal-header">
          <h2 class="quick-replies-modal-title" id="modal-title">Manage Quick Replies</h2>
          <button class="quick-replies-modal-close" id="close-modal">&times;</button>
        </div>
        <div class="quick-replies-modal-body" id="modal-body">
          <!-- Content will be populated dynamically -->
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  /* Attach event listeners */
  attachEventListeners() {
    // Quick replies button click
    document
      .getElementById("quick-replies-btn")
      ?.addEventListener("click", (e) => {
        e.preventDefault();
        console.log("quick-replies-btn");

        this.toggleDropdown();
        this.showManagementModal();
      });

    // Search functionality
    document
      .getElementById("quick-replies-search")
      ?.addEventListener("input", (e) => {
        this.filterQuickReplies(e.target.value);
      });

    // Manage button click
    document
      .getElementById("manage-quick-replies")
      ?.addEventListener("click", () => {
        console.log("manage-quick-replies");
        this.showManagementModal();
      });

    // Modal close
    document.getElementById("close-modal")?.addEventListener("click", () => {
      this.hideModal();
    });

    // Click outside to close dropdown
    document.addEventListener("click", (e) => {
      const dropdown = document.getElementById("quick-replies-dropdown");
      const button = document.getElementById("quick-replies-btn");

      if (
        dropdown &&
        !dropdown.contains(e.target) &&
        !button.contains(e.target)
      ) {
        this.hideDropdown();
      }
    });

    // Click outside to close modal
    document
      .getElementById("quick-replies-modal")
      ?.addEventListener("click", (e) => {
        if (e.target.id === "quick-replies-modal") {
          this.hideModal();
        }
      });
  }

  /* Initialize slash commands */
  initializeSlashCommands() {
    const messageInput = document.getElementById("input-send-message");
    if (!messageInput) return;

    // Listen for input changes
    messageInput.addEventListener("input", (e) => {
      this.handleInputChange(e.target.value);
    });

    // Listen for keydown events
    messageInput.addEventListener("keydown", (e) => {
      this.handleKeyDown(e);
    });

    // Listen for focus/blur events
    messageInput.addEventListener("blur", () => {
      // Hide dropdown after a short delay to allow for clicks
      setTimeout(() => {
        this.hideInlineDropdown();
      }, 200);
    });
  }

  /* Handle input changes for slash commands */
  handleInputChange(value) {
    // this.showManagementModal();

    if (value.startsWith("/")) {
      const searchTerm = value.substring(1); // Remove the slash
      this.showInlineDropdown(searchTerm);
    } else {
      this.hideInlineDropdown();
    }
  }

  /* Handle keyboard navigation */
  handleKeyDown(e) {
    const dropdown = document.getElementById("quick-replies-dropdown");
    if (!dropdown || dropdown.style.display === "none") return;

    const items = dropdown.querySelectorAll(".quick-reply-item");
    if (items.length === 0) return;

    let currentIndex = -1;
    items.forEach((item, index) => {
      if (item.classList.contains("selected")) {
        currentIndex = index;
      }
    });

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        this.selectNextItem(items, currentIndex);
        break;
      case "ArrowUp":
        e.preventDefault();
        this.selectPreviousItem(items, currentIndex);
        break;
      case "Enter":
        e.preventDefault();
        if (currentIndex >= 0 && items[currentIndex]) {
          const replyId = items[currentIndex].getAttribute("data-id");
          this.useQuickReplyFromSlash(replyId);
        }
        break;
      case "Escape":
        e.preventDefault();
        this.hideInlineDropdown();
        break;
    }
  }

  /* Select next item in dropdown */
  selectNextItem(items, currentIndex) {
    items.forEach((item) => item.classList.remove("selected"));
    const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
    items[nextIndex].classList.add("selected");
    items[nextIndex].scrollIntoView({ block: "nearest" });
  }

  /* Select previous item in dropdown */
  selectPreviousItem(items, currentIndex) {
    items.forEach((item) => item.classList.remove("selected"));
    const prevIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
    items[prevIndex].classList.add("selected");
    items[prevIndex].scrollIntoView({ block: "nearest" });
  }

  /* Show inline dropdown */
  showInlineDropdown(searchTerm = "") {
    const dropdown = document.getElementById("quick-replies-dropdown");
    if (!dropdown) return;

    this.populateInlineQuickRepliesList(searchTerm);
    dropdown.style.display = "block";
  }

  /* Hide inline dropdown */
  hideInlineDropdown() {
    const dropdown = document.getElementById("quick-replies-dropdown");
    if (dropdown) {
      dropdown.style.display = "none";
    }
  }

  /* Populate inline quick replies list */
  populateInlineQuickRepliesList(searchTerm = "") {
    const listContainer = document.getElementById("quick-replies-list-inline");
    if (!listContainer) return;

    const filteredReplies = this.quickReplies.filter((reply) => {
      const titleMatch = reply.title
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      const contentMatch = reply.content
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      return titleMatch || contentMatch;
    });

    if (filteredReplies.length === 0) {
      listContainer.innerHTML = `
        <div class="quick-replies-empty" style="padding: 16px; text-align: center; color: var(--primary);">
          <div>No quick replies found</div>
          <div style="font-size: 12px; margin-top: 4px;">Try a different search term</div>
        </div>
      `;
      return;
    }

    listContainer.innerHTML = filteredReplies
      .map(
        (reply, index) => `
      <div class="quick-reply-item ${index === 0 ? "selected" : ""}" data-id="${
          reply.id
        }" onclick="quickRepliesManager.useQuickReplyFromSlash('${reply.id}')">
        <div class="quick-reply-icon ${this.getMessageTypeClass(
          reply.messageType
        )}">
          ${this.getMessageTypeIcon(reply.messageType)}
        </div>
        <div class="quick-reply-content">
          <div class="quick-reply-title">${reply.title}</div>
          <div class="quick-reply-preview">${this.getPreviewText(reply)}</div>
        </div>
      </div>
    `
      )
      .join("");
  }

  /* Use quick reply from slash command */
  async useQuickReplyFromSlash(replyId) {
    const messageInput = document.getElementById("input-send-message");
    if (!messageInput) return;

    // Clear the input first
    messageInput.value = "";

    // Use the quick reply
    await this.useQuickReply(replyId);

    // Hide the dropdown
    this.hideInlineDropdown();

    // Focus back on input
    messageInput.focus();
  }

  /* Toggle dropdown visibility */
  toggleDropdown() {
    const dropdown = document.getElementById("quick-replies-dropdown");
    if (!dropdown) return;

    if (dropdown.classList.contains("show")) {
      this.hideDropdown();
    } else {
      this.showDropdown();
    }
  }

  /* Show dropdown */
  showDropdown() {
    const dropdown = document.getElementById("quick-replies-dropdown");
    if (!dropdown) return;

    this.populateQuickRepliesList();
    dropdown.classList.add("show");
  }

  /* Hide dropdown */
  hideDropdown() {
    const dropdown = document.getElementById("quick-replies-dropdown");
    if (!dropdown) return;

    dropdown.classList.remove("show");
  }

  /* Populate quick replies list */
  populateQuickRepliesList(searchTerm = "") {
    const listContainer = document.getElementById("quick-replies-list");
    if (!listContainer) return;

    const filteredReplies = this.quickReplies.filter(
      (reply) =>
        reply.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        reply.content.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (filteredReplies.length === 0) {
      listContainer.innerHTML = `
        <div class="quick-replies-empty">
          <div class="quick-replies-empty-title">No quick replies found</div>
          <div class="quick-replies-empty-text">
            ${
              searchTerm
                ? "Try a different search term"
                : "Create your first quick reply"
            }
          </div>
        </div>
      `;
      return;
    }

    listContainer.innerHTML = filteredReplies
      .map(
        (reply) => `
      <div class="quick-reply-item" data-id="${
        reply.id
      }" onclick="quickRepliesManager.useQuickReply('${reply.id}')">
        <div class="quick-reply-icon ${this.getMessageTypeClass(
          reply.messageType
        )}">
          ${this.getMessageTypeIcon(reply.messageType)}
        </div>
        <div class="quick-reply-content">
          <div class="quick-reply-title">${reply.title}</div>
          <div class="quick-reply-preview">${this.getPreviewText(reply)}</div>
        </div>
      </div>
    `
      )
      .join("");
  }

  /* Filter quick replies */
  filterQuickReplies(searchTerm) {
    this.populateQuickRepliesList(searchTerm);
  }

  /* Use quick reply */
  async useQuickReply(replyId) {
    try {
      const reply = this.quickReplies.find((r) => r.id === replyId);
      if (!reply) return;

      const messageInput = document.getElementById("input-send-message");
      if (!messageInput) return;

      // Handle different message types
      switch (reply.messageType) {
        case 0: // Text
          messageInput.value = reply.content;
          messageInput.focus();
          // Automatically send the text message
          await this.sendTextMessage(reply.content);
          // Clear the input after sending
          messageInput.value = "";
          break;

        case 1: // Image
        case 2: // Video
        case 3: // Audio
        case 4: // Document
        case 9: // OtherFiles
          await this.handleFileQuickReply(reply);
          break;

        case 5: // ContactCard
          await this.handleContactQuickReply(reply);
          break;

        case 6: // Location
          await this.handleLocationQuickReply(reply);
          break;

        case 7: // Poll
          await this.handlePollQuickReply(reply);
          break;

        case 8: // RecordVoice
          // Voice messages are typically recorded live
          messageInput.value = reply.content || "Voice message template";
          messageInput.focus();
          break;
      }

      this.hideDropdown();
    } catch (error) {
      console.error("Error using quick reply:", error);
    }
  }

  /* Send text message directly */
  async sendTextMessage(messageText) {
    try {
      // Get current chat
      const currentChat = window.chat;
      if (!currentChat) {
        console.error("No active chat");
        return;
      }

      // Create message data
      const messageData = {
        messageType: 0, // Text
        messageText: messageText,
        chatID: currentChat.id,
        senderID: ChatProcessor.processedData.currentUser?.id,
        source: 1,
        deviceInfo: "web",
      };

      // Send message using existing function
      if (window.saveSendingMessage) {
        await window.saveSendingMessage(messageData);
      } else {
        console.error("saveSendingMessage function not found");
      }
    } catch (error) {
      console.error("Error sending text message:", error);
    }
  }

  /* Handle file quick reply */
  async handleFileQuickReply(reply) {
    try {
      if (reply.fileData) {
        // If file data is stored, create a file object and trigger file upload
        const fileUploadManager = window.fileUploadManager;
        if (fileUploadManager) {
          // Convert base64 to blob if needed
          const blob = this.base64ToBlob(reply.fileData, reply.mimeType);
          const file = new File([blob], reply.fileName || "quick-reply-file", {
            type: reply.mimeType,
          });

          // Trigger file upload process
          fileUploadManager.handleFileSelection([file], reply.content);
        }
      } else {
        // If no file data, just set the caption
        const messageInput = document.getElementById("input-send-message");
        messageInput.value = reply.content || "";
        messageInput.focus();
      }
    } catch (error) {
      console.error("Error handling file quick reply:", error);
    }
  }

  /* Handle contact quick reply */
  async handleContactQuickReply(reply) {
    try {
      // Parse contact data
      const contactData = JSON.parse(reply.content);

      // Create contact message
      const messageData = {
        messageType: 5,
        messageText: "",
        otherContent: {
          contentType: "ContactCard",
          contactCard: {
            name: contactData.name,
            phoneNumber: contactData.phoneNumber,
          },
        },
      };

      // Send contact message
      await this.sendQuickReplyMessage(messageData);
    } catch (error) {
      console.error("Error handling contact quick reply:", error);
    }
  }

  /* Handle location quick reply */
  async handleLocationQuickReply(reply) {
    try {
      // Parse location data
      const locationData = JSON.parse(reply.content);

      // Create location message
      const messageData = {
        messageType: 6,
        messageText: locationData.description || "",
        otherContent: {
          contentType: "Location",
          location: {
            latitude: locationData.latitude,
            longitude: locationData.longitude,
            name: locationData.name,
          },
        },
      };

      // Send location message
      await this.sendQuickReplyMessage(messageData);
    } catch (error) {
      console.error("Error handling location quick reply:", error);
    }
  }

  /* Handle poll quick reply */
  async handlePollQuickReply(reply) {
    try {
      // Parse poll data
      const pollData = JSON.parse(reply.content);

      // Create poll message
      const messageData = {
        messageType: 7,
        messageText: pollData.question,
        poll: {
          question: pollData.question,
          options: pollData.options.map((option) => ({
            optionText: option,
            votes: 0,
          })),
        },
      };

      // Send poll message
      await this.sendQuickReplyMessage(messageData);
    } catch (error) {
      console.error("Error handling poll quick reply:", error);
    }
  }

  /* Send quick reply message */
  async sendQuickReplyMessage(messageData) {
    try {
      // Get current chat
      const currentChat = window.chat;
      if (!currentChat) {
        console.error("No active chat");
        return;
      }

      // Add chat ID and sender info
      messageData.chatID = currentChat.id;
      messageData.senderID = ChatProcessor.processedData.currentUser?.id;
      messageData.source = 1;
      messageData.deviceInfo = "web";

      // Save and send message
      await saveSendingMessage(messageData);
    } catch (error) {
      console.error("Error sending quick reply message:", error);
    }
  }

  /* Show management modal */
  showManagementModal() {
    console.log("showManagementModal called");
    const modal = document.getElementById("quick-replies-modal");
    const modalBody = document.getElementById("modal-body");
    const modalTitle = document.getElementById("modal-title");

    console.log("Modal elements:", { modal, modalBody, modalTitle });

    if (!modal || !modalBody || !modalTitle) {
      console.error("Modal elements not found");
      return;
    }

    modalTitle.textContent = "Manage Quick Replies";
    modalBody.innerHTML = this.getManagementHTML();

    this.attachManagementEventListeners();
    modal.classList.add("show");
    this.hideDropdown();
    console.log("Management modal should be visible now");
  }

  /* Get management HTML */
  getManagementHTML() {
    return `
      <div class="quick-replies-management">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h3 style="margin: 0; color: var(--h4);">Quick Replies (${
            this.quickReplies.length
          })</h3>
          <button class="btn btn-primary" id="add-quick-reply">
            <i class="fe fe-plus"></i> Add New
          </button>
        </div>

        <div class="quick-replies-management-list" id="management-list">
          ${
            this.quickReplies.length === 0
              ? this.getEmptyStateHTML()
              : this.getManagementListHTML()
          }
        </div>
      </div>
    `;
  }

  /* Get management list HTML */
  getManagementListHTML() {
    return this.quickReplies
      .map(
        (reply) => `
      <div class="quick-reply-management-item" data-id="${reply.id}">
        <div class="quick-reply-icon ${this.getMessageTypeClass(
          reply.messageType
        )}">
          ${this.getMessageTypeIcon(reply.messageType)}
        </div>
        <div class="quick-reply-management-content">
          <div class="quick-reply-title">${reply.title}</div>
          <div class="quick-reply-preview">${this.getPreviewText(reply)}</div>
        </div>
        <div class="quick-reply-management-actions">
          <button class="action-btn edit" onclick="quickRepliesManager.editQuickReply('${
            reply.id
          }')" title="Edit">
            <i class="fe fe-edit-2"></i>
          </button>
          <button class="action-btn delete" onclick="quickRepliesManager.deleteQuickReply('${
            reply.id
          }')" title="Delete">
            <i class="fe fe-trash-2"></i>
          </button>
        </div>
      </div>
    `
      )
      .join("");
  }

  /* Get empty state HTML */
  getEmptyStateHTML() {
    return `
      <div class="quick-replies-empty">
        <div class="quick-replies-empty-icon">
          <svg viewBox="0 0 24 24" width="64" height="64" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
        <div class="quick-replies-empty-title">No Quick Replies Yet</div>
        <div class="quick-replies-empty-text">Create your first quick reply to get started</div>
      </div>
    `;
  }

  /* Attach management event listeners */
  attachManagementEventListeners() {
    document
      .getElementById("add-quick-reply")
      ?.addEventListener("click", () => {
        this.showAddEditForm();
      });
  }

  /* Show add/edit form */
  showAddEditForm(replyId = null) {
    const modalBody = document.getElementById("modal-body");
    const modalTitle = document.getElementById("modal-title");

    if (!modalBody || !modalTitle) return;

    const isEdit = replyId !== null;
    const reply = isEdit
      ? this.quickReplies.find((r) => r.id === replyId)
      : null;

    modalTitle.textContent = isEdit ? "Edit Quick Reply" : "Add Quick Reply";
    this.currentEditingId = replyId;

    modalBody.innerHTML = this.getFormHTML(reply);
    this.attachFormEventListeners();
  }

  /* Get form HTML */
  getFormHTML(reply = null) {
    const messageTypeOptions = Object.entries(this.messageTypes)
      .map(
        ([value, type]) =>
          `<option value="${value}" ${
            reply && reply.messageType == value ? "selected" : ""
          }>${type.name}</option>`
      )
      .join("");

    return `
      <form class="quick-reply-form" id="quick-reply-form">
        <div class="form-group">
          <label class="form-label">Title *</label>
          <input type="text" class="form-input" id="reply-title" value="${
            reply?.title || ""
          }" placeholder="Enter a title for this quick reply" required>
        </div>

        <div class="form-group">
          <label class="form-label">Message Type *</label>
          <select class="form-select" id="reply-type" required>
            <option value="">Select message type</option>
            ${messageTypeOptions}
          </select>
        </div>

        <div class="form-group" id="content-group">
          <label class="form-label">Content *</label>
          <textarea class="form-textarea" id="reply-content" placeholder="Enter your message content" required>${
            reply?.content || ""
          }</textarea>
        </div>

        <div class="form-group" id="file-group" style="display: none;">
          <label class="form-label">File</label>
          <label for="reply-file" class="form-file-label" id="file-label">
            <i class="fe fe-upload"></i>
            <span>Click to select file or drag and drop</span>
          </label>
          <input type="file" class="form-file-input" id="reply-file" accept="*/*">
          <div class="file-preview" id="file-preview">
            <div class="file-preview-content">
              <div class="file-preview-icon">
                <i class="fe fe-file"></i>
              </div>
              <div class="file-preview-info">
                <div class="file-preview-name" id="file-name"></div>
                <div class="file-preview-size" id="file-size"></div>
              </div>
              <button type="button" class="file-preview-remove" id="remove-file">
                <i class="fe fe-x"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="quick-replies-modal-footer">
          <button type="button" class="btn btn-secondary" id="cancel-form-btn">Cancel</button>
          <button type="submit" class="btn btn-primary">
            ${reply ? "Update" : "Create"} Quick Reply
          </button>
        </div>
      </form>
    `;
  }

  /* Attach form event listeners */
  attachFormEventListeners() {
    const typeSelect = document.getElementById("reply-type");
    const contentGroup = document.getElementById("content-group");
    const fileGroup = document.getElementById("file-group");
    const form = document.getElementById("quick-reply-form");
    const fileInput = document.getElementById("reply-file");

    // Handle message type change
    typeSelect?.addEventListener("change", (e) => {
      const messageType = parseInt(e.target.value);
      this.handleMessageTypeChange(messageType);
    });

    // Handle file selection
    fileInput?.addEventListener("change", (e) => {
      this.handleFileSelection(e.target.files[0]);
    });

    // Handle file removal
    document.getElementById("remove-file")?.addEventListener("click", () => {
      this.removeSelectedFile();
    });

    // Handle cancel button
    document
      .getElementById("cancel-form-btn")
      ?.addEventListener("click", () => {
        this.showManagementModal();
      });

    // Handle form submission
    form?.addEventListener("submit", (e) => {
      e.preventDefault();
      this.saveQuickReply();
    });

    // Initialize form based on current type
    if (typeSelect?.value) {
      this.handleMessageTypeChange(parseInt(typeSelect.value));
    }
  }

  /* Handle message type change */
  handleMessageTypeChange(messageType) {
    const contentGroup = document.getElementById("content-group");
    const fileGroup = document.getElementById("file-group");
    const contentTextarea = document.getElementById("reply-content");

    if (!contentGroup || !fileGroup || !contentTextarea) return;

    // Show/hide appropriate fields based on message type
    switch (messageType) {
      case 0: // Text
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        contentTextarea.placeholder = "Enter your text message";
        break;

      case 1: // Image
      case 2: // Video
      case 3: // Audio
      case 4: // Document
      case 9: // OtherFiles
        contentGroup.style.display = "block";
        fileGroup.style.display = "block";
        contentTextarea.placeholder = "Enter caption (optional)";
        break;

      case 5: // ContactCard
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        contentTextarea.placeholder =
          'Enter contact data as JSON: {"name": "John Doe", "phoneNumber": "+1234567890"}';
        break;

      case 6: // Location
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        contentTextarea.placeholder =
          'Enter location data as JSON: {"latitude": 0, "longitude": 0, "name": "Location Name", "description": "Description"}';
        break;

      case 7: // Poll
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        contentTextarea.placeholder =
          'Enter poll data as JSON: {"question": "Your question?", "options": ["Option 1", "Option 2"]}';
        break;

      case 8: // RecordVoice
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        contentTextarea.placeholder =
          "Enter description for voice message template";
        break;

      default:
        contentGroup.style.display = "block";
        fileGroup.style.display = "none";
        contentTextarea.placeholder = "Enter content";
    }
  }

  /* Handle file selection */
  handleFileSelection(file) {
    if (!file) return;

    const filePreview = document.getElementById("file-preview");
    const fileName = document.getElementById("file-name");
    const fileSize = document.getElementById("file-size");
    const fileLabel = document.getElementById("file-label");

    if (filePreview && fileName && fileSize && fileLabel) {
      fileName.textContent = file.name;
      fileSize.textContent = this.formatFileSize(file.size);
      filePreview.classList.add("show");
      fileLabel.classList.add("has-file");
      fileLabel.innerHTML = `<i class="fe fe-check"></i> File selected: ${file.name}`;
    }
  }

  /* Remove selected file */
  removeSelectedFile() {
    const fileInput = document.getElementById("reply-file");
    const filePreview = document.getElementById("file-preview");
    const fileLabel = document.getElementById("file-label");

    if (fileInput) fileInput.value = "";
    if (filePreview) filePreview.classList.remove("show");
    if (fileLabel) {
      fileLabel.classList.remove("has-file");
      fileLabel.innerHTML =
        '<i class="fe fe-upload"></i> <span>Click to select file or drag and drop</span>';
    }
  }

  /* Save quick reply */
  async saveQuickReply() {
    try {
      const form = document.getElementById("quick-reply-form");
      if (!form) return;

      const formData = new FormData(form);
      const title = document.getElementById("reply-title")?.value;
      const messageType = parseInt(
        document.getElementById("reply-type")?.value
      );
      const content = document.getElementById("reply-content")?.value;
      const fileInput = document.getElementById("reply-file");

      if (!title || isNaN(messageType) || !content) {
        alert("Please fill in all required fields");
        return;
      }

      const quickReply = {
        id: this.currentEditingId || this.generateId(),
        title: title.trim(),
        messageType: messageType,
        content: content.trim(),
        createdDate: new Date().toISOString(),
        updatedDate: new Date().toISOString(),
      };

      // Handle file if present
      if (fileInput?.files[0]) {
        const file = fileInput.files[0];
        quickReply.fileName = file.name;
        quickReply.fileSize = file.size;
        quickReply.mimeType = file.type;
        quickReply.fileData = await this.fileToBase64(file);
      }

      // Save to database and sync with server
      await this.saveQuickReplyToDatabase(quickReply);

      // Update local array
      if (this.currentEditingId) {
        const index = this.quickReplies.findIndex(
          (r) => r.id === this.currentEditingId
        );
        if (index !== -1) {
          this.quickReplies[index] = quickReply;
        }
      } else {
        this.quickReplies.push(quickReply);
      }

      // Reset form and show management
      this.currentEditingId = null;
      this.showManagementModal();

      console.log("Quick reply saved successfully");
    } catch (error) {
      console.error("Error saving quick reply:", error);
      alert("Error saving quick reply. Please try again.");
    }
  }

  /* Save quick reply to database */
  async saveQuickReplyToDatabase(quickReply) {
    try {
      // Save to IndexedDB
      await DBManager.saveQuickReply(quickReply);

      // Sync with server
      try {
        if (this.currentEditingId) {
          await this.ajaxManager.post(
            `api/v1/QuickReplies/${quickReply.id}`,
            quickReply
          );
        } else {
          await this.ajaxManager.post("api/v1/QuickReplies", quickReply);
        }
      } catch (serverError) {
        console.warn("Server sync failed, saved locally:", serverError);
      }
    } catch (error) {
      console.error("Error saving to database:", error);
      throw error;
    }
  }

  /* Edit quick reply */
  editQuickReply(replyId) {
    this.showAddEditForm(replyId);
  }

  /* Delete quick reply */
  async deleteQuickReply(replyId) {
    if (!confirm("Are you sure you want to delete this quick reply?")) {
      return;
    }

    try {
      // Remove from database
      await DBManager.deleteQuickReply(replyId);

      // Sync with server
      try {
        await this.ajaxManager.delete(`api/v1/QuickReplies/${replyId}`);
      } catch (serverError) {
        console.warn("Server sync failed, deleted locally:", serverError);
      }

      // Remove from local array
      this.quickReplies = this.quickReplies.filter((r) => r.id !== replyId);

      // Refresh management view
      this.showManagementModal();

      console.log("Quick reply deleted successfully");
    } catch (error) {
      console.error("Error deleting quick reply:", error);
      alert("Error deleting quick reply. Please try again.");
    }
  }

  /* Hide modal */
  hideModal() {
    const modal = document.getElementById("quick-replies-modal");
    if (modal) {
      modal.classList.remove("show");
    }
    this.currentEditingId = null;
  }

  /* Utility methods */
  getMessageTypeClass(messageType) {
    const typeMap = {
      0: "text",
      1: "image",
      2: "video",
      3: "audio",
      4: "document",
      5: "contact",
      6: "location",
      7: "poll",
      8: "voice",
      9: "file",
    };
    return typeMap[messageType] || "text";
  }

  getMessageTypeIcon(messageType) {
    const iconMap = {
      0: '<i class="fe fe-message-circle"></i>',
      1: '<i class="fe fe-image"></i>',
      2: '<i class="fe fe-video"></i>',
      3: '<i class="fe fe-headphones"></i>',
      4: '<i class="fe fe-file-text"></i>',
      5: '<i class="fe fe-user"></i>',
      6: '<i class="fe fe-map-pin"></i>',
      7: '<i class="fe fe-bar-chart"></i>',
      8: '<i class="fe fe-mic"></i>',
      9: '<i class="fe fe-paperclip"></i>',
    };
    return iconMap[messageType] || '<i class="fe fe-message-circle"></i>';
  }

  getPreviewText(reply) {
    if (reply.messageType === 0) {
      return reply.content.length > 50
        ? reply.content.substring(0, 50) + "..."
        : reply.content;
    }

    const typeNames = {
      1: "Image",
      2: "Video",
      3: "Audio",
      4: "Document",
      5: "Contact",
      6: "Location",
      7: "Poll",
      8: "Voice",
      9: "File",
    };

    const typeName = typeNames[reply.messageType] || "Message";
    const caption = reply.content
      ? ` - ${reply.content.substring(0, 30)}${
          reply.content.length > 30 ? "..." : ""
        }`
      : "";

    return `${typeName}${caption}`;
  }

  generateId() {
    return "qr_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9);
  }

  formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  async fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  }

  base64ToBlob(base64, mimeType) {
    const byteCharacters = atob(base64.split(",")[1]);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }
}

// Initialize global instance
window.quickRepliesManager = new QuickRepliesManager();
