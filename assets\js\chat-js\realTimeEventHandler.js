/**
 * RealTimeEvent<PERSON><PERSON>ler - Handles real-time events from SignalR
 * Processes events and updates the UI accordingly
 */
class RealTimeEventHandler {
  constructor(signalRManager, chatListManager, chatProcessor) {
    this.signalRManager = signalRManager;
    this.chatListManager = chatListManager;
    this.chatProcessor = chatProcessor;

    // Register event handlers
    this.registerEventHandlers();
    //this.displayNoif();
  }

  /**
   * Register event handlers for all SignalR events
   */
  registerEventHandlers() {
    // User chat online status
    this.signalRManager.on("userChatOnline", (data) =>
      this.handleUserChatOnline(data)
    );

    // Chat updates
    this.signalRManager.on("chatUpdate", (data) => this.handleChatUpdate(data));

    // Group events
    this.signalRManager.on("groupEvents", (data) =>
      this.handleGroupEvents(data)
    );

    // New messages
    this.signalRManager.on("newMessage", (data) => this.handleNewMessage(data));

    // Message status updates
    this.signalRManager.on("messageStatus", (data) =>
      this.handleMessageStatus(data)
    );

    // Message updates or deletions
    this.signalRManager.on("messageUpdateOrDelete", (data) =>
      this.handleMessageUpdateOrDelete(data)
    );

    // Poll vote events
    this.signalRManager.on("pollVote", (data) => this.handlePollVote(data));

    // Blocking events
    this.signalRManager.on("blockingEvent", (data) =>
      this.handleBlockingEvent(data)
    );

    // User info events
    this.signalRManager.on("userInfo", (data) => this.handleUserInfo(data));
  }

  /**
   * Handle user chat online status updates
   * @param {Array} data - List of UserConnectionStatus objects
   */
  async handleUserChatOnline(data) {
    if (!data || !Array.isArray(data)) return;
    // Update chat list with new online status
    if (this.chatListManager) {
      await this.chatListManager.updateUsersOnlineStatus(data);
    }
  }

  /**
   * Handle chat updates
   * @param {Array} data - List of ChatResponseDto objects
   */
  async handleChatUpdate(data) {
    if (!data || !Array.isArray(data)) return;
    const Chats = await this.chatProcessor.addChats(data);
    // Update chat list with new chat data
    if (this.chatListManager) {
      Chats.forEach(async (chat) => {
        try {
          await this.chatListManager.addNewChat(chat);
        } catch (err) {
          console.error("failed to add chat", chat, err);
        }
      });
    }
  }

  /**
   * Handle group events
   * @param {Array} data - List of ChatGroupEventDto objects
   */
  async handleGroupEvents(data) {
    if (!data || !Array.isArray(data)) return;

    console.log("Processing group events:", data);

    // Process each group event
    for (const event of data) {
      // Update chat list to reflect group changes
      if (this.chatListManager) {
        await this.chatListManager.handleGroupEvent(event);
      }
    }
  }

  /**
   * Handle new messages
   * @param {Array} data - List of MessageResponseDto objects
   */
  async handleNewMessage(data) {
    //console.log("Processing new messagesssss:", data);

    if (!data || !Array.isArray(data)) {
      data = data.resObject;
    }

    //console.log("Processing new messages:", data);

    // Group messages by chat ID
    const messagesByChat = {};
    data.forEach((message) => {
      if (!messagesByChat[message.chatID]) {
        messagesByChat[message.chatID] = [];
      }
      messagesByChat[message.chatID].push(message);
    });

    //console.log("messagesByChat", messagesByChat);
    // Process messages for each chat
    for (const chatId in messagesByChat) {
      const messages = messagesByChat[chatId];

      //console.log("= messagesByChat[chatId];", messagesByChat[chatId]);
      //console.log("= messagesByChat", messagesByChat);
      // Update chat list with new messages
      if (this.chatListManager) {
        await this.chatListManager.updateChatWithMessages(
          parseInt(chatId),
          messages
        );
      }
      for (const message of messages) {
        //console.log("addMessage", message);
        await this.chatProcessor.addMessage(message);
        if (this.chatListManager.getActiveChatId() === parseInt(chatId)) {
          addMessageToMessageArea(message);
        }
      }
      // Update current chat if it's the chat with new messages
      if (this.chatListManager.getActiveChatId() === parseInt(chatId)) {
        // Add new messages to the current chat

        // Play notification sound if the message is not from the current user
        const currentUserId = this.chatProcessor.processedData.currentUser?.id;
        const hasIncomingMessages = messages.some(
          (msg) => msg.senderID !== currentUserId
        );

        if (hasIncomingMessages) {
          this.playMessageNotificationSound();
        }
      } else {
        // Show notification for new messages in other chats
        this.showNewMessageNotification(chatId, messages);
      }
    }
  }
  /**
   * Handle message status updates
   * @param {Array} data - List of MessageStatusBasicResponseDto objects
   */
  async handleMessageStatus(data) {
    if (!data || !Array.isArray(data)) return;

    //console.log("Processing message status updates:", data);

    // Group status updates by message ID
    const statusByMessage = {};
    data.forEach((status) => {
      if (!statusByMessage[status.messageID]) {
        statusByMessage[status.messageID] = [];
      }
      statusByMessage[status.messageID].push(status);
    });
    // Update message statuses in the UI
    for (const messageId in statusByMessage) {
      const statuses = statusByMessage[messageId];
      // Update chat list to reflect message status changes
      if (this.chatListManager) {
        await this.chatListManager.updateMessageStatuses(
          parseInt(messageId),
          statuses
        );
      }
    }
  }

  /**
   * Handle message updates or deletions
   * @param {Array} data - List of MessageResponseDto objects
   */
  async handleMessageUpdateOrDelete(data) {
    if (!data || !Array.isArray(data)) return;
    // Group messages by chat ID
    const messagesByChat = {};
    data.forEach((message) => {
      if (!messagesByChat[message.chatID]) {
        messagesByChat[message.chatID] = [];
      }
      messagesByChat[message.chatID].push(message);
    });

    // Process messages for each chat
    for (const chatId in messagesByChat) {
      const messages = messagesByChat[chatId];

      // Update current chat if it's the chat with updated/deleted messages
      const currentChat = await this.chatProcessor.getCurrentChat();
      if (currentChat && currentChat.id === parseInt(chatId)) {
        // Update or delete messages in the current chat
        this.chatProcessor.updateOrDeleteMessages(messages);
      }

      // Update chat list to reflect message changes
      if (this.chatListManager) {
        await this.chatListManager.updateChatWithMessages(chatId, messages);
      }
    }
  }

  /**
   * Handle poll vote events
   * @param {Array} data - List of PollMessageResponseDto objects
   */
  async handlePollVote(data) {
    if (!data || !Array.isArray(data)) return;
    // Group polls by chat ID
    const pollsByChat = {};
    data.forEach((poll) => {
      if (!pollsByChat[poll.chatID]) {
        pollsByChat[poll.chatID] = [];
      }
      pollsByChat[poll.chatID].push(poll);
    });

    // Process polls for each chat
    for (const chatId in pollsByChat) {
      const polls = pollsByChat[chatId];

      // Update current chat if it's the chat with updated polls
      const currentChat = await this.chatProcessor.getCurrentChat();
      if (currentChat && currentChat.id === parseInt(chatId)) {
        // Update polls in the current chat
        this.chatProcessor.updatePolls(polls);
      }
    }
  }

  /**
   * Handle blocking events
   * @param {Object} data - BlockedUserResponseDto object
   */
  async handleBlockingEvent(data) {
    if (!data) return;

    //console.log("Processing blocking event:", data);

    // Update blocked users list
    if (this.chatProcessor) {
      await this.chatProcessor.updateBlockedUsers(data);
    }

    // Update chat list to reflect blocking status
    if (this.chatListManager) {
      await this.chatListManager.handleBlockingEvent(data);
    }

    // Show notification about the blocking event
    this.showBlockingNotification(data);
  }

  /**
   * Handle user info events
   * @param {Object} data - UserCreatedResponseDto object
   */
  async handleUserInfo(data) {
    if (!data) return;

    //console.log("Processing user info event:", data);

    // Update user info
    if (this.chatProcessor) {
      await this.chatProcessor.updateUserInfo(data);
    }

    // Update UI with new user info
    this.updateUserInfoUI(data);
  }

  /**
   * Show notification for group events
   * @param {Object} event - ChatGroupEventDto object
   */
  showGroupEventNotification(event) {
    let message = "";

    switch (event.eventType) {
      case "Join":
        message = `${event.targetUser.userName} انضم إلى المجموعة`;
        break;
      case "Leave":
        message = `${event.targetUser.userName} غادر المجموعة`;
        break;
      case "ChangeRoleMember":
        message = `تم تغيير دور ${event.targetUser.userName} في المجموعة`;
        break;
      default:
        message = "حدث تغيير في المجموعة";
    }

    this.showNotification(message);
  }

  /**
   * Show notification for new messages
   * @param {string} chatId - Chat ID
   * @param {Array} messages - List of messages
   */
  showNewMessageNotification(chatId, messages) {
    // Get chat info
    const chat = this.chatProcessor.getChatById(parseInt(chatId));
    if (!chat) return;

    // Get the latest message
    const latestMessage = messages[messages.length - 1];
    //console.log("latestMessage", latestMessage);

    // Create notification message
    let messageText = "";
    if (
      latestMessage.messageType === "0" ||
      latestMessage.messageType === "Text"
    ) {
      // Text message
      messageText = latestMessage.messageText;
    } else if (
      latestMessage.messageType === "1" ||
      latestMessage.messageType === "Image"
    ) {
      // Image
      messageText = "صورة";
    } else if (
      latestMessage.messageType === "2" ||
      latestMessage.messageType === "Video"
    ) {
      // Video
      messageText = "فيديو";
    } else if (
      latestMessage.messageType === "3" ||
      latestMessage.messageType === "Audio"
    ) {
      // Audio
      messageText = "رسالة صوتية";
    } else if (
      latestMessage.messageType === "4" ||
      latestMessage.messageType === "File"
    ) {
      // File
      messageText = "ملف";
    } else if (
      latestMessage.messageType === "5" ||
      latestMessage.messageType === "Location"
    ) {
      // Location
      messageText = "موقع";
    } else if (
      latestMessage.messageType === "6" ||
      latestMessage.messageType === "Contact"
    ) {
      // Contact
      messageText = "جهة اتصال";
    } else if (
      latestMessage.messageType === "7" ||
      latestMessage.messageType === "Poll"
    ) {
      // Poll
      messageText = "استطلاع";
    }

    //console.log("showNewMessageNotification messageText", messageText);
    // Show notification
    this.showNotification(`${chat.name}: ${messageText}`);

    // Play notification sound
    this.playMessageNotificationSound();
  }

  /**
   * Show notification for blocking events
   * @param {Object} data - BlockedUserResponseDto object
   */
  showBlockingNotification(data) {
    let message = "";

    if (data.blockedStatus) {
      message = `تم حظر ${data.blockedUser.userName}`;
    } else {
      message = `تم إلغاء حظر ${data.blockedUser.userName}`;
    }

    this.showNotification(message);
  }

  /**
   * Show a notification
   * @param {string} message - Notification message
   */
  showNotification(message) {
    // Check if browser supports notifications
    if (!("Notification" in window)) {
      //console.log("This browser does not support desktop notifications");
      return;
    }

    // Check if permission is already granted
    if (Notification.permission === "granted") {
      this.createNotification(message);
    }
    // Otherwise, ask for permission
    else if (Notification.permission !== "denied") {
      Notification.requestPermission().then((permission) => {
        if (permission === "granted") {
          this.createNotification(message);
        }
      });
    }
  }

  /**
   * Create a notification
   * @param {string} message - Notification message
   */
  createNotification(message) {
    const notification = new Notification("Chat App ", {
      body: message,
      //vibrate: [200, 100, 200],
      //tag: "custom-notification",
      icon: "/assets/images/logoD.png",
    });

    // Close notification after 5 seconds
    setTimeout(() => {
      notification.close();
    }, 50000);
  }

  //displayNoif() {
  //    // Check if the browser supports notifications
  //    // Check if the browser supports notifications
  //    if ("Notification" in window) {
  //        Notification.requestPermission().then((permission) => {
  //            if (permission === "granted") {
  //                const options = {
  //                    body: "Here is the body of your custom notification message.",
  //                    icon: "https://example.com/path/to/your-icon.png",
  //                    vibrate: [200, 100, 200],
  //                    tag: "custom-notification"
  //                    // Remove actions property to avoid error:
  //                    // actions: [ { action: "open", title: "Open App", icon: "https://example.com/path/to/open-icon.png" } ]
  //                };

  //                const notification = new Notification("Custom Notification Title", options);

  //                // Event handlers...
  //                notification.onclick = (event) => {
  //                    event.preventDefault();
  //                    window.open("https://example.com", "_blank");
  //                };
  //                notification.onshow = () =>//console.log("Notification is shown");
  //                notification.onclose = () =>//console.log("Notification is closed");
  //                notification.onerror = (event) => console.error("Notification error:", event);
  //            } else {
  //                console.error("Notification permission not granted.");
  //            }
  //        });
  //    } else {
  //        console.error("This browser does not support notifications.");
  //    }

  //    //if ("Notification" in window) {
  //    //    // Request permission to show notifications
  //    //    Notification.requestPermission().then((permission) => {
  //    //        if (permission === "granted") {
  //    //            // Options for the notification
  //    //            const options = {
  //    //                body: "Here is the body of your custom notification message.",
  //    //                icon: "https://example.com/path/to/your-icon.png",
  //    //                // Optional: vibration pattern (mostly for mobile devices)
  //    //                vibrate: [200, 100, 200],
  //    //                // A unique tag so multiple notifications can be managed (they will replace each other if they share the same tag)
  //    //                tag: "custom-notification",
  //    //                // Define possible actions (supported in some browsers and platforms)
  //    //                actions: [
  //    //                    { action: "open", title: "Open App", icon: "https://example.com/path/to/open-icon.png" },
  //    //                    { action: "dismiss", title: "Dismiss", icon: "https://example.com/path/to/dismiss-icon.png" }
  //    //                ]
  //    //            };

  //    //            // Create the notification with a custom title and the specified options
  //    //            const notification = new Notification("Custom Notification Title", options);

  //    //            // Attach event handlers

  //    //            // When the notification is clicked:
  //    //            notification.onclick = function (event) {
  //    //                event.preventDefault(); // Prevent the default action (e.g., focusing the notification's tab)
  //    //               //console.log("Notification clicked");
  //    //                // Perform any custom action (for example, open your app or navigate to a particular URL)
  //    //                window.open("https://example.com", "_blank");
  //    //            };

  //    //            // When the notification is displayed:
  //    //            notification.onshow = function (event) {
  //    //               //console.log("Notification is shown");
  //    //            };

  //    //            // When the notification is closed:
  //    //            notification.onclose = function (event) {
  //    //               //console.log("Notification is closed");
  //    //            };

  //    //            // If an error occurs:
  //    //            notification.onerror = function (event) {
  //    //                console.error("Notification error:", event);
  //    //            };
  //    //        } else {
  //    //            console.error("Notification permission not granted.");
  //    //        }
  //    //    });
  //    //} else {
  //    //    console.error("This browser does not support notifications.");
  //    //}

  //}
  /**
   * Play notification sound
   */
  playMessageNotificationSound() {
    // Create audio element
    const audio = new Audio("/assets/sounds/notification.mp3");

    // Play sound
    audio.play().catch((error) => {
      console.error("Error playing notification sound:", error);
    });
  }

  /**
   * Update user info UI
   * @param {Object} userInfo - UserCreatedResponseDto object
   */
  updateUserInfoUI(userInfo) {
    // Update profile picture
    const profilePicture = document.querySelector(".userImg .cover");
    if (profilePicture && userInfo.picture) {
      profilePicture.src = userInfo.picture;
    }

    // Update user name
    const userName = document.querySelector(".user-name");
    if (userName && userInfo.userName) {
      userName.textContent = userInfo.userName;
    }

    // Update user about
    const userAbout = document.querySelector(".user-about");
    if (userAbout && userInfo.about) {
      userAbout.textContent = userInfo.about;
    }

    // Check if user is blocked
    if (userInfo.status === false) {
      // Show blocked user message
      this.showNotification("تم إيقاف حسابك من قبل المسؤول");

      // Redirect to login page after 3 seconds
      setTimeout(() => {
        window.location.href = "/login.html";
      }, 3000);
    }
  }
}

// Export the RealTimeEventHandler class
//export default RealTimeEventHandler;
