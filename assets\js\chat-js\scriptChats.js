/* دوال مساعدة للوصول إلى عناصر DOM */
let getById = (id, parent) =>
  parent ? parent.getElementById(id) : getById(id, document);
let getByClass = (className, parent) =>
  parent
    ? parent.getElementsByClassName(className)
    : getByClass(className, document);

/* تعريف عناصر DOM الرئيسية */
const DOM = {
  chatListArea: getById("leftSid"),
  messageArea: getById("message-area"),
  inputArea: getById("input-area"),
  chatList: getById("chat-list"),
  rightSide: getById("rightSide"),
  IntroLeft: getById("Intro-Left"),
  messages: getById("messages"),
  chatListItem: getByClass("chat-list-item"),
  messageAreaName: getById("name", this.messageArea),
  messageAreaPic: getById("pic", this.messageArea),
  messageAreaNavbar: getById("navbar", this.messageArea),
  messageAreaDetails: getById("details", this.messageAreaNavbar),
  messageAreaOverlay: getByClass("overlay", this.messageArea)[0],
  messageInput: getById("input-send-message"),
};

/* دالة مساعدة للتعامل مع classes في DOM */
let mClassList = (element) => {
  return {
    add: (className) => {
      element.classList.add(className);
      return mClassList(element);
    },
    remove: (className) => {
      element.classList.remove(className);
      return mClassList(element);
    },
    contains: (className, callback) => {
      if (element.classList.contains(className)) callback(mClassList(element));
    },
  };
};

//////console.log(DOM);

/* متغيرات عامة للتطبيق */
let areaSwapped = false;
let chat = null;
let contact = null;
let chatList = [];
let showUnread = false;
let hasAddedUnreadTag = false;
let lastDate = "";
let currentDate = "";
let chatAbout = null;
let chatProfile = null;
let chatListManager = null;
let realTimeEventHandler = null;
let chatListItemManager = null;
let signalRManager = null;
let userToken = null; // Th
let contactListManager = null; // Th
let userListManager = null; // Th
let teamListManager = null; // Th

/* تهيئة التطبيق */
async function initializeApp() {
  try {
    // تهيئة قاعدة البيانات
    // await DBManager.initializeDB();

    //const currentUser = await ChatProcessor.getCurrentUser();

    // تهيئة معالج المحادثات
    await ChatProcessor.initializeChatData();

    // تهيئة مدير قائمة المحادثات
    chatListManager = new ChatListManager();

    // عرض قائمة المحادثات
    await generateChatList();

    // عرض معلومات المستخدم الحالي
    await displayCurrentUserInfo();

    //contactListManager = new ContactListManager();
    //await contactListManager.initialize();

    userListManager = new UserListManager();
    await userListManager.initialize();
    teamListManager = new TeamListManager();
    await teamListManager.initialize();
    ////// Initialize SignalR manager
    signalRManager = new SignalRManager();

    userToken = ChatProcessor.processedData.currentUser.tokenInfo.token;

    //Initialize SignalR connection
    await signalRManager.initialize(
      "http://*************:3000/chatproject",
      userToken
    );
    //await signalRManager.initialize('http://localhost:5236', userToken);

    // //Initialize real-time event handler
    realTimeEventHandler = new RealTimeEventHandler(
      signalRManager,
      chatListManager,
      ChatProcessor
    );

    // Initialize the chat list item manager
    //chatListItemManager = new ChatListItemManager();

    // //Initialize chat items with the current chat list
    //chatListItemManager.initializeChatItems(chatListManager.getChats());

    // Initialize Quick Replies Manager
    if (window.quickRepliesManager) {
      await window.quickRepliesManager.initialize();
      console.log("Quick Replies Manager initialized");

      // Initialize sample data if needed
      if (window.initializeSampleQuickReplies) {
        await window.initializeSampleQuickReplies();
      }
    }
  } catch (error) {
    console.error("Error initializing app:", error);
    //showError('Failed to initialize the application. Please try again.');
  }
}

/* دالة لإنشاء قائمة المحادثات وعرضها في الواجهة */
async function generateChatList() {
  try {
    await chatListManager.initializeChatList();
  } catch (error) {
    console.error("Error viewing chat list:", error);
  }
}

/* دالة لفتح منطقة المحادثة */
async function generateMessageArea(elem, chatID) {
  try {
    DOM.messageInput.focus();
    console.log("generateMessageArea chatID", DOM.messageInput.focus());
    // Remove any existing scroll handler before loading new chat
    console.log("DOM.messages._scrollHandler", DOM.messages._scrollHandler);
    if (DOM.messages._scrollHandler) {
      DOM.messages.removeEventListener("scroll", DOM.messages._scrollHandler);
    }
    if (contact) {
      contact = null;
    }
    if (chatAbout) {
      chatAbout.close();
    }
    closeChatProfile();

    // جلب بيانات المحادثة من قاعدة البيانات
    chat = await ChatProcessor.getChatByLocId(chatID);
    //////console.log("chatsssssssssssss", chat);
    // تحديث واجهة المستخدم
    mClassList(DOM.inputArea).contains("d-none", (elem) =>
      elem.remove("d-none").add("d-flex")
    );
    mClassList(DOM.rightSide).add("d-flex");
    mClassList(DOM.IntroLeft).add("d-none");
    // تحديث المحادثة في قائمة المحادثات

    // تحديث العرض في الأجهزة المحمولة
    if (window.innerWidth <= 575) {
      mClassList(DOM.chatListArea).remove("d-flex").add("d-none");
      mClassList(DOM.messageArea).remove("d-none").add("d-flex");
      areaSwapped = true;
    } else {
    }

    // تحديث معلومات المحادثة
    DOM.messageAreaName.innerHTML = chat.name;
    DOM.messageAreaPic.src = chat.picture || "/assets/avatars/imageGrey.jpg";

    // تحديث تفاصيل المحادثة
    if (chat.type === "Group") {
      let memberNames = chat.members
        .slice(0, 5)
        .map((contact) => (contact.id === 1 ? "أنت" : contact.user.userName))
        .join(", ");
      DOM.messageAreaDetails.innerHTML = `${memberNames}`;
    } else {
      let userState = chat.members?.[0].user.statusConnection;
      //////console.log("user.statusConnection", userState)
      if (userState && userState.statusConnection) {
        DOM.messageAreaDetails.innerHTML = "متصل";
      } else if (userState && userState.lastSeenDatetime) {
        DOM.messageAreaDetails.innerHTML = `اخر ظهور ${mDate(
          chat.members?.[0].user.statusConnection.lastSeenDatetime
        ).lastSeenFormat()}`;
      } else {
        DOM.messageAreaDetails.innerHTML = "";
      }
    }

    // Reset chat state
    chat.currentPage = 1;
    chat.hasMore = true;

    //عرض الرسائل مع دعم الصفحات
    await loadMessages(chat.id, 1);

    // إضافة مستمع التمرير للتحميل التلقائي
    setupInfiniteScroll(chat.id);
    // تحديث حالة الرسائل غير المقروءة
    if (elem.classList.contains("unread")) {
      const activeElement = DOM.chatList.querySelector(
        `[data-chat-id="${chat.id}"]`
      );
      // await ChatProcessor.updateChatReadStatus(chat.id);
      mClassList(activeElement).remove("unread");
      mClassList(activeElement.querySelector("#unread-count")).add("d-none");
    }
    //debugger;
  } catch (error) {
    console.error("Error generating message area:", error);
  }
}

/* دالة لفتح منطقة المحادثة للجهات الاتصال*/
async function generateMessageAreaForContact(elem, contactID) {
  try {
    // Remove any existing scroll handler before loading new chat
    if (DOM.messages._scrollHandler) {
      DOM.messages.removeEventListener("scroll", DOM.messages._scrollHandler);
    }
    if (chat) {
      chat = null;
    }
    if (chatAbout) {
      chatAbout.close();
    }
    closeChatProfile();
    // جلب بيانات المحادثة من قاعدة البيانات
    contact = await userListManager.userProcessor.getUserById(contactID);
    const existingChat = await ChatProcessor.getUserChats(contactID);

    if (existingChat?.[0]) {
      const chatElement = document.querySelector(
        `[data-chat-locid="${existingChat?.[0].locId}"]`
      );
      await this.generateMessageArea(elem, existingChat?.[0].locId);
      return;
    }
    // تحديث واجهة المستخدم
    mClassList(DOM.inputArea).contains("d-none", (elem) =>
      elem.remove("d-none").add("d-flex")
    );
    mClassList(DOM.rightSide).add("d-flex");
    mClassList(DOM.IntroLeft).add("d-none");
    // تحديث المحادثة في قائمة المحادثات

    // تحديث العرض في الأجهزة المحمولة
    if (window.innerWidth <= 575) {
      mClassList(DOM.chatListArea).remove("d-flex").add("d-none");
      mClassList(DOM.messageArea).remove("d-none").add("d-flex");
      areaSwapped = true;
    } else {
      mClassList(elem).add("active");
    }

    // تحديث معلومات المحادثة
    DOM.messageAreaName.innerHTML = contact.userName;
    DOM.messageAreaPic.src = contact.picture || "/assets/avatars/imageGrey.jpg";
    DOM.messageAreaDetails.innerHTML = "";
    DOM.messages.innerHTML = "";
  } catch (error) {
    console.error("Error generating message area:", error);
  }
}
/* دالة تحميل الرسائل */
// async function loadMessages(chatID, page) {
//   try {
//     // إظهار حالة التحميل
//     if (page === 1) {
//       DOM.messages.innerHTML = '<div class="loading">Loading messages...</div>';
//       showUnread = false; // Reset unread flag for new chat
//     } else {
//       const loadingDiv = document.createElement("div");
//       loadingDiv.className = "loading";
//       loadingDiv.textContent = "Loading more messages...";
//       DOM.messages.insertBefore(loadingDiv, DOM.messages.firstChild);
//     }
//     // جلب الرسائل
//     const result = await ChatProcessor.getChatMessages(chatID, page);

//     // إزالة مؤشر التحميل
//     const loadingElement = DOM.messages.querySelector(".loading");
//     if (loadingElement) {
//       loadingElement.remove();
//     }
//     // حفظ ارتفاع التمرير قبل إضافة الرسائل الجديدة
//     const oldScrollHeight = DOM.messages.scrollHeight;
//     const oldScrollTop = DOM.messages.scrollTop;

//     // إضافة الرسائل
//     if (page === 1) {
//       DOM.messages.innerHTML = "";
//       lastDate = "";
//       showUnread = false;
//     }

//     // إنشاء fragment للرسائل الجديدة
//     const fragment = document.createDocumentFragment();
//     lastDate = "";
//     // إضافة الرسائل إلى fragment
//     result.messages.forEach((msg, i) => {
//       // Check and add date separator if needed
//       const msgDate = mDate(msg.createdDate).getDate();

//       if (lastDate !== msgDate) {
//         const dateDiv = document.createElement("div");
//         dateDiv.className = "chat__date-wrapper";
//         dateDiv.innerHTML = `<span class="chat__date">${msgDate}</span>`;
//         fragment.appendChild(dateDiv);
//         lastDate = msgDate;
//       }
//       // Check and add unread messages tag if needed
//       if (
//         !showUnread &&
//         msg.messageStatus !== "Read" &&
//         msg.senderID !== ChatProcessor.processedData.currentUser?.id
//       ) {
//         const unreadDiv = document.createElement("div");
//         unreadDiv.className = "chat__unread-wrapper";
//         unreadDiv.innerHTML =
//           '<span class="chat__unread">الرسائل غير المقروءة</span>';
//         fragment.appendChild(unreadDiv);
//         showUnread = true;
//       }

//       // Add the message
//       const messageContainer = document.createElement("div");
//       messageContainer.innerHTML = generateMessageHTML(msg);
//       fragment.appendChild(messageContainer.firstElementChild); // We only want the containerMessage div
//     });

//     // إضافة الرسائل إلى DOM
//     if (page === 1) {
//       DOM.messages.appendChild(fragment);
//       // التمرير إلى الأسفل عند تحميل الصفحة الأولى
//       requestAnimationFrame(() => {
//         DOM.messages.scrollTo({
//           top: DOM.messages.scrollHeight,
//           behavior: "smooth",
//         });
//       });
//     } else {
//       // إضافة الرسائل في بداية القائمة
//       DOM.messages.insertBefore(fragment, DOM.messages.firstChild);
//       // الحفاظ على موضع التمرير
//       requestAnimationFrame(() => {
//         const newScrollHeight = DOM.messages.scrollHeight;
//         DOM.messages.scrollTop =
//           oldScrollTop + (newScrollHeight - oldScrollHeight);
//       });
//     }

//     // تحديث حالة التحميل
//     chat.currentPage = result.currentPage;
//     chat.hasMore = result.hasMore;
//   } catch (error) {
//     console.error("Error loading messages:", error);
//     const loadingElement = DOM.messages.querySelector(".loading");
//     if (loadingElement) {
//       loadingElement.remove();
//     }
//   }
// }
async function loadMessages(chatID, page) {
  try {
    if (page === 1) {
      DOM.messages.innerHTML = '<div class="loading">Loading messages...</div>';
      showUnread = false;
    } else {
      const loadingDiv = document.createElement("div");
      loadingDiv.className = "loading";
      loadingDiv.textContent = "Loading more messages...";
      DOM.messages.insertBefore(loadingDiv, DOM.messages.firstChild);
    }

    const result = await ChatProcessor.getChatMessages(chatID, page);
    const loadingEl = DOM.messages.querySelector(".loading");
    if (loadingEl) loadingEl.remove();

    // preserve scroll for pagination
    const oldScrollHeight = DOM.messages.scrollHeight;
    const oldScrollTop = DOM.messages.scrollTop;

    if (page === 1) {
      DOM.messages.innerHTML = "";
      lastDate = "";
      showUnread = false;
    }

    const fragment = document.createDocumentFragment();
    lastDate = "";

    // إضافة الرسائل إلى fragment
    result.messages.forEach((msg, i) => {
      // Check and add date separator if needed
      const msgDate = mDate(msg.createdDate).getDate();

      if (lastDate !== msgDate) {
        const dateDiv = document.createElement("div");
        dateDiv.className = "chat__date-wrapper";
        dateDiv.innerHTML = `<span class="chat__date">${msgDate}</span>`;
        fragment.appendChild(dateDiv);
        lastDate = msgDate;
      }
      // Check and add unread messages tag if needed
      if (
        !showUnread &&
        msg.messageStatus !== "Read" &&
        msg.senderID !== ChatProcessor.processedData.currentUser?.id
      ) {
        const unreadDiv = document.createElement("div");
        unreadDiv.className = "chat__unread-wrapper";
        unreadDiv.innerHTML =
          '<span class="chat__unread">الرسائل غير المقروءة</span>';
        fragment.appendChild(unreadDiv);
        showUnread = true;
      }

      // Add the message
      const messageContainer = document.createElement("div");
      messageContainer.innerHTML = generateMessageHTML(msg);
      fragment.appendChild(messageContainer.firstElementChild); // We only want the containerMessage div
    });

    if (page === 1) {
      DOM.messages.appendChild(fragment);
      requestAnimationFrame(() => {
        const unreadMarker = DOM.messages.querySelector(
          ".chat__unread-wrapper"
        );
        if (unreadMarker) {
          DOM.messages.scrollTo({
            top: unreadMarker.offsetTop,
            behavior: "smooth",
          });
        } else {
          DOM.messages.scrollTo({
            top: DOM.messages.scrollHeight,
            behavior: "smooth",
          });
        }
      });
    } else {
      DOM.messages.insertBefore(fragment, DOM.messages.firstChild);
      requestAnimationFrame(() => {
        const newScrollHeight = DOM.messages.scrollHeight;
        DOM.messages.scrollTop =
          oldScrollTop + (newScrollHeight - oldScrollHeight);
      });
    }

    chat.currentPage = result.currentPage;
    chat.hasMore = result.hasMore;
  } catch (error) {
    console.error("Error loading messages:", error);
    const loadingEl = DOM.messages.querySelector(".loading");
    if (loadingEl) loadingEl.remove();
  }
}
/* إعداد التمرير اللانهائي */
function setupInfiniteScroll(chatID) {
  let isLoading = false;
  const scrollThreshold = 80; // pixels from top to trigger load

  console.log("DOM.messages", DOM.messages);
  // Remove any existing scroll handler
  DOM.messages.removeEventListener("scroll", DOM.messages._scrollHandler);

  // Create a new scroll handler that captures the current chatID
  const scrollHandler = async () => {
    // Check if we're still in the same chat
    if (!chat || chat.id !== chatID) {
      return;
    }

    if (isLoading || !chat.hasMore) return;

    const scrollTop = DOM.messages.scrollTop;
    if (scrollTop <= scrollThreshold) {
      isLoading = true;
      try {
        await loadMessages(chatID, chat.currentPage + 1);
      } finally {
        isLoading = false;
      }
    }
  };

  // Store the handler reference on the messages element
  DOM.messages._scrollHandler = scrollHandler;

  // Add the new scroll handler
  DOM.messages.addEventListener("scroll", scrollHandler);
}
// Grab elements
const scrollBtn = document.getElementById("scrollDownBtn");

// 1) On scroll, check if we’re at the bottom or not:
function updateScrollButton() {
  // how far from the bottom?
  const distanceFromBottom =
    DOM.messages.scrollHeight -
    DOM.messages.scrollTop -
    DOM.messages.clientHeight;

  if (distanceFromBottom > 100) {
    // scrolled up at least 20px: show button
    scrollBtn.style.display = "flex";
  } else {
    // near bottom: hide it
    scrollBtn.style.display = "none";
  }
}

// throttle/debounce if you want, but for simplicity:
DOM.messages.addEventListener("scroll", updateScrollButton);

// 2) On click, scroll to bottom:
scrollBtn.addEventListener("click", () => {
  DOM.messages.scrollTo({
    top: DOM.messages.scrollHeight,
    behavior: "smooth",
  });
});

// 3) Also call once on load in case messages start mid‑stream:
updateScrollButton();

/* دالة لإنشاء HTML للرسالة */
/* دالة لإنشاء HTML للرسالة */
function generateMessageHTML(msg) {
  return MessageGenerator.generateMessageHTML(msg, chat);
}

/* دالة لإرسال رسالة جديدة */
async function sendMessage() {
  try {
    // Get message text and check if it's empty
    let value = DOM.messageInput.value.trim();
    const replyMessageId = DOM.messageInput.getAttribute("reply-message-id");

    if (value === "") return;

    // Clear input field
    DOM.messageInput.value = "";

    // Check if we're in a new chat context (no chat.id)
    if (!chat || !chat.id) {
      // Create new chat
      try {
        const chatLocal = await ChatProcessor.createNewIndividualChat(contact);
        if (!chatLocal) {
          showNotification("Failed to create new chat", "error");
          return;
        }

        let msg = {
          isNewChat: true,
          chatID: Number(contact.phoneNumber),
          senderID: ChatProcessor.processedData.currentUser?.id,
          messageType: "0",
          source: 1,
          messageText: value,
          deviceInfo: "string",
          messageStatus: "Pending",
        };

        if (chatListManager) {
          //console.log("chatListManager chatLocal", chatLocal);
          await chatListManager.addNewChat(chatLocal);
        }

        // Load the new chat
        const chatElement = document.querySelector(
          `[data-chat-locid="${chatLocal.locId}"]`
        );

        if (chatElement) {
          await generateMessageArea(chatElement, chatLocal.locId);
        }

        //First, save message locally with "Pending" status
        const message = await saveSendingMessage(msg);
        //console.log("Chat Old Id :", message.chatID);
        chat = await ChatProcessor.updateChatId(msg.chatID, message.chatID);
        //console.log("Message saved locally New message:", message);

        await generateChatList();
        //}
      } catch (error) {
        console.error("Error creating new chat:", error);
        showNotification("Failed to create new chat", "error");
        return;
      }
    } else {
      let msg = {
        chatID: chat.id,
        senderID: ChatProcessor.processedData.currentUser?.id,
        messageType: 0,
        source: 1,
        messageText: value,
        deviceInfo: "string",
        messageStatus: "Pending",
      };

      //debugger
      if (replyMessageId) {
        msg["replyToMessageID"] = replyMessageId;
      }

      // //////console.log("Preparing to send message:", msg);
      await saveSendingMessage(msg);
      await generateChatList();
    }
    //console.log("sendMessage chat", chat.id);
    // //////console.log("sendMessage contact", contact);
  } catch (error) {
    console.error("Error sending message:", error);
  }
}
async function saveSendingMessage(msg) {
  // First, save message locally with "Pending" status
  const localMessage = await ChatProcessor.addMessage(msg);

  console.log("Message saved locally:", localMessage);

  // Add message to UI with pending status
  addMessageToMessageArea(localMessage);
  removeQuotedMessage();

  //localMessage.chatID = 204
  // Check if we're online
  if (!navigator.onLine) {
    // //////console.log("Device is offline, message will be sent when connection is restored");
    // Update UI to show pending status
    MessageGenerator.updateMessageStatus(localMessage.locId, "Pending");
    alert(
      "فقدت الاتصال بالإنترنت. الرجاء التحقق من إعدادات الشبكة والمحاولة مرة أخرى."
    );

    // Update chat list to show latest message
    await generateChatList();
    return;
  }
  //Send message to server
  try {
    const ajaxManager = new AjaxManager();
    const messageData = await ajaxManager.post(
      "api/v1/Message/Create/Text",
      msg
    );

    if (messageData.resCode === 201) {
      //Update local message with server data
      const updatedMessage = await ChatProcessor.updateMessage(
        localMessage.locId,
        {
          id: messageData.resObject.id,
          messageStatus: "Sent",
          createdDate: messageData.resObject.createdDate,
        }
      );
      if (updatedMessage.chatID !== messageData.resObject.chatID) {
        updatedMessage.chatID = messageData.resObject.chatID;
      }
      //Update UI to show sent status
      MessageGenerator.updateMessageStatus(localMessage.locId, "Sent");
      return updatedMessage;
    } else {
      // Server returned an error
      console.error("Server error:", messageData);
      MessageGenerator.updateMessageStatus(localMessage.locId, "Failed");
      showMessageError(
        localMessage.id,
        "Failed to send message. Tap to retry."
      );
    }
  } catch (error) {
    console.error("Error sending message to server:", error);
    MessageGenerator.updateMessageStatus(localMessage.locId, "Failed");
    showMessageError(
      localMessage.locId,
      "Failed to send message. Tap to retry."
    );
  }
  //console.log("Message updated localMessage:", localMessage);

  return localMessage;
}

/* دالة لعرض رسالة خطأ على الرسالة */
function showMessageError(messageId, errorText) {
  const messageElement = document.querySelector(
    `[data-message-id="${messageId}"]`
  );
  if (!messageElement) return;

  // Add error class
  messageElement.classList.add("message-error");

  // Add error message
  const errorElement = document.createElement("div");
  errorElement.className = "message-error-text";
  errorElement.textContent = errorText;

  // Add retry button
  const retryButton = document.createElement("button");
  retryButton.className = "message-retry-button";
  retryButton.textContent = "Retry";
  retryButton.onclick = (e) => {
    e.stopPropagation();
    retryMessage(messageId);
  };

  errorElement.appendChild(retryButton);
  messageElement.appendChild(errorElement);

  // Add click handler to retry
  messageElement.addEventListener("click", () => retryMessage(messageId));
}
/* دالة لإعادة محاولة إرسال رسالة فاشلة */
async function retryMessage(messageId) {
  try {
    // Get the failed message
    const message = await ChatProcessor.getMessageById(messageId);
    if (!message) {
      console.error("Message not found:", messageId);
      return;
    }

    // Update status to pending
    MessageGenerator.updateMessageStatus(messageId, "Pending");

    // Remove error message
    const messageElement = document.querySelector(
      `[data-message-id="${messageId}"]`
    );
    if (messageElement) {
      const errorElement = messageElement.querySelector(".message-error-text");
      if (errorElement) {
        errorElement.remove();
      }
      messageElement.classList.remove("message-error");
    }

    // Try to send again
    const ajaxManager = new AjaxManager();
    const messageData = await ajaxManager.post("Message/CreateTextMessage", {
      chatID: message.chatID,
      senderID: message.senderID,
      messageType: message.messageType,
      source: message.source,
      messageText: message.messageText,
      deviceInfo: message.deviceInfo,
      replyToMessageID: message.replyToMessageID,
    });

    if (messageData.resCode === 201) {
      // Update local message with server data
      const updatedMessage = await ChatProcessor.updateMessage(
        messageId,
        messageData.resObject
        //    {
        //    id: messageData.data.id,
        //    messageStatus: "Sent",
        //    serverTimestamp: messageData.data.createdDate
        //}
      );

      // Update UI
      MessageGenerator.updateMessageStatus(updatedMessage.id, "Sent");

      // Update chat list
      await generateChatList();

      // Notify other users
      if (
        signalRManager &&
        typeof signalRManager.notifyNewMessage === "function"
      ) {
        signalRManager.notifyNewMessage(updatedMessage);
      }
    } else {
      // Server returned an error
      console.error("Server error on retry:", messageData);
      MessageGenerator.updateMessageStatus(messageId, "Failed");
      showMessageError(messageId, "Failed to send message. Tap to retry.");
    }
  } catch (error) {
    console.error("Error retrying message:", error);
    MessageGenerator.updateMessageStatus(messageId, "Failed");
    showMessageError(messageId, "Failed to send message. Tap to retry.");
  }
}
async function showReply(id, senderName, message) {
  var replyDiv = document.getElementById("reply-div");
  // //////console.log("replyDiv", replyDiv);
  DOM.messageInput.setAttribute("reply-message-id", id);
  // Display the reply div
  replyDiv.style.display = "block";
  const msg = await ChatProcessor.getMessageById(id);
  // //////console.log("msg", msg)
  // الحصول على العنصر الذي يعرض اسم المرسل
  var senderNameElement = replyDiv.querySelector(".sender-name");
  // الحصول على العنصر الذي يعرض نص الرسالة المُقتبسة
  var quotedTextElement = replyDiv.querySelector(".quoted-text");

  // تحديث محتوى العناصر بالنصوص الممررة
  const name = msg.sender?.userName === "You" ? "أنت" : msg.sender?.userName;

  senderNameElement.textContent = name;
  quotedTextElement.textContent = msg.messageText;
}
function removeQuotedMessage() {
  var replyDiv = document.getElementById("reply-div");
  // Assuming DOM.messageInput references your element
  if (DOM.messageInput.hasAttribute("reply-message-id")) {
    DOM.messageInput.removeAttribute("reply-message-id");
  }
  // var iconContainer = document.querySelector(".icon-container");
  if (replyDiv.style.display === "block") {
    replyDiv.style.display = "none";
  }
  // Display the reply div

  // iconContainer.style.bottom = "90px";
}

/* إضافة مستمعات الأحداث */
document.addEventListener("DOMContentLoaded", () => {
  initializeApp();

  // مستمع لزر الإرسال
  document
    .getElementById("input-send-message")
    .addEventListener("keydown", function (event) {
      if (event.key === "Enter") {
        event.preventDefault();
        sendMessage();
      }
    });
});

async function addMessageToMessageArea(msg) {
  //console.log("addMessageToMessageArea msg", msg);
  if (chat && chat.id === msg.chatID) {
    //console.log("addMessageToMessageArea", chat);

    const activeElement = DOM.chatList.querySelector(
      `[data-chat-id="${chat.id}"]`
    );
    //const chatElement = document.querySelector(`[data-chat-id="${existingChat?.[0].id}"]`);

    // تحديث حالة الرسائل غير المقروءة
    if (activeElement.classList.contains("unread")) {
      //const activeElement = DOM.chatList.querySelector(`[data-chat-id="${chat.id}"]`)
      mClassList(activeElement).remove("unread");
      mClassList(activeElement.querySelector("#unread-count")).add("d-none");
    }
    if (
      chat &&
      msg.messageStatus !== "Read" &&
      msg.senderID !== ChatProcessor.processedData.currentUser?.id
    ) {
      await ChatProcessor.updateChatReadStatus(chat.id);
    }
    // Create a document fragment for better performance
    const fragment = document.createDocumentFragment();

    // Check and add date separator if needed
    const msgDate = mDate(msg.createdDate).getDate();
    // //////console.log("lastDate", lastDate);
    if (lastDate !== msgDate) {
      const dateDiv = document.createElement("div");
      dateDiv.className = "chat__date-wrapper";
      dateDiv.innerHTML = `<span class="chat__date">${msgDate}</span>`;
      fragment.appendChild(dateDiv);
      lastDate = msgDate;
    }

    // Check and add unread messages tag if needed
    if (
      !showUnread &&
      msg.messageStatus !== "Read" &&
      msg.senderID !== ChatProcessor.processedData.currentUser?.id
    ) {
      const unreadDiv = document.createElement("div");
      unreadDiv.className = "chat__unread-wrapper";
      unreadDiv.innerHTML =
        '<span class="chat__unread">الرسائل غير المقروءة</span>';
      fragment.appendChild(unreadDiv);
      showUnread = true;
    }

    // Create message container
    const messageContainer = document.createElement("div");
    messageContainer.innerHTML = generateMessageHTML(msg);
    fragment.appendChild(messageContainer.firstElementChild);

    // Batch DOM updates
    requestAnimationFrame(() => {
      DOM.messages.appendChild(fragment);
      DOM.messages.scrollTo(0, DOM.messages.scrollHeight);
    });
  }
}

/* دالة لعرض معلومات المستخدم الحالي في واجهة المستخدم */
async function displayCurrentUserInfo() {
  try {
    // الحصول على معلومات المستخدم الحالي
    const currentUser = await ChatProcessor.getCurrentUser();

    if (!currentUser) {
      console.warn("No current user found");
      return;
    }
    // تحديث صورة الملف الشخصي في جميع المواضع
    const profileImages = document.querySelectorAll(
      "#imageUserProfile img,#aboutUserProfile .imgBox img, .chats-profile .top .imgBox img"
    );
    profileImages.forEach((img) => {
      img.src = currentUser.picture || "/assets/avatars/imageGrey.jpg";
      img.alt = currentUser.userName || "User Profile";
    });

    // تحديث اسم المستخدم
    const userNameElements = document.querySelectorAll(
      "#aboutUserProfile .h-text .head h4, .chats-profile .block .head h4"
    );
    userNameElements.forEach((element) => {
      element.textContent = currentUser.userName || "اسم المستخدم";
    });

    // تحديث معلومات "عن"
    const aboutElements = document.querySelectorAll(
      "#aboutUserProfile .h-text .message p, .chats-profile .block .head.about1 h4"
    );
    aboutElements.forEach((element) => {
      element.textContent = currentUser.about || "";
    });

    // تحديث اسم مزيف
    const displayNameElement = document.querySelector(
      ".chats-profile .block:nth-child(2) .head h4"
    );
    if (displayNameElement) {
      displayNameElement.textContent =
        currentUser.displayName || currentUser.userName || "اسم مزيف";
    }
  } catch (error) {
    console.error("Error displaying current user info:", error);
  }
}

/**
 * فتح ملف تعريف الدردشة
 * @param {string} type - نوع الملف التعريفي ('chat' أو 'contact')
 */
function openChatProfile() {
  chatProfile = new ChatProfile();
  if (chatProfile) {
    if (chat) {
      chatProfile.open(chat, "chat");
    } else if (contact) {
      chatProfile.open(contact, "contact");
    }
  }
}

/* Global function to hide quick replies dropdown */
function hideQuickRepliesDropdown() {
  if (window.quickRepliesManager) {
    window.quickRepliesManager.hideInlineDropdown();
  }
}

/* Global function to show quick replies management */
function showQuickRepliesManagement() {
  if (window.quickRepliesManager) {
    window.quickRepliesManager.showManagementModal();
    window.quickRepliesManager.hideInlineDropdown();
  }
}

/**
 * إغلاق ملف تعريف الدردشة
 */
function closeChatProfile() {
  if (chatProfile) {
    chatProfile.close();
  }
}
