/**
 * خدمة إدارة المجموعات - تتعامل مع طلبات API وتخزين البيانات
 */
class GroupService {
  constructor() {
    this.apiBaseUrl = "/api/v1"; // عنوان API الأساسي
    this.ajaxManagers = new AjaxManager();
  }

  //setSocket(socket) {
  //    this.socket = socket;
  //    // يمكنك هنا بدء الاستماع لأحداث SignalR المتعلقة بالمجموعات إذا لزم الأمر
  //}

  /**
   * تحويل الصورة إلى Base64
   * @param {string} imageUrl - رابط الصورة أو بيانات Base64 الموجودة
   * @returns {Promise<string|null>} - بيانات Base64 أو null
   */
  async convertImageToBase64(imageUrl) {
    if (!imageUrl || !imageUrl.startsWith("data:image")) {
      // إذا لم تكن بيانات base64 بالفعل، حاول جلبها وتحويلها
      // هذا الجزء يعتمد على كيفية تخزين الصور، قد تحتاج لتعديله
      // في هذا المثال، نفترض أننا نتعامل فقط مع بيانات base64 من input file
      return imageUrl; // أعد القيمة كما هي إذا لم تكن بحاجة للتحويل
    }
    return imageUrl;
  }

  async createGroup(groupData) {
    // تحويل الصورة إلى Base64 قبل الإرسال
    //groupData.ChatPicture = await this.convertImageToBase64(groupData.ChatPicture);

    if (!navigator.onLine) {
      return this.createGroupOffline(groupData);
    }

    try {
      const responseData = await this.ajaxManagers.post(
        `Team/CreateTeam`,
        groupData
      );
      //////console.log("responseData group", responseData)

      if (responseData.resCode === 201) {
        console.log("responseData group", responseData);
      }

      if (!responseData.resCode === 201) {
        const errorData = await responseData.text(); // اقرأ الخطأ كنص أولاً
        console.error("API Error Response:", errorData);
        throw new Error(
          `فشل إنشاء المجموعة: ${response.status} ${response.statusText}`
        );
      }
      return responseData; // إرجاع الاستجابة من الخادم
    } catch (error) {
      console.error("Error creating group via API:", error);
      throw error; // أو إعادة رمي الخطأ للتعامل معه في الوجهة
    }
  }

  async createGroupOffline(groupData) {
    try {
      //console.log("Creating group offline:", groupData);
      const tempId = `temp_group_${Date.now()}`;
      const currentUserId = 1; // --- استبدل بمعرف المستخدم الحالي ---

      const offlineGroup = {
        // بنية مشابهة لـ ChatResponseDto قدر الإمكان
        id: tempId, // استخدام المعرف المؤقت كمعرف أساسي مؤقتًا
        locId: tempId, // معرف محلي مميز
        chatName: groupData.ChatName,
        chatDescription: groupData.ChatDescription,
        chatPicture: groupData.ChatPicture, // يجب أن تكون Base64 بالفعل هنا
        chatStatus: groupData.ChatStatus,
        companyID: groupData.CompanyID,
        chatType: 1, // نوع المجموعة
        createdDate: new Date().toISOString(),
        lastMessageDate: new Date().toISOString(),
        unreadCount: 0,
        isSync: false, // مهم: يشير إلى أنه لم تتم المزامنة
        members: groupData.GroupMembers.map((m) => ({
          userId: m.UserID,
          userRoleInChat: m.UserRoleInChat,
          user: {
            // قد تحتاج لجلب بيانات المستخدم الأساسية إذا لزم الأمر
            id: m.UserID,
            userName: `User ${m.UserID}`, // اسم مؤقت
            picture: "/assets/avatars/imageGrey.jpg", // صورة افتراضية
          },
        })),

      };

      // إضافة المستخدم الحالي كمشرف إذا لم يكن موجودًا
      if (!offlineGroup.members.some((m) => m.userId === currentUserId)) {
        offlineGroup.members.push({
          userId: currentUserId,
          userRoleInChat: 1, // UserTypeInChat.Admin
          user: {
            id: currentUserId,
            userName: "أنت",
            picture: "path/to/your/avatar.png",
          }, // استبدل بالبيانات الفعلية
        });
      }
      return offlineGroup; // إرجاع المجموعة المحفوظة محليًا
    } catch (error) {
      console.error("Error creating offline group:", error);
      throw error;
    }
  }



  // --- دوال مساعدة (يمكن إضافتها حسب الحاجة) ---
  async refreshChatList() {
    // هذه الدالة يجب أن تقوم بتحديث قائمة المحادثات في الواجهة
    // يمكن استدعاؤها بعد إنشاء مجموعة بنجاح أو بعد المزامنة
    //console.log("Refreshing chat list...");
    // مثال: استدعاء دالة في chatListManager أو إرسال حدث مخصص
    document.dispatchEvent(new CustomEvent("chatListUpdated"));
  }
}
