/**
 * خدمة إدارة الردود السريعة - تتعامل مع طلبات API وتخزين البيانات
 */
class QuickRepliesService {
  constructor() {
    this.apiBaseUrl = "/api/v1"; // عنوان API الأساسي
    this.ajaxManager = new AjaxManager();
    this.quickReplies = [];

    // Message type mappings
    this.messageTypes = {
      0: { name: "نص", icon: "message-circle", color: "#25d366" },
      1: { name: "صورة", icon: "image", color: "#ac44cf" },
      2: { name: "فيديو", icon: "video", color: "#d3396d" },
      3: { name: "صوت", icon: "headphones", color: "#ff9800" },
      4: { name: "مستند", icon: "file-text", color: "#5157ae" },
      5: { name: "جهة اتصال", icon: "user", color: "#00bcd4" },
      6: { name: "موقع", icon: "map-pin", color: "#4caf50" },
      7: { name: "استطلاع", icon: "bar-chart", color: "#9c27b0" },
      8: { name: "تسجيل صوتي", icon: "mic", color: "#ff5722" },
      9: { name: "ملفات أخرى", icon: "paperclip", color: "#607d8b" },
    };
  }

  /**
   * تحميل الردود السريعة من قاعدة البيانات المحلية
   */
  async loadQuickReplies() {
    try {
      // محاولة التحميل من IndexedDB أولاً
      const localReplies = await DBManager.getQuickReplies();

      if (localReplies && localReplies.length > 0) {
        this.quickReplies = localReplies;
      } else {
        // إذا لم توجد بيانات محلية، جلب من الخادم
        await this.syncWithServer();
      }

      console.log("تم تحميل الردود السريعة:", this.quickReplies.length);
      return this.quickReplies;
    } catch (error) {
      console.error("خطأ في تحميل الردود السريعة:", error);
      this.quickReplies = [];
      return [];
    }
  }

  /**
   * مزامنة مع الخادم
   */
  async syncWithServer() {
    try {
      const response = await this.ajaxManager.get("QuickReplies");

      if (response && response.resCode === 200 && response.resObject) {
        this.quickReplies = response.resObject;
        // حفظ في قاعدة البيانات المحلية
        await DBManager.saveQuickReplies(this.quickReplies);
      }
    } catch (error) {
      console.error("خطأ في المزامنة مع الخادم:", error);
      // الاستمرار مع البيانات المحلية إذا فشلت المزامنة
    }
  }

  /**
   * إنشاء رد سريع جديد
   */
  async createQuickReply(quickReplyData) {
    try {
      // إضافة معرف فريد ووقت الإنشاء
      const newQuickReply = {
        ...quickReplyData,
        id: this.generateId(),
        createdDate: new Date().toISOString(),
        updatedDate: new Date().toISOString(),
      };

      // حفظ في قاعدة البيانات المحلية
      await DBManager.saveQuickReply(newQuickReply);

      // إضافة للمصفوفة المحلية
      this.quickReplies.push(newQuickReply);

      // محاولة الحفظ في الخادم
      try {
        await this.ajaxManager.post("QuickReplies", newQuickReply);
      } catch (serverError) {
        console.warn(
          "فشل في حفظ الرد السريع في الخادم، تم الحفظ محلياً:",
          serverError
        );
      }

      return newQuickReply;
    } catch (error) {
      console.error("خطأ في إنشاء الرد السريع:", error);
      throw error;
    }
  }

  /**
   * تحديث رد سريع موجود
   */
  async updateQuickReply(id, updates) {
    try {
      const index = this.quickReplies.findIndex((reply) => reply.id === id);
      if (index === -1) {
        throw new Error(`الرد السريع بالمعرف ${id} غير موجود`);
      }

      // تحديث البيانات
      const updatedReply = {
        ...this.quickReplies[index],
        ...updates,
        updatedDate: new Date().toISOString(),
      };

      // تحديث في قاعدة البيانات المحلية
      await DBManager.saveQuickReply(updatedReply);

      // تحديث في المصفوفة المحلية
      this.quickReplies[index] = updatedReply;

      // محاولة التحديث في الخادم
      try {
        await this.ajaxManager.post(`QuickReplies/${id}`, updatedReply);
      } catch (serverError) {
        console.warn(
          "فشل في تحديث الرد السريع في الخادم، تم التحديث محلياً:",
          serverError
        );
      }

      return updatedReply;
    } catch (error) {
      console.error("خطأ في تحديث الرد السريع:", error);
      throw error;
    }
  }

  /**
   * حذف رد سريع
   */
  async deleteQuickReply(id) {
    try {
      // حذف من قاعدة البيانات المحلية
      await DBManager.deleteQuickReply(id);

      // حذف من المصفوفة المحلية
      this.quickReplies = this.quickReplies.filter((reply) => reply.id !== id);

      // محاولة الحذف من الخادم
      try {
        await this.ajaxManager.delete(`QuickReplies/${id}`);
      } catch (serverError) {
        console.warn(
          "فشل في حذف الرد السريع من الخادم، تم الحذف محلياً:",
          serverError
        );
      }

      return true;
    } catch (error) {
      console.error("خطأ في حذف الرد السريع:", error);
      throw error;
    }
  }

  /**
   * البحث في الردود السريعة
   */
  searchQuickReplies(searchTerm) {
    if (!searchTerm || searchTerm.trim() === "") {
      return this.quickReplies;
    }

    const term = searchTerm.toLowerCase().trim();
    return this.quickReplies.filter(
      (reply) =>
        reply.title.toLowerCase().includes(term) ||
        reply.content.toLowerCase().includes(term)
    );
  }

  /**
   * الحصول على الردود السريعة حسب نوع الرسالة
   */
  getQuickRepliesByType(messageType) {
    return this.quickReplies.filter(
      (reply) => reply.messageType === messageType
    );
  }

  /**
   * الحصول على رد سريع بالمعرف
   */
  getQuickReplyById(id) {
    return this.quickReplies.find((reply) => reply.id === id);
  }

  /**
   * الحصول على إحصائيات الردود السريعة
   */
  getQuickRepliesStats() {
    const stats = {
      total: this.quickReplies.length,
      byType: {},
    };

    this.quickReplies.forEach((reply) => {
      const type = reply.messageType;
      stats.byType[type] = (stats.byType[type] || 0) + 1;
    });

    return stats;
  }

  /**
   * الحصول على معلومات نوع الرسالة
   */
  getMessageTypeInfo(messageType) {
    return this.messageTypes[messageType] || this.messageTypes[0];
  }

  /**
   * تحويل الملف إلى Base64
   */
  async fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  }

  /**
   * تحويل Base64 إلى Blob
   */
  base64ToBlob(base64, mimeType) {
    const byteCharacters = atob(base64.split(",")[1]);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }

  /**
   * تنسيق حجم الملف
   */
  formatFileSize(bytes) {
    if (bytes === 0) return "0 بايت";
    const k = 1024;
    const sizes = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * توليد معرف فريد
   */
  generateId() {
    return (
      "qr_" + Date.now() + "_" + Math.random().toString(36).substring(2, 9)
    );
  }

  /**
   * الحصول على نص المعاينة للرد السريع
   */
  getPreviewText(reply) {
    if (reply.messageType === 0) {
      return reply.content.length > 50
        ? reply.content.substring(0, 50) + "..."
        : reply.content;
    }

    const typeInfo = this.getMessageTypeInfo(reply.messageType);
    const caption = reply.content
      ? ` - ${reply.content.substring(0, 30)}${
          reply.content.length > 30 ? "..." : ""
        }`
      : "";

    return `${typeInfo.name}${caption}`;
  }

  /**
   * الحصول على أيقونة نوع الرسالة
   */
  getMessageTypeIcon(messageType) {
    const typeInfo = this.getMessageTypeInfo(messageType);
    return `<i class="fe fe-${typeInfo.icon}"></i>`;
  }

  /**
   * الحصول على فئة CSS لنوع الرسالة
   */
  getMessageTypeClass(messageType) {
    const typeMap = {
      0: "text",
      1: "image",
      2: "video",
      3: "audio",
      4: "document",
      5: "contact",
      6: "location",
      7: "poll",
      8: "voice",
      9: "file",
    };
    return typeMap[messageType] || "text";
  }
}
