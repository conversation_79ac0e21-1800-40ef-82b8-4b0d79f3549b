/**
 * خدمة جلب جهات الاتصال
 */
class UserContactsService {
  constructor(dbManager, ajaxManager, stateManager) {
    this.dbManager = dbManager;
    this.ajaxManager = ajaxManager;
    this.stateManager = stateManager;
    // Dependencies are injected or accessed via imports
    this.contactsCache = null; // Simple in-memory cache
  }

  /**
   * Fetches contacts from the server or loads from DB cache.
   * @param {boolean} forceRefresh - If true, bypasses cache and fetches from server.
   * @param {string} [search=''] - Optional search term.
   * @param {number} [page=1] - Page number for server fetch.
   * @param {number} [pageSize=50] - Page size for server fetch.
   * @param {string} [sort='Desc'] - Sort order for server fetch.
   * @returns {Promise<object[]>} A promise resolving with an array of contact membership objects.
   */
  async getContacts(
    forceRefresh = false,
    search = "",
    page = 1,
    pageSize = 50,
    sort = "Desc"
  ) {
    //console.log(
    //   `Getting contacts (forceRefresh: ${forceRefresh}, search: ${search}, page: ${page})`
    // );

    // Use cache if available and not forcing refresh (and no search term)
    if (!forceRefresh && !search && this.contactsCache) {
      //console.log("Returning contacts from cache.");
      return this.contactsCache;
    }

    // Try loading from DB first if no cache and not forcing refresh
    if (!forceRefresh && !search) {
      try {
        const dbContacts = await this.dbManager.getAllData(
          "contactMemberships"
        );
        if (dbContacts && dbContacts.length > 0) {
          //console.log("Contacts loaded from DB.");
          this.contactsCache = dbContacts; // Update cache
          this.stateManager.set("contacts", dbContacts); // Update global state if needed
          return dbContacts;
        }
      } catch (error) {
        console.error("Error loading contacts from DB:", error);
        // Proceed to fetch from server
      }
    }

    // Fetch from server
    //console.log("Fetching contacts from server...");
    try {
      // Adjust endpoint and parameters based on your API
      const responseData = await this.ajaxManager.get(
        `Contact/GetAllContacts`,
        {
          search,
          page,
          pageSize,
          sort,
        }
      );

      if (
        responseData &&
        responseData.resCode === 200 &&
        responseData.resObject?.pageData
      ) {
        // Process and model the data
        const memberships = responseData.resObject.pageData.map(
          (item) => new ContactModels.ContactMembership(item) // Use model constructor
        );

        //console.log("Contacts fetched successfully from server:", memberships);

        // Save to IndexedDB (clear old data first if fetching all)
        if (page === 1 && !search) {
          // Only clear if fetching the first page without search
          await this.dbManager.clearStore("contactMemberships");
        }
        await this.dbManager.saveData("contactMemberships", memberships);

        // Update cache and state only if fetching all contacts without search
        if (!search) {
          if (page === 1) {
            this.contactsCache = memberships;
          } else if (this.contactsCache) {
            // Append if loading subsequent pages (less ideal for cache consistency)
            this.contactsCache = [...this.contactsCache, ...memberships];
          }
          this.stateManager.set("contacts", this.contactsCache || memberships); // Update global state
        }

        return memberships; // Return the newly fetched memberships
      } else {
        console.error(
          "Failed to fetch contacts or invalid response format:",
          responseData
        );
        throw new Error(responseData?.resMeg || "Failed to fetch contacts");
      }
    } catch (error) {
      console.error("Error fetching contacts from server:", error);
      // Optionally return cached or DB data as fallback if available
      if (this.contactsCache) return this.contactsCache;
      const dbData = await dbManager
        .getAllData("contactMemberships")
        .catch(() => []);
      return dbData; // Return empty array on fetch error if no cache/DB data
    }
  }

  /**
   * Gets a specific contact membership by its ID from cache or DB.
   * @param {number} membershipId - The ID of the contact membership record.
   * @returns {Promise<object|null>} The contact membership object or null.
   */
  async getContactMembershipById(membershipId) {
    // Try cache first
    if (this.contactsCache) {
      const cached = this.contactsCache.find((m) => m.id === membershipId);
      if (cached) return cached;
    }
    // Try DB
    return await this.dbManager.getDataByKey(
      "contactMemberships",
      membershipId
    );
  }

  /**
   * Gets a specific contact membership by the user ID it represents.
   * Note: A user might be part of multiple contact lists (memberships). This finds the first one.
   * @param {number} userId - The ID of the user.
   * @returns {Promise<object|null>} The contact membership object or null.
   */
  async getContactMembershipByUserId(userId) {
    // Try cache first
    if (this.contactsCache) {
      const cached = this.contactsCache.find((m) => m.userId === userId);
      if (cached) return cached;
    }
    // Try DB using index
    let result = null;
    await this.dbManager._runTransaction(
      "contactMemberships",
      "readonly",
      async (tx, stores) => {
        const index = stores.contactMemberships.index("userId");
        const request = index.get(userId); // Get the first match
        result = await this.dbManager._requestToPromise(request);
      }
    );
    return result;
  }

  /**
   * Searches contacts locally (from cache or DB).
   * @param {string} searchTerm - The term to search for (case-insensitive).
   * @returns {Promise<object[]>} An array of matching contact membership objects.
   */
  async searchContactsLocally(searchTerm) {
    if (!searchTerm || searchTerm.trim().length < 1) {
      return (
        this.contactsCache ||
        (await this.dbManager.getAllData("contactMemberships"))
      );
    }
    const term = searchTerm.toLowerCase().trim();
    const contactsToSearch =
      this.contactsCache ||
      (await this.dbManager.getAllData("contactMemberships"));

    if (!Array.isArray(contactsToSearch)) {
      console.warn("No contacts data available for local search.");
      return [];
    }

    const results = contactsToSearch.filter((membership) => {
      const name = membership?.user?.userName?.toLowerCase();
      const phone = membership?.user?.phoneNumber; // Search phone number too
      return (name && name.includes(term)) || (phone && phone.includes(term));
    });

    //console.log(
    //   `Local search for "${searchTerm}" found ${results.length} results.`
    // );
    return results;
  }

  /**
   * Adds a new contact.
   * @param {object} contactData - Data for the new contact (structure depends on your API).
   * @returns {Promise<object>} The newly created contact membership object from the server.
   */
  async addContact(contactData) {
    try {
      // Example: API might expect userId or phoneNumber to add
      const responseData = await this.ajaxManager.post(
        "Contact/AddContact",
        contactData
      ); // Adjust endpoint and payload
      if (
        responseData &&
        responseData.resCode === 201 &&
        responseData.resObject
      ) {
        // Assuming 201 Created
        const newMembership = new ContactModels.ContactMembership(
          responseData.resObject
        );

        // Add to DB
        await this.dbManager.saveData("contactMemberships", newMembership);

        // Update cache
        if (this.contactsCache) {
          this.contactsCache.push(newMembership);
          this.stateManager.set("contacts", this.contactsCache); // Update global state
        } else {
          await this.getContacts(true); // Force refresh cache if it wasn't populated
        }

        //console.log("Contact added successfully:", newMembership);
        return newMembership;
      } else {
        throw new Error(responseData?.resMeg || "Failed to add contact");
      }
    } catch (error) {
      console.error("Error adding contact:", error);
      throw error;
    }
  }

  /**
   * Deletes a contact membership.
   * @param {number} membershipId - The ID of the contact membership to delete.
   * @returns {Promise<boolean>} True if deletion was successful, false otherwise.
   */
  async deleteContact(membershipId) {
    try {
      // Example: API might expect the membership ID
      const responseData = await this.ajaxManager.delete(
        `Contact/DeleteContact/${membershipId}`
      ); // Adjust endpoint
      // Check response code, e.g., 204 No Content or a success flag
      if (
        responseData === null ||
        responseData?.resCode === 200 ||
        responseData?.resCode === 204
      ) {
        // Remove from DB
        await this.dbManager.deleteDataByKey(
          "contactMemberships",
          membershipId
        );

        // Update cache
        if (this.contactsCache) {
          this.contactsCache = this.contactsCache.filter(
            (m) => m.id !== membershipId
          );
          this.stateManager.set("contacts", this.contactsCache); // Update global state
        }

        //console.log(`Contact membership ${membershipId} deleted successfully.`);
        return true;
      } else {
        throw new Error(responseData?.resMeg || "Failed to delete contact");
      }
    } catch (error) {
      console.error(
        `Error deleting contact membership ${membershipId}:`,
        error
      );
      return false;
    }
  }

  /**
   * Clears the local contact cache.
   */
  clearCache() {
    this.contactsCache = null;
    //console.log("Contact cache cleared.");
  }
}

// Export a singleton instance
export const contactService = new UserContactsService();
