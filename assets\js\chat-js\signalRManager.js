/**
 * SignalRManager - Manages SignalR connection and event handling
 * Handles real-time updates for chat status, messages, and other events
 */
class SignalRManager {
  constructor() {
    this.connection = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 3;
    this.reconnectDelay = 2000; // 2 seconds
    this.hubUrl = null;
    this.token = null;
    this.eventHandlers = {
      userChatOnline: [],
      chatUpdate: [],
      groupEvents: [],
      newMessage: [],
      messageStatus: [],
      messageUpdateOrDelete: [],
      pollVote: [],
      blockingEvent: [],
      userInfo: [],
    };
  }

  /**
   * Initialize the SignalR connection
   * @param {string} hubUrl - The SignalR hub URL
   * @param {string} token - Authentication token
   * @returns {Promise} - Promise that resolves when connection is established
   */
  async initialize(hubUrl, token) {
    this.hubUrl = hubUrl;
    this.token = token;
    //console.log(
    //   "this.token",
    //   `${this.hubUrl}/ChatHub?access_token=${this.token}`
    // );

    try {
      await this.connect();
      return true;
    } catch (error) {
      console.error("Failed to initialize SignalR connection:", error);
      return false;
    }
  }

  /**
   * Connect to the SignalR hub
   * @returns {Promise} - Promise that resolves when connection is established
   */
  async connect() {
    if (this.connection) {
      await this.connection.stop();
    }

    // Create connection with authentication token
    //this.connection = new signalR.HubConnectionBuilder()
    //    .withUrl(`${this.hubUrl}/ChatHub?access_token=${this.token}`)
    //    .withAutomaticReconnect([0, 2000, 5000, 10000, 30000]) // Retry intervals in milliseconds
    //    .configureLogging(signalR.LogLevel.Information)
    //    .build();

    this.connection = await new signalR.HubConnectionBuilder()
      .withUrl(`${this.hubUrl}/ChatHub`, {
        //transport: signalR.HttpTransportType.WebSockets |
        //    signalR.HttpTransportType.LongPolling,
        //accessTokenFactory: () => this.accessToken
        accessTokenFactory: () => new Promise((resolve) => resolve(this.token)),
      })
      .configureLogging(signalR.LogLevel.Debug)
      //.withHubProtocol(new signalR.protocols.msgpack.MessagePackHubProtocol())
      .withAutomaticReconnect()
      .build();
    // Set up event handlers
    this.setupEventHandlers();

    // Start the connection
    try {
      await this.connection.start();
      //console.log("SignalR connection established");
      this.isConnected = true;
      this.reconnectAttempts = 0;

      // Notify that user is online
      //this.notifyUserOnline();

      return true;
    } catch (error) {
      console.error("Error establishing SignalR connection:", error);
      this.isConnected = false;

      // Attempt to reconnect
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        //console.log(
        //   `Reconnection attempt ${this.reconnectAttempts} of ${this.maxReconnectAttempts}`
        // );

        setTimeout(() => {
          this.connect();
        }, this.reconnectDelay);
      } else {
        console.error("Max reconnection attempts reached");
        // Notify UI about connection failure
        this.notifyConnectionFailure();
      }

      return false;
    }
  }

  /**
   * Set up event handlers for all SignalR events
   */
  setupEventHandlers() {
    // Connection events
    this.connection.onreconnecting((error) => {
      //console.log("Reconnecting to SignalR...", error);
      this.isConnected = false;
    });

    this.connection.onreconnected((connectionId) => {
      //console.log("Reconnected to SignalR. Connection ID:", connectionId);
      this.isConnected = true;
      this.reconnectAttempts = 0;

      // Notify that user is online after reconnection
      //this.notifyUserOnline();
    });

    this.connection.onclose((error) => {
      //console.log("SignalR connection closed", error);
      this.isConnected = false;

      // Attempt to reconnect if not manually closed
      if (error) {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++;
          //console.log(
          //   `Reconnection attempt ${this.reconnectAttempts} of ${this.maxReconnectAttempts}`
          // );

          setTimeout(() => {
            this.connect();
          }, this.reconnectDelay);
        } else {
          console.error("Max reconnection attempts reached");
          this.notifyConnectionFailure();
        }
      }
    });

    // User chat online status
    this.connection.on("ReceiveUserChatOnlineAsync", (data) => {
      //console.log("User chat online status update:", data);
      this.notifyEventHandlers("userChatOnline", data);
    });

    // Chat updates
    this.connection.on("ReceiveChatAsync", (data) => {
      //console.log("Chat update:", data);
      this.notifyEventHandlers("chatUpdate", data);
    });

    // Group events
    this.connection.on("ReceiveEventsInGroupAsync", (data) => {
      //console.log("Group event:", data);
      this.notifyEventHandlers("groupEvents", data);
    });

    // New messages
    this.connection.on("ReceiveMessageAsync", (data) => {
      //console.log("New message:", data);
      //const newMessage = await ChatProcessor.addMessage(msg);
      //addMessageToMessageArea(newMessage);

      this.notifyEventHandlers("newMessage", data);
    });

    // Message status updates
    this.connection.on("ReceiveMessageStatusAsync", (data) => {
      //console.log("Message status update:", data);
      this.notifyEventHandlers("messageStatus", data);
    });

    // Message updates or deletions
    this.connection.on("ReceiveMessagesUpdatedOrDeletedAsync", (data) => {
      //console.log("Message updated or deleted:", data);
      this.notifyEventHandlers("messageUpdateOrDelete", data);
    });

    // Poll vote events
    this.connection.on("ReceivePollVoteEventsAsync", (data) => {
      //console.log("Poll vote event:", data);
      this.notifyEventHandlers("pollVote", data);
    });

    // Blocking events
    this.connection.on("ReceiveBlockingEventAsync", (data) => {
      //console.log("Blocking event:", data);
      this.notifyEventHandlers("blockingEvent", data);
    });

    // User info events
    this.connection.on("ReceiveUserInfoEventAsync", (data) => {
      //console.log("User info event:", data);
      this.notifyEventHandlers("userInfo", data);
    });
  }

  /**
   * Notify all registered handlers for a specific event
   * @param {string} eventName - Name of the event
   * @param {any} data - Event data
   */
  notifyEventHandlers(eventName, data) {
    if (this.eventHandlers[eventName]) {
      this.eventHandlers[eventName].forEach((handler) => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in ${eventName} event handler:`, error);
        }
      });
    }
  }

  /**
   * Register an event handler
   * @param {string} eventName - Name of the event
   * @param {Function} handler - Event handler function
   */
  on(eventName, handler) {
    if (this.eventHandlers[eventName]) {
      this.eventHandlers[eventName].push(handler);
    } else {
      console.warn(`Unknown event: ${eventName}`);
    }
  }

  /**
   * Remove an event handler
   * @param {string} eventName - Name of the event
   * @param {Function} handler - Event handler function to remove
   */
  off(eventName, handler) {
    if (this.eventHandlers[eventName]) {
      this.eventHandlers[eventName] = this.eventHandlers[eventName].filter(
        (h) => h !== handler
      );
    }
  }

  /**
   * Notify the server that the user is online
   */
  async notifyUserOnline() {
    if (this.isConnected) {
      try {
        // Check if the method exists on the server
        if (
          this.connection.methods &&
          this.connection.methods.includes("UpdateUserOnlineStatus")
        ) {
          await this.connection.invoke("UpdateUserOnlineStatus", true);
          //console.log("User online status updated");
        } else {
          console.warn(
            "UpdateUserOnlineStatus method not available on the server"
          );
        }
      } catch (error) {
        // Check if the error is due to the method not existing
        if (error.message && error.message.includes("Method does not exist")) {
          console.warn(
            "UpdateUserOnlineStatus method not available on the server"
          );
        } else {
          console.error("Error updating user online status:", error);
        }
      }
    }
  }

  /**
   * Notify the server that the user is going offline
   */
  async notifyUserOffline() {
    if (this.isConnected) {
      try {
        // Check if the method exists on the server
        if (
          this.connection.methods &&
          this.connection.methods.includes("UpdateUserOnlineStatus")
        ) {
          await this.connection.invoke("UpdateUserOnlineStatus", false);
          //console.log("User offline status updated");
        } else {
          console.warn(
            "UpdateUserOnlineStatus method not available on the server"
          );
        }
      } catch (error) {
        // Check if the error is due to the method not existing
        if (error.message && error.message.includes("Method does not exist")) {
          console.warn(
            "UpdateUserOnlineStatus method not available on the server"
          );
        } else {
          console.error("Error updating user offline status:", error);
        }
      }
    }
  }

  /**
   * Notify UI about connection failure
   */
  notifyConnectionFailure() {
    // Dispatch a custom event that the UI can listen for
    const event = new CustomEvent("signalRConnectionFailure", {
      detail: { message: "Failed to connect to chat server" },
    });
    document.dispatchEvent(event);
  }

  /**
   * Disconnect from SignalR
   */
  async disconnect() {
    if (this.connection && this.isConnected) {
      await this.notifyUserOffline();
      await this.connection.stop();
      this.isConnected = false;
      //console.log("SignalR connection stopped");
    }
  }

  /**
   * Check if the connection is active
   * @returns {boolean} - Connection status
   */
  isConnectionActive() {
    return (
      this.isConnected &&
      this.connection &&
      this.connection.state === signalR.HubConnectionState.Connected
    );
  }
}

// Export the SignalRManager class
//export default SignalRManager;
