﻿class TeamListManager {
  constructor() {
    this.teamListContainer = document.getElementById("team-elementList");

    this.teams = [];
    this.currentPage = 1;
    this.pageSize = 20;
    this.hasMore = false;
    this.searchTerm = "";
    this.isLoading = false;
    this.teamProcessor = null;
  }

  async initialize() {
    try {
      this.teamProcessor = new TeamService();
      // Initialize team data
      await this.teamProcessor.initializeTeamsData();

      // Fetch initial Teams
      await this.fetchTeams();

      // Set up event listeners
      this.setupEventListeners();
      this.setupActiveStateHandler();
      ////console.log("team list manager initialized successfully");
    } catch (error) {
      console.error("Error initializing team list manager:", error);
      throw error;
    }
  }

  setupEventListeners() {
    // Search input handler
    const searchInput = document.getElementById("team-search");
    if (searchInput) {
      searchInput.addEventListener("input", this.handleSearch.bind(this));
    }

    // Scroll handler for infinite loading
    //const teamList = document.getElementById('team-elementList');
    if (this.teamListContainer) {
      this.teamListContainer.addEventListener(
        "scroll",
        this.handleScroll.bind(this)
      );
    }
  }

  async handleSearch(event) {
    const searchTerm = event.target.value;
    this.searchTerm = searchTerm;

    // If search term is empty, fetch from server
    if (!searchTerm.trim()) {
      this.currentPage = 1;
      await this.fetchTeams();
      return;
    }

    // For non-empty search terms, use local search for immediate results
    const searchResults = this.teamProcessor.getTeamsBySearch(searchTerm);
    this.teams = searchResults;
    this.renderTeams();

    // Show a message if no results found
    //const teamList = document.getElementById('team-elementList');
    if (this.teamListContainer && searchResults.length === 0) {
      this.teamListContainer.innerHTML = `
                <div class="no-results">
                    <p>لم يتم العثور على فرق متطابقة "${searchTerm}"</p>
                </div>
            `;
    }
    // Also fetch from server to ensure we have all matching Teams
    // Only if the search term is at least 2 characters to avoid too many requests
    if (searchTerm.trim().length >= 2 && searchResults.length !== 0) {
      this.currentPage = 1;
      await this.fetchTeams();
    }
    //await this.fetchTeams();
  }

  async handleScroll(event) {
    const element = event.target;
    if (element.scrollHeight - element.scrollTop === element.clientHeight) {
      if (this.hasMore && !this.isLoading) {
        this.currentPage++;
        await this.fetchTeams(true);
      }
    }
  }

  async fetchTeams(isLoadMore = false) {
    try {
      this.isLoading = true;

      const result = await this.teamProcessor.fetchTeams(
        this.searchTerm,
        this.currentPage,
        this.pageSize
      );
      ////////console.log()
      ////console.log("result team", result);
      // If we're doing a local search, don't overwrite the results
      if (this.searchTerm.trim() && !isLoadMore) {
        // Merge server results with local results to ensure we have all matches
        const localteamIds = new Set(
          this.teams.map((c) => {
            return c.name;
          })
        );
        ////////console.log("localteamIds", localteamIds)
        // Add any server results that aren't already in our local results
        result.teams.forEach((team) => {
          if (!localteamIds.has(team.name)) {
            ////////console.log("this.teams.push", team)

            this.teams.push(team);
          }
        });
      } else if (!isLoadMore) {
        // For non-search or empty search, replace Teams
        this.teams = result.teams;
      } else {
        // For load more, append new Teams
        this.teams = [...this.teams, ...result.teams];
      }

      this.hasMore = result.pagination.hasMore;

      // Update UI
      this.renderTeams();

      ////////console.log('Teams fetched successfully');
    } catch (error) {
      console.error("Error fetching Teams:", error);
      throw error;
    } finally {
      this.isLoading = false;
    }
  }
  renderTeams() {
    //const teamList = document.getElementById('team-elementList');
    if (!this.teamListContainer) return;
    // If no Teams, show a message
    if (this.teams.length === 0) {
      this.teamListContainer.innerHTML = `
                <div class="no-results">
                    <p>لم يتم العثور على أي فرق</p>
                </div>
            `;
      return;
    }
    let html = "";

    for (const team of this.teams) {
      html += this.generateteamHTML(team);
    }

    this.teamListContainer.innerHTML = html;
  }

  generateteamHTML(team) {
    return `
            <div class="block top item-team-list " data-team-id="${team.id}" >
                <div class="imgBox">
                    <img src="${
                      team.picture || "/assets/avatars/imageGrey.jpg"
                    }" class="cover" alt="${team.name}">
                </div>
                <div class="h-text">
                    <div class="head">
                        <h4 title="${team.name}" aria-label="${team.name}">${
      team.name
    }</h4>
                    </div>
                    <div class="message">
                        <p title="${team.description || ""}" aria-label="${
      team.description || ""
    }">
                            ${team.description || ""}
                        </p>
                    </div>
                </div>
            </div>
        `;
  }
  setupActiveStateHandler() {
    const chatProfile = new ChatProfile();

    if (!this.teamListContainer) {
      console.error("chatListContainer element not found.");
      return;
    }

    this.teamListContainer.addEventListener("click", async (event) => {
      const item = event.target.closest(".item-team-list");
      ////console.log("item item ", item);
      if (item) {
        const teamId = item.getAttribute("data-team-id"),
          team = this.getChatModel(
            this.teamProcessor.getTeamById(parseInt(teamId))
          );
        ////console.log("selectec team", team);
        if (chatProfile) {
          if (team) {
            chatProfile.open(team, "chat");
          }
        }
      }

      //const chatLocId = item.getAttribute("data-chat-locid");
      //        //const chat = this.chats.find(c =>
      //        //    c.id === parseInt(chatId) || c.locId === chatLocId
      //        //);
      //        ////////console.log("chatId", chatId)
      //        //await generateMessageArea(item, parseInt(chatLocId));
      //        //this.setActiveChat(chatId, chatLocId);

      //    }
    });
  }
  getChatModel(data) {
    return {
      id: data.id || null,
      chatName: data.name || "",
      chatPicture: data.Picture || "",
      chatType: "Group",
      //unreadCount = data.unreadCount || 0,
      companyID: data.companyID || null,
      createdUserId: data.createdUserId || null,
      createdDate: data.createdDate ? new Date(data.createdDate) : null,
      chatMembers: (data.teamMembers || []).map(
        (m) => new ChatModels.ChatMember(m)
      ),

      chatDescription: data.description || "",
    };
  }

  async deleteTeam(teamId) {
    try {
      await this.teamProcessor.deleteTeam(teamId);
      this.teams = this.teams.filter((c) => c.id !== teamId);
      this.renderTeams();
      ////////console.log('team deleted successfully');
    } catch (error) {
      console.error("Error deleting team:", error);
      throw error;
    }
  }

  showDeleteConfirmation(teamId) {
    if (confirm("هل أنت متأكد أنك تريد حذف جهة الاتصال هذه؟")) {
      this.deleteTeam(teamId);
    }
  }
}

//export default new teamListManager();
