﻿//import { DBManager } from './dbManager.js';
//import { Models } from './models.js';

class TeamService {
  constructor() {
    this.processedData = {
      teams: [],
      currentPage: 1,
      pageSize: 20,
      totalPages: 0,
      hasMore: false,
    };
    this.ajaxManager = new AjaxManager();
  }

  async initializeTeamsData() {
    try {
      // Initialize database if not already initialized
      if (!DBManager.isInitialized) {
        await DBManager.initializeDB();
      }

      // Load Teams from IndexedDB
      await this.loadTeamsFromDB();

      // //////console.log('team data initialized successfully');
    } catch (error) {
      //console.error('Error initializing team data:', error);
      throw error;
    }
  }

  async loadTeamsFromDB() {
    try {
      const Teams = await DBManager.getAllData("team");

      this.processedData.teams = Teams || [];
    } catch (error) {
      //console.error('Error loading Teams from DB:', error);
      throw error;
    }
  }

  async fetchTeams(search = "", page = 1, pageSize = 20, sort = "Desc") {
    try {
      // const data = await this.ajaxManager.get(`Team/GetAllTeams`);
      const data = await this.ajaxManager.get(
        `api/v1/Team?companyId=1&currentPage=1&pageSize=0`
      );
      //console.log("datateam", data);
      if (!data || !data.resObject) {
        throw new Error("Invalid response format from server");
      }
      if (data.resCode === 200) {
        // Process and store Teams
        //const Teams = data.resObject.pageData.map(item => teamModels.team.fromResponse(item.team));
        const teamsRes = data.resObject.pageData.map((item) => {
          //////console.log("item", item)

          return new TeamModels.Team(item);
        });

        this.processedData.teams = teamsRes;
        this.processedData.currentPage = data.resObject.currentPage;
        this.processedData.pageSize = data.resObject.pageSize;
        this.processedData.totalPages = data.resObject.totalPages;
        this.processedData.hasMore = data.resObject.hasNextPage;

        // Save to IndexedDB
        await this.saveTeamsToDB(teamsRes);

        return {
          teams: teamsRes,
          pagination: {
            currentPage: data.resObject.currentPage,
            pageSize: data.resObject.pageSize,
            totalPages: data.resObject.totalPages,
            hasMore: data.resObject.hasNextPage,
          },
        };
      } else {
        throw new Error(data.resMeg || "Failed to fetch Teams");
      }
    } catch (error) {
      console.error("Error fetching Teams:", error);
      // Return empty data instead of throwing error
      return {
        teams: [],
        pagination: {
          currentPage: 1,
          pageSize: pageSize,
          totalPages: 0,
          hasMore: false,
        },
      };
    }
  }

  async saveTeamsToDB(Teams) {
    try {
      // Clear existing data
      await DBManager.clearStore("team");

      // Save new data
      for (const team of Teams) {
        await DBManager.saveData("team", team);
      }
    } catch (error) {
      //console.error('Error saving Teams to DB:', error);
      throw error;
    }
  }

  async deleteTeam(teamId) {
    try {
      // Remove from IndexedDB
      await DBManager.delete("team", teamId);

      // Update processed data
      this.processedData.teams = this.processedData.teams.filter(
        (c) => c.id !== teamId
      );
    } catch (error) {
      //console.error('Error deleting team:', error);
      throw error;
    }
  }

  getTeamById(teamId) {
    //console.log("this.processedData.teams", this.processedData.teams);
    //console.log("teamId", teamId);
    return this.processedData.teams.find((c) => c.id === teamId);
  }

  getTeamsBySearch(searchTerm) {
    if (!searchTerm || searchTerm.trim().length < 1) {
      return this.processedData.teams;
    }
    const term = searchTerm?.toLowerCase()?.trim();
    if (!this.processedData || !Array.isArray(this.processedData.teams)) {
      console.warn("No Teams data to search.");
      return [];
    }

    //console.log("this.processedData.teams", this.processedData.teams);
    const results = this.processedData.teams.filter((team) => {
      const name = team?.name?.toLowerCase();
      return name && name.includes(term);
    });
    return results;
  }
}
