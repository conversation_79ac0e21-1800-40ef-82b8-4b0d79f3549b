/**
 * إدارة تسجيل الرسائل الصوتية
 * هذا الملف مسؤول عن معالجة تسجيل وعرض الرسائل الصوتية في تطبيق الدردشة
 */

class VoiceRecorder {
  constructor() {
    // معلمات التسجيل
    this.isRecording = false;
    this.startTime = null;
    this.recorder = null;
    this.audioBlob = null;
    this.audioUrl = null;
    this.recordingTimer = null;
    this.recordingDuration = 0;
    this.audioStream = null;
    this.audioContext = null;
    this.analyser = null;
    this.dataArray = null;
    this.visualizerCanvas = null;
    this.canvasContext = null;
    this.visualizerActive = false;
    this.waveformBars = null;
    this.chatListManager = new ChatListManager();

    // إضافة معلمات الحالة المفقودة
    this.isLocked = false;
    this.lockTimeout = null;
    this.dragStartY = 0;
    this.dragCurrentY = 0;

    // تهيئة عناصر واجهة المستخدم
    this.initElements();

    // ربط الأحداث بالعناصر
    this.bindEvents();
  }

  /**
   * تهيئة عناصر واجهة المستخدم
   */
  initElements() {
    // أزرار التسجيل
    this.micButton = document.getElementById("voice-record-btn");
    this.sendButton = document.getElementById("send-message-btn");
    this.messageInput = document.getElementById("input-send-message");

    // حاوية التسجيل
    this.recordingContainer = document.getElementById("recording-container");
    this.recordingTimer = document.getElementById("recording-timer");
    this.recordingWaveform = document.getElementById("recording-waveform");
    this.cancelHint = document.getElementById("slide-to-cancel");
    this.lockHint = document.getElementById("slide-to-lock");
    this.stopButton = document.getElementById("stop-recording-btn");

    // حاوية معاينة التسجيل
    this.previewContainer = document.getElementById("recording-preview");
    this.previewPlayButton = document.getElementById("preview-play-btn");
    this.previewWaveform = document.getElementById("preview-waveform");
    this.previewDuration = document.getElementById("preview-duration");
    this.previewSendButton = document.getElementById("preview-send-btn");
    this.previewCancelButton = document.getElementById("preview-cancel-btn");

    // إنشاء قماش التحليل البصري للصوت
    this.visualizerCanvas = document.createElement("canvas");
    this.visualizerCanvas.width = 200;
    this.visualizerCanvas.height = 60;
    this.visualizerCanvas.className = "waveform-canvas";
    this.canvasContext = this.visualizerCanvas.getContext("2d");

    if (this.recordingWaveform) {
      // تأكد من خلو عنصر التحليل البصري قبل إضافة القماش
      this.recordingWaveform.innerHTML = "";
      this.recordingWaveform.appendChild(this.visualizerCanvas);
    }
  }

  /**
   * ربط الأحداث بالعناصر
   */
  bindEvents() {
    if (this.micButton) {
      this.micButton.addEventListener(
        "mousedown",
        this.startRecording.bind(this)
      );
      this.micButton.addEventListener(
        "touchstart",
        this.startRecording.bind(this)
      );
      document.addEventListener("mouseup", this.handleRecordingEnd.bind(this));
      document.addEventListener("touchend", this.handleRecordingEnd.bind(this));

      // إضافة مستمعي أحداث للسحب للتحكم في القفل/الإلغاء
      document.addEventListener("mousemove", this.checkLockPosition.bind(this));
      document.addEventListener("touchmove", this.handleTouchMove.bind(this), {
        passive: false,
      });
    }

    if (this.stopButton) {
      this.stopButton.addEventListener("click", this.stopRecording.bind(this));
    }

    if (this.previewPlayButton) {
      this.previewPlayButton.addEventListener(
        "click",
        this.togglePreviewPlayback.bind(this)
      );
    }

    if (this.previewSendButton) {
      this.previewSendButton.addEventListener(
        "click",
        this.sendRecording.bind(this)
      );
    }

    if (this.previewCancelButton) {
      this.previewCancelButton.addEventListener(
        "click",
        this.cancelRecording.bind(this)
      );
    }
  }

  /**
   * معالجة حركة اللمس
   * @param {TouchEvent} event - حدث اللمس
   */
  handleTouchMove(event) {
    if (this.isRecording) {
      event.preventDefault();
      if (event.touches.length > 0) {
        const touch = event.touches[0];
        this.dragCurrentY = touch.clientY;
        this.checkLockPosition({ clientY: this.dragCurrentY });
      }
    }
  }

  /**
   * التحقق من موضع القفل أو الإلغاء بناءً على حركة السحب
   * @param {Event} event - حدث الحركة
   */
  checkLockPosition(event) {
    if (!this.isRecording || this.isLocked) return;

    // إذا لم نحدد موضع البداية بعد، نقوم بتعيينه
    if (this.dragStartY === 0) {
      this.dragStartY = event.clientY;
      return;
    }

    const movementY = event.clientY - this.dragStartY;

    // إذا تحرك المؤشر للأعلى (قفل التسجيل)
    if (movementY < -50) {
      this.lockRecording();
    }
    // إذا تحرك المؤشر للأسفل (إلغاء التسجيل)
    else if (movementY > 50) {
      this.cancelRecording();
    }
  }

  /**
   * قفل التسجيل (للتسجيل المستمر)
   */
  lockRecording() {
    if (!this.isRecording || this.isLocked) return;

    this.isLocked = true;

    // إضافة صنف CSS لإظهار حالة القفل
    if (this.recordingContainer) {
      this.recordingContainer.classList.add("locked-recording");
    }

    // إظهار زر الإيقاف
    if (this.stopButton) {
      this.stopButton.style.display = "block";
    }

    // إخفاء تلميحات السحب
    if (this.cancelHint) {
      this.cancelHint.style.display = "none";
    }

    if (this.lockHint) {
      this.lockHint.style.display = "none";
    }
  }

  /**
   * بدء تسجيل الرسالة الصوتية
   * @param {Event} event - حدث النقر أو اللمس
   */
  async startRecording(event) {
    // إذا كان التسجيل جاريًا بالفعل أو كان حقل الرسالة غير فارغ، لا نبدأ التسجيل
    if (this.isRecording || this.messageInput.value.trim() !== "") {
      return;
    }

    try {
      // تعيين موضع بداية السحب
      this.dragStartY =
        event.clientY ||
        (event.touches && event.touches[0] ? event.touches[0].clientY : 0);

      // طلب إذن استخدام الميكروفون
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: true,
      });

      // إنشاء مسجل الصوت
      this.recorder = new MediaRecorder(this.audioStream);

      // إنشاء مصفوفة لتخزين الأجزاء المسجلة
      const audioChunks = [];

      // الاستماع لحدث توفر بيانات جديدة
      this.recorder.addEventListener("dataavailable", (event) => {
        audioChunks.push(event.data);
      });

      // الاستماع لحدث انتهاء التسجيل
      this.recorder.addEventListener("stop", () => {
        //const header = new Uint8Array([
        //    0x52, 0x49, 0x46, 0x46, // "RIFF"
        //    0x00, 0x00, 0x00, 0x00, // حجم الملف (يتم تحديثه لاحقًا)
        //    0x57, 0x41, 0x56, 0x45, // "WAVE"
        //    0x66, 0x6d, 0x74, 0x20  // "fmt "
        //]);
        // دمج الأجزاء المسجلة في ملف صوتي واحد
        this.audioBlob = new Blob(audioChunks, { type: "audio/wav" });

        // إنشاء URL للملف الصوتي للتشغيل
        this.audioUrl = URL.createObjectURL(this.audioBlob);

        // إيقاف التحليل البصري
        this.stopVisualizer();

        // إغلاق تدفق الميكروفون
        this.audioStream.getTracks().forEach((track) => track.stop());

        // إذا لم يتم إلغاء التسجيل وليس في وضع القفل، نقوم بإرسال التسجيل مباشرة
        if (!this.isLocked && this.recordingDuration > 1) {
          this.sendRecording();
        } else if (this.isLocked && this.recordingDuration > 1) {
          // إذا كان في وضع القفل، نعرض المعاينة
          this.showRecordingPreview();
        } else {
          // تسجيل قصير جدًا، نلغيه
          this.cancelRecording();
        }
      });

      // بدء التسجيل
      this.recorder.start();

      // ضبط حالة التسجيل
      this.isRecording = true;
      this.startTime = Date.now();
      this.recordingDuration = 0;
      this.isLocked = false;

      // إظهار واجهة التسجيل
      this.showRecordingInterface();

      // بدء تحديث مؤقت التسجيل
      this.startTimer();

      // بدء التحليل البصري
      this.startVisualizer();

      // تعيين مؤقت لقفل التسجيل تلقائيًا بعد وقت معين (اختياري)
      this.lockTimeout = setTimeout(() => {
        if (this.isRecording && !this.isLocked) {
          this.lockRecording();
        }
      }, 60000); // قفل تلقائي بعد 60 ثانية
    } catch (error) {
      console.error("خطأ في بدء التسجيل:", error);

      // إظهار رسالة خطأ للمستخدم
      if (
        error.name === "NotAllowedError" ||
        error.name === "PermissionDeniedError"
      ) {
        alert("لا يمكن تسجيل الصوت. يرجى السماح بالوصول إلى الميكروفون.");
      } else {
        alert("حدث خطأ أثناء بدء التسجيل الصوتي.");
      }
    }
  }

  /**
   * معالجة إنهاء التسجيل (رفع الضغط عن زر التسجيل)
   */
  handleRecordingEnd(event) {
    // إذا لم يكن التسجيل قد بدأ، لا نفعل شيئًا
    if (!this.isRecording || this.isLocked) return;

    // تجنب معالجة نهاية التسجيل إذا كانت حركة في أثناء السحب
    if (Math.abs(event.clientY - this.dragStartY) > 50) {
      return;
    }

    // إيقاف التسجيل
    this.stopRecording();
  }

  /**
   * إيقاف التسجيل
   */
  stopRecording() {
    if (!this.isRecording) return;

    // إيقاف تسجيل الصوت
    if (this.recorder && this.recorder.state !== "inactive") {
      this.recorder.stop();
    }

    // إيقاف مؤقت التسجيل
    this.stopTimer();

    // إعادة ضبط حالة التسجيل
    this.isRecording = false;

    // إزالة مستمعي أحداث السحب
    document.removeEventListener(
      "mousemove",
      this.checkLockPosition.bind(this)
    );
    document.removeEventListener("touchmove", this.handleTouchMove.bind(this));

    // إزالة مؤقت قفل التسجيل
    if (this.lockTimeout) {
      clearTimeout(this.lockTimeout);
    }

    // إخفاء واجهة التسجيل
    this.hideRecordingInterface();
  }

  /**
   * إلغاء التسجيل
   */
  cancelRecording() {
    // إيقاف التسجيل إذا كان جاريًا
    if (this.isRecording) {
      if (this.recorder && this.recorder.state !== "inactive") {
        this.recorder.stop();
      }

      // إيقاف تدفق الميكروفون
      if (this.audioStream) {
        this.audioStream.getTracks().forEach((track) => track.stop());
      }

      // إيقاف مؤقت التسجيل
      this.stopTimer();

      // إيقاف التحليل البصري
      this.stopVisualizer();
    }

    // إعادة ضبط جميع المتغيرات
    this.isRecording = false;
    this.isLocked = false;
    this.recordingDuration = 0;
    this.dragStartY = 0;
    this.dragCurrentY = 0;

    // تحرير مصادر الصوت
    if (this.audioUrl) {
      URL.revokeObjectURL(this.audioUrl);
      this.audioUrl = null;
    }

    this.audioBlob = null;

    // إخفاء واجهة التسجيل وواجهة المعاينة
    this.hideRecordingInterface();
    this.hideRecordingPreview();

    // إعادة عرض واجهة الدردشة العادية
    this.showNormalChatInterface();
  }

  /**
   * عرض واجهة التسجيل
   */
  showRecordingInterface() {
    // إخفاء حقل الإدخال النصي وزر الإرسال
    if (this.messageInput) {
      this.messageInput.style.display = "none";
    }

    if (this.sendButton) {
      this.sendButton.style.display = "none";
    }

    // إظهار حاوية التسجيل مع تخصيص مظهرها
    if (this.recordingContainer) {
      this.recordingContainer.style.display = "flex";

      // إظهار زر إيقاف التسجيل مباشرة
      if (this.stopButton) {
        this.stopButton.style.display = "block";
      }

      // إخفاء إرشادات السحب
      if (this.cancelHint) {
        this.cancelHint.style.display = "none";
      }

      if (this.lockHint) {
        this.lockHint.style.display = "none";
      }
    }

    // إضافة صنف التسجيل النشط إلى زر الميكروفون
    if (this.micButton) {
      this.micButton.classList.add("recording-active");
    }
  }

  /**
   * إخفاء واجهة التسجيل
   */
  hideRecordingInterface() {
    if (this.recordingContainer) {
      this.recordingContainer.style.display = "none";
      this.recordingContainer.classList.remove("locked-recording");
    }

    if (this.micButton) {
      this.micButton.classList.remove("recording-active");
    }

    if (this.stopButton) {
      this.stopButton.style.display = "none";
    }
  }

  /**
   * عرض واجهة معاينة التسجيل
   */
  showRecordingPreview() {
    // إخفاء واجهة التسجيل
    this.hideRecordingInterface();

    // ضبط مدة التسجيل في واجهة المعاينة
    if (this.previewDuration) {
      this.previewDuration.textContent = this.formatTime(
        this.recordingDuration
      );
    }

    // إضافة التحليل البصري المحسن في معاينة التسجيل
    if (this.previewWaveform) {
      this.previewWaveform.innerHTML = "";

      // إنشاء حاوية التحليل البصري المحسن للمعاينة
      const enhancedWaveform = document.createElement("div");
      enhancedWaveform.className = "enhanced-waveform";

      // إنشاء أعمدة التحليل البصري
      const barCount = 27;

      // إنشاء أعمدة بارتفاعات عشوائية للتمثيل البصري
      for (let i = 0; i < barCount; i++) {
        const bar = document.createElement("span");
        // ارتفاع عشوائي بين 20% و 100%
        const height = Math.floor(Math.random() * 80 + 20);
        bar.style.height = `${height}%`;
        enhancedWaveform.appendChild(bar);
      }

      this.previewWaveform.appendChild(enhancedWaveform);
    }

    // إظهار حاوية المعاينة
    if (this.previewContainer) {
      this.previewContainer.style.display = "flex";
    }
  }

  /**
   * إخفاء واجهة معاينة التسجيل
   */
  hideRecordingPreview() {
    if (this.previewContainer) {
      this.previewContainer.style.display = "none";
    }
  }

  /**
   * إظهار واجهة الدردشة العادية
   */
  showNormalChatInterface() {
    if (this.messageInput) {
      this.messageInput.style.display = "block";
    }

    if (this.sendButton) {
      this.sendButton.style.display = "block";
    }
  }

  /**
   * بدء مؤقت التسجيل
   */
  startTimer() {
    // إعادة ضبط مؤقت التسجيل
    this.recordingDuration = 0;

    // تحديث المؤقت كل ثانية
    this.timerInterval = setInterval(() => {
      this.recordingDuration++;

      // تحديث نص المؤقت
      if (this.recordingTimer) {
        this.recordingTimer.textContent = this.formatTime(
          this.recordingDuration
        );
      }
    }, 1000);
  }

  /**
   * إيقاف مؤقت التسجيل
   */
  stopTimer() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  /**
   * تنسيق الوقت بالثواني إلى تنسيق mm:ss
   * @param {number} seconds - الوقت بالثواني
   * @returns {string} الوقت بتنسيق mm:ss
   */
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  }

  /**
   * بدء التحليل البصري للصوت
   */
  startVisualizer() {
    if (!this.audioStream || !this.canvasContext) return;

    try {
      // إنشاء سياق الصوت
      this.audioContext = new (window.AudioContext ||
        window.webkitAudioContext)();

      // إنشاء مصدر الصوت من تدفق الميكروفون
      const source = this.audioContext.createMediaStreamSource(
        this.audioStream
      );

      // إنشاء محلل الصوت
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 256;
      this.analyser.smoothingTimeConstant = 0.7; // إضافة تنعيم للحصول على حركة أكثر سلاسة

      // توصيل المصدر بالمحلل
      source.connect(this.analyser);

      // إنشاء مصفوفة لبيانات التحليل
      const bufferLength = this.analyser.frequencyBinCount;
      this.dataArray = new Uint8Array(bufferLength);

      // وضع علامة على التحليل البصري كنشط
      this.visualizerActive = true;

      // إنشاء عنصر HTML للتحليل البصري المحسن
      this.createEnhancedWaveform();

      // بدء رسم التحليل البصري
      this.drawVisualizer();
    } catch (error) {
      console.error("خطأ في بدء التحليل البصري:", error);
    }
  }

  /**
   * إنشاء عنصر HTML للتحليل البصري المحسن
   */
  createEnhancedWaveform() {
    // إزالة القماش السابق
    if (this.recordingWaveform) {
      this.recordingWaveform.innerHTML = "";

      // إنشاء حاوية التحليل البصري المحسن
      const enhancedWaveform = document.createElement("div");
      enhancedWaveform.className = "enhanced-waveform";

      // إنشاء أعمدة التحليل البصري
      const barCount = 27; // عدد الأعمدة

      for (let i = 0; i < barCount; i++) {
        const bar = document.createElement("span");
        bar.style.height = "10%"; // ارتفاع افتراضي
        enhancedWaveform.appendChild(bar);
      }

      this.recordingWaveform.appendChild(enhancedWaveform);
      this.waveformBars = enhancedWaveform.querySelectorAll("span");
    }
  }

  /**
   * رسم التحليل البصري للصوت
   */
  drawVisualizer() {
    if (!this.visualizerActive || !this.analyser || !this.waveformBars) return;

    // الحصول على بيانات التحليل
    this.analyser.getByteFrequencyData(this.dataArray);

    // متوسط لحجم البيانات لكل شريط
    const barCount = this.waveformBars.length;
    const frequencyStep = Math.floor(this.dataArray.length / barCount);

    // تحديث ارتفاع كل شريط بناءً على بيانات الصوت
    for (let i = 0; i < barCount; i++) {
      // حساب متوسط قيمة التردد لهذا الشريط
      let sum = 0;
      const startFreq = i * frequencyStep;

      // أخذ متوسط من عدة قيم تردد
      for (let j = 0; j < frequencyStep; j++) {
        const index = startFreq + j;
        if (index < this.dataArray.length) {
          sum += this.dataArray[index];
        }
      }

      // حساب متوسط قيمة الترددات
      const average = sum / frequencyStep;

      // تعيين الحد الأدنى للارتفاع لتجنب الأشرطة الصغيرة جداً
      const minHeight = 10;

      // تحويل قيمة الصوت إلى نسبة مئوية من الارتفاع (تتراوح من 10% إلى 100%)
      const heightPercentage = Math.max(
        minHeight,
        Math.min(100, (average / 255) * 100)
      );

      // إضافة تأخير للحركة بناءً على الموضع
      const delay = i * 20; // تأخير بالميلي ثانية

      // تطبيق الارتفاع الجديد مع تأثير انتقالي سلس
      setTimeout(() => {
        if (this.visualizerActive && this.waveformBars[i]) {
          this.waveformBars[i].style.height = `${heightPercentage}%`;
        }
      }, delay);
    }

    // استدعاء الدالة مرة أخرى لرسم الإطار التالي
    requestAnimationFrame(this.drawVisualizer.bind(this));
  }

  /**
   * إيقاف التحليل البصري للصوت
   */
  stopVisualizer() {
    this.visualizerActive = false;

    if (this.audioContext) {
      if (this.audioContext.state !== "closed") {
        this.audioContext.close();
      }
      this.audioContext = null;
    }

    this.analyser = null;
    this.dataArray = null;

    // إعادة ارتفاع الأشرطة إلى الحالة الافتراضية
    if (this.waveformBars) {
      for (let i = 0; i < this.waveformBars.length; i++) {
        this.waveformBars[i].style.height = "10%";
      }
    }
  }

  /**
   * تبديل تشغيل/إيقاف معاينة التسجيل
   */
  togglePreviewPlayback() {
    if (!this.audioUrl) return;

    const audio = document.getElementById("preview-audio");

    if (!audio) {
      // إنشاء عنصر صوت إذا لم يكن موجودًا
      const newAudio = document.createElement("audio");
      newAudio.id = "preview-audio";
      newAudio.src = this.audioUrl;
      newAudio.style.display = "none";

      // إضافة مستمع انتهاء التشغيل
      newAudio.addEventListener("ended", () => {
        if (this.previewPlayButton) {
          this.previewPlayButton.classList.remove("playing");
          this.previewPlayButton.innerHTML = '<i class="fe fe-play"></i>';
        }
      });

      // إضافة مستمع لخطأ التشغيل
      newAudio.addEventListener("error", (e) => {
        console.error("خطأ في تشغيل التسجيل:", e);
        if (this.previewPlayButton) {
          this.previewPlayButton.classList.remove("playing");
          this.previewPlayButton.innerHTML = '<i class="fe fe-play"></i>';
        }
      });

      document.body.appendChild(newAudio);

      // محاولة بدء التشغيل
      const playPromise = newAudio.play();

      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            // التشغيل بدأ بنجاح
            if (this.previewPlayButton) {
              this.previewPlayButton.classList.add("playing");
              this.previewPlayButton.innerHTML = '<i class="fe fe-pause"></i>';
            }
          })
          .catch((error) => {
            // فشل التشغيل (غالبًا بسبب تفاعل المستخدم المطلوب)
            console.error("فشل تشغيل التسجيل تلقائيًا:", error);
          });
      }
    } else {
      // إذا كان عنصر الصوت موجودًا بالفعل
      if (audio.paused) {
        // محاولة بدء التشغيل
        const playPromise = audio.play();

        if (playPromise !== undefined) {
          playPromise
            .then(() => {
              // التشغيل بدأ بنجاح
              if (this.previewPlayButton) {
                this.previewPlayButton.classList.add("playing");
                this.previewPlayButton.innerHTML =
                  '<i class="fe fe-pause"></i>';
              }
            })
            .catch((error) => {
              // فشل التشغيل
              console.error("فشل تشغيل التسجيل:", error);
            });
        }
      } else {
        // إيقاف التشغيل
        audio.pause();

        // تحديث زر التشغيل
        if (this.previewPlayButton) {
          this.previewPlayButton.classList.remove("playing");
          this.previewPlayButton.innerHTML = '<i class="fe fe-play"></i>';
        }
      }
    }
  }

  /**
   * إنهاء التسجيل الصوتي وإرساله
   */
  async sendRecording() {
    try {
      // إغلاق معاينة التسجيل
      this.hideRecordingPreview();
      this.showNormalChatInterface();

      // تحديد نوع الملف المناسب للتسجيل الصوتي
      // استخدام audio/mpeg بدلاً من audio/mp3 لضمان التوافق
      const mimeType = "audio/wav";
      const extension = ".wav";

      // إنشاء ملف بلوب من التسجيل الصوتي مع MIME Type صحيح
      const audioFile = new File(
        [this.audioBlob],
        `voice_message_${Date.now()}${extension}`,
        {
          type: mimeType,
        }
      );
      //console.log("audioFile", audioFile);
      //console.log("audioFile", chat);

      // إنشاء بيانات FormData
      const formData = new FormData();
      formData.append("File.MultipartFile", audioFile);
      formData.append("File.FileName", audioFile.name);
      formData.append("File.MimeFileType", mimeType);
      formData.append("File.FileSize", audioFile.size);
      formData.append("File.StorageSourseType", "LoaclStorage");
      formData.append("File.FileType", "audio");
      formData.append("File.Seconds", Math.round(this.recordingDuration));

      formData.append("ChatID", chat.id);
      formData.append("MessageType", 8); // 8 يمثل نوع رسالة صوتية RecordVoice
      formData.append("Source", 1);
      formData.append("MessageText", "d");
      formData.append("DeviceInfo", "web");
      formData.append("messageText", "");
      formData.append("senderID", ChatProcessor.processedData.currentUser?.id);

      // إنشاء رسالة محلية بحالة "قيد الانتظار"
      const localMessage = await ChatProcessor.addMessage({
        chatID: chat.id,
        senderID: ChatProcessor.processedData.currentUser?.id,
        messageType: "8", // نوع رسالة صوتية
        source: 1,
        //messageText: ,
        deviceInfo: "web",
        messageStatus: "Pending",
        file: {
          fileName: audioFile.name,
          mimeFileType: mimeType,
          fileSize: audioFile.size,
          isUploadComplete: false,
          fileContent: this.audioUrl,
          seconds: Math.round(this.recordingDuration),
        },
      });

      // إضافة الرسالة إلى واجهة المستخدم مع مؤشر التقدم
      if (
        window.ChatProcessor &&
        typeof window.ChatProcessor.addMessage === "function"
      ) {
        await window.ChatProcessor.addMessage(localMessage);
      }

      if (typeof window.addMessageToMessageArea === "function") {
        window.addMessageToMessageArea(localMessage);
      }

      // تحديث قائمة الدردشات
      //if (typeof window.generateChatList === 'function') {
      //   //console.log(" تحديث قائمة الدردشات")
      await generateChatList();
      //}

      // إرسال التسجيل الصوتي
      if (navigator.onLine) {
        try {
          // إرسال الطلب إلى الخادم
          const response = await fetch("/Message/Create/FileMultipart", {
            method: "POST",
            body: formData,
            headers: {
              Accept: "application/json",
            },
          });

          if (!response.ok) {
            throw new Error(`Error ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();

          if (data && data.resObject) {
            // تحديث حالة الرسالة
            const updatedMessage = await ChatProcessor.updateMessage(
              localMessage.locId,
              {
                id: data.resObject.id,
                messageStatus: "Sent",
                createdDate: data.resObject.createdDate,
              }
            );
            //console.log("updatedMessage", updatedMessage);
            //MessageGenerator.updateMessageStatus(localMessage.locId, "Sent");
            //const { message, chat } = await ChatProcessor.updateMessageStatus(data.resObject.id, "Sent")

            if (this.chatListManager) {
              //console.log("update message status chat");
              await this.chatListManager.updateMessageStatuse(
                parseInt(data.resObject.id),
                "Sent"
              );
            }
          } else {
            throw new Error(data.resMeg || "فشل في إرسال التسجيل الصوتي");
          }
        } catch (error) {
          console.error("خطأ في إرسال التسجيل الصوتي:", error);

          if (
            window.MessageGenerator &&
            typeof window.MessageGenerator.updateMessageStatus === "function"
          ) {
            window.MessageGenerator.updateMessageStatus(
              localMessage.locId,
              "Failed"
            );
          }

          if (typeof window.showMessageError === "function") {
            window.showMessageError(
              localMessage.locId,
              "فشل الإرسال. انقر لإعادة المحاولة"
            );
          }

          // إضافة وظيفة إعادة المحاولة للرسالة
          if (typeof window.retryMessage === "function") {
            const messageElement = document.getElementById(
              `message-${localMessage.locId}`
            );
            if (messageElement) {
              messageElement.onclick = function () {
                window.retryMessage(localMessage.locId, formData);
              };
            }
          }
        }
      } else {
        // تحديث حالة الرسالة إلى "غير مرسلة"
        if (
          window.MessageGenerator &&
          typeof window.MessageGenerator.updateMessageStatus === "function"
        ) {
          window.MessageGenerator.updateMessageStatus(
            localMessage.locId,
            "Failed"
          );
        }

        // إظهار رسالة خطأ
        if (typeof window.showMessageError === "function") {
          window.showMessageError(
            localMessage.locId,
            "فشل الإرسال، أنت غير متصل بالإنترنت"
          );
        }

        // إضافة وظيفة إعادة المحاولة للرسالة عند عودة الاتصال
        if (typeof window.retryMessage === "function") {
          const messageElement = document.getElementById(
            `message-${localMessage.locId}`
          );
          if (messageElement) {
            messageElement.onclick = function () {
              window.retryMessage(localMessage.locId, formData);
            };
          }
        }
      }
    } catch (error) {
      console.error("خطأ في إرسال التسجيل الصوتي:", error);
    }
  }
}

// إنشاء مثيل لمدير التسجيل الصوتي عند تحميل الصفحة
let voiceRecorder;

document.addEventListener("DOMContentLoaded", () => {
  try {
    // إنشاء مثيل جديد من مدير التسجيل الصوتي
    voiceRecorder = new VoiceRecorder();

    // إضافة المثيل إلى الكائن العام window للوصول إليه من أي مكان
    window.voiceRecorder = voiceRecorder;

    //console.log("تم تهيئة مدير التسجيل الصوتي بنجاح");
  } catch (error) {
    console.error("حدث خطأ أثناء تهيئة مدير التسجيل الصوتي:", error);
  }
});
