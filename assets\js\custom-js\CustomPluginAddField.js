
var isEmpty = function isEmpty(f) {
	return (/^function[^{]+\{\s*\}/m.test(f.toString())
	);
}

	; (function ($) {
		'use strict';

		$.CustomPluginAddField = {
			_baseConfig: {
				createTrigger: '.js-create-field',
				deleteTrigger: '.js-delete-field',
				defaultCreated: 1,
				nameSeparator: '_',
				configAttributes: {},
				addedField: function () { },
				deletedField: function () { }
			},
			pageCollection: $(),
			flags: {
				name: 'data-name',
				delete: 'data-hs-add-field-delete',
				inputNameUpdateValue:'data-hs-input-name',
				inputValue :'data-hs-input-value'
			},
			fieldsCount: 1,
			init: function (selector, config) {
				this.collection = selector && $(selector).length ? $(selector) : $();
				if (!$(selector).length) return;
				this.config = config && $.isPlainObject(config) ?
					$.extend({}, this._baseConfig, config) : this._baseConfig;

				this.config.itemSelector = selector;

				this.initAddField();
				// debugger
				return this.pageCollection;
			},

			initAddField: function () {
				//Variables
				var $self = this,
					config = $self.config,
					collection = $self.pageCollection;
				console.log($self)
				// debugger
				this.fieldsCount = config.defaultCreated;
				//Actions
				var configAttributes = {};
				this.collection.each(function (i, el) {
					const $this = $(el)
					config.defaultCreated = $self.getDataSettings(el).defaultCreated === config.defaultCreated ? config.defaultCreated : $self.getDataSettings(el).defaultCreated;
					configAttributes[i] = $.extend({}, $self.getDataSettings(el));
					console.log(config.defaultCreated)
					
					// Create default fields based on the defaultCreated value
					for (var i = 1; i < config.defaultCreated; i++) {
						$self.addField($self.getDataSettings(el));
						// debugger
						// console.log($self.addField(config));
					}

					// Set up event handlers for creating and deleting fields
					$(el).on('click', config.createTrigger, () => {
						$self.addField($self.getDataSettings(el));
						console.log(config);
					});
					$(el).on('click', config.deleteTrigger, (e) => {


						if (e.currentTarget.hasAttribute("data-hs-hide-field-delete-button")) {
							$self.hideField($self.getDataSettings(el), e.currentTarget);
							//console.log($self.getDataSettings(el), $(e.currentTarget)
						} else {
							$self.deleteField($self.getDataSettings(el), $(e.currentTarget).attr($self.flags.delete));
							console.log($self.getDataSettings(el), $(e.currentTarget).attr($self.flags.delete))
						}
					});

					//Actions
					$self.config.configAttributes = Object.assign({}, configAttributes);
					collection = collection.add($this);
				});
				console.log(configAttributes)
			},

			addField: function (params) {
				var settings = params;
				console.log(params)
				console.log(this.fieldsCount >= 0)
				// Check if the number of fields is within the limit
				if (this.fieldsCount >= 0) {
					// Clone the template, remove its ID, and append it to the container
					let field = $(settings.template).clone().removeAttr('id').css({ display: '' }).appendTo($(settings.container));
					console.log(this)
					// Update various aspects of the field
					this.updateFieldsCount(settings);
					this.renderName(settings);
					this.renderKeys(settings);
					this.toggleCreateButton();

					// // Call the addedField callback
					this.config.addedField();
					console.log("field", field)
					this.initSelect2InAdded(field);
				}
			},
			deleteField: function (params, index) {
				var settings = params;
				console.log(this.fieldsCount > 0)
				if (this.fieldsCount > 0) {
					$(settings.container).children()[index].parentNode.removeChild($(settings.container).children()[index]);
					//console.log("node", $(settings.container).children()[index].parentNode)
					console.log("node", $(settings.container).children()[index])
					console.log("node", params)
					this.updateFieldsCount(settings);
					this.renderName(settings);
					this.renderKeys(settings);
					this.toggleCreateButton();

					this.config.deletedField();
				}
			},
			hideField: function (params, event) {
				var $self = this,
				 settings = params,
					index = $(event).attr($self.flags.delete),
					inputUpdateValue = $(event).attr($self.flags.inputNameUpdateValue),
				inputValue = $(event).attr($self.flags.inputValue);

						console.log("e.currentTarget", $(event).attr($self.flags.inputNameUpdateValue))
				console.log("e.currentTarget", $(event).attr($self.flags.inputValue))

				console.log("params", params,index)
				if (this.fieldsCount > 0) {
					var $field = $($(settings.container).children()[index]);

					//console.log("ddddddd", $field, index, $(settings.container).children().find(`input[name='${inputUpdateValue}']`))
					$field.hide(),
					$field.find(`input[name='${inputUpdateValue}']`).val(inputValue);
					//console.log("aaa", inputUpdateValue, $(settings.container).children()[index])
					
					//this.updateFieldsCount(settings);
					//this.renderName(settings);
					//this.renderKeys(settings);
					this.toggleCreateButton();

					this.config.deletedField();
				}
			},
			renderName: function (settings) {
				var $this = this;

				console.log("$this", $this)

				$(settings.container).children().each(function (i, el) {
					//var key = i + 1;
					
					$(el).find('[' + $this.flags.name + ']').each(function (index, el) {
						//console.log("key", el.placeholder)
						//el.placeholder = `${el.placeholder} ${key}`
						var name = $(el).attr($this.flags.name);
						let regex = new RegExp('{indexdata}', 'g');
						name = name.includes("{indexdata}") ? name.replace(regex, i) : name;
						$(el).attr('name', name);
						   //$(el).attr('name', name + $this.config.nameSeparator + key);
					});
				});


			},

			renderKeys: function (settings) {
				var $this = this;
				console.log(this.config.deleteTrigger)
				$(settings.container).children().find(this.config.deleteTrigger).each(function (i, el) {
					$(el).attr($this.flags.delete, i);
					console.log("ii",i)
				});

			},

			updateFieldsCount: function (settings) {
				const $this = this;
				// Object.values( $this.config.configAttributes).forEach(function (el, i) {
				$this.fieldsCount = $(settings.container).children().length;
				console.log("count",$this.fieldsCount)
				if ( $this.fieldsCount == 1) {
					$($this.config.deleteTrigger).hide()
				} else {
					$($this.config.deleteTrigger).show()
				}
				// });

			},

			toggleCreateButton: function () {
				if (this.fieldsCount < 0) {
					$(this.config.createTrigger).fadeOut(0);
				} else {
					$(this.config.createTrigger).fadeIn(0);
				}
			},

			getDataSettings: function (el) {
				return $(el).attr('data-hs-add-field-options') ? JSON.parse($(el).attr('data-hs-add-field-options')) : {};
			},
			initSelect2InAdded: function (template) {
				var ss = template.find(".select2");
				if (ss && ss.length > 0) {
					// ss is not null and has elements
					console.log("Element found:", ss);
					ss.each(function (index, obj) {
						$.CustomPluginSelect2.setSelect2InitElement(obj);

					});
				} else {
					// ss is null or empty
					console.log("Element not found or has no elements.");
				}			}

		}

	})(jQuery);
