﻿(function ($) {
    "use strict";

    $.CustomPluginDatatablesReport = {
        /**
         *
         *
         * @var Object _baseConfig
         */
        _baseConfig: {
            paging: true,
        },

        /**
         *
         *
         * @var jQuery pageCollection
         */
        pageCollection: $(),
        collections: [],

        /**
         * Initialization of Datatables wrapper.
         *
         * @param String selector (optional)
         * @param Object config (optional)
         *
         * @return jQuery pageCollection - collection of initialized items.
         */

        init: function (selector, config) {
            this.collection = selector && $(selector).length ? $(selector) : $();
            if (!$(selector).length) return;

            this.config =
                config && $.isPlainObject(config)
                    ? $.extend({}, this._baseConfig, config)
                    : this._baseConfig;

            this.config.itemSelector = selector;

            this.initDatatables();
            return this.pageCollection;
        },

        initDatatables: function () {
            //Variables
            var $self = this,
                config = $self.config,
                collection = $self.pageCollection;

            //Actions
            this.collection.each(function (i, el) {
                //Variables

                var $this = $(el),
                    $buttonConfig = $this.data("dt-button-config"),
                    $formConfig = $this.data("dt-form-config"),
                    $tableHasRowAction = $this.data("has-action"),
                    $taColDefs = $this.data("column-defs"),
                    $tableHasFilter = $this.jHasAttribute("data-has-filter"),
                    $tableServerFilter = $this.jHasAttribute("data-server-filter"),
                    $tableServerSide = $this.jHasAttribute("data-server-side"),
                    $urlTableInfo = $this.data("table-info"),
                    $urlTableInfoAll = $this.data("table-info-all");
                console.log("$taColDefs", $taColDefs)
                var table = $this.DataTable({
                    // Enable client-side searching and sorting
                    //searching: true,
                    //paging: true,
                    //ordering: true,
                    ///
                    filter: true,
                    processing: true,
                    serverSide: true,
                        scrollX: true,
                        scrollCollapse: true,
                        scrollY: 1000,
                    fixedColumns: {
                        start: 1
                    },
                    dom: '<"float-left"B><"float-left"f>rt<"row"<"col-sm-4"l><"col-sm-4"i><"col-sm-4"p>>',
                    ajax:
                    {
                        url: $urlTableInfo,
                        type: $this.data("type"),
                        contentType: 'application/json',
                        datetype: "json",
                        data: function (d) {
                            var data = $self.getCustomParams();
                            var datas = $.extend({}, d, { data });
                             console.log("dd", datas)
                            //debugger
                            return JSON.stringify(datas);
                        }
                    },

                    columnDefs: [
                        {
                            // "targets": [11, 12],
                            targets: 0 ,
                            visible: true,
                            searchable: false,
                            orderable: false,
                        },
                    ],
                    search: "#datatableSearch",
                    columns:
                        $self.getDataColume(this),
                    drawCallback: function (settings) {
                        //console.log('DataTable has been reloaded.', settings);
                        // Perform actions that depend on the DataTable being reloaded


                        // This function is called every time the table is redrawn

                        if ($tableHasRowAction === true && $self.collections.length !== 0) {
                            const b = $self.getItem('datatableReport');
                            $self.rowAction(b);
                            console.log("drawCallback", b)
                        }
                        console.log("drawCallback", settings)
                    },
                    initComplete: function (settings, json) {
                        // this for edit the data row
                        //console.log(settings)
                        if ($tableHasRowAction === true && json["data"].length !== 0) {
                            console.log($tableHasRowAction)
                            $self.rowAction(table);

                        }


                    },
                    language: {
                        "search": "_INPUT_",
                        "searchPlaceholder": "ابحث",
                        "sProcessing": `<div>
              <div class="spinner-border mr-3 text-primary" role="status">
                  <span class="sr-only">جارٍ التحميل...</span>
              </div>
              <span>جارٍ التحميل...</span></div>`,
                        "sLengthMenu": "أظهر _MENU_ مدخلات",
                        "sZeroRecords": "لم يعثر على أية سجلات",
                        "sInfo": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                        "sInfoEmpty": "يعرض 0 إلى 0 من أصل 0 سجل",
                        "sInfoFiltered": "(منتقاة من مجموع _MAX_ مُدخل)",
                        "sInfoPostFix": "",
                        "sSearch": "ابحث:",
                        "sUrl": "",
                        "oPaginate": {
                            "sFirst": "الأول",
                            "sPrevious": "السابق",
                            "sNext": "التالي",
                            "sLast": "الأخير"
                        }
                    },

                    buttons: [
                        //{
                        //    extend: 'copyHtml5',
                        //    exportOptions: {
                        //        columns: [':visible']
                        //    },
                        //    action: function (e, dt, button, config) {
                        //        exportDataWithAjax(this, '/Report/getOrdersReportAdd', 'copyHtml5', e, dt, button, config);
                        //    },
                        //},

                        {
                            extend: 'excelHtml5',
                            exportOptions: {
                                columns: [':visible']
                            },
                            action: function (e, dt, button, config) {
                                exportDataWithAjax(this, $urlTableInfoAll, 'excelHtml5', e, dt, button, config);
                            },


                        },
                        {
                            title: document.title,
                            extend: 'pdfHtml5',
                            //filename: document.title,
                            orientation: 'landscape', // 'portrait' or 'landscape'
                            pageSize: 'A4', // 'A3', 'A5', 'A6', 'legal', 'letter'
                            exportOptions: {
                                columns: ':visible',
                                search: 'applied',
                                order: 'applied'
                            },
                            action: function (e, dt, button, config) {
                                exportDataWithAjax(this, $urlTableInfoAll, 'pdfHtml5', e, dt, button, config);
                            },
                            customize: function (doc) {
                                // Remove the title created by DataTables
                                doc.content.splice(0, 1);

                                // Create a date string for the footer
                                var now = new Date();
                                var jsDate = now.getDate() + '-' + (now.getMonth() + 1) + '-' + now.getFullYear();

                                // Set page margins
                                doc.pageMargins = [10, 60, 10, 60];

                                // Set the font size for the entire document
                                doc.defaultStyle.fontSize = 6;
                                doc.defaultStyle.font = "Cairo";
                                // Set the font size for the table header
                                doc.styles.tableHeader.fontSize = 6;
                                //doc.content[0].text = doc.content[0].text.toString().split(' ').reverse().join(' ');
                                console.log('doc.content[0].text', doc);
                                // Dynamically calculate column widths
                                const numColumns = doc.content[0].table.body[0].length;
                                const columnWidthPercentage = 100 / numColumns;

                                // Create an array with the calculated width percentage for each column
                                const columnWidths = Array.from({ length: numColumns }, () => columnWidthPercentage + '%');

                                // Set column widths in the document
                                doc.content[0].table.widths = columnWidths;

                                // Custom header
                                //doc['header'] = function () {
                                //    return {
                                //        columns: [
                                //            {
                                //                alignment: 'left',
                                //                text: jsDate.toString(),
                                //                fontSize: 10
                                //            },
                                //            {
                                //                alignment: 'center',
                                //                image: imageLogoBase64,
                                //                width: 48
                                //            },
                                //            //{
                                //            //    alignment: 'left',
                                //            //    text: 'dataTables',
                                //            //    fontSize: 18,
                                //            //    margin: [10, 0]
                                //            //}
                                //        ],
                                //        margin: 20
                                //    }
                                //};

                                //// Custom footer
                                //doc['footer'] = function (page, pages) {
                                //    return {
                                //        columns: [
                                //            {
                                //                alignment: 'right',
                                //                text: ['page ', { text: page.toString() }, ' of ', { text: pages.toString() }]
                                //            },
                                //            {
                                //                alignment: 'left',
                                //                text: ['Created on: ', { text: jsDate.toString() }]
                                //            }
                                //        ],
                                //        margin: 20
                                //    }
                                //};

                                // Custom table layout
                                var objLayout = {
                                    'hLineWidth': function (i) { return .5; },
                                    'vLineWidth': function (i) { return .5; },
                                    'hLineColor': function (i) { return '#aaa'; },
                                    'vLineColor': function (i) { return '#aaa'; },
                                    'paddingLeft': function (i) { return 4; },
                                    'paddingRight': function (i) { return 4; }
                                };

                                doc.content[0].layout = objLayout;

                                // Set RTL direction for the entire document
                                doc.defaultStyle.alignment = 'right';

                                // Reverse the text in the table header and body
                                const arabic = /[\u0600-\u06FF]/;

                                //doc.content[0].table.body[0].reverse();
                                //// Reverse the table header
                                //doc.content[0].table.body[0].forEach(function (cell) {
                                //    if (arabic.test(cell.text.toString())) {
                                //        cell.text = cell.text.toString().split('  ').reverse().join('  ');
                                //        console.log("cell.text", cell.text)
                                //        // Wait for the document to be fully loaded (replace with your library's method)
                                       
                                //    }
                                //    //cell.alignment = 'right';
                                //});

                                //// Reverse the table body
                                //let bodyRows = doc.content[0].table.body.slice(1); // Exclude the header row
                                console.log("bodyRows", bodyRows);
                                console.log(" doc.content", doc);

                                doc.content[0].table.body.forEach(function (row) {
                                   
                                    row.forEach(function (cell) {
                                        cell.alignment = 'right';
                                        if (arabic.test(cell.text.toString()) && row.length <=10) {
                                            cell.text = cell.text.toString().split(' ').reverse().join('   ');
                                            console.log("cell.text", cell.text)
                                        }
                                    
                                    });
                                    row.reverse();
                                
                                });
                                
                                doc.content.unshift({

                                    text: `تم الإنشاء بتاريخ : ${jsDate.toString()}`.toString().split(' ').reverse().join('  '),
                                    margin: [0, 0, 0, 12], // Adjust margins as needed
                                    alignment: 'right',
                                    fontSize : 7

                                });
                                doc.content.unshift({

                                    text: document.title.toString().split(' ').reverse().join('  '),
                                    margin: [0, 0, 0, 12], // Adjust margins as needed
                                    alignment: 'center',
                                    fontSize: 12

                                });
                                 // Add custom header
                                doc.content.unshift({

                                    image: imageLogoBase64,
                                    margin: [0, 0, 0, 12], // Adjust margins as needed
                                    alignment: 'center',
                                    width: 75,
                                    height: 40,

                                });
                                //doc.content[0].table.body[0].forEach(function (cell) {
                                //    if (typeof cell.text === 'string' && arabic.test(cell.text)) {
                                //        cell.text = cell.text.split(' ').reverse().join('  ');
                                //        console.log("Updated cell.text:", cell.text);
                                //    } else if (Array.isArray(cell.text)) {
                                //        cell.text = cell.text.map(item => {
                                //            if (typeof item === 'string' && arabic.test(item)) {
                                //                return item.split(' ').reverse().join(' ');
                                //            }
                                //            return item;
                                //        });
                                //        console.log("Updated cell.text (array):", cell.text);
                                //    } else if (typeof cell.text === 'object') {
                                //        Object.keys(cell.text).forEach(key => {
                                //            if (typeof cell.text[key] === 'string' && arabic.test(cell.text[key])) {
                                //                cell.text[key] = cell.text[key].split(' ').reverse().join(' ');
                                //            }
                                //        });
                                //        console.log("Updated cell.text (object):", cell.text);
                                //    }
                                //});



                            //    // Reassign the reversed body rows back to the document
                               // doc.content[0].table.body = [doc.content[0].table.body[0]].concat(bodyRows);
                            }
                        },

                        //{
                        //    text: 'Custom PDF',
                        //    extend: 'pdfHtml5',
                        //    filename: 'dt_custom_pdf',
                        //    orientation: 'landscape', //portrait
                        //    pageSize: 'legal', //A3 , A5 , A6 , legal , letter
                        //    exportOptions: {
                        //        columns: ':visible',
                        //        search: 'applied',
                        //        order: 'applied'
                        //    },
                        //    customize: function (doc) {
                        //        //Remove the title created by datatTables
                        //        doc.content.splice(0, 1);
                        //        //Create a date string that we use in the footer. Format is dd-mm-yyyy
                        //        var now = new Date();
                        //        var jsDate = now.getDate() + '-' + (now.getMonth() + 1) + '-' + now.getFullYear();
                                
                        //        doc.pageMargins = [20, 60, 20, 60];
                        //        // Set the font size fot the entire document
                        //        doc.defaultStyle.fontSize = 7;
                        //        doc.defaultStyle.font = "Cairo";
                        //        // Set the fontsize for the table header
                        //        doc.styles.tableHeader.fontSize = 7;
                                

                        //        const numColumns = doc.content[0].table.body[0].length;
                        //        const columnWidthPercentage = 100 / numColumns;
                        //        console.log("Column Width Percentage:", columnWidthPercentage);

                        //        // Construct an array with the calculated width percentage for each column
                        //        const columnWidths = Array.from({ length: numColumns }, () => columnWidthPercentage.toString() + '%');
                        //        console.log("Column Widths Array:", columnWidths);
                        //        doc.content[0].table.widths = columnWidths
                                
                        //        doc['header'] = (function () {
                        //            return {
                        //                columns: [
                                            
                        //                    {
                        //                        alignment: 'left',
                        //                        italics: true,
                        //                        text: 'dataTables',
                        //                        fontSize: 18,
                        //                        margin: [10, 0]
                        //                    },
                        //                    {
                        //                        image: imageLogoBase64,
                        //                        width: 48,
                        //                        alignment: 'center',
                                                
                        //                    },
                                            
                        //                    {
                        //                        alignment: 'right',
                        //                        fontSize: 14,
                        //                        stack: [
                        //                            'Let\'s try an unordered list',
                        //                            'Let\'s try an unordered list',
                        //                            {
                        //                                ul: [
                        //                                    'item 1',
                        //                                    'item 2'
                        //                                ]
                        //                            }
                        //                        ],
                        //                    },
                        //                    //{
                        //                    //    alignment: 'right',
                        //                    //    fontSize: 14,
                        //                    //    text: 'Custom PDF export with dataTables'
                        //                    //}

                        //                ],
                        //                margin: 20
                        //            }
                        //        });
                        //        // Create a footer object with 2 columns
                        //        // Left side: report creation date
                        //        // Right side: current page and total pages
                        //        doc['footer'] = (function (page, pages) {
                        //            return {
                        //                columns: [
                        //                    {
                        //                        alignment: 'left',
                        //                        text: ['Created on: ', { text: jsDate.toString() }]
                        //                    },
                        //                    {
                        //                        alignment: 'right',
                        //                        text: ['page ', { text: page.toString() }, ' of ', { text: pages.toString() }]
                        //                    }
                        //                ],
                        //                margin: 20
                        //            }
                        //        });
                        //        // Change dataTable layout (Table styling)
                        //        // To use predefined layouts uncomment the line below and comment the custom lines below
                        //        // doc.content[0].layout = 'lightHorizontalLines'; // noBorders , headerLineOnly
                        //        var objLayout = {};
                        //        objLayout['width'] = function (i) { return 800; };
                        //        objLayout['hLineWidth'] = function (i) { return .5; };
                        //        objLayout['vLineWidth'] = function (i) { return .5; };
                        //        objLayout['hLineColor'] = function (i) { return '#aaa'; };
                        //        objLayout['vLineColor'] = function (i) { return '#aaa'; };
                        //        objLayout['paddingLeft'] = function (i) { return 4; };
                        //        objLayout['paddingRight'] = function (i) { return 4; };
                        //        doc.content[0].layout = objLayout;
                        //    }
                        //},
                        //{
                        //    text: 'Custom PDF',
                        //    extend: 'pdfHtml5',
                        //    filename: 'dt_custom_pdf',
                        //    orientation: 'landscape', //portrait
                        //    pageSize: 'A4', //A3 , A5 , A6 , legal , letter
                        //    exportOptions: {
                        //        columns: ':visible',
                        //        search: 'applied',
                        //        order: 'applied'
                        //    },
                        //    customize: function (doc) {
                        //        const arabic = /[\u0600-\u06FF]/;
                        //        doc.defaultStyle =
                        //        {
                        //            font: 'Cairo',
                        //            alignment: 'right',
                        //        };
                        //         Customize table structure
                        //        let tableBody = doc.content[1].table.body;
                        //        let columnCount = tableBody[0].length;
                        //        const numColumns = doc.content[1].table.body[0].length;
                        //        const columnWidthPercentage = 100 / numColumns;
                        //        console.log("Column Width Percentage:", columnWidthPercentage);

                        //         Construct an array with the calculated width percentage for each column
                        //        const columnWidths = Array.from({ length: numColumns }, () => columnWidthPercentage.toString() + '%');
                        //        console.log("Column Widths Array:", columnWidths);
                        //        doc.content[0].table.widths = columnWidths

                        //        var objLayout = {};
                        //        objLayout['hLineWidth'] = function (i) { return .5; };
                        //        objLayout['vLineWidth'] = function (i) { return .5; };
                        //        objLayout['hLineColor'] = function (i) { return '#aaa'; };
                        //        objLayout['vLineColor'] = function (i) { return '#aaa'; };
                        //        objLayout['paddingLeft'] = function (i) { return 4; };
                        //        objLayout['paddingRight'] = function (i) { return 4; };
                        //        doc.content[1].layout = objLayout;
                        //        doc.content[1].table = {
                        //            headerRows: 1,
                        //            rtl: true,
                        //            widths: columnWidths, // Set column widths
                        //            body: tableBody,
                        //            layout: objLayout,
                        //        };
                        //        doc.defaultStyle.alignment = 'center';

                        //        doc.styles.tableHeader.alignment = 'center';
                        //        doc.styles.title.alignment = 'center';

                        //        doc.content[1].table.headerRows = 1; // Ensure headerRows is set
                        //        doc.content[1].table.body[0].reverse();

                        //         Ensure text alignment is set to right for the header cells
                        //        doc.content[1].table.body[0].forEach(function (cell) {
                        //            if (arabic.test(cell.text.toString())) {
                        //                cell.text = cell.text.toString().split(' ').reverse().join('   ');

                        //            }
                        //            cell.alignment = 'right';
                        //            cell.fontSize = 8;
                        //            cell.width = 125;
                        //            cell.bold = true; 
                        //        });

                        //         Reverse the rows in the table body
                        //        let bodyRows = doc.content[1].table.body.slice(1); // Exclude the header row
                        //        bodyRows.reverse();
                        //        console.log("bodyRows", bodyRows)
                        //         Reverse the cells in each body row to transform right-to-left and left-to-right
                        //        bodyRows.forEach(function (row) {
                        //            row.reverse();
                        //            row.forEach(function (cell) {
                        //                 Check if the cell contains a date in the format 'YYYY-MM-DDTHH:MM:SS'
                        //                 Split date and time into two lines
                        //                if (cell.text.toString().includes('T')) {
                        //                    let dateTimeParts = cell.text.toString().split('T');
                        //                    cell.text = dateTimeParts[0] + '\n' + dateTimeParts[1];
                        //                }
                        //                console.log("arabic.test(data)", arabic.test(data), data)
                        //                if (arabic.test(cell.text.toString())) {
                        //                    return data.toString().split(' ').reverse().join(' ');
                        //                    cell.text = cell.text.toString().split(' ').reverse().join('  ');
                        //                    console.log(" cell.text", cell.text)
                        //                }

                        //                cell.alignment = 'right'; // Align text to the right
                        //                cell.fontSize = 8;
                        //                cell.width = 125;
                        //                cell.bold = true;
                        //            });
                        //        });


                        //        Reassign the reversed body rows back to the document
                        //        doc.content[1].table.body = [doc.content[1].table.body[0]].concat(bodyRows);

                        //            console.log(" doc.conten", doc)
                        //        const text = doc.content[0].text;
                        //        doc.content.unshift({
                        //            text: text,
                        //            margin: [0, 0, 0, 12], // Adjust margins as needed
                        //            alignment: 'center',
                        //            fontSize: 18, // Adjust font size as needed
                        //            bold: true, // Optionally make the header bold
                        //            dir: 'ltr'
                        //        });
                        //            // Add custom header
                        //        doc.content.unshift({

                        //            image: imageLogoBase64,
                        //            margin: [0, 0, 0, 12], // Adjust margins as needed
                        //            alignment: 'center',
                        //            width: 125,
                        //            height: 75,

                        //        });

                        //         Ensure the table fits within the page width
                        //        doc.pageMargins = [10, 10, 10, 10]; // Adjust margins as needed
                        //        doc.content[1].layout = {
                        //            hLineWidth: function (i, node) {
                        //                console.log("i1", i, node)
                        //                return (i === 0 || i === node.table.body.length) ? 1 : 0;
                        //            },
                        //            vLineWidth: function (i, node) {
                        //                console.log("i2", i, node)
                        //                return (i === 0 || i === node.table.widths.length) ? 1 : 0;
                        //            },
                        //            hLineColor: function (i, node) {
                        //                console.log("i3", i, node)
                        //                return (i === 0 || i === node.table.body.length) ? 'black' : 'gray';
                        //            },
                        //            vLineColor: function (i, node) {
                        //                console.log("i3", i, node)
                        //                return (i === 0 || i === node.table.widths.length) ? 'black' : 'gray';
                        //            }
                        //        };
                        //        doc.content[1].table = {
                        //            widths: ['*', 'auto', 100, '*'],
                        //        },
                        //        console.log("doc.content[1].table", doc.content[3])
                        //         Set _minWidth for each column
                        //        for (let i = 0; i < columnCount; i++) {

                        //            doc.content[3].table.widths[i] = {
                        //                _minWidth: 50, // Set minimum width for each column
                        //                _maxWidth: 100
                        //            };
                        //        }
                            
                       
                        //        Remove the title created by datatTables
                        //        doc.content.splice(0, 1);
                        //        //Create a date string that we use in the footer. Format is dd-mm-yyyy
                        //        var now = new Date();
                        //        var jsDate = now.getDate() + '-' + (now.getMonth() + 1) + '-' + now.getFullYear();
                        //        console.log("jsDate", jsDate,now)
                                
                        //        var logo = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAICAgICAQICAgIDAgIDAwYEAwMDAwcFBQQGCAcJCAgHCAgJCg0LCQoMCggICw8LDA0ODg8OCQsQERAOEQ0ODg7/2wBDAQIDAwMDAwcEBAcOCQgJDg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg7/wAARCAAwADADASIAAhEBAxEB/8QAGgAAAwEAAwAAAAAAAAAAAAAABwgJBgIFCv/EADUQAAEDAgQDBgUDBAMAAAAAAAECAwQFBgAHESEIEjEJEyJBUXEUI0JhgRVSYhYXMpEzcrH/xAAYAQADAQEAAAAAAAAAAAAAAAAEBQYHAv/EAC4RAAEDAgMGBQQDAAAAAAAAAAECAxEABAUGEhMhMUFRcSIyYaHBFkKB0ZGx8P/aAAwDAQACEQMRAD8Avy44hlhTrqw22kEqUo6BIG5JPkMSxz67RlFPzFquWnDParOaN4QVlmqXDKcKKLS19CCsf8qh6A6e+OfaK573LDTanDJllVV0q8r3ZVIuGqR1fMpdJSdHCCOinN0j7e+FjymydjRKdSbGsikpbSlG5O3/AHfeX5nU6knck6DFdg+DovkquLlWllHE8yeg+f4FBPvluEpEqNC657/4yr4ecm3ZxH1OghzxfptpQERI7X8QrqdPXGNpucXGLltU0SbZ4jazW0tHX4C6IiJcd37HUEj8YoHNtTKOzwuHVPj79rTfhkfCudxEbUOqQQd9Pc4HlaoGRt2JVAcptRsOe54WZZkd6yFHpzakgD3098ahYWuVVDQ/YrKD9wJnvGqfb8UAHH584npWw4eu0+iVO+6Vl3xO2zHy1uKa4GafdcBwqos5w7AOE6lgk+epT68uK8MvNPxmnmHEvMuJCm3EKCkqSRqCCNiCPPHmbzdyWcozkq1rpitVSkzGyqHNbT4HU+S0H6Vp22/9Bw8XZkcQ1wuzLg4V8yqq5U69a0X42zalJXq5NpeuhZJO5LWo0/idPpxI5ryszgyG77D3Nrau+U8weh/cDgQRI3sGXi54VCCKXK6Ku5fnbOcTt2znO/8A0SfFtymcx17llpGqgPTUjDj5WOIOUmYFPpLgjXQ5ES627r43I6R40I9D16fuGEfzPZeyq7afiRtec0W03O/GuSj82wdbdb8ZB89FEjb0xvrIzGk2pmnSrgcdUttl3lkoB2UyrZadPbf8DFFhGHuX+W0bASUyY6kKJg96XPK0XJmt9MrkFuIQw2XNup8IwFbruVaWXkttMgadCCcEfNuPTbbzPkiK87+jVRsTqctlIKVNubkD2J/0RgBVFDVQUpTTEksjdTjpG4xc4TYOvBu5AhB3yf8AcfmgTIUUmiMxcs27+CG42Koy3JqFqym3YLytebuVfRr9gVD2AwvOWt5u2f2qXDle0FK4UhVwijzgFbPMSUlBSftqdcMAqN/TfCVV0yGBDl3O+huMwvZXw6Oqzr67n8jC85VWw/fnakZD2tAaL/wtwGsSuTfu2YyCeY+6ikY5x1yzVlDECB4C8Nn3lEx6SFe9MWtW3R1jfVTu0l4a7lv6wbaz8yqp6p2Z2X6FmXT2U6uVelq8TrQA3UtG6gPMFQG+mJe2Xf8ASL5s1qp0p35qfDLhuHR2M4P8kLT5aH/ePUSpIUnQjUemJh8SXZs2fmVf8/MvJevKyfzNkEuTPhGeamVNZ3JeZGnKonqpPXqQTjE8tZmdwF4hSdbSjvHMHqP1zo24tw8J4EUn9MvWz7iymo9tX27PgTqQ4tMCfGY735SuiFdenTTTyGOIrGV1DSJLCqndb7Z1aamIDEZJHQqGg5vyDga3Fw28bVhS1wqrlHAzAjtkhFSt2sIQHR5HkXoQftjrqJw5cYt81BESDkuxaCVnRU24K0Fpb+/I3qT7Y1b6kygptSi88lKiSWxIEkyRygE8tUUDsbieA71mM2M0mZxlVytTQ0w0jkQlIIQ2PpabR1JJ6Abk4oP2bHDhW6O9WuITMKlLplxV9hMeg06Sn5lPgjdIUPJayedX4HljvOHvs16VbF7Uy/c86/8A3DuyIoOwoAaDdPgL66ts7gqH7lan2xVaJEjQaezFiMIjx2khLbaBoEgYyzMmZTjWi2t0bK3b8qfk+v8AW/jNMGWdn4lGVGv/2SAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA=';
                        //        doc.pageMargins = [20, 60, 20, 30];
                        //        // Set the font size fot the entire document
                        //        doc.defaultStyle.fontSize = 7;
                        //        // Set the fontsize for the table header
                        //        doc.styles.tableHeader.fontSize = 7;
                        //        // Create a header object with 3 columns
                        //        // Left side: Logo
                        //        // Middle: brandname
                        //        // Right side: A document title
                        //        doc['header'] = (function () {
                        //            return {
                        //                columns: [
                        //                    {
                        //                        image: logo,
                        //                        width: 24
                        //                    },
                        //                    {
                        //                        alignment: 'left',
                        //                        italics: true,
                        //                        text: 'dataTables',
                        //                        fontSize: 18,
                        //                        margin: [1, 0]
                        //                    },
                        //                    {
                        //                        alignment: 'right',
                        //                        text: ['Created on: ', { text: jsDate.toString() }]
                        //                    },
                        //                    {
                        //                        alignment: 'right',
                        //                        fontSize: 14,
                        //                        text: 'Custom PDF export with dataTables'
                        //                    },
                                            
                                            
                        //                ],
                        //                margin: 20
                        //            }
                        //        });
                        //        // Create a footer object with 2 columns
                        //        // Left side: report creation date
                        //        // Right side: current page and total pages
                        //        doc['footer'] = (function (page, pages) {
                        //            console.log("page, pages", page, pages)
                        //            return {
                        //                columns: [
                        //                    {
                        //                        alignment: 'right',
                        //                        text: ['Created on: ', { text: jsDate.toString() }]
                        //                    },
                        //                    {
                        //                        alignment: 'right',
                        //                        text: ['page ', { text: page.toString() }, ' of ', { text: pages.toString() }]
                        //                    }
                        //                ],
                        //                margin: 20
                        //            }
                        //        });
                        //        // Change dataTable layout (Table styling)
                        //        // To use predefined layouts uncomment the line below and comment the custom lines below
                        //        // doc.content[0].layout = 'lightHorizontalLines'; // noBorders , headerLineOnly
                        //        var objLayout = {};
                        //        objLayout['hLineWidth'] = function (i) { return .5; };
                        //        objLayout['vLineWidth'] = function (i) { return .5; };
                        //        objLayout['hLineColor'] = function (i) { return '#aaa'; };
                        //        objLayout['vLineColor'] = function (i) { return '#aaa'; };
                        //        objLayout['paddingLeft'] = function (i) { return 4; };
                        //        objLayout['paddingRight'] = function (i) { return 4; };
                        //        doc.content[1].layout = objLayout;
                        //    }
                        //},
                        //{
                        //    extend: 'pdfHtml5',
                        //    charset: 'utf-8',
                        //    orientation: 'portrait',
                        //    orientation: 'landscape',
                        //    pageSize: 'A4',
                        //    title: document.title,
                        //    action: function (e, dt, button, config) {
                        //        //testElement();
                        //        console.log("e", e)
                        //        console.log("dt",dt)
                        //        console.log("button", button)
                        //        console.log("config", config.title)
                        //        pdfMake.createPdf(e).open();
                        //        //exportDataWithAjax(this, $urlTableInfoAll, 'pdfHtml5', e, dt, button, config);
                        //    },

                        //    exportOptions: {
                        //        columns: [':visible'],
                        //         optional space between columns
                        //        columnGap: 10,
                        //        width: '10%',
                        //    },

                        //    customize: function (doc) {
                        //        doc.styles['td:nth-child(2)'] = {
                        //            width: '100px',
                        //            'max-width': '100px'
                        //        }
                        //        console.log("doc.content", doc)
                        //        const arabic = /[\u0600-\u06FF]/;
                        //        doc.content[0].text = doc.content[0].text.split(' ').reverse().join(' ');
                        //        doc.defaultStyle =
                        //        {
                        //            font: 'Cairo',
                        //            alignment: 'right',
                        //        };
                                
                        //         Customize table structure
                        //        let tableBody = doc.content[1].table.body;
                        //        let columnCount = tableBody[0].length;

                        //        doc.content[1].table = {
                        //            headerRows: 1,
                        //            rtl: true,
                        //            widths: [90,90,90,90,90,90,90,90], // Set column widths
                        //            body: tableBody,
                        //            layout: 'lightHorizontalLines',
                        //        };
                        //        doc.defaultStyle.alignment = 'center';
                                
                        //        doc.styles.tableHeader.alignment = 'center';
                        //        doc.styles.title.alignment = 'center';
                                
                        //        doc.content[1].table.headerRows = 1; // Ensure headerRows is set
                        //        doc.content[1].table.body[0].reverse();

                        //         Ensure text alignment is set to right for the header cells
                        //        doc.content[1].table.body[0].forEach(function (cell) {
                        //            if (arabic.test(cell.text.toString())) {
                        //                cell.text = cell.text.toString().split(' ').reverse().join('   ');
                                      
                        //            }
                        //            cell.alignment = 'right';
                        //            cell.fontSize = 10;
                        //            cell.width= 125;
                        //        });

                        //         Reverse the rows in the table body
                        //        let bodyRows = doc.content[1].table.body.slice(1); // Exclude the header row
                        //        bodyRows.reverse();
                        //        console.log("bodyRows", bodyRows)
                        //         Reverse the cells in each body row to transform right-to-left and left-to-right
                        //        bodyRows.forEach(function (row) {
                        //            row.reverse();
                        //            row.forEach(function (cell) {
                                       
                        //                console.log("arabic.test(data)", arabic.test(data), data)
                        //                if (arabic.test(cell.text.toString())) {
                        //                    return data.toString().split(' ').reverse().join(' ');
                        //                    cell.text = cell.text.toString().split(' ').reverse().join('  ');
                        //                    console.log(" cell.text", cell.text)
                        //                }
                                        
                        //                cell.alignment = 'right'; // Align text to the right
                        //                cell.fontSize = 10;
                        //                cell.width = 125;
                        //            });
                        //        });


                        //        Reassign the reversed body rows back to the document
                        //        doc.content[1].table.body = [doc.content[1].table.body[0]].concat(bodyRows);
                            
                        //        console.log(" doc.conten", doc)
                        //        const text = doc.content[0].text;
                        //        doc.content.unshift({
                        //            text: text,
                        //            margin: [0, 0, 0, 12], // Adjust margins as needed
                        //            alignment: 'center',
                        //            fontSize: 18, // Adjust font size as needed
                        //            bold: true, // Optionally make the header bold
                        //            dir: 'ltr'
                        //        });
                        //        // Add custom header
                        //        doc.content.unshift({

                        //            image: imageLogoBase64,
                        //            margin: [0, 0, 0, 12], // Adjust margins as needed
                        //            alignment: 'center',
                        //            width: 125,
                        //            height: 75,

                        //        });

                        //         Ensure the table fits within the page width
                        //        doc.pageMargins = [40, 60, 40, 60]; // Adjust margins as needed
                        //        doc.content[1].layout = {
                        //            hLineWidth: function (i, node) {
                        //                console.log("i1",i,node)
                        //                return (i === 0 || i === node.table.body.length) ? 1 : 0;
                        //            },
                        //            vLineWidth: function (i, node) {
                        //                console.log("i2", i, node)
                        //                return (i === 0 || i === node.table.widths.length) ? 1 : 0;
                        //            },
                        //            hLineColor: function (i, node) {
                        //                console.log("i3", i, node)
                        //                return (i === 0 || i === node.table.body.length) ? 'black' : 'gray';
                        //            },
                        //            vLineColor: function (i, node) {
                        //                console.log("i3", i, node)
                        //                return (i === 0 || i === node.table.widths.length) ? 'black' : 'gray';
                        //            }
                        //        };
                        //        doc.content[1].table = {
                        //            widths: ['*', 'auto', 100, '*'],
                        //        },
                        //        console.log("doc.content[1].table", doc.content[3])
                        //         Set _minWidth for each column
                        //        for (let i = 0; i < columnCount; i++) {
                                    
                        //            doc.content[3].table.widths[i] = {
                        //                _minWidth: 50, // Set minimum width for each column
                        //                _maxWidth: 100
                        //            };
                        //        }
                        //    },
                        //},
                        {
                            extend: 'csvHtml5',
                            text: '<i class="fa fa-file-text-o"></i> CSV',
                            exportOptions: {
                                columns: [':visible']
                            },
                            charset: 'UTF-8',
                            fieldSeparator: ';',
                            bom: true,
                            init: function (api, node, config) {
                                $(node).removeClass('btn-default')
                            },
                            action: function (e, dt, button, config) {
                                exportDataWithAjax(this, $urlTableInfoAll, 'csvHtml5', e, dt, button, config);
                            },
                        },
                        {
                            extend: 'print',
                            orientation: 'portrait',
                            orientation: 'landscape',
                            pageSize: 'A0',

                            exportOptions: {
                                columns: [':visible'],

                            },
                            //action: function (e, dt, button, config) {
                            //    console.log("e",e)
                            //  //exportDataWithAjax(this, '/Report/getOrdersReportAdd', 'print', e, dt, button, config);
                            //},

                            customize: function (doc) {
                                console.log("doc", doc.document.body)
                                console.log("doc", doc)
                                //$(doc.document.body)
                                //    .css('font-size', '10pt')
                                //    .prepend(
                                //        '<img src="" style="position:absolute; top:0; left:0;" />'
                                //    );
                                //$(doc.document.body).find('table')
                                //    .addClass('compact')
                                //    .css('font-size', 'inherit');
                                //$(doc.document.body).find('th, td')
                                //    .css('width', '5%');

                            }



                        },
                        {
                            extend: 'colvis',
                            columnText: function (dt, idx, title) {
                                //         console.log(dt, idx, title)
                                const toggleButton = `<div class="col-md-3 mb-2">
            <div class="custom-control custom-switch">
                <input type="checkbox" class="custom-control-input" id="${idx}" value="true">
                <label class="custom-control-label" for="${idx}">${title}</label>
            </div>
        </div>`
                                return toggleButton;
                            }
                        }
                    ],
                    
                   
                });


                table.columns.adjust().draw();
                $self.customSearch(table)
                $self.ShowOrHideColumn($this)
                $self.collections.push({
                    $el: el,
                    id: el.id || null,
                    $initializedEl: table
                });
            });

        },
        getItem(t) {
            if ("number" == typeof t) {
                console.log("number", t)
                console.log("yes", this.collection[t].$initializedEl)
                return this.collections[t].$initializedEl
            } else {
                console.log("no",this )
                return this.collections.find((e) => e.id === t).$initializedEl
            }
        },
        getCustomParams: function () {
            // Define your custom parameters here
            return {

            };
        },
        getDataColume: function ($this) {
            var $value = $($this).children().children().children(),
                colData = [];
            var count = 0;
            $value.each(function (i, el) {
                if (this.hasAttribute("data-th-name")) {
                    var ne = $(el).data("th-name");
                    colData[i] = {
                        data: ne,
                        name: ne.charAt(0).toUpperCase() + ne.slice(1),
                        autoWidth: true,
                        render: function (data, type, row, meta) {

                            // Get the th element
                            let regex = new RegExp('{rowdata}', 'g');
                            // Check if data-th-render attribute exists
                            if (el.hasAttribute("data-th-render")) {
                                // Use the data-th-render attribute content to render the data
                                var template = $(el).data("th-render");

                                if (!Object.is(data, undefined) && Object.is(data, null)) {
                                    if (data.length > 100) {
                                        // If data is not a string, convert it
                                        if (typeof data !== 'string') {
                                            data = data.toString();
                                        }
                                        // Replace placeholders in the template with the actual data value
                                        const lineBreaks = data.split('\n').length - 1;

                                        if (lineBreaks > 0) {
                                            data = data.replace(/\n/g, ' ');
                                        }

                                    }
                                }
                                return template.includes("{rowdata}") ? template.replace(regex, data) : template;
                            }
                            // Check if data-th-custom-render attribute exists
                            else if (el.hasAttribute("data-th-custom-render")) {
                                // Parse the content of data-th-custom-render attribute into an object
                                var cR = JSON.stringify($(el).data("th-custom-render"));
                                var customRender = JSON.parse(cR);
                                let stringWithoutSpaces = data.toString().toLowerCase().replace(/\s/g, '');
                                console.log(customRender[stringWithoutSpaces]);
                                // debugger
                                // Use the custom render object to map the data value to a specific template
                                console.log("aa", stringWithoutSpaces)
                                return customRender[stringWithoutSpaces] ? customRender[stringWithoutSpaces] : data;
                            }
                            else if (el.hasAttribute("data-th-condition") && el.hasAttribute("data-th-condition-render")) {
                                const sd = $(el).data("th-condition"),
                                    sds = JSON.stringify($(el).data("th-condition-render")),
                                    cDR = JSON.parse(sds),

                                    tem = cDR[row[sd]] === undefined || cDR[row[sd]] === null ? cDR["def"] : cDR[row[sd]];
                                return tem.includes("{rowdata}") ? tem.replace(regex, data) : tem;
                            } else if (el.hasAttribute("data-th-condition") && el.hasAttribute("data-th-custom-render-configure")) {
                                const sd = $(el).data("th-condition"),
                                    sds = JSON.stringify(configureDatatable[$(el).data("th-custom-render-configure")]),
                                    cDR = JSON.parse(sds),

                                    tem = cDR[row[sd]] === undefined || cDR[row[sd]] === null ? cDR["def"] : cDR[row[sd]];
                                return tem.includes("{rowdata}") ? tem.replace(regex, data) : tem;
                            } else if (el.hasAttribute("data-th-name") && el.hasAttribute("data-th-custom-render-configure")) {
                                // Parse the content of data-th-custom-render attribute into an object

                                var cR = JSON.stringify(configureDatatable[$(el).data("th-custom-render-configure")]);
                                var customRender = JSON.parse(cR);
                                let stringWithoutSpaces = data.toString().toLowerCase().replace(/\s/g, '');
                                console.log(customRender[stringWithoutSpaces])
                                // Use the custom render object to map the data value to a specific template
                                // return data;
                                return customRender[stringWithoutSpaces] ? customRender[stringWithoutSpaces] : data;
                            }
                            else if (Array.isArray(row[ne])) {
                                const ds = [];
                                const arrName = $(el).data("arr-name");
                                row[ne].forEach(function (o, i) {
                                    //console.log(o[arrName]);
                                    ds.push(o[arrName])


                                });
                                return ds;
                            } else {
                                // If data-th-render attribute doesn't exist, use the default data

                                return data;
                            }

                        }
                    };
                }
            });

            // console.log("colData",colData)
            return colData;

        },
     
        customSearch: function (t) {
        var o = t.settings().init();

        $(o.search).on("keyup", function (e) {
            var trimmedValue = this.value.trim();
            if (trimmedValue) {
            t.search(trimmedValue).draw();
            }
           
        });

        $(o.search).on("input", function (c) {
            if (!c.target.value) {
                t.search("").draw();
               
            }
        });
    },

        rowAction: function (t) {
            console.log("rowAction", $(t.context[0].nTable).data("column-defs"))
            const rowId = $(t.context[0].nTable).data("column-defs")[0].targets[0];
            t.context[0].aoData.forEach(function (e, i) {
                $($(e.anCells)[rowId]).children().attr("href")
                var href = $($(e.anCells)[rowId]).children().attr("href");
                $(e.nTr).attr("data-href", href)
                // e.nTr.setAttribute("class", "democlass");

                $($(e.anCells)[rowId]).css('background-color', '#f8f9fa');

            })
            // Add click event handler

            $(t.context[0].nTBody).on('click', 'tr', function (event) {
                // Get data from the first cell in the row (adjust index as needed)
                // Find the closest row to the clicked element
                // Check if the clicked element is a button
                if (event.target.tagName.toLowerCase() === 'button' || event.target.tagName.toLowerCase() === "a") {
                    event.target.tagName
                    console.log("event.target.tagName", event.target.tagName)
                    return;
                }
                event.stopImmediatePropagation();
                const clickedRow = event.target.closest('tr');


                console.log('clickedRow', event)
                // Do not proceed if the click was outside of a row
                if (clickedRow) {
                    //clickedRow.getAttribute("data-href")
                    //window.location.href = clickedRow.getAttribute("data-href");
                    const newUrl = clickedRow.getAttribute("data-href"); // Get the current URL
                    window.open(newUrl, '_blank');
                    return;
                }
            });
            //Add hover effect
            $(t.context[0].nTBody).on('mouseover', 'tr', function () {
                $(this).css('background-color', '#f8f9fa');
                $(this).css('cursor', 'pointer');
                var lastTd = $(this).find('td:last');
                lastTd.children().addClass("btn-white-radius");
            });

            $(t.context[0].nTBody).on('mouseout', 'tr', function () {
                $(this).css('background-color', '');
                var lastTd = $(this).find('td:last');
                lastTd.children().removeClass("btn-white-radius")
            });
        },
        // Show or hide columns dynamically
        ShowOrHideColumn: function (table) {
            var $value = table.children().children().children("th");
            // console.log(table)

            $value.each(function (i, el) {
                var ne = el.innerText;
                // const toggleButton = `<a class="toggle-vis" data-column="${i}">${ne}</a> -`
                // console.log("el", el)
                const toggleButton = `<div class="dropdown-item">
            <div class="custom-control custom-switch m-0">
                <input type="checkbox" class="custom-control-input toggle-vis" data-column="${i}" id="${i}" value="true">
                <label class="custom-control-label" for="${i}">${ne}</label>
            </div>
        </div>`
                console.log(toggleButton,i)
                $("#columnSelector").append(toggleButton)
            });
            document.querySelectorAll('input.toggle-vis').forEach((el) => {
                //console.log(el)
                el.addEventListener('change', function (e) {
                    e.preventDefault();


                    let columnIdx = parseInt(e.target.getAttribute('data-column')) + 1;
                    let column = $(table).DataTable().column(columnIdx);
                    console.log("columnIdx", columnIdx)
                    // Toggle the visibility
                    column.visible(!column.visible());
                });
            });
        },
    };
})(jQuery);


const downloadContent = document.getElementById('download-content');
const downloadNotification = document.getElementById('download-notification');
const cancelButton = document.getElementById('cancel-button');
let downloadItems = [];
let xhr = null; // To store the current XMLHttpRequest object

// Function to add a download item
function addDownloadItem(filename) {
    const downloadItem = document.createElement('div');
    downloadItem.className = 'download-item';
    downloadItem.innerHTML = `
           
<div>
<i class="fe fe-file fe-16 pr-1"></i>
         <span>${filename}</span>
    </div>
            <div class="circular-progress">
                <svg class="progress-circle" width="30" height="30">
                    <circle cx="15" cy="15" r="14"></circle>
                    <circle cx="15" cy="15" r="14" id="progress-bar"></circle>
                </svg>
                <div class="progress-text" id="progress-percentage">0%</div>
            </div>
        `;
    downloadContent.appendChild(downloadItem);
    downloadItems.push(downloadItem);
}

// Function to update the download percentage
function updateDownloadPercentage(downloadItem, percentComplete) {
    const progressBar = downloadItem.querySelector('#progress-bar');
    const percentageSpan = downloadItem.querySelector('#progress-percentage');
    const dashoffset = 94.2 - (percentComplete / 100) * 94.2; // Adjusted for 30px diameter
    progressBar.style.strokeDashoffset = dashoffset;
    percentageSpan.textContent = `${Math.round(percentComplete)}%`;
}

// Function to complete a download item
function completeDownload(downloadItem, name) {
    updateDownloadPercentage(downloadItem, 100);
    downloadItem.querySelector('#progress-bar').style.stroke = '#3ad29f';
    downloadItem.querySelector('span').textContent = 'اكتمل التنزيل ل' + name;
}

// Show download notification box
function showDownloadNotification() {
    downloadNotification.style.display = 'block';
}

// Hide download notification box
function hideDownloadNotification() {
    downloadNotification.style.display = 'none';
    downloadContent.innerHTML = '';
    downloadItems = [];
}

// Handle cancel button
cancelButton.addEventListener('click', () => {
    if (xhr) {
        xhr.abort(); // Cancel the current XMLHttpRequest
    }
    hideDownloadNotification();
});

// Function to handle AJAX download with progress and notifications
function exportDataWithAjax($this, url, btnName, e, dt, button, config) {
    // Show the loading spinner
    showDownloadNotification();
    addDownloadItem(config.title); // Replace 'file.zip' with actual filename

    // Get the latest download item
    const downloadItem = downloadItems[downloadItems.length - 1];

    // Create a new XMLHttpRequest object
    xhr = new XMLHttpRequest();

    // Configure it: GET-request for the URL
    xhr.open('GET', url, true);
    xhr.responseType = 'json';

    // Update the progress bar
    xhr.onprogress = function (event) {
        console.log("event", event)
        //if (event.lengthComputable) {
        const percentComplete = (event.loaded / event.total) * 100;
        updateDownloadPercentage(downloadItem, percentComplete);
        //}
    };

    // When the request is complete
    xhr.onload = function () {
        if (xhr.status === 200) {

            // Success: process the data
            var response = xhr.response;
            var data = response.data; // Assuming your data is in a 'data' property

            // Create a DataTable instance with the fetched data
            //.clear()
            dt.clear().rows.add(data);
            console.log("data", data)
            $.fn.DataTable.ext.buttons[btnName].action.call($this, e, dt, button, config);

            // Success: process the data
            completeDownload(downloadItem, config.title);

            // Hide notification after a short delay
            setTimeout(hideDownloadNotification, 3000);
        } else {
            console.error('Error fetching data:', xhr.statusText);
            hideDownloadNotification();
        }
    };

    // Handle network errors
    xhr.onerror = function () {
        console.error('Network error');
        hideDownloadNotification();
    };

    // Handle request abort (cancel)
    xhr.onabort = function () {
        console.log('Download cancelled');
    };

    // Send the request
    xhr.send();
}

// Event listener for the download button
//    document.getElementById('start-download').addEventListener('click', function () {
//        exportDataWithAjax('/api/Download/getOrdersReportAdd');
//    });
//});

function testElement() {
    // console.log("dt",dt)
    showDownloadNotification();
    addDownloadItem('file.zip'); // Replace 'file.zip' with actual filename

    // Get the latest download item
    const downloadItem = downloadItems[downloadItems.length - 1];
    var loaded = 2000;
    function disconnectWrapper() {
        console.log("loaded", loaded)
        if (loaded <= 10000) {
            var percentComplete = (loaded / 10000) * 100;
            updateDownloadPercentage(downloadItem, percentComplete);
            loaded = loaded + 2000;
        } else {
            // Mark download as complete
            completeDownload(downloadItem);
        }
    }
    // Set up the interval to call disconnectWrapper every 10 seconds
    setInterval(disconnectWrapper, 3000);
}





function getBase64FromImage(imageUrl) {
    const img = new Image();
    img.src = imageUrl;

    img.onload = function () {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");
        canvas.width = this.width;
        canvas.height = this.height;
        ctx.drawImage(this, 0, 0);

        const base64String = canvas.toDataURL();
        // Do something with the base64String
        console.log(base64String);
    };

    img.onerror = function (error) {
        console.error("Error loading image:", error);
    };
}

//const imageUrl = "/assets/images/logoD.png"; // Replace with your image URL
//getBase64FromImage(imageUrl);
console.log("ss", getBase64FromImage(imageUrl))

var imageLogoBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAADJIAAAZOCAYAAACiTegnAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAALiMAAC4jAXilP3YAAP+lSURBVHhe7P1vbFz3fSf+2pbFSBYZjqqGa0raaGivyQTRVqM/d5Umdknq4jZKWsnjdi+c2g9EAQ6sxQV+pooY2Ac/mJSBi92Fg5C+Dy5kxIAoXDixgUVFW60h94E5rNPcqJDD0VbFlvItOOraksFW1bCiapey4cuv8mVWdmyLHM4Mz8y8XgAx5/OZPIqHZw6PPu/zuQ0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKBG3B5fAQAAACAx0hvXpcNPOO7Z1dZzozmvtaUptb0rlYnl5ypcvFYIP7G8LT9ZzBevzhWLV68X85NX8rENsGQL56Wbz1XBtq71mfUtq1Ox/Fy5M9O5eHjbzeelhXPVjTcAAAAAoIY1NTWlv/CFL9y4f9bc3Pyre/2rVq1K3XXXXYu61w+QZFevXv3Vvf4PP/yw+********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";
