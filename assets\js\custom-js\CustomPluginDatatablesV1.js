﻿
(function ($) {
  "use strict";

  $.CustomPluginDatatablesV1 = {
    /**
     *
     *
     * @var Object _baseConfig
     */
    _baseConfig: {
      paging: true,
    },

    /**
     *
     *
     * @var jQuery pageCollection
     */
    pageCollection: $(),
    collections: [],
    /**
     * Initialization of Datatables wrapper.
     *
     * @param String selector (optional)
     * @param Object config (optional)
     *
     * @return jQuery pageCollection - collection of initialized items.
     */

    init: function (selector, config) {
      this.collection = selector && $(selector).length ? $(selector) : $();
      if (!$(selector).length) return;

      this.config =
        config && $.isPlainObject(config)
          ? $.extend({}, this._baseConfig, config)
          : this._baseConfig;

      this.config.itemSelector = selector;

      this.initDatatables();

      return this.pageCollection;
    },

    initDatatables: function () {
      //Variables
      var $self = this,
        config = $self.config,
        collection = $self.pageCollection;

      //Actions
      this.collection.each(function (i, el) {
        //Variables
        
          var $this = $(el),
              $buttonConfig = $this.data("dt-button-config"),
              $formConfig = $this.data("dt-form-config"),
              $tableHasRowAction = $this.data("has-action"),
              $taColDefs = $this.data("column-defs"),
              $tableHasFilter = $this.jHasAttribute("data-has-filter"),
              $tableServerFilter = $this.jHasAttribute("data-server-filter"),
              $tableServerSide = $this.jHasAttribute("data-server-side"),
              $tableScrollY = $this.jHasAttribute("data-scroll-y");

          
          //console.log("$tableServerFilter", $tableServerFilter);
          //console.log("$tableServerFilter", $this.data("type"));
          if ($.fn.DataTable.isDataTable($this)) {
              // Destroy the existing DataTable instance
              $this.DataTable().destroy();
          }
        var table = $this.DataTable({
          filter: true,
            processing: true,
            serverSide: $tableServerSide ? $this.data("server-side"):false,
            responsive: true,
            scrollX: true,
            scrollCollapse: true,
            scrollY: $tableScrollY ? $this.data("scroll-y") :1000,
            // scrollCollapse: true,
            // autoWidth: true, // Enable auto-width
            // "scrolly": true,
            //     "scrollX": true,
            //     "scrollCollapse": true,
            //     // "paging": false,
            //     // "fixedColumns": true,
            //     // "order": [],
            //     // "autoWidth": true ,
            // "columnDefs": [{
            //   // "targets": [0, 4, 9],
            //   "width": "5%",
            //   // "orderable": false
            // }],
          
          dom: '<"float-left"B><"float-left"f>rt<"row"<"col-sm-4"l><"col-sm-4"i><"col-sm-4"p>>',
            ajax: $tableServerFilter?{
            url: $this.data("table-info"),
              type: $this.data("type"),
              contentType: "application/json; application/x-www-form-urlencoded; charset=UTF-8",
              datetype: "json",
                data: function (d) {
                    //console.log("ff", isValidJSON($this.attr('data-server-filter')))
                    if (isValidJSON($this.attr('data-server-filter'))) {
                        const $dataTableServerFilter = $this.attr('data-server-filter') ? JSON.parse($this.attr('data-server-filter')) : {};
                        //console.log($dataTableServerFilter);
                        d[$this.attr('data-model')] = $dataTableServerFilter;
                        //console.log("dd",d)
                    } else {
                        var data = $self.getCustomParams();
                        d = $.extend({}, d, { data });
                        //console.log("dd", d)
                        //debugger
                    }
                    
                  return JSON.stringify(d);
                },
                 error: function (xhr, error, thrown) {
                    //alert('An error occurred while loading the data. Please try again later.');
                    console.error('Error:', error);
                    console.error('Thrown:', thrown);
                    console.dir(xhr);
                }
            } : {
                url: $this.data("table-info"),
                type: $this.data("type"),
                contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                    datetype: "json",
                    error: function (xhr, error, thrown) {
                        //alert('An error occurred while loading the data. Please try again later.');
                        $('.dataTables_processing').hide();
                        console.log('Error:', error);
                        console.log('Thrown:', thrown);
                        console.dir(xhr);
                        console.log("status", xhr['status']);
                        console.log("this", this);
                    }
            },
        
            columnDefs: $this.jHasAttribute("data-column-defs") ? $taColDefs : [
            {
              // "targets": [11, 12],
              visible: true,
              searchable: false,
                    orderable: false,
                   

            },
          ],
            search: $this.jHasAttribute("data-search-id") ? $this.data("search-id"):"#datatableSearch",
          columns: 
          $self.getDataColume(this),
            drawCallback: function (settings) {
                //console.log('DataTable has been reloaded.', settings);
                // Perform actions that depend on the DataTable being reloaded
                
                if ($tableHasRowAction === true && $self.collections.length !==0) {
                    const b = $self.reloadDataTable();
                    $self.rowAction(b);
                   // console.log("drawCallback", b)
                }
            },
          initComplete: function (settings, json) {
     
              if ($tableHasRowAction === true && json["data"].length !== 0) {
                  //console.log($tableHasRowAction)
                  $self.rowAction(table);
                  //console.log("initComplete", table)
                  //debugger
                  //console.log(settings, json)
              }
              if ($tableHasFilter) {
                  $self.filterDataTable(table);
              }
              if (json['customData']) {
                  //console.log("table", json['customData'])

                  var nameDisplayItem = $('[data-custom-display-name]'),
                      jsonCustomData = json['customData'];

                  nameDisplayItem.each(function (i, v) {
                      const dataNameItem = $(this).data("custom-display-name"),
                          textItem = jsonCustomData[dataNameItem];
                          $(this).text(textItem);

                  })
                  
              }
              
              
          },
          language: {
            "search": "_INPUT_",
                "searchPlaceholder": "ابحث",
              "sProcessing": `<div>
              <div class="spinner-border mr-3 text-primary" role="status">
                  <span class="sr-only">جارٍ التحميل...</span>
              </div>
              <span>جارٍ التحميل...</span></div>`,
                "sLengthMenu": "أظهر _MENU_ مدخلات",
                "sZeroRecords": "لم يعثر على أية سجلات",
                "sInfo": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                "sInfoEmpty": "يعرض 0 إلى 0 من أصل 0 سجل",
                "sInfoFiltered": "(منتقاة من مجموع _MAX_ مُدخل)",
                "sInfoPostFix": "",
                "sSearch": "ابحث:",
                "sUrl": "",
                "oPaginate": {
                    "sFirst": "الأول",
                    "sPrevious": "السابق",
                    "sNext": "التالي",
                    "sLast": "الأخير"
                }
            },
         
            // paging: false,
            scrollCollapse: true,
            fixedColumns: {
                start: 2
            },

        
      });

          //if ($tableServerFilter) {
          //    $self.reloadDataTable(table)
          //    console.log("table",table)
          //}

          //console.log(table)
          $self.customSearch(table)
          $self.actionWithData(table)
          
          $self.collections.push({
              $el: el,
              id: el.id || null,
              $initializedEl: table
          });
          
        //Actions
        collection = collection.add($this);
      });
    },
    getDataColume: function ($this) {
      var $value = $($this).children().children().children(),
            colData = [];
                
        var count=0; 
        $value.each(function (i, el) {
            
        if (this.hasAttribute("data-th-name")) {
          var ne = $(el).data("th-name");
          colData[i] = {
              data: ne,
              name: ne.charAt(0).toUpperCase() + ne.slice(1),
              autoWidth: true,
              
              render: function (data, type, row, meta) {
                  
                 // console.log('meta', meta.settings.json)
                  // Get the th element
                 
                  let regex = new RegExp('{rowdata}', 'g');
                  // Check if data-th-render attribute exists
                  if (el.hasAttribute("data-th-render")) {
                      // Use the data-th-render attribute content to render the data
                      var template = $(el).data("th-render");
                      
                      if (Object.is(data, undefined) && Object.is(data, null)) {
                          console.log("data", !Object.is(data, undefined) && Object.is(data, null));
                          console.log("data", data, type, row, meta);
                          if (data.length > 100) {
                              // If data is not a string, convert it
                              if (typeof data !== 'string') {
                                  data = data.toString();
                              }
                              // Replace placeholders in the template with the actual data value
                              const lineBreaks = data.split('\n').length - 1;

                              if (lineBreaks > 0) {
                                  data = data.replace(/\n/g, ' ');
                              }

                          }
                      }
                      return template.includes("{rowdata}") ? template.replace(regex, data) : template;
                  }
                  // Check if data-th-custom-render attribute exists
                  else if (el.hasAttribute("data-th-custom-render")) {
                      // Parse the content of data-th-custom-render attribute into an object
                      var cR = JSON.stringify($(el).data("th-custom-render"));
                      var customRender = JSON.parse(cR);
                      let stringWithoutSpaces = data.toString().toLowerCase().replace(/\s/g, '');
                      //console.log("el", el)
                      
                      if (el == "isDeviceLicensed") {
                          console.log("aa", stringWithoutSpaces)
                          console.log("aa", customRender[stringWithoutSpaces], customRender[stringWithoutSpaces], data)
                      }
                      // Use the custom render object to map the data value to a specific template
                      return customRender[stringWithoutSpaces] ? customRender[stringWithoutSpaces] : data;
                  }
                  else if (el.hasAttribute("data-th-condition") && el.hasAttribute("data-th-condition-render")) {
                      const sd = $(el).data("th-condition"),
                          sds = JSON.stringify($(el).data("th-condition-render")),
                          cDR = JSON.parse(sds),
                            
                          tem = cDR[row[sd]] === undefined || cDR[row[sd]] === null ? cDR["def"] : cDR[row[sd]];
                      
                      try {
                         // console.log("s", tem)
                          return tem.includes("{rowdata}") ? tem.replace(regex, data) : tem;

                      } catch (e) {
                         // console.log("s",sds,cDR,tem)
                          //debugger
                          throw(e)
                      }

                  } else if (el.hasAttribute("data-th-condition") && el.hasAttribute("data-th-custom-render-configure")) {
                      const sd = $(el).data("th-condition"),
                          sds = JSON.stringify(configureDatatable[$(el).data("th-custom-render-configure")]),
                          cDR = JSON.parse(sds),
                      
                          tem = cDR[row[sd]] === undefined || cDR[row[sd]] === null ? cDR["def"] : cDR[row[sd]];
                      return tem.includes("{rowdata}") ? tem.replace(regex, data) : tem;
                  } else if (el.hasAttribute("data-th-name") && el.hasAttribute("data-th-custom-render-configure")) {
                      // Parse the content of data-th-custom-render attribute into an object

                      var cR = JSON.stringify(configureDatatable[$(el).data("th-custom-render-configure")]);
                      var customRender = JSON.parse(cR);
                      let stringWithoutSpaces = data.toString().toLowerCase().replace(/\s/g, '');
                      console.log(customRender[stringWithoutSpaces])
                      // Use the custom render object to map the data value to a specific template
                      // return data;
                      return customRender[stringWithoutSpaces] ? customRender[stringWithoutSpaces] : data;
                  }
                  else if (Array.isArray(row[ne])) {
                      const ds = [];
                      const arrName = $(el).data("arr-name");
                     // console.log("arrName", row[ne]);
                      if (arrName === 'claimValue') {
                          //row[ne].forEach(function (o, i) {
                          //    console.log(o[arrName]);
                          const ClaValue = groupClaimsTwo(row[ne], arrName);
                           //   console.log(ClaValue);
                              ds.push(ClaValue)


                          //});
                      }
                      else {
                         
                          if (arrName === 'branchWorkTimes') {
                              
                                  
                              const formatTime = formatWorkTime(row[ne],row['id']);
                                //  console.log("formatTime", formatTime)
                                  ds.push(formatTime)
                                 


                              //});
                          }
                          else if (arrName === 'branchCategories') {
                              const format = formatBranchCategories(row[ne]);
                              ds.push(format)
                          }
                          else if (arrName === 'itemOptions') {
                              const format = formatItemOptions(row[ne]);
                              ds.push(format)
                          }
                          else if (arrName === 'itemAdditives') {
                              const format = formatitemAdditives(row[ne]);
                              ds.push(format)
                          }

                          else {
                              row[ne].forEach(function (o, i) {

                                  if (arrName === null || arrName === undefined) {
                                      ds.push(JSON.stringify(o))
                                  } else {
                                      ds.push(o[arrName])

                                  }


                              });
                          }
                         
                      }
//                      console.log("ds",ds)
                      return ds;
                  } else {
                      // If data-th-render attribute doesn't exist, use the default data
                      try {
                          return data !== undefined ? data : '_';

                      } catch (error) {
                          console.error('Error occurred during rendering:', error);
                          return ''; // Return default content if an error occurs
                      }
                  }
           
              }
            
            };
        }
      });
    
      //console.log(count)
      return colData;
    },
    customSearch: function (t) {
      var o = t.settings().init();

      $(o.search).on("keyup", function (e) {
        console.log(t)
        t.search(this.value).draw();
      }),
        $(o.search).on("input", function (c) {
          c.target.value || t.search("").draw();
        });
        // debugger
      },
      actionWithData:  function (t) {
          //console.log("actionWithData", t)

         
          $(t.context[0].nTBody).on('click', '.read-more', function (e) {
              const readMoreLink = $(e.target).closest(".read-more");
              if (readMoreLink.length === 0) return; // Exit if the clicked element is not .read-more
              e.preventDefault();
              // Toggle the expanded class on the `readMoreLink` element
              const isExpanded = readMoreLink.toggleClass("expanded").hasClass("expanded");
              // Get the above item
              const aboveItem = readMoreLink.prev();
              // Select `.hidden-row` elements within the `aboveItem` and toggle their display
              aboveItem.find("tbody .hidden-row").css("display", isExpanded ? "table-row" : "none");
              // Update the icon in the `readMoreLink` based on expansion state
              readMoreLink.html(isExpanded
                  ? `<i class="fe fe-chevron-up fe-16"></i>`
                  : `<i class="fe fe-chevron-down fe-16"></i>`
              );
          })
          $(t.context[0].nTBody).on('click', '.dropdown-item', function (e) {
              var $this = $(this),
                  data = t.row($this.parents('tr')).data();



             

             
              if (this.hasAttribute("data-tb-action")) {
                  passingDataRoute($this.data("tb-action"), data)
              }
              if ($this.hasClass("hide")) {
                  var row = t.row($this.parents('tr'));
                  row.nodes().to$().hide();
              }
              if (this.hasAttribute("data-toggle")) {
                

                  const model = $this.data("target");
                    const  $form = $(model).find("form");
                  console.log("model", $form);
                
                 
                 
                  // Get form field values as an array of objects
                  var formDataArray = $($form).serializeArray().filter(function (field) {
                      // Exclude fields by name
                      return field.name !== 'AntiforgeryFieldname';
                  });
                
                  $(formDataArray).each(function (index, obj) {
                      const el = $(`[name="${obj.name}"]`);
                      if (el.jHasAttribute("data-other-name")) {
                          let oName = el.attr("data-other-name");
                          console.log("true", data[oName])
                          el.val(data[oName]);
                          //console.log(data[])
                      } else {
                          data.hasOwnProperty(obj.name) ? el.val(data[obj.name]) : el.val(obj.value);
                          
                       
                      }
                      
                      
                  });

                

                
              }

              if (this.hasAttribute("data-component")) {
                  const model = $this.data("component");
                  passingDataRouteModel(model, data);
              }
              
              console.log(data)
          });
      },

      rowAction: function (t) {
          //console.log("rowAction", $(t.context[0].nTable), JSON.parse(JSON.stringify($(t.context[0].nTable).data("column-defs"))))
          const context = t.context && t.context[0];
          if (context && context.nTable) {
              const columnDefs = $(context.nTable).data("column-defs");
              if (Array.isArray(columnDefs) && columnDefs.length > 0) {
                  const rowId = $(t.context[0].nTable).data("column-defs")[0].targets[0];
                  t.context[0].aoData.forEach(function (e, i) {
                      $($(e.anCells)[rowId]).children().attr("href")
                      var href = $($(e.anCells)[rowId]).children().attr("href");
                      $(e.nTr).attr("data-href", href)
                      // e.nTr.setAttribute("class", "democlass");

                      $($(e.anCells)[rowId]).css('background-color', '#f8f9fa');

                  });

                  // Add click event handler
                  $(t.context[0].nTBody).on('click', 'tr', function (event) {
                      // Get data from the first cell in the row (adjust index as needed)
                      // Find the closest row to the clicked element
                      // Check if the clicked element is a button
                      if (event.target.tagName.toLowerCase() === 'button' || event.target.tagName.toLowerCase() === "a") {
                          event.target.tagName
                          return;
                      }

                      const clickedRow = event.target.closest('tr');

                      // Do not proceed if the click was outside of a row
                      if (clickedRow) {
                          clickedRow.getAttribute("data-href")
                          window.location.href = clickedRow.getAttribute("data-href");
                          return;
                      }
                  });
                  //Add hover effect
                  $(t.context[0].nTBody).on('mouseover', 'tr', function () {
                      $(this).css('background-color', '#f8f9fa');
                      $(this).css('cursor', 'pointer');
                      var lastTd = $(this).find('td:last');
                      lastTd.children().addClass("btn-white-radius");
                  });

                  $(t.context[0].nTBody).on('mouseout', 'tr', function () {
                      $(this).css('background-color', '');
                      var lastTd = $(this).find('td:last');
                      lastTd.children().removeClass("btn-white-radius")
                  });
              } else {
                  //console.error("Column definitions are not defined or invalid.");
              }
          } else {
              //console.error("t.context or t.context[0].nTable is not defined.");
          }
         
        
      },


      filterDataTable: function (dataTable) {
         
              dataTable.column(2).data().unique().sort().each(function (d, j) {
                  //select.append('<option value="' + d + '">' + d + '</option>');
                  console.log(d, j)
                  //debugger
              });
        

          $("#pills-filter-orders").on('click', 'li.nav-item a.nav-link', function (e) {

              console.log(this.getAttribute("data-value"))
              var val = $.fn.dataTable.util.escapeRegex($(this).data("value").toString());
              const col = $("#pills-filter-orders").data('colunm-num');
          
              dataTable.column(col).search(val ? '^' + val + '$' : '', true, false).draw();
              console.log(val)


          });
          //select.on('change', function () {
          //    var val = $.fn.dataTable.util.escapeRegex($(this).val());
          //    dataTable.column(2).search(val ? '^' + val + '$' : '', true, false).draw();
          //    console.log(val)
          //});
      //});
      },
      reloadDataTable: function () {
          if ("number" == typeof t) {
              //console.log("number", t)
              //console.log("yes", this.collection[t].$initializedEl)
              return this.collections[t].$initializedEl
          } else {
              //console.log("no", this)
              return this.collections[0].$initializedEl
          }
      },
      getDataTable: function (id) {
          return this.collection.filter(id)
      },

      getCustomParams: function () {
          // Define your custom parameters here
          return {

          };
      },
      getItem: function (t) {
          if ("number" == typeof t) {
              console.log("number", t)
              console.log("yes", this.collection[t].$initializedEl)
              return this.collections[t].$initializedEl
          } else {
              console.log("no",this )
              return this.collections.find((e) => e.id === t).$initializedEl
          }
      },
  
  };
})(jQuery);
function groupPermissions(roleClaims) {
    const permissionsByUser = {};

    $(roleClaims).each(function (index, claimObject) {
        const claimValue = claimObject.DisplayValue;
        if (claimValue) {
            const parts = claimValue.split('.');
            if (parts.length > 2 && parts[1] === 'Permission.User') {
                const permission = parts[2];
                const user = parts[1];

                permissionsByUser[user] = permissionsByUser[user] || [];
                permissionsByUser[user].push(permission);
            }
        }
    });

    return permissionsByUser;
}

// Function to group claims
function groupClaims(claims,name) {
    const groupedClaims = {};

    claims.forEach(claim => {
        console.log("claim", claim[name])
                          
        const parts = claim[name].split('.');
        if (parts.length === 3) {
            const group = parts[1]; // User or Admin
            const action = parts[2]; // UpdateRole, ChangePassword, etc.

            if (!groupedClaims[group]) {
                groupedClaims[group] = [];
            }
           // var s = `<div class="card d-inline-flex mb-2"><div class="card-body bg-light py-2 px-3"> ${}</div></div>`
            groupedClaims[group].push(action);
        }
    });

    return JSON.stringify(groupedClaims);
}

// Function to group claims and format the output
function groupClaimsTwo(claims,name) {
    const groupedClaims = {};

    claims.forEach(claim => {
        const parts = claim[name].split('.');
        if (parts.length === 3) {
            const group = parts[1]; // User or Admin
            const action = parts[2]; // UpdateRole, ChangePassword, etc.

            if (!groupedClaims[group]) {
                groupedClaims[group] = [];
            }
           
            groupedClaims[group].push(action);
          
        }
    });

    let result = '';
    for (const group in groupedClaims) {
        const mm = `<div class="card d-inline-flex  mb-1">
                <div class="py-2 px-2">
                  ${group} : ${groupedClaims[group].join(', ')}
                </div>
        </div> \n`;

           
        result += mm;
       
    }
   // console.log("DD", json.stringify(result.trim()))
    const div = `<div>${result.trim() }</div>`
    return div; // Remove the trailing newline
}
function formatWorkTime(workTimes,id) {

    const dayMap= {
        1: "السبت",
        2: "الأحد",
        3: "الاثنين",
        4: "الثلاثاء",
        5: "الأربعاء",
        6: "الخميس",
        7: "الجمعة"
    };
    // Begin the table structure with a hidden rows class
    let customOutput = `
    <div class="card d-inline-flex mb-1 text-card small-table box-shadow-none ">
      <table class="table table-borderless mb-0">
        <thead>
          <tr>
            <th class="fs-8 m-0 py-1">اليوم</th>
            <th class="fs-8 m-0 py-1">بدء العمل</th>
            <th class="fs-8 m-0 py-1">نهاية العمل</th>
          </tr>
        </thead>
        <tbody>`;
    
    // Loop through each workTime item and add a row for each
    workTimes.forEach(({ dayNumber, startWorkTime, endWorkTime }, index) => {
        const hiddenClass = index >= 1 ? 'hidden-row' : ''; // Hide rows after the first two
        customOutput += `
          <tr class="${hiddenClass}">
            <td class="fs-8 m-0 py-1"><strong>${dayMap[dayNumber]}</strong></td>
            <td class="fs-8 m-0 py-1"><strong>${startWorkTime}</strong></td>
            <td class="fs-8 m-0 py-1"><strong>${endWorkTime}</strong></td>
          </tr>`;
        
    });
    // Close the table tags and add the "Read More" link
    customOutput += `
        </tbody>
      </table>
     <a href="javascript:void(0);" id="${id}" class="read-more text-center"><i class="fe fe-chevron-down fe-16"></i></a>
    </div>`;

    // Output the custom-designed string to the console or return it
    // console.log(customOutput);
    //console.log("indexx",id)
    return customOutput;
}
function formatItemOptions(itemOptions) {

    let customOutput = `
    <div class="card small-table d-inline-flex mb-1 text-card box-shadow-none ">
      <table class="table  table-borderless mb-0">
        <thead>
          <tr>
            <th>الصورة</th>
            <th>الاسم</th>
            <th>السعر</th>
            <th>الوزن</th>
            <th>الباركود</th>
            <th>سقف الطلبات</th>
            <th>توفر المنتج </th>
          </tr>
        </thead>
        <tbody>`;
    
    // Loop through each workTime item and add a row for each
    itemOptions.forEach(({ imageValue,
name,
        price,
        weight,
parcode,
        maximumOrderCount, availability }, index) => {
        const hiddenClass = index >= 1 ? 'hidden-row' : ''; // Hide rows after the first two
        customOutput += `
          <tr class="${hiddenClass}">
            <td>
            <div class="d-flex justify-content-center avatar avatar-md"><img src="${imageValue !== null ? imageValue : '/assets/avatars/imageGrey.jpg'}" class="rounded avatar-img"></div>
            </td>
            <td>${name}</td>
            <td>${price}</td>
            <td>${weight}</td>
            <td>${parcode}</td>
            <td>${maximumOrderCount}</td>
            <td>${availability === true ? ` <span class="badge badge-success">متوفر</span>` : `<span class="badge badge-danger">غير متوفر</span>`}
           
            </td>
          </tr>`;
        
    });
    // Close the table tags and add the "Read More" link
    customOutput += `
        </tbody>
      </table>
      ${itemOptions.length <= 1 ? '': '<a href="javascript:void(0);" class="read-more text-center"><i class="fe fe-chevron-down fe-16"></i></a>'

      
      }
    </div>`;


    return customOutput;
}
function formatitemAdditives(itemOptions) {

    let customOutput = `
    <div class="card small-table d-inline-flex mb-1 text-card box-shadow-none ">
      <table class="table  table-borderless mb-0">
        <thead>
          <tr>
            <th>الاسم</th>
          </tr>
        </thead>
        <tbody>`;
    
    // Loop through each workTime item and add a row for each
    itemOptions.forEach(({

        itemOptionName }, index) => {
        const hiddenClass = index >= 1 ? 'hidden-row' : ''; // Hide rows after the first two
        customOutput += `
          <tr class="${hiddenClass}">
            <td>${itemOptionName}</td>
            </td>
          </tr>`;
        
    });
    // Close the table tags and add the "Read More" link
    customOutput += `
        </tbody>
      </table>
      ${itemOptions.length <= 1 ? '': '<a href="javascript:void(0);" class="read-more text-center"><i class="fe fe-chevron-down fe-16"></i></a>'

      
      }
    </div>`;


    return customOutput;
}




function formatBranchCategories(branchCategories) {

    var customOutput = '';
   
    //{ "id": 1, "name": "string", "businessId": 1, "isBase64": null, "imageValue": null, "ownerTypeId": 13 }

    // Loop through each workTime item and add a row for each
    branchCategories.forEach(({ name, imageValue }, index) => {
        customOutput += 
            `<span class="badge badge-pill bg-white border p-1  align-items-center m-1">
    <img src=${imageValue === null ? "/assets/avatars/imageGrey.jpg" : imageValue} class="avatar-img" style="width: 20px; height: 20px;border-radius:0.35rem !important;">
    <span class="text-muted">${name}</span>
</span>`;
    });

    
    return customOutput;
}





function getPermissionFromClaimValue(claimValue) {
    if (!claimValue) {
        return null; // Handle empty claim value
    }
    const parts = claimValue.split('.');
    return parts.length > 2 ? parts[2] : null; // Return permission if valid, otherwise null
}

function isValidJSON(str) {
    try {
        JSON.parse(str);
        return true;
    } catch (e) {
        return false;
    }
}
const element = document.querySelector("[data-th-per-item]");
var permissionData;

if (element) {
    const dataString = element.dataset.thPerItem;
    if (dataString) {
         permissionData = JSON.parse(dataString);
        console.log("CreateCardFiles permission:", permissionData);
        // ... access other permission properties
    }
}
var configureDatatable = {
    stateOfDriverRequest: {
        0: `<span class="badge badge-danger">رفض الطلب</span>`,
        1: `<span class="badge badge-warning">قيد المراجعة</span>`,
        2: `<span class="badge badge-success">تم الموافقة - في إنتظار إستكمال</span>`,
        3: `<span class="badge badge-success">تم إنشاء بيانات البطاقة</span>`,
        4: `<span class="badge badge-success">تم إستكمال الطلب</span>`,
        5: `<span class="badge badge-success">تم إستكمال الطلب</span>`,
        6: `<span class="badge badge-success">تم إستكمال الطلب</span>`,
        10: `<span class="badge badge-success">يمتلك حساب</span>`
    },
    eventOfDriverRequest: element ?{
        1: `<div class="btn-group justify-content-center " style="width:140px" role="group">
${permissionData["compFiles"] ? `<a class="btn btn-white btn-sm" href="RequestJoin/Complete/{rowdata}">
  <i class="fe fe-plus fe-16 me-1"></i> استكمال طلب
</a>`:''}
${permissionData["UpdateReq"] ? `<div class="btn-group">
  <button type="button" class="btn btn-white btn-icon btn-sm dropdown-toggle dropdown-toggle-empty" id="productsEditDropdown2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></button>

  <div class="dropdown-menu dropdown-menu-end mt-1 dropdown-menu dropdown-menu-right" aria-labelledby="productsEditDropdown2" data-popper-placement="bottom-end" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-72px, 554.4px, 0px);">
   <a class="dropdown-item" href="RequestJoin/Edit/{rowdata}">
       تعديل
    </a>
  </div>
</div>`:''}

</div>`,
        4:`<div class="btn-group justify-content-center " style="width:140px" role="group">

${permissionData["CreateAccount"] || permissionData["UpdateReq"] ? `
<div class="btn-group">
  <button type="button" class="btn btn-white btn-icon btn-sm dropdown-toggle dropdown-toggle-empty" id="productsEditDropdown2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></button>

  <div class="dropdown-menu dropdown-menu-end mt-1 dropdown-menu dropdown-menu-right" aria-labelledby="productsEditDropdown2" data-popper-placement="bottom-end" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-72px, 554.4px, 0px);">
${permissionData['CreateAccount'] ? `<a class="dropdown-item" data-name-id="id" data-value-id="{rowdata}" data-toggle="modal" data-target="#CreateDriverAccountModal">
       انشاء حساب للسائق
    </a>`: ''}   
${permissionData["UpdateReq"] ? ` <a class="dropdown-item" href="RequestJoin/Edit/{rowdata}">
       تعديل
    </a>`: ''}
   
  </div>
</div>
` : ''}
</div>`,
        5: `<div class="btn-group justify-content-center " style="width:140px" role="group">

${permissionData["CreateAccount"] || permissionData["UpdateReq"] ? `
<div class="btn-group">
  <button type="button" class="btn btn-white btn-icon btn-sm dropdown-toggle dropdown-toggle-empty" id="productsEditDropdown2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></button>

  <div class="dropdown-menu dropdown-menu-end mt-1 dropdown-menu dropdown-menu-right" aria-labelledby="productsEditDropdown2" data-popper-placement="bottom-end" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-72px, 554.4px, 0px);">
${permissionData['CreateAccount'] ? `<a class="dropdown-item" data-name-id="id" data-value-id="{rowdata}" data-toggle="modal" data-target="#CreateDriverAccountModal">
       انشاء حساب للسائق
    </a>`: ''}   
${permissionData["UpdateReq"] ? ` <a class="dropdown-item" href="RequestJoin/Edit/{rowdata}">
       تعديل
    </a>`: ''}
   
  </div>
</div>
` : ''}
</div>`,
        def: `<div class="btn-group justify-content-center " style="width:140px" role="group">
${permissionData["compFiles"] ? `<a class="btn btn-white btn-sm" href="RequestJoin/Complete/{rowdata}">
  <i class="fe fe-plus fe-16 me-1"></i> استكمال طلب
</a>`: ''}
${permissionData["CreateAccount"] || permissionData["UpdateReq"] ? `
<div class="btn-group justify-content-center ">
  <button type="button" class="btn btn-white btn-icon btn-sm dropdown-toggle dropdown-toggle-empty" id="productsEditDropdown2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></button>

  <div class="dropdown-menu dropdown-menu-end mt-1 dropdown-menu dropdown-menu-right" aria-labelledby="productsEditDropdown2" data-popper-placement="bottom-end" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate3d(-72px, 554.4px, 0px);">
${permissionData['CreateAccount'] ? `<a class="dropdown-item" data-name-id="id" data-value-id="{rowdata}" data-toggle="modal" data-target="#CreateDriverAccountModal">
       انشاء حساب للسائق
    </a>`: ''}   
${permissionData["UpdateReq"] ? ` <a class="dropdown-item" href="RequestJoin/Edit/{rowdata}">
       تعديل
    </a>`: ''}
   
  </div>
</div>
` : ''}
</div>`
        } :'',
    gender: {
    male: "<span>ذكر</span>",
    female: "<span>أنثى</span>"
    },
    isCancelOrder: {

        false: `<span class="badge badge-success">مفعل</span>`,
        true: `<span class="badge badge-danger">تم الغاء الطلب</span>`
    },
    stateOrder: {
        '-2': `<span class="badge badge-warning">معلق</span>`,
        "-1":`<span class="badge badge-danger">تم إلغاء الطلب</span>`,
        1: `<span class="badge badge-success">تم إسناد الطلب الى السائق</span>`,
        2: `<span class="badge badge-success-blue">متجهة الى العميل</span>`,
        3: `<span class="badge badge-primary">بدأ المشوار</span>`,
        4: `<span class="badge badge-light"> بإنتظار الدفع</span>`,
        5: `<span class="badge badge-success">الطلب مكتملة</span>`,
        6: `<span class="badge badge-warning">لم يتم اسناد سائق بعد</span>`
    },
    isOrderMultiDestination: {
        false: `<span class="badge badge-success">الطلب غير متعدد الوجهات</span>`,
        true: `<span class="badge badge-success-blue">الطلب متعدد الوجهات</span>`
        }
}

 
 
 
 

 
 








$.fn.dataTable.ext.errMode = 'throw';