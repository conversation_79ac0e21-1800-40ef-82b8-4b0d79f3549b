// Define the jQuery plugin

$.fn.jHasAttribute = function (attributeName) {
    // Check if the first element in the collection has the specified attribute
    return this.first().attr(attributeName) !== undefined;
};
// Example: Use the plugin to check if an element with ID 'myElement' has the 'data-special' attribute
//var hasAttribute = $('#myElement').jHasAttribute('data-special');

function getRouteAjax(url, userInfo) {
    //console.log("getRouteAjax", userInfo)
    $.ajax({
        headers: {
            "X-XSRF-TOKEN": $("[name='AntiforgeryFieldname']").val(),
            "Authorization": $("[name='AntiforgeryFieldname']").val()
        },
        type: 'POST',
        contentType: "application/json; charset=utf-8",
        url: url,
        dataType: "json",
        cache: false,
        data: userInfo,
        success: function (data) {

            if (data['url'] === "1") {
               // console.log("data", data['resObject']);

                DrawingChartBarType(data['resObject']);
            } else if (data['url'] === "2") {

               // console.log("data 2 ", data['resObject']);
                generateCharts(data['resObject']);
            }

        },
        error: function (req, status, err) {
            console.log('something went wrong', status, err, req);

        }
    });
}