﻿    var isEmpty = function isEmpty(f) {

    return (/^function[^{]+\{\s*\}/m.test(f.toString())
    );
};
(function ($) {
  'use strict';
    
    $.CustomPluginSelect2 = {
    /**
     *
     *
     * @var Object _baseConfig
     */
    _baseConfig: {
            width: 'resolve',
            minimumResultsForSearch: 1,
            dir: "rtl",
            placeholder: "يرجى الإختيار",
            templateResult: function () { },
            data: function () { },
            processResults: function () { },
            getSelected: function () { },
            getAjax:function(){},

    },
      
    /**
     *
     *
     * @var jQuery pageCollection
     */
    pageCollection: $(),

    /**
     * Initialization of Datatables wrapper.
     *
     * @param String selector (optional)
     * @param Object config (optional)
     *
     * @return jQuery pageCollection - collection of initialized items.
     */

    init: function (selector, config) {
      this.collection = selector && $(selector).length ? $(selector) : $();
      if (!$(selector).length) return;

      this.config = config && $.isPlainObject(config) ?
        $.extend({}, this._baseConfig, config) : this._baseConfig;

      this.config.itemSelector = selector;


        this.initSelect2();

      return this.pageCollection;
    },

    initSelect2: function () {
      //Variables
      var $self = this,
        config = $self.config,
            collection = $self.pageCollection;
      
      //Actions
        this.collection.each(function (i, el) {
            //Variables
            //console.log(el)
            var $this = $(el),
                $placeholder = $this.data('s2-placeholder') === "undefined" ? $this.data('s2-placeholder') : $this.attr('placeholder'),
                $searchBox = $this.data('s2-search-box'),
                $routeMethod = $this.data('s2-method'),
                $loaclData = $this.attr('data-s2-local');

             
            console.log("nnn", $this.prop("multiple"))


           // debugger
           
            $this.select2({
                allowClear: $this.prop("multiple") ?false :true,
                theme: 'bootstrap4',
                dropdownParent: $this.parent(),
               placeholder: $placeholder != null ? $placeholder : config['placeholder'],
            
               dir: $this.attr("dir") != null ? $this.attr("dir") : config['dir'],
                multiple: $this.prop("multiple"),
               minimumResultsForSearch: $searchBox == false ? Infinity : config['minimumResultsForSearch'],
               width: config['width'],
               //initSelection: $loaclData === true ? true : false,
                //tags: true,
                //tokenSeparators: [',', ' '],
               //allowClear: true,
               //closeOnSelect: false,
                //maximumSelectionLength: 2,
                //minimumInputLength: 0,
                //maximumInputLength: 20,
               //searching: function () {
               //    return $('<div class="loader-select2 show" id=""></div>');
               //},
                language: {
                    errorLoading: function () {
                        return "لا يمكن تحميل النتائج.";
                    },
                    inputTooLong: function (e) {
                        var t = e.input.length - e.maximum,
                            n = "امسح من فضلك " + t + " الحرف";
                        return 1 != t && (n += "s"), n;
                    },
                    inputTooShort: function (e) {
                        return (
                            "الرجاء إدخال" +
                            (e.minimum - e.input.length) +
                            " أو المزيد من الأحرف"
                        );
                    },
                    loadingMore: function () {
                        return "جارٍ تحميل المزيد من النتائج…";
                    },
                    maximumSelected: function (e) {
                        var t = "يمكنك الاختيار فقط " + e.maximum + "عنصر";
                        return 1 != e.maximum && (t += "s"), t;
                    },
                    noResults: function () {
                        return "لم يتم العثور على نتائج";
                    },
                    searching: function () {
                        return "يبحث…";
                    },
                    removeAllItems: function () {
                        return "إزالة كافة العناصر";
                    },
                },
                templateResult: isEmpty(config['templateResult']) === true ? $self.getTemplateResult : config['templateResult'],
               ajax: isEmpty(config['getAjax']) === true && !$loaclData ? {
                   url: $routeMethod,
                   dataType: 'json',
                   contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                   //headers: { "X-XSRF-TOKEN": $("[name='AntiforgeryFieldname']").val() },
                   delay: 250,
                   type: 'GET',
                   data: isEmpty(config['data']) === true ? $self.getData : config['data'],
                   processResults: isEmpty(config['processResults']) === true ? $self.getProcessResults : config['processResults'],

                   cache: false
                } : null,
               matcher: $self.matchStart,
            });


           
            $self.multipleSelectedWithoutSearchBox($this, $searchBox);
            
            if (!isEmpty(config['getSelected'])) {
                console.log(isEmpty(config['getSelected']),this)
                $($this).on('select2:select', config['getSelected']
                );
            };
           
        
            if (el.hasAttribute('data-s2-insert')) {
                var insVal = $this.data('s2-insert-name');
                var insName = $this.data('s2-insert');
                $($this).on('select2:select', function (e) {
                    var data = e.params.data, val;
                    if ($($this).attr("data-s2-local") === "true") {
                        val = data[insVal];
                    } else {
                        val = data.object[insVal]; 
                    }
                    $(`input[name='${insName}']`).val(val)
                    console.log(data)
                    //
                });
            }
            //when unselected..
            $(this).on('select2:unselect', function (e) {
                $(this).val(null).trigger('change'); //clear other
            });

        //Actions
            collection = collection.add($this);
        });
      },

        setSelect2InitElement: function (element) {
            console.log("elemnt", element)
            //console.log(element)
            //$(element).next().remove();
            //element.removeAttr("data-select2-id")
            var $self = this,
                config = $self.config,
                $this = $(element),
                $placeholder = $this.data('s2-placeholder') === "undefined" ? $this.data('s2-placeholder') : $this.attr('placeholder'),
                $searchBox = $this.data('s2-search-box'),
                $routeMethod = $this.data('s2-method'),
                $loaclData = $this.attr('data-s2-local');

            $this.select2({
                allowClear: $this.prop("multiple") ? false : true,
                theme: 'bootstrap4',
                dropdownParent: $this.parent(),
                placeholder: $placeholder != null ? $placeholder : config['placeholder'],

                dir: $this.attr("dir") != null ? $this.attr("dir") : config['dir'],
                multiple: $this.prop("multiple"),
                minimumResultsForSearch: $searchBox == false ? Infinity : config['minimumResultsForSearch'],
                width: config['width'],
                //initSelection: $loaclData === true ? true : false,
                //tags: true,
                //tokenSeparators: [',', ' '],
                //allowClear: true,
                //closeOnSelect: false,
                //maximumSelectionLength: 2,
                //minimumInputLength: 0,
                //maximumInputLength: 20,
                //searching: function () {
                //    return $('<div class="loader-select2 show" id=""></div>');
                //},
                language: {
                    errorLoading: function () {
                        return "لا يمكن تحميل النتائج.";
                    },
                    inputTooLong: function (e) {
                        var t = e.input.length - e.maximum,
                            n = "امسح من فضلك " + t + " الحرف";
                        return 1 != t && (n += "s"), n;
                    },
                    inputTooShort: function (e) {
                        return (
                            "الرجاء إدخال" +
                            (e.minimum - e.input.length) +
                            " أو المزيد من الأحرف"
                        );
                    },
                    loadingMore: function () {
                        return "جارٍ تحميل المزيد من النتائج…";
                    },
                    maximumSelected: function (e) {
                        var t = "يمكنك الاختيار فقط " + e.maximum + "عنصر";
                        return 1 != e.maximum && (t += "s"), t;
                    },
                    noResults: function () {
                        return "لم يتم العثور على نتائج";
                    },
                    searching: function () {
                        return "يبحث…";
                    },
                    removeAllItems: function () {
                        return "إزالة كافة العناصر";
                    },
                },
                templateResult: isEmpty(config['templateResult']) === true ? $self.getTemplateResult : config['templateResult'],
                ajax: isEmpty(config['getAjax']) === true && !$loaclData ? {
                    url: $routeMethod,
                    dataType: 'json',
                    contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                    //headers: { "X-XSRF-TOKEN": $("[name='AntiforgeryFieldname']").val() },
                    delay: 250,
                    type: 'GET',
                    data: isEmpty(config['data']) === true ? $self.getData : config['data'],
                    processResults: isEmpty(config['processResults']) === true ? $self.getProcessResults : config['processResults'],

                    cache: false
                } : null,
                matcher: $self.matchStart,
            });
            console.log("this",$this)

        },
        getProcessResults: function (result) { 
            var $this = $(this.$element),
                $id = $this.data('s2-id'),
                $text = $this.data('s2-text');
        
            return {

                results: $.map(result, function (item) {
                    console.log(item);
                    return {

                        id: item[$id],
                        text: item[$text],
                        object: item,
                    };
                }),
            };
        },
        getData: function (params) {
            var $parentSelected = this.data('s2-parent'),
                $form = $(this).closest('form'),
                $parentSelect = $form.find(`select[name=${$parentSelected}]`),
                selectedId = $parentSelect.val();
            if (selectedId != null) {

                return {
                    search: params.term,
                    parentId: selectedId
                };
            } else {
               
                return {
                    search: params.term,
                };
            }
        },
        getTemplateResult: function (state, container) {
            console.log("ss", state, container)
            if (!state.id) {
                return $('<div style="width: 1rem!important;height: 1rem!important;" class="spinner-border mr-3 text-warning" role="status"><span class= "sr-only" > Loading...</span ></div >');

            }

                var $state = $(
                    `<span class="t-select">  ${state.text} </span>`
                );
                return $state;

        },
        multipleSelectedWithoutSearchBox: function ($this, $searchBox) {

            if ($this.prop("multiple") && $searchBox == false) {
                $($this).on('select2:opening select2:closing', function (event) {

                    var $searchfield = $(this).parent().find('.select2-search__field');
                    console.log($searchfield);
                    $searchfield.prop('disabled', true);
                });
            }
        },
        getSelected: function (e) {
            var data = e.params.data;
            console.log(data);
            console.log(e.params);

        },

        matchStart: function  (params, data) {
        // If there are no search terms, return all of the data
           // console.log(data);
           // console.log(params);
        if ($.trim(params.term) === '') {
            return data;
        }

        // Skip if there is no 'children' property
        if (typeof data.children === 'undefined') {
            return null;
        }

        // `data.children` contains the actual options that we are matching against
        var filteredChildren = [];
        $.each(data.children, function (idx, child) {
            if (child.text.toUpperCase().indexOf(params.term.toUpperCase()) == 0) {
                filteredChildren.push(child);
            }
        });

        // If we matched any of the timezone group's children, then set the matched children on the group
        // and return the group object
        if (filteredChildren.length) {
            var modifiedData = $.extend({}, data, true);
            modifiedData.children = filteredChildren;

            // You can return modified objects from here
            // This includes matching the `children` how you want in nested data sets
            return modifiedData;
        }

        // Return `null` if the term should not be displayed
        return null;
        },
      


      
  };
    
})(jQuery);
