﻿
class SignalRClient {
    constructor() {
        if (!SignalRClient.instance) {
            this.accessToken = null;
            this.connection = null;
            this.notFoundDrivers = $("#notFoundDrivers");
            this.mapId = $("#map");
            this.cardDriverScrollable = $("#cardDrivers").children(".simplebar-wrapper").children(".simplebar-mask").children().children().children();
            this.extractedData = [];
            SignalRClient.instance = this;

            console.log("SignalRClient.instance 1", SignalRClient.instance)
        }
        console.log("SignalRClient.instance 2", SignalRClient.instance)
        return SignalRClient.instance;
    }

    async initialize() {
        try {
            await this.getAccessToken();
            await this.createConnection();
            this.configureConnection();
            //this.attachDriverEventHandlers();
            await this.startConnection();
           
         
        } catch (error) {
            console.error("Error initializing SignalR client:", error);
        }
    }

  

    async getAccessToken() {
        const response = await fetch('/get-access-token');
        this.accessToken = response.headers.get('X-Access-Token');
    }

    async createConnection() {
        this.connection = await new signalR.HubConnectionBuilder()
            .withUrl("http://82.114.181.89:3000/Notification/NotificationHub", {
                transport: signalR.HttpTransportType.WebSockets |
                    signalR.HttpTransportType.LongPolling,
                //accessTokenFactory: () => this.accessToken
                accessTokenFactory: () => new Promise(resolve => resolve(this.accessToken))
            })
            .configureLogging(signalR.LogLevel.Debug)
            .withHubProtocol(new signalR.protocols.msgpack.MessagePackHubProtocol())
            .withAutomaticReconnect()
            .build();
    }
  

    configureConnection() {
        // Set the server timeout to 30 seconds
        this.connection.serverTimeoutInMilliseconds = 30000;

        // Set the interval for sending keep-alive messages to the server to 10 seconds
        this.connection.keepAliveIntervalInMilliseconds = 10000;

        // Set the interval for sending ping messages to the server to 30 seconds
        this.connection.pingIntervalInMilliseconds = 30000;

        // Set the timeout for closing the connection to 30 seconds
        this.connection.closeTimeout = 30000;

        // Set the delay before attempting to reconnect after disconnection to 3 seconds
        this.connection.reconnectDelay = 3000;

        // Set the maximum number of reconnection attempts to 5
        this.connection.maxReconnectAttempts = 5;

    }

    attachDriverEventHandlers() {
        // Define event handlers for connection events
        this.connection.on("online", this.handleOnline.bind(this));
        this.connection.on("updateLocations", this.handleUpdateLocations.bind(this));
        this.connection.on("sendDisconectedCustomer", this.handlDisconectedDriver.bind(this));
        this.connection.on("updateDailyOrdersList", this.handlUpdateDailyOrdersList.bind(this));

        //this.connection.on('disconnected', async (error) => {
        //    console.error('Disconnected:', error);
        //    await startConnection(); // Call your reconnect function
        //});
    }

    attachFazaaOrderEventHandlers() {
        // Define event handlers for connection events
        this.connection.on("sendFazaaOrdersCount", this.handlFazaaOrdersCount.bind(this));
        console.log("attachFazaaOrderEventHandlers");
       


    }

    async startConnection() {
        
        try {
            
            await this.connection.start();
            sessionStorage.setItem("signalRConnection", JSON.stringify(this.connection));
           // retryCount = 0; // Reset retry count on successful connection

           
        } catch (error) {
            console.error("Error starting SignalR connection:", error);
           //await this.startConnection();
        }
    }

    getConnection() {
        const storedConnection = sessionStorage.getItem("signalRConnection");
        if (storedConnection) {
            this.connection = JSON.parse(storedConnection);
            console.log("signalRConnection", storedConnection)
        }
        console.log("signalRConnection", this.connection)
        return this.connection;
    }
    handleOnline(user) {
        console.log("User online:", user);
        //// Update UI or perform other actions
        //document.getElementById("driverOnline").textContent = JSON.parse(user)["CountDrivers"];
       
        const driverCount = JSON.parse(user)["CountDrivers"];
        const CountCustomer = JSON.parse(user)["CountCustomer"];
        
        if (driverCount > 0 || CountCustomer >0) {
            document.getElementById("driverOnline").textContent = driverCount;
            document.getElementById("customerOnline").textContent = CountCustomer;
        } else {
            console.log("this.extractedData.length < 0", this.extractedData.length)
            document.getElementById("driverOnline").textContent = driverCount;
            document.getElementById("customerOnline").textContent = CountCustomer;
            this.notFoundDrivers.show();
        }
    }

    handleUpdateLocations(driverInfo) {
        const driverInfoList = JSON.parse(driverInfo);
        console.log("Updated locations:", driverInfoList);
        try {
            if (driverInfoList.length !== 0) {
                this.getConnectionWidget(driverInfoList);
            } else {
                console.log("driverInfo[] ");
            }

        } catch (error) {

            console.error("Error parsing JSON data:", error);
            console.error("Error parsing JSON data:", driverInfo);
            // Handle the error gracefully, e.g., log the error or display a message to the user
        }

    }

    handlDisconectedDriver(id) {
        console.log("handlDisconectedDriver:", id);


        this.cardDriverScrollable.children(`#${id}`).remove();
        this.extractedData = this.extractedData.filter(function (item) {

            return item.Id !== id;
        });

        this.mapId.attr("data-drivers-location", JSON.stringify(this.extractedData));
        console.log("ssssmmm",JSON.stringify(this.extractedData));
        //initMap()
        //updateDriverMarker();
        removeMarkerById(id);
    }

    handlFazaaOrdersCount(countNumber) {
        try {
            var lastNumber=0;
            
                console.log("sendFazaaOrdersCount:", countNumber);
            document.getElementById("counterOrder").textContent = countNumber;

            if (lastNumber !== countNumber) {
                const exportDatatable = $.CustomPluginDatatablesV1.reloadDataTable();
                console.log("exportDatatable", exportDatatable)
                exportDatatable.ajax.reload()
                lastNumber = countNumber;
            }
                
            //$.CustomPluginDatatablesV1.rowAction(exportDatatable);

            
                //debugger
                // Parse the JSON string to an array

        
        } catch (err) {
            console.error(err);
        }
    }
    getConnectionWidget(driverInfoList) {
        const $this = this;
        this.cardDriverScrollable = $("#cardDrivers").children(".simplebar-wrapper").children(".simplebar-mask").children().children().children();
        
        // Check if the parsed data is not null
        if (driverInfoList !== null || driverInfoList.length === 0) {
            // Iterate over the array using forEach
            $this.notFoundDrivers.hide();
            const driver = driverInfoList[0];
         //   driverInfoList.forEach(function (driver, index) {
                // Process and handle each driver's information here
                // Create HTML string for div element

                if ($this.cardDriverScrollable.children(`#${driver['Id']}`).length === 0) {
                    $this.cardDriverScrollable.children().first().before($this.createDriverCard(driver));
                } else {
                    console.log("ccccccccccccc", false)
                }
                const mma = {
                    Id: driver.Id,
                    Location: {
                        lat: parseFloat(driver.Lat),
                        lng: parseFloat(driver.Long),
                    },
                    Name: driver.Name,
                    CarModel: driver.CarModel,
                    CustomerTypeId: driver.CustomerTypeId
                };


                $this.extractedData.forEach((item, index) => {
                    // Check if the item with the same ID exists
                    //console.log("hshshs", item.Id === mma.Id)
                    if (item.Id === mma.Id) {
                        // Replace the item with the new item
                        $this.extractedData[index] = mma;
                        //replaced = true; // Set the flag to true
                    }
                });
                $this.extractedData.splice(0, 0, mma);
               // console.log("$this.extractedData", $this.extractedData)



            //});

            //$this.mapId.attr("data-drivers-location",'');
            $this.mapId.attr("data-drivers-location", JSON.stringify($this.extractedData));
            console.log("after list empty",$this.extractedData);
            $this.extractedData = [];
            console.log("before list empty",$this.extractedData);
            //initMap()
            updateDriverMarker();
        } else {
            this.notFoundDrivers.show();
            console.log("Received null or invalid data for updateLocations.");
        }


    }
    createDriverCard(driver) {
    // Create a new div element
    const cardDiv = document.createElement('div');
    cardDiv.classList.add('card', 'mb-1');
    //const id = `${driver["Name"]}_${driver['Id']}`
    //console.log("id", id)
    // Add an id attribute to the div element
    cardDiv.setAttribute('id', driver['Id']);
    // Create inner elements
    const innerDiv1 = document.createElement('div');
    innerDiv1.classList.add('p-2');

    const innerDiv2 = document.createElement('div');
    innerDiv2.classList.add('d-flex', 'align-items-center');

    // Inside innerDiv2
    const colDiv1 = document.createElement('div');
    colDiv1.classList.add('col-auto');

    const anchorTag = document.createElement('a');
    anchorTag.href = `/Driver/Details/${driver['Id']}`;
    anchorTag.classList.add('avatar', 'avatar-md');

    const imgTag = document.createElement('img');
    imgTag.src = driver['PictureUrl'] !== null ? driver['PictureUrl'] : './assets/avatars/imageGrey.jpg';
    imgTag.alt = '...';
    imgTag.classList.add('avatar-img', 'rounded-circle');

    anchorTag.appendChild(imgTag);
    colDiv1.appendChild(anchorTag);

    const colDiv2 = document.createElement('div');
    colDiv2.classList.add('col', 'pr-0');

    const strongTag = document.createElement('strong');
    strongTag.classList.add('mb-1', 'fs-7');
    strongTag.textContent = driver['Name'];

    const spanTag = document.createElement('span');
    spanTag.classList.add('dot', 'dot-lg', 'bg-success', 'ml-1');

    const pTag = document.createElement('p');
    pTag.classList.add('small', 'text-muted', 'mb-1', 'fs-8');
    pTag.textContent = 'متصل';

    colDiv2.appendChild(strongTag);
    colDiv2.appendChild(spanTag);
    colDiv2.appendChild(pTag);

    const colDiv3 = document.createElement('div');
    colDiv3.classList.add('col-auto');

    const cardShadowDiv = document.createElement('div');
    cardShadowDiv.classList.add('card', 'shadow-none');

    const cardBodyDiv = document.createElement('div');
    cardBodyDiv.classList.add('card-body', 'py-1', 'px-3');

    const strongTag2 = document.createElement('strong');
    strongTag2.classList.add('fs-8');
        strongTag2.textContent = driver['CustomerTypeId'] === 2 ?"سائق": "عميل" ;

    cardBodyDiv.appendChild(strongTag2);
    cardShadowDiv.appendChild(cardBodyDiv);
    colDiv3.appendChild(cardShadowDiv);

    innerDiv2.appendChild(colDiv1);
    innerDiv2.appendChild(colDiv2);
    innerDiv2.appendChild(colDiv3);

    innerDiv1.appendChild(innerDiv2);

    // Inside cardDiv
    const pxDiv = document.createElement('div');
    pxDiv.classList.add('px-3');

    const timelineDiv = document.createElement('div');
    timelineDiv.classList.add('timeline', 'p-3');

    const timelineItemEndDiv = document.createElement('div');
    timelineItemEndDiv.classList.add('timeline-item-end');

    const plDiv = document.createElement('div');
    plDiv.classList.add('pl-5');

    const mb1Div = document.createElement('div');
    mb1Div.classList.add('mb-1', 'd-flex', 'align-items-md-center');

    const strongTag3 = document.createElement('strong');
    strongTag3.classList.add('fs-8');
    console.log(driver['AddressDesc'] !== null && driver['AddressDesc'] !== "")
    const address = driver['AddressDesc'] !== null && driver['AddressDesc'] !== "" ? driver['AddressDesc'] : 'مديرية الوحدة أمانة العاصمة, اليمن';
    strongTag3.textContent = address;

    mb1Div.appendChild(strongTag3);
    plDiv.appendChild(mb1Div);
    timelineItemEndDiv.appendChild(plDiv);
    timelineDiv.appendChild(timelineItemEndDiv);
    pxDiv.appendChild(timelineDiv);
    cardDiv.appendChild(innerDiv1);
    cardDiv.appendChild(pxDiv);



    return cardDiv;
    }
    handlUpdateDailyOrdersList(order) {
        console.log("order online:", order);
        // Ensure the function is accessible via the global window object
        window.getAllDailyOrders();
    }


}




