
  (function ($) {
    "use strict";

    $.fn.CustomToast = function (options) {
      const $container = this;

      function showToast(options) {
          const $toast = createToast(options),
              $progress = $toast.find(".progress");
        $container.append($toast);
          
        setTimeout(function () {
          $toast.addClass("active");
             $progress.addClass("active"); 
        }, 500);
        
        
        const timer1 = setTimeout(function () {
          hideToast($toast, $progress);
        }, 5000);

        const timer2 = setTimeout(function () {
          $progress.removeClass("active");
        }, 5300);

        $toast.find(".close-toast").on("click", function () {
          hideToast($toast, $progress);
        });
      }

      function createToast(options) {
        const $toast = $("<div>").addClass("toast");
        const $toastContent = $("<div>").addClass("toast-content");
        const $checkIcon = $("<i>")
          .addClass("fe fe-box " + options.icon + " check")
          .css("background-color", options.color);
        const $message = $("<div>").addClass("message");
        const $typeSpan = $("<span>")
          .addClass("fs-7")
          .text(options.type.charAt(0).toUpperCase() + options.type.slice(1));
        const $textSpan = $("<span>").addClass("fs-8").text(options.text);
        const $closeIcon = $("<i>").addClass("fe fe-x close-toast");
        const $progress = $("<div>")
          .addClass("progress").addClass(options.status);
        $toast.css("border-right-color", options.color);
        $message.append($typeSpan, $textSpan);
        $toastContent.append($checkIcon, $message);
        $toast.append($toastContent, $closeIcon, $progress);

        return $toast;
      }

      function hideToast($toast, $progress) {
        $toast.removeClass("active");

        setTimeout(function () {
          $progress.removeClass("active");
          $toast.remove();
        }, 300);
      }

      return {
        showToast: showToast,
      };
    };
  })(jQuery);



