﻿$(document).ready(function () {
    var myElement = $('.requiredSummernote')
    const form = $.Validation.getForm();
    form.settings.ignore = ":hidden:not(textarea), [contenteditable='true']:not([name])";
    myElement.summernote({
        lang: 'ar-AR',
        height: 300,                 // set editor height
        minHeight: null,             // set minimum height of editor
        maxHeight: null,             // set maximum height of editor
        focus: true,
        callbacks: {
            onChange: function (contents, $editable) {
                myElement.val(myElement.summernote('isEmpty') ? "" : contents);
                form.element(myElement);
            }
        }

    });
});