﻿; (function ($) {
	'use strict';

    // Global error handler for synchronous errors
    window.onerror = function (message, source, lineno, colno, error) {
        console.error("Global error caught:", { message, source, lineno, colno, error });
        //debugger
        // Optionally, send the error details to your logging server or display a user-friendly message
        return false; // Returning false lets the browser's default error handling occur.
    };

    // Global error handler for unhandled promise rejections
    window.addEventListener('unhandledrejection', function (event) {
        console.error("Unhandled promise rejection:", event.reason);
        //debugger
        // Optionally, report the error to your logging service
    });
})(jQuery);