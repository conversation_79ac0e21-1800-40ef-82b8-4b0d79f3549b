﻿class GoogleMap {
    constructor() {
        this.map = null;
        this.directionsService = null;
        this.directionsDisplay = null;
    }

    async initMap() {
        const mapElement = document.getElementById('map');
        const locationData = this.getLocationData(mapElement);

        if (locationData) {
            this.createMap(locationData.pickupPoint);
            this.createRoute(locationData.pickupPoint, locationData.destination);
        } else {
            this.createDefaultMap();
        }

        this.addUserMarkers();
        this.findNearbyCabs();
        document.getElementById('loading-spinner').style.display = 'none';
    }

    getLocationData(mapElement) {
        if (mapElement.hasAttribute('data-location')) {
            return JSON.parse(mapElement.getAttribute('data-location'));
        }
        return null;
    }

    createMap(location) {
        this.map = new MapManager().createMap(location);
    }

    createRoute(pointA, pointB) {
        this.directionsService = new RouteManager().createDirectionsService();
        this.directionsDisplay = new RouteManager().createDirectionsRenderer(this.map);
        new RouteManager().calculateAndDisplayRoute(this.directionsService, this.directionsDisplay, pointA, pointB);
    }

    createDefaultMap() {
        const defaultLocation = { lat: 37.7749, lng: -122.4194 };
        this.map = new MapManager().createMap(defaultLocation);
    }

    addUserMarkers() {
        const users = [
            { lat: 37.7749, lng: -122.4194 },
            { lat: 37.774899453025796, lng: -122.40802228068014 },
            { lat: 37.774899453025796, lng: -122.43077771931983 },
            { lat: 37.765906783940814, lng: -122.4194 },
            { lat: 37.783893216059184, lng: -122.4194 }
        ];

        const carIcon = {
            url: "/assets/avatars/customer.svg",
            scaledSize: new google.maps.Size(48, 48),
            anchor: new google.maps.Point(16, 32)
        };

        users.forEach(user => {
            new MarkerManager().addMarker(this.map, user, 'Customer', carIcon);
        });
    }

    findNearbyCabs() {
        var driverIcon = {
            url: "/assets/avatars/taxi3d.svg",
            scaledSize: new google.maps.Size(48, 48), // Adjust size as needed
            anchor: new google.maps.Point(16, 32) // Set anchor point for precise placement
        };
        var cabLocations = [
            {
                "lat": 37.75880508654381,
                "lng": -122.45967576052189,
                "id": 1
            },
            {
                "lat": 37.76260500078658,
                "lng": -122.42843338991642,
                "id": 2
            },
            {
                "lat": 37.77536042799209,
                "lng": -122.43083664919376,
                "id": 3
            },
            {
                "lat": 37.77563179615532,
                "lng": -122.41298386599064,
                "id": 4
            },
            {
                "lat": 37.77508905883279,
                "lng": -122.42328354860783,
                "id": 5
            },
            {
                "lat": 37.766947520889495,
                "lng": -122.41847703005314,
                "id": 6
            },

            {
                "lat": 37.76559051074694,
                "lng": -122.44834610964298,
                "id": 12
            },
            {
                "lat": 37.7695257715604,
                "lng": -122.42620179201603,
                "id": 13
            },
            {
                "lat": 37.77017196507862,
                "lng": -122.4704360961914,
                "id": 17
            }
        ];
        cabLocations.forEach(cab => {
            new MarkerManager().addMarker(this.map, cab, `Cab id: ${cab.id}`, driverIcon);
        });
    }
}

class MapManager {
    createMap(location) {
        return new google.maps.Map(document.getElementById('map'), {
            center: new google.maps.LatLng(location.lat, location.lng),
            zoom: 13
        });
    }
}

class RouteManager {
    createDirectionsService() {
        return new google.maps.DirectionsService();
    }

    createDirectionsRenderer(map) {
        return new google.maps.DirectionsRenderer({ map: map });
    }

    calculateAndDisplayRoute(directionsService, directionsDisplay, pointA, pointB) {
        directionsService.route({
            origin: new google.maps.LatLng(pointA.latitude, pointA.longitude),
            destination: new google.maps.LatLng(pointB.latitude, pointB.longitude),
            avoidTolls: true,
            avoidHighways: false,
            travelMode: google.maps.TravelMode.DRIVING
        }, (response, status) => {
            if (status == google.maps.DirectionsStatus.OK) {
                directionsDisplay.setDirections(response);
            } else {
                window.alert('Directions request failed due to ' + status);
            }
        });
    }
}

class MarkerManager {
    addMarker(map, position, title, icon) {
        new google.maps.Marker({
            position: new google.maps.LatLng(position.latitude, position.longitude),
            title: title,
            icon: icon,
            map: map
        });
    }
}

// Instantiate and initialize the GoogleMap class
function initMap() {
    const googleMap = new GoogleMap();
    googleMap.initMap();

}
function loadGoogleMapsScript() {
    // Show the loading spinner while waiting for the Google Maps API to load
    document.getElementById('loading-spinner').style.display = 'block';

    // Load the Google Maps API script
    /*';*/
    var script = document.createElement('script');
    script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyAtA01tsg6sH2RYZj7Wwp7d7YQvuMzscr0&libraries=geometry&callback=initMap&v=weekly&language=ar&region=YE';
    script.defer = true;
    document.head.appendChild(script);
}

//class GoogleMap {
//    constructor() {
//        this.map = null;
//        this.directionsService = null;
//        this.directionsDisplay = null;
//    }

//    async initMap() {
//        const mapElement = document.getElementById('map');
//        const locationData = this.getLocationData(mapElement);

//        if (locationData) {
//            this.createMap(locationData.pickupPoint);
//            this.createRoute(locationData.pickupPoint, locationData.destination);
//        } else {
//            this.createDefaultMap();
//        }

//        this.addUserMarkers();
//        this.findNearbyCabs();
//        this.hideLoadingSpinner();
//    }

//    getLocationData(mapElement) {
//        return mapElement.hasAttribute('data-location') ? JSON.parse(mapElement.getAttribute('data-location')) : null;
//    }

//    createMap(location) {
//        this.map = MapManager.createMap(location);
//    }

//    createRoute(pointA, pointB) {
//        this.directionsService = RouteManager.createDirectionsService();
//        this.directionsDisplay = RouteManager.createDirectionsRenderer(this.map);
//        RouteManager.calculateAndDisplayRoute(this.directionsService, this.directionsDisplay, pointA, pointB);
//    }

//    createDefaultMap() {
//        const defaultLocation = { lat: 37.7749, lng: -122.4194 };
//        this.map = MapManager.createMap(defaultLocation);
//    }

//    addUserMarkers() {
//        const users = [
//            { lat: 37.7749, lng: -122.4194 },
//            { lat: 37.774899453025796, lng: -122.40802228068014 },
//            { lat: 37.774899453025796, lng: -122.43077771931983 },
//            { lat: 37.765906783940814, lng: -122.4194 },
//            { lat: 37.783893216059184, lng: -122.4194 }
//        ];

//        const carIcon = {
//            url: "/assets/avatars/customer.svg",
//            scaledSize: new google.maps.Size(48, 48),
//            anchor: new google.maps.Point(16, 32)
//        };

//        users.forEach(user => {
//            MarkerManager.addMarker(this.map, user, 'Customer', carIcon);
//        });
//    }

//    findNearbyCabs() {
//        const cabLocations = []; // Assuming this is defined somewhere
//        const driverIcon = {
//            url: "/assets/avatars/taxi3d.svg",
//            scaledSize: new google.maps.Size(48, 48),
//            anchor: new google.maps.Point(16, 32)
//        };

//        cabLocations.forEach(cab => {
//            MarkerManager.addMarker(this.map, cab, `Cab id: ${cab.id}`, driverIcon);
//        });
//    }

//    hideLoadingSpinner() {
//        document.getElementById('loading-spinner').style.display = 'none';
//    }
//}

//class MapManager {
//    static createMap(location) {
//        return new google.maps.Map(document.getElementById('map'), {
//            center: new google.maps.LatLng(location.lat, location.lng),
//            zoom: 13
//        });
//    }
//}

//class RouteManager {
//    static createDirectionsService() {
//        return new google.maps.DirectionsService();
//    }

//    static createDirectionsRenderer(map) {
//        return new google.maps.DirectionsRenderer({ map });
//    }

//    static calculateAndDisplayRoute(directionsService, directionsDisplay, pointA, pointB) {
//        directionsService.route({
//            origin: new google.maps.LatLng(pointA.latitude, pointA.longitude),
//            destination: new google.maps.LatLng(pointB.latitude, pointB.longitude),
//            avoidTolls: true,
//            avoidHighways: false,
//            travelMode: google.maps.TravelMode.DRIVING
//        }, (response, status) => {
//            if (status == google.maps.DirectionsStatus.OK) {
//                directionsDisplay.setDirections(response);
//            } else {
//                window.alert('Directions request failed due to ' + status);
//            }
//        });
//    }
//}

//class MarkerManager {
//    static addMarker(map, position, title, icon) {
//        new google.maps.Marker({
//            position: new google.maps.LatLng(position.latitude, position.longitude),
//            title,
//            icon,
//            map
//        });
//    }
//}

//// Instantiate and initialize the GoogleMap class
//function initMap() {
//    const googleMap = new GoogleMap();
//    googleMap.initMap();
//}

//function loadGoogleMapsScript() {
//    // Show the loading spinner while waiting for the Google Maps API to load
//    document.getElementById('loading-spinner').style.display = 'block';

//    // Load the Google Maps API script
//    var script = document.createElement('script');
//    script.src = 'https://maps.googleapis.com/maps/api/js?key=KeyMap&libraries=geometry&callback=initMap&v=weekly&language=ar&region=YE';
//    script.defer = true;
//    document.head.appendChild(script);
//}


