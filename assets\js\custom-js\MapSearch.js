﻿let map, marker, autocomplete, geocoder;

function initMap() {
    // Initialize the map centered at a default location
    map = new google.maps.Map(document.getElementById("map"), {
        center: { lat: 15.552, lng: 48.516 }, // Example: San Francisco
        zoom: 7,
    });

    // Initialize the geocoder
    geocoder = new google.maps.Geocoder();

    // Create a marker
    marker = new google.maps.Marker({
        map: map,
        anchorPoint: new google.maps.Point(0, -29),
    });

    const options = {
        componentRestrictions: { country: "YE" }, // Restrict to Yemen
        fields: ["address_components", "geometry", "icon", "name"], // Specify the fields you need
    };
    // Link the search box to the UI element
    const input = document.getElementById("searchBox");
    autocomplete = new google.maps.places.Autocomplete(input, options);
    autocomplete.bindTo("bounds", map);
    map.controls[google.maps.ControlPosition.TOP_CENTER].push(input);




    // Add listener for autocomplete selection
    autocomplete.addListener("place_changed", () => {
        const place = autocomplete.getPlace();
        if (!place.geometry || !place.geometry.location) {
            console.log("No details available for input: '" + place.name + "'");
            return;
        }

        // Set the marker and map view
        map.setCenter(place.geometry.location);
        map.setZoom(15);
        marker.setPosition(place.geometry.location);
        marker.setVisible(true);

        // Get the latitude, longitude, and location description
        const latitude = place.geometry.location.lat().toString();
        const longitude = place.geometry.location.lng().toString();
        const locationDescription = place.formatted_address || place.name;


        console.log("place.formatted_address ", place.formatted_address)
        console.log("place.name ", place.name)
        // Update input fields with location data
        document.getElementById("latitude").value = latitude;
        document.getElementById("longitude").value = longitude;
        document.getElementById("locationDescription").value =
            locationDescription;
    });

    // Add click listener to the map to get coordinates and address
    map.addListener("click", (event) => {
        const latitude = event.latLng.lat().toString();
        const longitude = event.latLng.lng().toString();

        // Move the marker to clicked location
        marker.setPosition(event.latLng);
        marker.setVisible(true);    

        // Update latitude and longitude fields
        document.getElementById("latitude").value = latitude;
        document.getElementById("longitude").value = longitude;

        // Get address from geocoder
        geocoder.geocode({ location: event.latLng }, (results, status) => {
            if (status === "OK" && results[0]) {
                const locationDescription = results[0].formatted_address;
                document.getElementById("locationDescription").value =
                    locationDescription;
            } else {
                document.getElementById("locationDescription").value =
                    "No address found";
                console.error("Geocoder failed due to: " + status);
            }
        });
    });


    const latitude = document.getElementById("latitude").value;
    const longitude = document.getElementById("longitude").value;
    const locationDescription = document.getElementById("locationDescription").value;

    if (latitude !== null && longitude !== null && locationDescription !== null) {
        addMarker(map, new google.maps.LatLng(latitude, longitude), locationDescription)
    }
    function addMarker(map, position, title) {
        new google.maps.Marker({
            position: position,
            title: title,
            map: map,
            anchorPoint: new google.maps.Point(0, -29)
        });
        map.setCenter(position);
        map.setZoom(15)
    }
}

// Initialize map after page load
window.onload = initMap;