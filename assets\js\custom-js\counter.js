﻿;(function ($) {
	'use strict';

    var elementsWithDataUrl = $('[data-count-url]');
    //console.log(elementsWithDataUrl)
    //debugger
    function getDataRoute() {
        elementsWithDataUrl.each(function (i,v) {
            
            $.ajax({
                headers: {
                    "X-XSRF-TOKEN": $("[name='AntiforgeryFieldname']").val(),
                    "Authorization": $("[name='AntiforgeryFieldname']").val()
                },
                type: 'GET',
                contentType: "application/json; charset=utf-8",
                url: $(this).data('count-url'),
                dataType: "json",
                cache: false,
                success: function (data) {
                    //console.log("datacount", data)
                    //console.log("datacount", i,v)
                    if (data["url"] === "1") {
                        // Get the value of the data-url attribute for each element
                        var $this = $(v).children("[data-count-name]");
                        var dataUrlValue = $this.data('count-name');
                       
                        // Get the text content of the current element
                        var currentText = $this.text();

                        // Find the index of the first occurrence of '0'
                        var index = currentText.indexOf('0');
                        // If '0' is found, replace it with the value from data[dataUrlValue]
                        if (index !== -1) {
                            var newText = currentText.substring(0, index) + data["resObject"][dataUrlValue] + currentText.substring(index + 1);
                            $this.text(newText);
                        }
                    }else
                        if (data["url"] === "2") {
                            const countUserName = $("[data-user-count-name]");
                            countUserName.each(function (i,v) {
                        // Get the value of the data-url attribute for each element
                                var dataUrlValue = $(this).data('user-count-name');
                                var useId = $(this).data('user-id');
                        //$(this).text(`${data[dataUrlValue]}+`);

                        // Get the text content of the current element
                        var currentText = $(this).text();

                        // Find the index of the first occurrence of '0'
                        var index = currentText.indexOf('0');
                        // If '0' is found, replace it with the value from data[dataUrlValue]
                                if (index !== -1) {
                                    const matchingName = data["resObject"].filter(item => item.id === useId)[0];

                                    //console.log(matchingName, useId)
                                    var newText = currentText.substring(0, index) + matchingName[dataUrlValue] + currentText.substring(index + 1);
                            $(this).text(newText);
                        }
                        //console.log(currentText,index);
                    });
                        }
                    
                },
                error: function (req, status, err) {
                    console.log('something went wrong', status, err + "   !!!!    " + JSON.stringify(req, null, 4));
                    //debugger
                }
            });
        });
    }

  
    getDataRoute();

    function getAllDailyOrders() {
        $.ajax({
            headers: {
                "X-XSRF-TOKEN": $("[name='AntiforgeryFieldname']").val(),
                "Authorization": $("[name='AntiforgeryFieldname']").val()
            },
            type: 'GET',
            contentType: "application/json; charset=utf-8",
            url: "/Order/GetAllDailyOrders",
            dataType: "json",
            cache: false,
            success: function (data) {

                console.log("data", data)
                const notFoundOrder = $("#notFoundOrder");
                if (data !== null && data['dailyOrderList'] !== null) {
                    const dailyOrderListCount = data['dailyOrderListCount'];
                    const dailyOrderList = data['dailyOrderList'];
                    const statusOrderWithStaticsDtos = data['statusOrderWithStaticsDtos'];
                    const cardOrderScrollable = $("#cardDailyOrder").children(".simplebar-wrapper").children(".simplebar-mask").children().children().children();
                    console.log("cardOrderScrollable", cardOrderScrollable);
                    document.getElementById("orderDailyCount").textContent = data['dailyOrderListCount'];
                    if (dailyOrderListCount > 0 && dailyOrderList.length > 0) {
                        notFoundOrder.hide();

                        dailyOrderList.forEach(function (v, i) {
                            v['orderStatusNmae'] = statusOrderWithStaticsDtos.find(i => i.id === v.orderStatus)['statusDescription'];
                            if (cardOrderScrollable.children(`#${v['id']}`).length === 0) {
                                 cardOrderScrollable.children().first().before(createWidget(v))

                                //$(cardOrderScrollable).on('click', '.dropdown-item', function (e) {

                                //    if (this.hasAttribute("data-component")) {
                                //        const model = $(this).data("component");
                                //        console.log(v)
                                //        passingDataRouteModel(model, v);
                                //    }
                                //});
                            }
                        });

                        const result = statusOrderWithStaticsDtos.reduce((acc, item) => {
                            acc.label.push(item.statusDescription);
                            acc.statusCount.push(item.statusCount);
                            return acc;
                        }, { label: [], statusCount: [] });

                        createChart(result)
                    }
                    else {
                        notFoundOrder.show();
                    }

                } else {
                    notFoundOrder.show();
                }
              
            },
            error: function (req, status, err) {
                console.log('something went wrong DailyOrders', status, err + "   !!!!    " + JSON.stringify(req, null, 4));
                //debugger
            }
        });
    }
    getAllDailyOrders();
    // Attach the function to the global window object
    window.getAllDailyOrders = getAllDailyOrders;

    window.addEventListener('online', function () {
        getDataRoute();
        getAllDailyOrders();
       
        DrawingChartBarType();
    });
})(jQuery);

function createWidget(data) {
    const ele = document.createElement('a');
    ele.className = 'card mb-1';
    ele.id = data['id'];
    ele.href = `/Order/Details/${data['id']}`;
    ele.target = "_blank";

    ele.innerHTML = `
        <div class="p-2">
            <div class="d-flex align-items-center">
                <div class="col-auto">
                    <a href="/Customer/Details/${data['customerId']}" class="avatar avatar-md">
                        <img src="${data['customerImageUrl'] !== null ? data['customerImageUrl'] : './assets/avatars/imageGrey.jpg'}" alt="..." class="avatar-img rounded-circle">
                    </a>
                </div>
                <div class="col pr-0">
                    <a href="/Customer/Details/${data['customerId']}">
                        <strong class="mb-1 fs-7">${data['customerName']}</strong>
                    </a>
                </div>
                <div class="col-auto">
                    <div class="card shadow-none">
                        <div class="card-body py-1 px-3">
                            <strong class="fs-8">${timeAgo(data['orderTime'])}</strong>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="card shadow-none bg-light m-2 mb-2">
            <div class="card-body p-0">
                <div class="timeline p-3">
                    <div class="pb-3 timeline-item-start item-primary">
                        <div class="pl-5">
                            <div class="mb-1 d-flex align-items-md-center"><strong class="fs-8">${data['pickupAddress']}</strong></div>
                        </div>
                    </div>
                    <div class="timeline-item-end">
                        <div class="pl-5">
                            <div class="mb-1 d-flex align-items-md-center"><strong class="fs-8">${data['desitnationAddress']}</strong></div>
                        </div>
                    </div>
                </div>
                <div class="card shadow-none p-0 d-flex align-items-center">
                    <div class="card-body py-1 px-3">
                        <strong class="fs-8">${data['orderStatusNmae']}</strong>
                    </div>
                </div>
            </div>
        </div>`;

    

    return ele;
}

let chart;
function createChart(data) {

    var options = {
        series: data['statusCount'],
        chart: {
            type: 'donut',
            height: 350,
            zoom: { enabled: !1 },
            toolbar: { show: !1 },
        },
        theme: { mode: colors.chartTheme },
        plotOptions: {
            pie: {
                donut: { size: "40%", background: "transparent" },
                expandOnClick: !1,
            },
        },
        labels: data['label'],
        dataLabels: {
            enabled: !0,
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 200
                },
                legend: {
                    position: 'bottom'
                }
            }
        }]
    };
    if (chart) {
        // Update existing chart with new options and series data
        chart.updateOptions(options);
    } else {
        // Render new chart
        chart = new ApexCharts(document.querySelector("#dailyOrderCharts"), options);
        chart.render();
    }
}
function timeAgo(dateString) {
    // Parse the date string into a Date object
    const date = new Date(dateString);
    // Check if the parsed date is valid
    if (isNaN(date)) {
        return "Invalid date";
    }
    const seconds = Math.floor((new Date() - date) / 1000);
    console.log("seconds", seconds);
    const interval = Math.floor(seconds / 31536000);

    if (interval > 1) {
        return "منذ " + interval + " سنوات";
    }
    if (interval === 1) {
        return "منذ " + interval + " سنة";
    }

    const months = Math.floor(seconds / 2628000);
    if (months > 1) {
        return "منذ " + months + " أشهر";
    }
    if (months === 1) {
        return "منذ " + months + " شهر";
    }

    const days = Math.floor(seconds / 86400);
    if (days > 1) {
        return "منذ " + days + " أيام";
    }
    if (days === 1) {
        return "منذ " + days + " يوم";
    }

    const hours = Math.floor(seconds / 3600);
    if (hours > 1) {
        return "منذ " + hours + " ساعات";
    }
    if (hours === 1) {
        return "منذ " + hours + " ساعة";
    }

    const minutes = Math.floor(seconds / 60);
    if (minutes > 1) {
        return "منذ " + minutes + " دقائق";
    }
    if (minutes === 1) {
        return "منذ " + minutes + " دقيقة";
    }

    return "الآن";
}
function getDateTime() {
    var reportRangeToggle = $('#reportrange'),
        reportTypes = $('#report_types'),
        start = [moment().startOf('year'), moment()][0],
        end = moment();

    var dateStart = moment().subtract(29, 'days');
    var dateEnd = moment();

    function cb(start, end, label) {
        
        dateStart = start.format('YYYY-MM-DDTHH:mm:ss.SSSZ');
        dateEnd = end.format('YYYY-MM-DDTHH:mm:ss.SSSZ');
        var arabicMonths = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

        // Extract month and day from start and end dates
        var startMonth = start.format('M');
        var startDay = start.format('D');
        var endMonth = end.format('M');
        var endDay = end.format('D');

        // Convert month numbers to Arabic month names
        var startMonthArabic = arabicMonths[startMonth - 1];
        var endMonthArabic = arabicMonths[endMonth - 1];

        // Construct the Arabic date range string
        var arabicDateRange = startMonthArabic + ' ' + startDay + ' - ' + endMonthArabic + ' ' + endDay + ', ' + start.format('YYYY');
        //reportRangeToggle.find('span').text(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));

        // Update the reportRangeToggle element with the Arabic date range
        reportRangeToggle.find('span').text(arabicDateRange);

        //console.log("date ", dateStart, dateEnd)
        //console.log("date ", label)
        getRouteAjax("  /Home/GetStatisticsCountOrdersByDates",
           ` {
                "startDate": "${dateStart}",
                "endDate": "${dateEnd}",
                "filterType": "null"
            }`
        )
       

    }

    reportRangeToggle.daterangepicker(
        {
            autoApply: true,
            startDate: start,
            endDate: end,
            opens: 'left',
            locale:
            {
                format: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
                separator: " - ",
                applyLabel: "تطبيق",
                cancelLabel: "إلغاء",
                fromLabel: "من",
                toLabel: "إلى",
                direction: 'rtl',
                customRangeLabel: "تحديد المدى",
                weekLabel: "أسبوع",
                daysOfWeek: ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"],
                monthNames: [
                    "يناير", "فبراير", "مارس", "إبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
                ],
                firstDay: 1
            },
            ranges: {
                'اليوم': [moment(), moment()],
                'أمس': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'آخر 7 أيام': [moment().subtract(6, 'days'), moment()],
                'آخر 30 يومًا': [moment().subtract(29, 'days'), moment()],
                'هذا الشهر': [moment().startOf('month'), moment().endOf('month')],
                'الشهر الماضي': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                'السنة الحالية': [moment().startOf('year'), moment()],
                'منذ البداية': [moment().year(2024).month(00).date(01), moment()],
            }
        }, cb);
    //cb(start, end);
    cb(start, end);
}
var columnChart;
function DrawingChartBarType(dataReponse) {
   
    var columnChartoptions = {
            series: dataReponse['series'],
            chart: {
                type: "bar",
                height: 350,
                stacked: !1,
                columnWidth: "70%",
                zoom: { enabled: !0 },
                toolbar: { show: !1 },
                background: "transparent",
                scrollbar: {
                    enabled: true,
                },
            },
            dataLabels: { enabled: !1 },
            theme: { mode: colors.chartTheme },
            responsive: [
                {
                    breakpoint: 480,
                    options: { legend: { position: "bottom", offsetX: -10, offsetY: 0 } },
                },
            ],
            plotOptions: {
                bar: {
                    horizontal: !1,
                    columnWidth: "20%",
                    radius: 30,
                    enableShades: !1,
                    endingShape: "rounded",
                },
            },
            xaxis: {
                categories: dataReponse['categories'], // fetch categories from a database or an API
                labels: {
                    show: !0,
                    trim: !0,
                    minHeight: void 0,
                    maxHeight: 120,
                   
                    
                    style: {
                        colors: colors.mutedColor,
                        cssClass: "text-black2",
                        fontFamily: 'Cairo',
                        fontSize: '10px',
                    },
                    formatter: function (value) {
                        // format the label text here
                        return value;
                    }
                },
                axisBorder: { show: !1 },
            },
            yaxis: {
                labels: {
                    show: !0,
                    trim: !1,
                    offsetX: -10,
                    minHeight: void 0,
                    maxHeight: 120,
                    style: {
                        colors: colors.mutedColor,
                        cssClass: "text-black2",
                        fontFamily: 'Cairo',
                    },
                },
            },
            legend: {
                position: "top",
                fontFamily: base.defaultFontFamily,
                fontWeight: 400,
                labels: { colors: colors.mutedColor, useSeriesColors: !1 },
                markers: {
                    width: 10,
                    height: 10,
                    strokeWidth: 0,
                    strokeColor: "#fff",
                    fillColors: [extend.primaryColor, extend.primaryColorLighter],
                    radius: 6,
                    customHTML: void 0,
                    onClick: void 0,
                    offsetX: 0,
                    offsetY: 0,
                },
                itemMargin: { horizontal: 10, vertical: 0 },
                onItemClick: { toggleDataSeries: !0 },
                onItemHover: { highlightDataSeries: !0 },
            },
            fill: {
                opacity: 1,
                colors: [base.primaryColor, extend.primaryColorLighter],
            },
            grid: {
                show: !0,
                borderColor: colors.borderColor,
                strokeDashArray: 0,
                position: "back",
                xaxis: { lines: { show: !1 } },
                yaxis: { lines: { show: !0 } },
                row: { colors: void 0, opacity: 0.5 },
                column: { colors: void 0, opacity: 0.5 },
                padding: { top: 0, right: 0, bottom: 0, left: 0 },
            },
        };
    



    // Get the chart container width
    const chartContainer = document.querySelector("#columnCharts");
    const chartWidth = chartContainer.offsetWidth;
    const data = dataReponse['categories'];
    const dataLength = data.length;
    // Get the adjusted x-axis label settings
    const adjustedXAxisLabels = adjustXAxisLabels(chartWidth, dataLength);
    console.log("adjustedXAxisLabels", adjustedXAxisLabels)
    console.log("adjustedXAxisLabels", chartWidth)
    // Update the x-axis label settings in the chart options
    columnChartoptions.xaxis.labels.offsetX = adjustedXAxisLabels.offsetX;
    columnChartoptions.xaxis.labels.orientation = adjustedXAxisLabels.orientation;
    columnChartoptions.xaxis.labels.offsetY = adjustedXAxisLabels.offsetY;
    columnChartoptions.xaxis.labels.rotate = adjustedXAxisLabels.rotate;
    columnChartoptions.xaxis.labels.step = adjustedXAxisLabels.step;
    columnChartoptions.xaxis.labels.style.fontSize = adjustedXAxisLabels.fontSize;

   

    if (columnChart) {
        // Update existing chart with new options and series data
        columnChart.updateOptions(columnChartoptions);
    } else {
        // Render new chart
        columnChartCtn = document.querySelector("#columnCharts");
        columnChartCtn &&
            (columnChart = new ApexCharts(columnChartCtn, columnChartoptions)).render();
    }

}
function adjustXAxisLabels(chartWidth, dataLength) {
    let offsetX = -25;
    let orientation = 'horizontal';
    let offsetY = 50;
    let rotate = -45;
    let step = 2;
    let fontSize = '10px';

    if (chartWidth < 480 || dataLength <= 12) {
        offsetX = 0;
        orientation = 'horizontal';
        offsetY = 0;
        rotate = 0;
        step = 3;
        fontSize = '10px';
    } else if (chartWidth < 768 || dataLength <= 20) {
        offsetX = -20;
        orientation = 'horizontal';
        offsetY = 45;
        rotate = -45;
        step = 2;
        fontSize = '9px';
    } else {
        offsetX = 0;
        orientation = 'horizontal';
        offsetY = 0;
        rotate = 90;
        step = 1;
        fontSize = '10px';
    }

    return {
        offsetX,
        orientation,
        offsetY,
        rotate,
        step,
        fontSize,
    };
}
getDateTime();
