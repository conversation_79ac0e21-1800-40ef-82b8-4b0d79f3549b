﻿//; (function ($) {
//    'use strict';

    var colors = {
        chartTheme: 'light',
        mutedColor: '#999',
        borderColor: '#e0e0e0'
    };

    //var base = {
    //    defaultFontFamily: 'Cairo, Arial, sans-serif',
    //   // primaryColor: '#00E396'
    //};

    var chartColors = ['#008FFB', '#00E396', '#FEB019', '#FF4560', '#775DD0'];




    function createCustomLegend(chart, containerId) {
        var legendContainer = document.getElementById(containerId);
        var series = chart.w.globals.series;
        var labels = chart.w.globals.labels;
        var seriesTotals = chart.w.globals.seriesTotals;

        labels.forEach(function (label, index) {
            var legendItemContainer = document.createElement('div');
            legendItemContainer.classList.add('col-6');

            var legendItem = document.createElement('div');
            legendItem.classList.add('p-1');

            var small = document.createElement('small');

            var colorDot = document.createElement('span');
            colorDot.classList.add('dot', 'dot-lg', 'mr-2');
            colorDot.style.backgroundColor = chartColors[index];

            var labelSpan = document.createElement('span');
            labelSpan.classList.add('h6', 'mb-0');
            labelSpan.textContent = label;

            small.appendChild(colorDot);
            small.appendChild(labelSpan);

            var seriesTotalSpan = document.createElement('p');
            seriesTotalSpan.classList.add('h4', 'mb-0');
            seriesTotalSpan.textContent = seriesTotals[index];

            legendItem.appendChild(small);
            legendItem.appendChild(seriesTotalSpan);

            legendItemContainer.appendChild(legendItem);
            legendItemContainer.setAttribute('data-index', index);

            legendItemContainer.addEventListener('mouseover', function () {
                var seriesIndex = parseInt(this.getAttribute('data-index'));
                chart.toggleSeries(chart.w.globals.labels[seriesIndex]);
            });

            legendContainer.appendChild(legendItemContainer);
        });
    }
    function generateCharts(data) {
        var chartContainer = document.getElementById('chartContainer');
        data.forEach(function (item, index) {
            // Create chart container
            var chartWrapper = document.createElement('div');
            chartWrapper.classList.add('col-md-12');
            console.log("index", index, index < 1)
            if (index > 0) {
                chartWrapper.classList.add('border-top', "pt-4", 'mb-4');
            }

            var chartRow = document.createElement('div');
            chartRow.classList.add('row');

            var legendCol = document.createElement('div');
            legendCol.classList.add('col-md-6');
            var legendTitle = document.createElement('p');
            legendTitle.classList.add('mb-0');
            var legendStrong = document.createElement('strong');
            legendStrong.classList.add('h6', 'mb-0');
            legendStrong.textContent = item.serviceName;
            legendTitle.appendChild(legendStrong);
            var legendAmount = document.createElement('h3');
            legendAmount.textContent = `${item.orderCount}`;
            var legendDiv = document.createElement('div');
            legendDiv.classList.add('row', 'p-0');
            legendDiv.id = `customLegend-${index}`;

            legendCol.appendChild(legendTitle);
            legendCol.appendChild(legendAmount);
            legendCol.appendChild(legendDiv);

            var chartCol = document.createElement('div');
            chartCol.classList.add('col-md-6');
            var chartDiv = document.createElement('div');
            chartDiv.id = `donutChart-${index}`;

            chartCol.appendChild(chartDiv);

            chartRow.appendChild(legendCol);
            chartRow.appendChild(chartCol);
            chartWrapper.appendChild(chartRow);
            chartContainer.appendChild(chartWrapper);

            // Create chart options
            var chartOptions = {
                series: item.series[0].data,
                chart: { type: "donut", height: 150, zoom: { enabled: !1 } },
                theme: { mode: colors.chartTheme },
                plotOptions: { pie: { donut: { size: "40%" }, expandOnClick: !1 } },
                labels: item.series[0].categories,
                legend: { show: false },
                stroke: { colors: [colors.borderColor], width: 1 },
                fill: { opacity: 1, colors: chartColors },
            };

            // Render chart
            var chart = new ApexCharts(document.querySelector(`#donutChart-${index}`), chartOptions);
            chart.render().then(function () {
                createCustomLegend(chart, `customLegend-${index}`);
            });
        });
    }

    getRouteAjax("/Home/GetStatisticsCountOrdersByService")
//})(jQuery);