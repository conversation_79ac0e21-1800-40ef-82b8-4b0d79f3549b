!(function (t, e) {
  "object" == typeof exports && "object" == typeof module
    ? (module.exports = e())
    : "function" == typeof define && define.amd
    ? define([], e)
    : "object" == typeof exports
    ? (exports.HSStepForm = e())
    : (t.HSStepForm = e());
})(window, function () {
  return ((d = {
    "./src/js/hs-step-form.js": function (
      module,
      exports,
      __webpack_require__
    ) {
      "use strict";
      eval(
        "\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/*\n* HSStepForm Plugin\n* @version: 2.0.0 (Mon, 25 Nov 2019)\n* @requires: jQuery v3.0 or later\n* @author: HtmlStream\n* @event-namespace: .HSStepForm\n* @license: Htmlstream Libraries (https://htmlstream.com/)\n* Copyright 2019 Htmlstream\n*/\n\nvar HSStepForm = function () {\n\tfunction HSStepForm(elem, settings) {\n\t\t_classCallCheck(this, HSStepForm);\n\n\t\tthis.elem = elem;\n\t\tthis.defaults = {\n\t\t\tprogressSelector: null,\n\t\t\tprogressItems: null,\n\n\t\t\tstepsSelector: null,\n\t\t\tstepsItems: null,\n\t\t\tstepsActiveItem: null,\n\n\t\t\tnextSelector: '[data-hs-step-form-next-options]',\n\t\t\tprevSelector: '[data-hs-step-form-prev-options]',\n\n\t\t\tisValidate: false,\n\n\t\t\tclassMap: {\n\t\t\t\tactive: 'active',\n\t\t\t\tchecked: 'is-valid',\n\t\t\t\terror: 'is-invalid'\n\t\t\t}\n\t\t};\n\t\tthis.settings = settings;\n\t}\n\n\t_createClass(HSStepForm, [{\n\t\tkey: 'init',\n\t\tvalue: function init() {\n\t\t\tvar context = this,\n\t\t\t    $el = context.elem,\n\t\t\t    dataSettings = $el.attr('data-hs-step-form-options') ? JSON.parse($el.attr('data-hs-step-form-options')) : {};\n\t\t\tvar options = $.extend(true, context.defaults, dataSettings, context.settings);\n\n\t\t\toptions.progressItems = $(options.progressSelector).find('> *');\n\t\t\toptions.stepsItems = $(options.stepsSelector).find('> *');\n\t\t\toptions.stepsActiveItem = $(options.stepsSelector).find('> .' + options.classMap.active);\n\n\t\t\tcontext._prepareObject($el, options);\n\n\t\t\t$el.find(options.nextSelector).on('click', function () {\n\t\t\t\tcontext._nextClickEvents($el, options, $(this));\n\t\t\t});\n\n\t\t\t$el.find(options.prevSelector).on('click', function () {\n\t\t\t\tcontext._prevClickEvents($el, options, $(this));\n\t\t\t});\n\t\t}\n\t}, {\n\t\tkey: '_prepareObject',\n\t\tvalue: function _prepareObject(el, params) {\n\t\t\tvar options = params;\n\n\t\t\toptions.stepsItems.not('.' + options.classMap.active).hide();\n\t\t\toptions.progressItems.eq(options.stepsActiveItem.index()).addClass(options.classMap.active);\n\t\t}\n\t}, {\n\t\tkey: '_nextClickEvents',\n\t\tvalue: function _nextClickEvents(el, params, nextEl) {\n\t\t\tvar nextDataSettings = nextEl.attr('data-hs-step-form-next-options') ? JSON.parse(nextEl.attr('data-hs-step-form-next-options')) : {};\n\t\t\tvar options = params,\n\t\t\t    nextItemDefaults = {\n\t\t\t\ttargetSelector: null\n\t\t\t},\n\t\t\t    nextItemOptions = $.extend(true, nextItemDefaults, nextDataSettings);\n\n\t\t\tif (typeof $(window).validate !== 'undefined' && options.isValidate) {\n\t\t\t\tif (!el.valid()) {\n\t\t\t\t\toptions.progressItems.eq($(nextItemOptions.targetSelector).index() - 1).addClass(options.classMap.error);\n\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\toptions.progressItems.removeClass(options.classMap.active);\n\n\t\t\tif (typeof $(window).validate !== 'undefined' && options.isValidate) {\n\t\t\t\toptions.progressItems.eq($(nextItemOptions.targetSelector).index() - 1).removeClass(options.classMap.error);\n\t\t\t}\n\n\t\t\toptions.progressItems.eq($(nextItemOptions.targetSelector).index() - 1).addClass(options.classMap.checked);\n\t\t\toptions.progressItems.eq($(nextItemOptions.targetSelector).index()).addClass(options.classMap.active);\n\n\t\t\toptions.stepsItems.hide().removeClass(options.classMap.active);\n\t\t\t$(nextItemOptions.targetSelector).fadeIn(400).addClass(options.classMap.active);\n\t\t}\n\t}, {\n\t\tkey: '_prevClickEvents',\n\t\tvalue: function _prevClickEvents(el, params, prevEl) {\n\t\t\tvar options = params,\n\t\t\t    prevItemDefaults = {\n\t\t\t\ttargetSelector: null\n\t\t\t};\n\t\t\tvar prevDataSettings = prevEl.attr('data-hs-step-form-prev-options') ? JSON.parse(prevEl.attr('data-hs-step-form-prev-options')) : {};\n\t\t\tvar prevItemOptions = $.extend(true, prevItemDefaults, prevDataSettings);\n\n\t\t\toptions.progressItems.removeClass(options.classMap.active);\n\t\t\t//options.progressItems.eq($(prevItemOptions.targetSelector).index() - 1).addClass(options.classMap.checked);\n\t\t\toptions.progressItems.eq($(prevItemOptions.targetSelector).index()).addClass(options.classMap.active);\n\n\t\t\toptions.stepsItems.hide().removeClass(options.classMap.active);\n\t\t\t$(prevItemOptions.targetSelector).fadeIn(400).addClass(options.classMap.active);\n\t\t}\n\t}]);\n\n\treturn HSStepForm;\n}();\n\nexports.default = HSStepForm;\n\n//# sourceURL=webpack://HSStepForm/./src/js/hs-step-form.js?"
      );
    },
  }),
  (e = {}),
  (f.m = d),
  (f.c = e),
  (f.d = function (t, e, n) {
    f.o(t, e) || Object.defineProperty(t, e, { enumerable: !0, get: n });
  }),
  (f.r = function (t) {
    "undefined" != typeof Symbol &&
      Symbol.toStringTag &&
      Object.defineProperty(t, Symbol.toStringTag, { value: "Module" }),
      Object.defineProperty(t, "__esModule", { value: !0 });
  }),
  (f.t = function (e, t) {
    if ((1 & t && (e = f(e)), 8 & t)) return e;
    if (4 & t && "object" == typeof e && e && e.__esModule) return e;
    var n = Object.create(null);
    if (
      (f.r(n),
      Object.defineProperty(n, "default", { enumerable: !0, value: e }),
      2 & t && "string" != typeof e)
    )
      for (var s in e)
        f.d(
          n,
          s,
          function (t) {
            return e[t];
          }.bind(null, s)
        );
    return n;
  }),
  (f.n = function (t) {
    var e =
      t && t.__esModule
        ? function () {
            return t.default;
          }
        : function () {
            return t;
          };
    return f.d(e, "a", e), e;
  }),
  (f.o = function (t, e) {
    return Object.prototype.hasOwnProperty.call(t, e);
  }),
  (f.p = ""),
  f((f.s = "./src/js/hs-step-form.js"))).default;
  function f(t) {
    if (e[t]) return e[t].exports;
    var n = (e[t] = { i: t, l: !1, exports: {} });
    return d[t].call(n.exports, n, n.exports, f), (n.l = !0), n.exports;
  }
  var d, e;
});
