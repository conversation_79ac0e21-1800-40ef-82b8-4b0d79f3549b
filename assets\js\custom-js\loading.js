﻿; (function ($) {
    'use strict';
   
   
  
    $(window).on("load", function () {
        //debugger
        $('.loading').hide();
        console.log("load")
    });



    // Retrieve the last visited page from sessionStorage
    var savedPage = sessionStorage.getItem('page');
    console.log("Saved page:", savedPage);

    // Select all navigation items
    var navbarItems = $('ul.navbar-nav li.nav-item');
 
    function setActiveNavItem(pageUrl) {
        navbarItems.removeClass('active'); // Remove active from all

        navbarItems.each(function () {
            var navLink = $(this).find('a.nav-link');

            if (navLink.attr('href') === pageUrl) {
                $(this).addClass('active'); // Highlight the correct nav item
                $(this).closest('.nav-item.dropdown').addClass('active'); // Highlight dropdown parent
            }
        });
       
    }

    // Set active item based on saved page or default to first item
    if (savedPage) {
        setActiveNavItem(savedPage);
    } else {
        navbarItems.first().addClass('active'); // Default selection
    }

    // Click event for navigation links
    $(document).on('click', 'ul.navbar-nav li.nav-item a.nav-link', function () {
        var clickedPage = $(this).attr('href');
        sessionStorage.setItem('page', clickedPage); // Store in sessionStorage
        //history.pushState(null, null, clickedPage);
        setActiveNavItem(clickedPage);
    });
    //check pathName 
    var pathName = window.location.pathname;
    if (!(savedPage === pathName)) {
        console.log("pagessss", pathName);
        navbarItems.each(function () {
            var navLink = $(this).find('a.nav-link');

            if (navLink.attr('href') === pathName) {
                savedPage = pathName;
                sessionStorage.setItem('page', savedPage);
            }
        });
        setActiveNavItem(savedPage)
    }







})(jQuery);


//window.addEventListener("popstate", function (event) {
//    console.log("User clicked the back button!");
//    debugger
//});
//history.pushState(null, null, location.href);
//window.addEventListener("popstate", function () {
//    history.pushState(null, null, location.href);
//    alert("Back button is disabled!");
//    debugger
//});
//@* <script type="text/javascript">
//    $(document).ready(function () {
//              // Retrieve the 'page' value from sessionStorage
//              var page = sessionStorage.getItem('page');
//    console.log(page);

//    // Select all navigation items
//    var navbar = $('ul.navbar-nav li.nav-item');

//    // Check if 'page' is null
//    if (page == null) {
//        // Default to the first navigation item
//        navbar.first().addClass('active');
//              } else {
//        // Iterate through navigation items
//        navbar.each(function () {
//            // Compare href of each item with 'page' value
//            var navLink = $(this).find('a.nav-link');
//            if (navLink.attr('href') == page) {
//                // Add 'active' class to the clicked navigation item
//                $(this).addClass('active');
//                // Also highlight parent dropdown
//                $(this).closest('.nav-item.dropdown').addClass('active');
//                //$(this).closest('a.nav-link').addClass('active');
//                console.log("this", this);
//            }
//        });
//              }

//    // Set up click event listener for navigation items
//    $(document).on('click', 'ul.navbar-nav li.nav-item a.nav-link', function () {
//                  var x = $(this).attr('href');
//    // Store the clicked item's href value in sessionStorage as 'page'
//    sessionStorage.setItem('page', x);

//    // Remove 'active' class from all navigation items
//    navbar.removeClass('active');

//    // Add 'active' class to the clicked navigation item
//    $(this).closest('li.nav-item').addClass('active');

//    // Also highlight parent dropdown
//    $(this).closest('.nav-item.dropdown').addClass('active');
//    //$(this).closest('a.nav-link').addClass('active');
//    console.log("this",this);
//              });
//          });





//          //$('#reloadButton').click(function () {
//          //    //$.CustomPluginDatatablesV1.init('.js-datatable');
//          //    console.log(exportDatatable.ajax.reload())
//          //    //exportDatatable.ajax()  ;
//          //});

//      </script> * @