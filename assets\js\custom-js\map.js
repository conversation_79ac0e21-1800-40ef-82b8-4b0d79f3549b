﻿var mapElem = document.getElementById('map'),
    laco = getDataSettings(mapElem, "data-location");
var defaultLocation = { lat: 15.369445, lng: 44.191006 };
var map;
let retryCount = 0;
const retryDelay = 5000; // Adjust delay as needed
// Initialize the map
function initMap() {
    // Center the map on a default location (e.g., San Francisco)
    try {
        // Code to initialize Google Maps
    

    if (mapElem.hasAttribute("data-location")) {
        const pointA = new google.maps.LatLng(laco.pickupPoint.latitude, laco.pickupPoint.longitude),
            pointB = new google.maps.LatLng(laco.destination.latitude, laco.destination.longitude);
         map = new google.maps.Map(mapElem, {
            center: pointA,
            zoom: 12
        });
        // Instantiate a directions service.
        directionsService = new google.maps.DirectionsService,
            directionsDisplay = new google.maps.DirectionsRenderer({
                map: map
            }),
            markerA = new google.maps.Marker({
                position: pointA,
                title: "نقطة الالتقاط",
                label: "نقطة الالتقاط",
                map: map,
                id: 2,
            }),
            markerB = new google.maps.Marker({
                position: pointB,
                title: "point B",
                label: "B",
                id: 1,
                map: map
            });
        //console.log(laco.pickupPoint)
        //console.log(pointA)
        // get route from A to B
        calculateAndDisplayRoute(directionsService, directionsDisplay, pointA, pointB);
    } else if (mapElem.hasAttribute("data-drivers-location")) {
        
        var defaultLocation = { lat: 15.369445, lng: 44.191006 };
         map = new google.maps.Map(mapElem, {
            center: defaultLocation,
            zoom: 12
         });
    
        ////console.log(getDataSettings(mapElem, "data-drivers-location"))
        //setDriverMarker(map, getDataSettings(mapElem, "data-drivers-location"));
        
    } else {
        var defaultLocation = { lat: 15.369445, lng: 44.191006 };
        var radius = 1000;
         map = new google.maps.Map(mapElem, {
            center: defaultLocation,
            zoom: 12
        });

        
    }


} catch (error) {
    if (error.message.includes("net::ERR_INTERNET_DISCONNECTED")) {
        retryCount++;
        console.log(`Retrying Google Maps connection attempt ${retryCount}...`);
        debugger
        if (retryCount < 3) { // Limit retries to avoid overwhelming user
            setTimeout(initMap, retryDelay);
        } else {
            //console.error("Failed to connect to Google Maps after retries.");
            // Inform user about connection failure
        }
    } else {
        // Handle other errors
    }
}
    //updateDriverMarker();




    //setCustomerMarker(map, listCustLoc);

    document.getElementById('loading-spinner').style.display = 'none';
}

function calculateAndDisplayRoute(directionsService, directionsDisplay, pointA, pointB) {
    directionsService.route({
        origin: pointA,
        destination: pointB,
        avoidTolls: true,
        avoidHighways: false,
        travelMode: google.maps.TravelMode.DRIVING
    }, function (response, status) {
        if (status == google.maps.DirectionsStatus.OK) {
            directionsDisplay.setDirections(response);
        } else {
            window.alert('Directions request failed due to ' + status);
        }
    });
}


function getLiveTracking() {
    var map = new google.maps.Map(mapElem, {
        center: defaultLocation,
        zoom: 12
    });
    const startLocation = {
        lat: 15.370945,
        lng: 44.192066
    }
    const endLocation = {
        lat: 15.3438604,
        lng: 44.2115293
    }
    const directionsService = new google.maps.DirectionsService();
    const directionsRenderer = new google.maps.DirectionsRenderer();

    directionsService.route({
        origin: startLocation,
        destination: endLocation,
        travelMode: google.maps.TravelMode.DRIVING
    }, function (response, status) {
        if (status === google.maps.DirectionsStatus.OK) {
            directionsRenderer.setDirections(response);
            directionsRenderer.setMap(map);

            const steps = response.routes[0].overview_path;
            //console.log("steps", steps);

            const marker = new google.maps.Marker({
                map: map,
                position: {
                    lat: steps[0].lat(),
                    lng: steps[0].lng()
                },
                label: '🚘',
                zIndex: 1,
            });


            let i = 0;
            const interval = setInterval(function () {
                i++;
                if (i === steps.length) {
                    clearInterval(interval);
                    return
                }

                marker.setPosition({
                    lat: steps[i].lat(),
                    lng: steps[i].lng()
                });

            }, 1000);
        }
    })


    //console.log("miiiiiiiiiiiiiiiiiiiii")
}
// Find nearby cabs within a specific range
function setDriverMarker(map, cabLocations) {
    let markers = []; // Declare markers array globally
    //var driverIcon = {
    //    url: "/assets/avatars/taxi3d.svg",
    //    scaledSize: new google.maps.Size(48, 48), // Adjust size as needed
    //    anchor: new google.maps.Point(16, 32) // Set anchor point for precise placement
    //};

    cabLocations.forEach(function (driver, index) {

        var driveerMarker= new google.maps.Marker({
            position: driver['Location'],
            map: map,
            title: driver.Name,
            icon: driverIcon
        });

        let content = `
                    <div class="p-2">
                        <h6>اسم السائق : ${driver.Name ?? '-'} </h6>
                        <h6>طراز السيارة : ${driver.CarModel ?? '-'} </h6>
                        <h6>إحداثيات الموقع: ${driver.Location.lat}, ${driver.Location.lng} </h6>
                    </div>`;


        const infowindow = new google.maps.InfoWindow({
            content: content
                //`<strong>${driver.Name}</strong><br>Location: ${driver.Location.lat}, ${driver.Location.lng}`,
        });

        driveerMarker.addListener("click", () => {
            infowindow.open(map, driveerMarker);

        });

        markers.push(driveerMarker); // Add marker to markers array
    });



}
// new google.maps.Map(mapElem, {
//    center: defaultLocation,
//    zoom: 12
//});
let markers = []; // Declare markers array globally
function updateDriverMarker() {

  
    var driverIcon = {
        url: "/assets/avatars/taxi3d.svg",
        scaledSize: new google.maps.Size(38, 38), // Adjust size as needed
        anchor: new google.maps.Point(16, 32) // Set anchor point for precise placement
    };
    var customerIcon = {
        url: "/assets/avatars/customer.svg",
            scaledSize: new google.maps.Size(48, 48), // Adjust size as needed
                anchor: new google.maps.Point(16, 32) // Set anchor point for precise placement
    }
    if (mapElem.hasAttribute("data-drivers-location")){
        var cabLocations = getDataSettings(mapElem, "data-drivers-location");
        console.log("cabLocations ss", cabLocations)

 
        cabLocations.forEach(function (driver, index) {

            var existingMarker = findMarkerByName(driver.Name); // Check if marker already exists
            console.log("driver", existingMarker && cabLocations.length >= markers.length)
            console.log("driver2", existingMarker , cabLocations.length >= markers.length)
           
            if (existingMarker ) {
                // Marker exists, update its position
                console.log("driver3", existingMarker.getPosition().lat())
                console.log("driver3", existingMarker.getPosition().lng())
                existingMarker.setPosition(driver.Location);
                console.log("existingMarker", driver)
            } else {
                console.log("NotexistingMarker", driver)
                var driveerMarker = new google.maps.Marker({
                    position: driver['Location'],
                    map: map,
                    title: driver.Name,
                    icon: driver.CustomerTypeId === 2 ? driverIcon : customerIcon,
                    id: driver.Id
                });
                let content = `
                    <div class="p-2">
                        <h6>اسم : ${driver.Name ?? '-'} </h6>
                        <h6>إحداثيات الموقع: ${driver.Location.lat}, ${driver.Location.lng} </h6>
                    </div>`;

                //<div class="p-2">
                //    <h6>اسم السائق : ${driver.Name ?? '-'} </h6>
                //    <h6>طراز السيارة : ${driver.CarModel ?? '-'} </h6>
                //    <h6>إحداثيات الموقع: ${driver.Location.lat}, ${driver.Location.lng} </h6>
                //</div>
                const infowindow = new google.maps.InfoWindow({
                    content: content
                    //`<strong>${driver.Name}</strong><br>Location: ${driver.Location.lat}, ${driver.Location.lng}`,
                });

                driveerMarker.addListener("click", () => {
                    infowindow.open(map, driveerMarker);

                });

                markers.push(driveerMarker); // Add marker to markers array
            }
        });
    }
  
}
function removeMarkerById(id) {
    // Find the marker by its ID
    const marker = markers.find(marker => marker.id === id);
    if (marker) {
        // Remove the marker from the map
        marker.setMap(null);
        markers = markers.filter(item => item !== marker);
        ////console, log("remove marker ", markers);
        //console.log(`Marker with ID ${id} has been removed.`,markers);
    } else {
        //console.log(`Marker with ID ${id} not found.`);
    }
}
function findMarkerByName(name) {
    // Use filter() to find the marker by name
    var filteredMarkers = markers.filter(marker => marker.getTitle() === name);
    console.log("markers", filteredMarkers)
    //console.log("filteredMarkers", filteredMarkers.length > 0 ? filteredMarkers[0] : null)
    return filteredMarkers.length > 0 ? filteredMarkers[0] : null;
}
function setCustomerMarker(map, listCustLoc) {

    var carIcon = {
        url: "/assets/avatars/customer.svg",
        scaledSize: new google.maps.Size(48, 48), // Adjust size as needed
        anchor: new google.maps.Point(16, 32) // Set anchor point for precise placement
    };
    listCustLoc.forEach(function (cus, index) {

        // Create a marker for the user's location
        var userMarker = new google.maps.Marker({
            position: cus,
            map: map,
            title: 'custoer',
            icon: carIcon
        });

    });

}
var listCustLoc = [
    {
        Location:
            { lat: 15.370945, lng: 44.192066 }
    },
    {
        Location: { lat: 15.371245, lng: 44.193086 }
    },
    {
        Location: { lat: 15.372945, lng: 44.194096 }
    },
    {
        Location: { lat: 15.383445, lng: 44.195886 }
    },
    {
        Location: { lat: 15.374945, lng: 44.196666 }
    },
    {
        Location: { lat: 15.375495, lng: 44.197056 }
    },
    {
        Location: { lat: 15.376745, lng: 44.198806 }
    },
    {
        Location: { lat: 15.377945, lng: 44.199806 }
    },
    {
        Location: { lat: 15.378945, lng: 44.200906 }
    },
    {
        Location: { lat: 15.379845, lng: 44.201906 }
    }
];
var cabLocations = [
    {
        "lat": 37.75880508654381,
        "lng": -122.45967576052189,
        "id": 1
    },
    {
        "lat": 37.76260500078658,
        "lng": -122.42843338991642,
        "id": 2
    },
    {
        "lat": 37.77536042799209,
        "lng": -122.43083664919376,
        "id": 3
    },
    {
        "lat": 37.77563179615532,
        "lng": -122.41298386599064,
        "id": 4
    },
    {
        "lat": 37.77508905883279,
        "lng": -122.42328354860783,
        "id": 5
    },
    {
        "lat": 37.766947520889495,
        "lng": -122.41847703005314,
        "id": 6
    },

    {
        "lat": 37.76559051074694,
        "lng": -122.44834610964298,
        "id": 12
    },
    {
        "lat": 37.7695257715604,
        "lng": -122.42620179201603,
        "id": 13
    },
    {
        "lat": 37.77017196507862,
        "lng": -122.4704360961914,
        "id": 17
    }
];

function loadGoogleMapsScript() {
    // Show the loading spinner while waiting for the Google Maps API to load
    document.getElementById('loading-spinner').style.display = 'block';

    // Load the Google Maps API script
  
   


    if (navigator.onLine) {
        // Internet connection is available, load the Google Maps script
        var script = document.createElement('script');
        script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyAtA01tsg6sH2RYZj7Wwp7d7YQvuMzscr0&libraries=geometry&callback=initMap&v=weekly&language=ar&region=YE';
        script.defer = true;
        document.head.appendChild(script);

        script.onload = function () {
            console.log("Google Maps loaded successfully.");
            // Initialize your map here
        };

        script.onerror = function () {
            //console.error("Failed to load Google Maps.");
            //alert("Failed to load Google Maps. Please try again later.");
        };
    } else {
        // No internet connection
        //console.error("No internet connection.");
        //alert("لا يوجد اتصال بالإنترنت. يرجى التحقق من إعدادات الشبكة والمحاولة مرة أخرى.");
        markers = [];
    }

}
function getDataSettings(el, dataAttr) {
    return $(el).attr(dataAttr) ? JSON.parse($(el).attr(dataAttr)) : {};
}



// Constants for default locations
//const DEFAULT_LOCATION = { lat: 15.369445, lng: 44.191006 };
//const DEFAULT_ZOOM_LEVEL = 12;

//// Initialize the map
//function initMap() {
//    const mapElem = document.getElementById('map');
//    const locationData = getDataSettings(mapElem, "data-location");

//    if (locationData) {
//        const pointA = new google.maps.LatLng(locationData.pickupPoint.latitude, locationData.pickupPoint.longitude);
//        const pointB = new google.maps.LatLng(locationData.destination.latitude, locationData.destination.longitude);

//        const map = createMap(mapElem, pointA);
//        const directionsService = new google.maps.DirectionsService();
//        const directionsDisplay = new google.maps.DirectionsRenderer({ map });

//        const markerA = createMarker(map, pointA, "نقطة الالتقاط", "نقطة الالتقاط");
//        const markerB = createMarker(map, pointB, "Point B", "B");

//        calculateAndDisplayRoute(directionsService, directionsDisplay, pointA, pointB);
//    } else if (mapElem.hasAttribute("data-drivers-location")) {
//        const map = createMap(mapElem, DEFAULT_LOCATION);
//        setDriverMarker(map, getDataSettings(mapElem, "data-drivers-location"));
//    } else {
//        createMap(mapElem, DEFAULT_LOCATION);
//    }

//    document.getElementById('loading-spinner').style.display = 'none';
//}

//// Function to create map
//function createMap(mapElem, center) {
//    return new google.maps.Map(mapElem, {
//        center: center,
//        zoom: DEFAULT_ZOOM_LEVEL
//    });
//}

//// Function to create marker
//function createMarker(map, position, title, icon) {
//    return new google.maps.Marker({
//        position: position,
//        title: title,
//        icon: icon,
//        map: map
//    });
//}

//// Function to calculate and display route
//function calculateAndDisplayRoute(directionsService, directionsDisplay, pointA, pointB) {
//    directionsService.route({
//        origin: pointA,
//        destination: pointB,
//        avoidTolls: true,
//        avoidHighways: false,
//        travelMode: google.maps.TravelMode.DRIVING
//    }, function (response, status) {
//        if (status == google.maps.DirectionsStatus.OK) {
//            directionsDisplay.setDirections(response);
//        } else {
//            window.alert('Directions request failed due to ' + status);
//        }
//    });
//}

//// Function to set driver markers
//function setDriverMarker(map, cabLocations) {
//    var driverIcon = {
//        url: "/assets/avatars/taxi3d.svg",
//        scaledSize: new google.maps.Size(48, 48), // Adjust size as needed
//        anchor: new google.maps.Point(16, 32) // Set anchor point for precise placement
//    };
//    cabLocations.forEach(function (driver) {
//        const driverMarker = createMarker(map, driver.Location, driver.Name, driverIcon);
//        const content = `
//            <div class="p-2">
//                <h6>Driver Name: ${driver.Name ?? '-'}</h6>
//                <h6>Car Model: ${driver.CarModel ?? '-'}</h6>
//                <h6>Location Coordinates: ${driver.Location.lat}, ${driver.Location.lng}</h6>
//            </div>`;
//        const infowindow = new google.maps.InfoWindow({ content: content });

//        driverMarker.addListener("click", () => {
//            infowindow.open(map, driverMarker);
//        });
//    });
//}

//// Function to load Google Maps script
//function loadGoogleMapsScript() {
//    document.getElementById('loading-spinner').style.display = 'block';
//    const script = document.createElement('script');
//    script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyAtA01tsg6sH2RYZj7Wwp7d7YQvuMzscr0&libraries=geometry&callback=initMap&v=weekly&language=ar&region=YE';
//    script.defer = true;
//    document.head.appendChild(script);
//}

//// Function to get data settings from element attribute
//function getDataSettings(el, dataAttr) {
//    return el.getAttribute(dataAttr) ? JSON.parse(el.getAttribute(dataAttr)) : null;
//}


// here class map 
//class MapManager {
//    constructor() {
//        this.defaultLocation = { lat: 15.369445, lng: 44.191006 };
//        this.defaultZoomLevel = 12;
//    }

//    initMap() {
//        const mapElem = document.getElementById('map');
//        const locationData = this.getDataSettings(mapElem, "data-location");

//        if (locationData) {
//            this.initLocationMap(locationData);
//        } else if (mapElem.hasAttribute("data-drivers-location")) {
//            this.initDriverMap(mapElem);
//        } else {
//            this.initDefaultMap(mapElem);
//        }

//        this.hideLoadingSpinner();
//    }

//    initLocationMap(locationData) {
//        const pointA = new google.maps.LatLng(locationData.pickupPoint.latitude, locationData.pickupPoint.longitude);
//        const pointB = new google.maps.LatLng(locationData.destination.latitude, locationData.destination.longitude);
//        const map = this.createMap(pointA);
//        const directionsService = new google.maps.DirectionsService();
//        const directionsDisplay = new google.maps.DirectionsRenderer({ map });

//        const markerA = this.createMarker(map, pointA, "نقطة الالتقاط", "نقطة الالتقاط");
//        const markerB = this.createMarker(map, pointB, "Point B", "B");

//        this.calculateAndDisplayRoute(directionsService, directionsDisplay, pointA, pointB);
//    }

//    initDriverMap(mapElem) {
//        const map = this.createMap(this.defaultLocation);
//        const cabLocations = this.getDataSettings(mapElem, "data-drivers-location");
//        this.setDriverMarkers(map, cabLocations);
//    }

//    initDefaultMap(mapElem) {
//        this.createMap(this.defaultLocation);
//    }

//    createMap(center) {
//        return new google.maps.Map(document.getElementById('map'), {
//            center: center,
//            zoom: this.defaultZoomLevel
//        });
//    }

//    createMarker(map, position, title, label) {
//        return new google.maps.Marker({
//            position: position,
//            title: title,
//            label: label,
//            map: map
//        });
//    }

//    calculateAndDisplayRoute(directionsService, directionsDisplay, pointA, pointB) {
//        directionsService.route({
//            origin: pointA,
//            destination: pointB,
//            avoidTolls: true,
//            avoidHighways: false,
//            travelMode: google.maps.TravelMode.DRIVING
//        }, function (response, status) {
//            if (status == google.maps.DirectionsStatus.OK) {
//                directionsDisplay.setDirections(response);
//            } else {
//                window.alert('Directions request failed due to ' + status);
//            }
//        });
//    }

//    setDriverMarkers(map, cabLocations) {
//        cabLocations.forEach(driver => {
//            const driverMarker = this.createMarker(map, driver.Location, driver.Name, "🚖");
//            const content = `
//                <div class="p-2">
//                    <h6>Driver Name: ${driver.Name ?? '-'}</h6>
//                    <h6>Car Model: ${driver.CarModel ?? '-'}</h6>
//                    <h6>Location Coordinates: ${driver.Location.lat}, ${driver.Location.lng}</h6>
//                </div>`;
//            const infowindow = new google.maps.InfoWindow({ content: content });

//            driverMarker.addListener("click", () => {
//                infowindow.open(map, driverMarker);
//            });
//        });
//    }

//    hideLoadingSpinner() {
//        document.getElementById('loading-spinner').style.display = 'none';
//    }

//    getDataSettings(el, dataAttr) {
//        return el.getAttribute(dataAttr) ? JSON.parse(el.getAttribute(dataAttr)) : null;
//    }
//}

//const mapManager = new MapManager();

//function loadGoogleMapsScript() {
//    document.getElementById('loading-spinner').style.display = 'block';
//    const script = document.createElement('script');
//    script.src = 'https://maps.googleapis.com/maps/api/js?key=key_map&libraries=geometry&callback=initMap&v=weekly&language=ar&region=YE';
//    script.defer = true;
//    document.head.appendChild(script);
//}
