﻿; (function ($) {
	'use strict';


    const orderWrapper = '.order-st-entry';
    $(orderWrapper).click(function (e) {
        e.preventDefault();
        const $self = $(this),
            tableId = $self.data("table-id");
        const datatab = $.CustomPluginDatatablesV1.getDataTable(tableId);
        $self.toggleClass('active').siblings().removeClass('active');
        const statusData = $self.hasClass("active") ? $self.attr('data-filter-status') : datatab.attr('data-default-filter');
        datatab.attr("data-server-filter", statusData)
        console.log("lslsl", $self.hasClass("active"))
        $(datatab).DataTable().ajax.reload();
        console.log("e", statusData)
        console.log("e", this)

    });
})(jQuery);