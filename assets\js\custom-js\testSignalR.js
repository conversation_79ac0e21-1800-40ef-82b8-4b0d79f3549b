﻿//; (function ($) {
//	'use strict';

//        async function startConnection() {
//            // Make a request to get the access token
//            const response = await fetch('/get-access-token');
//            const accessToken = response.headers.get('X-Access-Token'); // Function to generate access token
//            console.log(accessToken)
//            //debugger
//        const connection = new signalR.HubConnectionBuilder()
//        .withUrl("http://*************:3000/Notification/NotificationHub",

//        {
//            transport: signalR.HttpTransportType.WebSockets |
//        signalR.HttpTransportType.LongPolling,
//                        accessTokenFactory: () => accessToken
//                    })
//        .configureLogging(signalR.LogLevel.Debug)
//        .withHubProtocol(new signalR.protocols.msgpack.MessagePackHubProtocol())
//        .withAutomaticReconnect()
//        .build();

//        try {
//            await connection.start();

//        connection.on("online", function (user) {
//            console.log("sdeeeeeeee", user)
//                    const driverCount = JSON.parse(user)["CountDrivers"];
//                    if(driverCount > 0) {
//            document.getElementById("driverOnline").textContent = driverCount;
//                    }else{
//            document.getElementById("driverOnline").textContent = driverCount;
//        notFoundDrivers.show();
//                    }


//                    //return user;
//                    ////if (user !== sessionStorage.getItem('online')){
//            ////      sessionStorage.setItem('online', user);
//            //// console.log("sessionStorage2", sessionStorage.getItem('online'))
//            ////    document.getElementById("driverOnline").textContent = JSON.parse(sessionStorage.getItem('online'))["CountDrivers"];
//            ////}else{
//            ////    document.getElementById("driverOnline").textContent = JSON.parse(sessionStorage.getItem('online'))["CountDrivers"];
//            ////}

//        });

//            } catch (err) {
//            console.error(err);
//            }

//        try {
//            connection.on("updateLocations", function (driverInfo) {
//                console.log("Received updateLocations:", driverInfo);

//                // Parse the JSON string to an array

//                try {


//                    if (driverInfo !== "[]") {
//                        const driverInfoList = JSON.parse(driverInfo);
//                        getConnectionWidget(driverInfoList);
//                        console.log("Received updateLocationsJson:", driverInfoList);
//                    } else {
//                        console.log("driverInfo data  ", driverInfo);
//                    }

//                } catch (error) {

//                    console.error("Error parsing JSON data:", error);
//                    console.error("Error parsing JSON data:", driverInfo);
//                    // Handle the error gracefully, e.g., log the error or display a message to the user
//                }


//                // Check if the parsed data is not null
//                //if (driverInfoList !== null) {
//                //    // Iterate over the array using forEach
//                //    driverInfoList.forEach(function (driver, index) {
//                //        console.log("Driver at index", index, ":", driver);
//                //        // Process and handle each driver's information here
//                //        // Create HTML string for div element
//                //        const driverDivHTML = `<div id="div${driver["name"]}">Driver Name: ${driver["name"]}, Location: ${driver["location"]}</div>`;

//                //        // Append the div element to the body or any other container element
//                //        //document.body.insertAdjacentHTML("beforeend", driverDivHTML);
//                //    console.log(driverDivHTML)
//                //    });
//                //} else {
//                //    console.log("Received null or invalid data for updateLocations.");
//                //}


//            });
//            } catch (err) {
//            console.error(err);
//            }


//        try {
//            connection.on("sendDisconectedCustomer", function (driverInfo) {
//                console.log("sendDisconectedCustomer:", driverInfo);
//                disConnect(driverInfo);
//                // Parse the JSON string to an array

//            });
//            } catch (err) {
//            console.error(err);
//        }

//            try {
//                connection.on("sendFazaaOrdersCount", function (countNumber) {
//                    console.log("sendFazaaOrdersCount:", countNumber);
//                    document.getElementById("counterOrder").textContent = countNumber;
//                    const exportDatatable = $.CustomPluginDatatablesV1.reloadDataTable();
//                    exportDatatable.ajax.reload()

//                    debugger
//                    // Parse the JSON string to an array

//                });
//            } catch (err) {
//                console.error(err);
//            }
//        console.log(connection)
//        console.log(connection.state)
//        console.log(signalR.HubConnectionState.Connected)

//        if (connection && connection.state === signalR.HubConnectionState.Connected) {

//            sessionStorage.setItem('connection', connection);
//        console.log("SignalR connection is established.", sessionStorage.getItem('connection'));
//} else {
//            console.log("SignalR connection is not established.");
//}
           
//        }

//        startConnection();
//        var extractedData=[];
//        var mapId = $("#map");
//        var  notFoundDrivers = $("#notFoundDrivers");
//        var elements = { },
//        replaced= false;
//        function getConnectionWidget(driverInfoList) {

           
//            const cardDriverScrollable = $("#cardDrivers").children(".simplebar-wrapper").children(".simplebar-mask").children().children().children();



//        console.log(cardDriverScrollable)
//        //cardDriverScrollable.empty();
//        // Check if the parsed data is not null
//        if (driverInfoList !== null || driverInfoList.length === 0) {
//            // Iterate over the array using forEach
//            notFoundDrivers.hide();
//        driverInfoList.forEach(function (driver, index) {
//            console.log("Driver at index", index, ":", driver);
//        // Process and handle each driver's information here
//        // Create HTML string for div element


//        if (cardDriverScrollable.children(`#${driver['Id']}`).length === 0){
//            console.log("ccccccccccccc",true)
//            // console.log("cardDriverScrollable.children()", cardDriverScrollable.children(`#${driver['Id']}`))
//            cardDriverScrollable.children().first().before(createDriverCard(driver));
//                    }else{
//            console.log("ccccccccccccc", false)
//        }

//                    //cardDriverScrollable.append(driverDivHTML);
//        console.log(driverDivHTML)

//        const mma = {
//            Id: driver.Id,
//        Location: {
//            lat: parseFloat(driver.Lat),
//        lng: parseFloat(driver.Long),
//                    },
//        Name: driver.Name,
//        CarModel: driver.CarModel
//                    };

               
//                    extractedData.forEach((item, index) => {
//                        // Check if the item with the same ID exists
//                        console.log("hshshs", item.Id === mma.Id)
//                        if (item.Id === mma.Id) {
//            // Replace the item with the new item
//            extractedData[index] = mma;
//                            //replaced = true; // Set the flag to true
//                        }
//                    });
//        extractedData.splice(0, 0, mma)
                   
                    

//                });


//        mapId.attr("data-drivers-location", JSON.stringify(extractedData));
//        console.log(extractedData);
//        initMap()
//            } else {
//            notFoundDrivers.show();
//        console.log("Received null or invalid data for updateLocations.");
//            }



//        }


//        function disConnect(id){
//             const cardDriverScrollable = $("#cardDrivers").children(".simplebar-wrapper").children(".simplebar-mask").children().children().children();

//        cardDriverScrollable.children(`#${id}`).remove();
//        extractedData=  extractedData.filter(function (item) {
              
//                return item.Id !== id;
//  });

//        mapId.attr("data-drivers-location", JSON.stringify(extractedData));
//        console.log(JSON.stringify(extractedData));
//        initMap()
//        console.log("disConnect")
//        }
//        // Function to create driver card element
//        function createDriverCard(driver) {
//            // Create a new div element
//            const cardDiv = document.createElement('div');
//        cardDiv.classList.add('card', 'mb-1');
//            //const id = `${driver["Name"]}_${driver['Id']}`
//        console.log("id", id)
//        // Add an id attribute to the div element
//        cardDiv.setAttribute('id', driver['Id']);
//        // Create inner elements
//        const innerDiv1 = document.createElement('div');
//        innerDiv1.classList.add('p-2');

//        const innerDiv2 = document.createElement('div');
//        innerDiv2.classList.add('d-flex', 'align-items-center');

//        // Inside innerDiv2
//        const colDiv1 = document.createElement('div');
//        colDiv1.classList.add('col-auto');

//        const anchorTag = document.createElement('a');
//        anchorTag.href = `/Driver/Details/${driver['Id']}`;
//        anchorTag.classList.add('avatar', 'avatar-md');

//        const imgTag = document.createElement('img');
//        imgTag.src = driver['PictureUrl'] !== null ? driver['PictureUrl'] : './assets/avatars/imageGrey.jpg';
//        imgTag.alt = '...';
//        imgTag.classList.add('avatar-img', 'rounded-circle');

//        anchorTag.appendChild(imgTag);
//        colDiv1.appendChild(anchorTag);

//        const colDiv2 = document.createElement('div');
//        colDiv2.classList.add('col', 'pr-0');

//        const strongTag = document.createElement('strong');
//        strongTag.classList.add('mb-1', 'fs-7');
//        strongTag.textContent = driver['Name'];

//        const spanTag = document.createElement('span');
//        spanTag.classList.add('dot', 'dot-lg', 'bg-success', 'ml-1');

//        const pTag = document.createElement('p');
//        pTag.classList.add('small', 'text-muted', 'mb-1', 'fs-8');
//        pTag.textContent = 'متصل';

//        colDiv2.appendChild(strongTag);
//        colDiv2.appendChild(spanTag);
//        colDiv2.appendChild(pTag);

//        const colDiv3 = document.createElement('div');
//        colDiv3.classList.add('col-auto');

//        const cardShadowDiv = document.createElement('div');
//        cardShadowDiv.classList.add('card', 'shadow-none');

//        const cardBodyDiv = document.createElement('div');
//        cardBodyDiv.classList.add('card-body', 'py-1', 'px-3');

//        const strongTag2 = document.createElement('strong');
//        strongTag2.classList.add('fs-8');
//        strongTag2.textContent = driver['CustomerTypeName'];

//        cardBodyDiv.appendChild(strongTag2);
//        cardShadowDiv.appendChild(cardBodyDiv);
//        colDiv3.appendChild(cardShadowDiv);

//        innerDiv2.appendChild(colDiv1);
//        innerDiv2.appendChild(colDiv2);
//        innerDiv2.appendChild(colDiv3);

//        innerDiv1.appendChild(innerDiv2);

//        // Inside cardDiv
//        const pxDiv = document.createElement('div');
//        pxDiv.classList.add('px-3');

//        const timelineDiv = document.createElement('div');
//        timelineDiv.classList.add('timeline', 'p-3');

//        const timelineItemEndDiv = document.createElement('div');
//        timelineItemEndDiv.classList.add('timeline-item-end');

//        const plDiv = document.createElement('div');
//        plDiv.classList.add('pl-5');

//        const mb1Div = document.createElement('div');
//        mb1Div.classList.add('mb-1', 'd-flex', 'align-items-md-center');

//        const strongTag3 = document.createElement('strong');
//        strongTag3.classList.add('fs-8');
//        console.log(driver['AddressDesc'] !== null && driver['AddressDesc'] !== "")
//        const address = driver['AddressDesc'] !== null && driver['AddressDesc'] !== "" ? driver['AddressDesc'] : 'مديرية الوحدة أمانة العاصمة, اليمن';
//        strongTag3.textContent = address;

//        mb1Div.appendChild(strongTag3);
//        plDiv.appendChild(mb1Div);
//        timelineItemEndDiv.appendChild(plDiv);
//        timelineDiv.appendChild(timelineItemEndDiv);
//        pxDiv.appendChild(timelineDiv);
//        cardDiv.appendChild(innerDiv1);
//        cardDiv.appendChild(pxDiv);



//        return cardDiv;
//        }


//        const driverInfoList = [
//        {
//            "Id": 9,
//        "PictureUrl": "http://*************:3000/PlatFoem/Image/Users/<USER>",
//        "CountryId": 1,
//        "CityId": 1,
//        "Lat": "15.370945",
//        "Long": "44.192066",
//        "Name": "احمد المحمد",
//        "CustomerTypeId": 2,
//        "AddressDesc": "jsjsjsjjjjjjjjjjjjjjjjjjj",
//        "CustomerTypeName": "Driver",
//        "CarId": 2,
//        "CarLicens": "8520",
//        "CarModel": "tayota"
//            },
//        {
//            "Id": 10,
//        "PictureUrl": null,
//        "CountryId": 1,
//        "CityId": 1,
//        "Lat": "15.3438604",
//        "Long": "44.2115293",
//        "Name": "محمد",
//        "CustomerTypeId": 2,
//        "AddressDesc": null,
//        "CustomerTypeName": "Driver",
//        "CarId": 2,
//        "CarLicens": "8520",
//        "CarModel": "tayota"
//            }

//        ];



//        function getRandomNumber(min, max) {
//    return Math.floor(Math.random() * (max - min + 1)) + min;
//}


//        //function executeEveryTenSeconds() {
//        //    console.log("last list", driverInfoList);
//        //const na = driverInfoList.length + 1;
//        //const lat = `15.3${getRandomNumber(1, 488804)}`,
//        //log = `44.2${getRandomNumber(1, 118293)}`;
//        //    console.log("lat",lat)
//        //    const modelData= {
//        //        "Id": 9,
//        //        "PictureUrl": "http://*************:3000/PlatFoem/Image/Users/<USER>",
//        //        "CountryId": 1,
//        //        "CityId": 1,
//        //        "Lat": lat.toString(),
//        //        "Long": log.toString(),
//        //        "Name": "احمد المحمد",
//        //        "CustomerTypeId": 2,
//        //        "AddressDesc": "jsjsjsjjjjjjjjjjjjjjjjjjj",
//        //        "CustomerTypeName": "Driver",
//        //        "CarId": 2,
//        //        "CarLicens": "8520",
//        //        "CarModel": "tayota"
//        //    };
//        //    //{
//        //    //    "Id": na,
//        //    //    "PictureUrl": null,
//        //    //    "CountryId": 1,
//        //    //    "CityId": 1,
//        //    //    "Lat": lat.toString(),
//        //    //    "Long": log.toString(),
//        //    //    "Name": na.toString(),
//        //    //    "CustomerTypeId": 2,
//        //    //    "AddressDesc": "",
//        //    //    "CustomerTypeName": "Driver",
//        //    //    "CarId": 2,
//        //    //    "CarLicens": "8520",
//        //    //    "CarModel": "tayota"
//        //    //};
//        //    driverInfoList.splice(0, 0, modelData)
//        //    // Your code to execute goes here
//        //    //driverInfoList.length =1;
//        //    getConnectionWidget([driverInfoList[0]]);
//        //    console.log("Executing every ten seconds...", driverInfoList);
//        //}

//        //// run demo 
//        //  setTimeout(function () {
//        //    getConnectionWidget(driverInfoList);
//        //    //initMap() 
//        //}, 2000);

//         //Set up an interval to call the function every ten seconds
////var intervalHandle = setInterval(executeEveryTenSeconds, 5000);

////// Stop the interval after a certain period (e.g., 15 seconds)
////setTimeout(function() {
////    clearInterval(intervalHandle); // Clear the interval using the handle
////    console.log("Interval stopped after 15 seconds.");
////}, 15000);
//         //Define the function to be called repeatedly
////disconnect 
////function disconnectWrapper() {
////    disConnect(9);
////}
////// Set up the interval to call disconnectWrapper every 10 seconds
////setInterval(disconnectWrapper, 5000);

        

    
//})(jQuery);