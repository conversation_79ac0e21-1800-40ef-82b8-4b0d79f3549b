$(document).ready(function () {

   // console.log($("input[type=file]"))
    const inputFiles = $('input[type="file"]:not(#groupImageInput)');

    inputFiles.each(function (i, ele) {
        var $this = $(this);

        $this.on("change", function (e) {
            var file = $(this),
                label = file.parent().children('.custom-file-label');
            if (this.files && this.files[0]) {
                var $file = this.files;
                var reader = new FileReader();
                reader.onload = function (e) {
                    console.log(e.target.result)
                    console.log($file[0])
                    console.log($(label).text($file[0].name))
                    //removeImage.onclick = function () {
                    //    //image_preview = ele.find('img').attr('src', '');
                    //    //image.val(null);
                    //    //image.trigger('change', { bubbles: true });
                    //    //$(textIm).text("إضافة صورة")
                    //    console.log(textIm)
                    //    //removeImage.classList.add("d-none");
                    //};
                    //image_preview.fadeIn(650);
                }
                console.log(reader);
                reader.readAsDataURL(this.files[0]);
            }
        });

    });

    const inputImage = $(".js-file-image");

    $("body").on("change", ".image-upload", function (e) {

        var lkthislk = $(this);
        console.log(lkthislk);
        console.log(this.files);
        var removeImage = document.getElementById("remove-image");
        if (this.files && this.files[0]) {
            var $file = this.files;
            var reader = new FileReader();

            reader.onload = function (e) {
                // console.log(e);
                $(".spinner-border.center-image-loading")
                var image_preview = lkthislk.parent().children('.preview').attr('src', e.target.result);
                
                if (lkthislk.jHasAttribute("data-upload-url")) {
                   // lkthislk.data("form");
                   // lkthislk.data("upload-url");
                   //lkthislk.data("form").serializeArray();
                    $(".spinner-border.center-image-loading").show();
                    const data = formDataMultipart($(lkthislk.data("form")));
                    console.log(data)
                    passingFormDataMultipartRoute(lkthislk.data("upload-url"), data)
                }
              
                //image_preview.hide();
                //removeImage.classList.remove("d-none");
                //removeImage.onclick = function () {
                //    image_preview = lkthislk.parent().parent().children('.preview').find('.image-preview').attr('src', "assets/img/products/vender-upload-preview.jpg");

                //    removeImage.classList.add("d-none");
                //};
                image_preview.fadeIn(650);
            }
            console.log(reader);
            reader.readAsDataURL(this.files[0]);
        }
    });


});



