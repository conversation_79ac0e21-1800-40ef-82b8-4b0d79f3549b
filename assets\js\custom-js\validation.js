﻿
var isEmpty = function isEmpty(f) {
	return (/^function[^{]+\{\s*\}/m.test(f.toString())
	);
  }
  
  ;(function ($) {
	'use strict';
  
	$.Validation = {
	  /**
	   *
	   *
	   * @var Object _baseConfig
	   */
	  _baseConfig: {
		errorElement: 'div',
		errorClass: 'invalid-feedback',
		rules: {},
			onkeyup: function (element) {
				
					$(element).valid()
				
			},
		errorPlacement: function(){},
		highlight: function(){},
		unhighlight: function(){},
		submitHandler: function(){}
	  },
  
	  /**
	   *
	   *
	   * @var jQuery pageCollection
	   */
		pageCollection: $(),
	  collections: [],
  
	  /**
	   * Initialization of Validation wrapper.
	   *
	   * @param String selector (optional)
	   * @param Object config (optional)
	   *
	   * @return jQuery pageCollection - collection of initialized items.
	   */
  
	  init: function (selector, config) {
		this.collection = selector && $(selector).length ? $(selector) : $();
		if (!$(selector).length) return;
  
		this.config = config && $.isPlainObject(config) ?
		  $.extend({}, this._baseConfig, config) : this._baseConfig;
  
		this.config.itemSelector = selector;
  
		this.initValidation();
		// ////////debugger
		return this.pageCollection;
	  },
  
	  initValidation: function () {
		//Variables
		var $self = this,
		  config = $self.config,
		  collection = $self.pageCollection;
		   //console.log($self)
		  // ////////debugger
		//Actions
		this.collection.each(function (i, el) {
		  //Variables
			var $this = $(el);
			$self.getInput(el);
		  //  ////////debugger
		  if ($this.hasClass('js-step-form')) {
			$.validator.setDefaults({
			  ignore: ':hidden:not(.active select)'
			});
		  } else {
			$.validator.setDefaults({
			  //ignore: ':hidden:not(select)'
				ignore: ":hidden:not(select)",
			});
		  }
			// ////////debugger
			const $form= $this.validate({
			errorElement: config['errorElement'],
			errorClass: config['errorClass'],
			rules: config['rules'],
			onkeyup: config['onkeyup'],
			errorPlacement: isEmpty(config['errorPlacement']) === true ? $self.errorPlacement : config['errorPlacement'],
			highlight: isEmpty(config['highlight']) === true ? $self.highlight : config['highlight'],
			unhighlight: isEmpty(config['unhighlight']) === true ? $self.unHighlight : config['unhighlight'],
			submitHandler: isEmpty(config['submitHandler']) === true ? $self.submitHandler : config['submitHandler']
		  });
  // ////////debugger
		  if($this.find('select').length) {
  
			$('select').change(function () {
			  $(this).valid();
			});
  
		  }
			$self.collections.push({
				$el: el,
				id: el.id || null,
				$initializedEl: $form
			});

		  collection = collection.add($this);
		});
		},
		getForm: function () {
			if ("number" == typeof t) {
				console.log("number", t)
				console.log("yes", this.collection[t].$initializedEl)
				return this.collections[t].$initializedEl
			} else {
				console.log("no", this)
				return this.collections[0].$initializedEl
			}
		},
		getInput: function (element) {
			var config = this._baseConfig,
				inp = Array.from(element), $fieldName;

			//console.log("inp ", inp)
			inp.forEach(function (el) {
				el.hasAttribute('name') ? config.rules[el.name] = changeAttToObject(el) : '';

			});
		},
	  errorPlacement: function (error, element) {
		var $this = $(element),
		  errorMsgClasses = $this.data('error-msg-classes');
  
		error.addClass(errorMsgClasses);
		error.appendTo(element.parents('.js-form-message'));
	  },
  
	  highlight: function (element,errorClass, validClass) {
		var $this = $(element),
		  errorClass = $this.data('error-class'),
		  successClass = $this.data('success-class');
		// console.log(this.currentForm.addClass("was-validated"))
		console.log($this,validClass)

		$this.removeClass(successClass).addClass(errorClass);
	  },
  
	  unHighlight: function (element) {
		var $this = $(element),
		  errorClass = $this.data('error-class'),
		  successClass = $this.data('success-class');
		//   ////////debugger
		  console.log(element.value);
		$this.removeClass(errorClass).addClass(successClass);
	  },
  
		submitHandler: function (form, e) {
			//debugger
			e.preventDefault();
		  var $this = $(form),
			  u = $this.data('url'),
				button = $(this.submitButton);
			console.log($this);
			addLoadingToButton(button)
			if (form.hasAttribute("enctype")) {
				var dataMulipart = formDataMultipart($this)
				passingFormDataMultipartRoute(u, dataMulipart);

				console.log("ssssssss", dataMulipart)
			} else if (form.hasAttribute("data-url") && !form.hasAttribute("data-insert-list") && !form.hasAttribute("data-insert-object-list") && !form.hasAttribute("data-form-filter")) {
				var obj = changeFormValueToObject(form);
				console.log($this)
				////debugger
				passingDataRoute(u, obj);
				
			} else if (form.hasAttribute("data-url") && form.hasAttribute("data-insert-list")) {
				var obj = changeFormValueToObject(form);
				reloadTableReportWithNewData(obj, button)
			}
			else if (form.hasAttribute("data-url") && form.hasAttribute("data-form-filter")) {
				var obj = changeFormValueToObject(form);
				reloadTableWithNewData(obj, button, $this.data("datatable-id"))
				////debugger
			}
			else if (form.hasAttribute("data-url") && form.hasAttribute("data-insert-object-list") && !form.hasAttribute("data-form-filter")) {
				var obj = changeFormValueToObject(form);
				passingDataRoute(u, obj)
				//var dataMulipart = formDataMultipart($this)
				//passingFormDataMultipartRoute(u, dataMulipart);

				//debugger
		  }
		 
	  }
	}
	// console.log($.HSCore.components.HSValidation);
  })(jQuery);


       

function reloadTableReportWithNewData(newParams, button) {
            // Update the getCustomParams function to return new parameters
			  
            $.CustomPluginDatatablesReport.getCustomParams = function () {
                return newParams;
            };

            // Reload the table
			//////debugger
			const exportDatatable = $.CustomPluginDatatablesReport.getItem('datatableReport')
			exportDatatable.ajax.reload();
			removeLoadingFromButton(button)
}
function reloadTableWithNewData(newParams, button,tableId) {
            // Update the getCustomParams function to return new parameters
			  
	$.CustomPluginDatatablesV1.getCustomParams = function () {
                return newParams;
            };

            // Reload the table
	//////debugger
	const datatable = $.CustomPluginDatatablesV1.getItem(tableId)
	datatable.ajax.reload();
	console.log('datatable', datatable);
	////debugger
			removeLoadingFromButton(button)
        }
       
//function changeFormValueToObject(form) {
//	var formdata = $(form).serializeArray();
//	var data = {};
//	var objectData = [];
//	var newObj = {};
//	console.log("formdata", formdata)
//	if (form.hasAttribute("data-insert-object")) {
	
//		function createTemplateObject(propertyName) {
//			if (!newObj[propertyName]) {
//				newObj[propertyName] = "";
//			}
//		}
//		$(formdata).each(function (index, obj) {
//			var residenceMatch = obj.name.match(/(\w+)\[(\d+)\]\.(\w+)/);
			
//			if (residenceMatch) {
//				objectData.push(obj);
//			}
//			else {
//				data[obj.name] = obj.value;
//			}
//		});
//		function combineObjects(arr) {
//			const result = {};
//			var residence = "";
//			arr.forEach((obj,i) => {
//				const match = obj.name.match(/(\w+)\[(\d+)\]\.(\w+)/);

//				//////////debugger
//				if (match) {
//					const [_, key, index, subKey] = match;
					//residence = key;
//					var d = result[index] = result[index] || {};
//					result[index][subKey] = obj.value;
//					createTemplateObject(subKey)
//					//////////debugger
//					console.log("1", result[index])
//					console.log("2", result[index][subKey])
//					console.log("3", [_, key, index, subKey])
//				}
				
				
//			});
			//if (!data[residence]) {
			//	data[residence] = [];
			//}
//			console.log("data[residence]1", data[residence])
//			data[residence] = 
//			 Object.values(result).filter(item => {
//				const keys1 = Object.keys(newObj);
//				 const keys2 = Object.keys(item);
//				 console.log("keys1.length === keys2.length",keys1.length === keys2.length)
//				 console.log("keys",keys1,keys2)
//				return keys1.length === keys2.length;
//			 });
//			console.log("data[residence]2", data[residence])

//			return Object.values(result).filter(item => {
//				const keys1 = Object.keys(newObj);
//				const keys2 = Object.keys(item);
//				return keys1.length === keys2.length;
//			});
//		}
//		const combinedObjects = combineObjects(objectData);
//		console.log("combinedObjects ", combinedObjects)

//	} else

//		if (form.hasAttribute("data-insert-list")) {
//		let objectData = [];
		
		
//		$(formdata).each(function (index, obj) {
//			const match = obj.name.match(/\[\](.+)/); // Modified regex to capture the key inside []
			
//			if (match) {
//				objectData.push(obj);
//			} else {
//				var trimmedValue = obj.value.trim();
//				if (trimmedValue) {
//					data[obj.name] = obj.value;
//				}
//			}
//		});

//		function combineLists(arr) {
//			const result = {};

//			arr.forEach((obj) => {
//				const match = obj.name.match(/\[\](.+)/);
				
//				if (match) {
//					const key = match[1]; // Capture the key, e.g., servicesId
//					if (!result[key]) {
//						result[key] = [];
//					}
//					if (obj.value !== null && obj.value !==''){
//								result[key].push(obj.value); // Push value into the array corresponding to the key
//							}else {
//								result[key] = null;
//							}
					
					
					
//				}
//			});
//			console.log("result[key]", result)
//			return result;
//		}

//		const combinedLists = combineLists(objectData);
//		Object.assign(data, combinedLists); // Combine the object data into the data object

//		console.log("final data", data);
//		} else

//		if (form.hasAttribute("data-insert-object-list")) {

//		function createTemplateObject(propertyName) {
//			if (!newObj[propertyName]) {
//				newObj[propertyName] = "";
//			}
//		}
//		$(formdata).each(function (index, obj) {
//			var residenceMatch = obj.name.match(/(\w+)\[]\.(\w+)/);
			
//			if (residenceMatch) {
//				objectData.push(obj);
//			}
//			else {
//				data[obj.name] = obj.value;
//			}
//		});
		
//		function combineObjectsa(arr) {
//			const result = {};
//			var residence = "";
			
//			arr.forEach((obj, i) => {
//				const match = obj.name.match(/(\w+)\[]\.(\w+)/);

//				//////////debugger
//				if (match) {
//					const [_, key, subKey] = match;
//					residence = key;
//					var d = result[i] = result[i] || {};
//					result[i][subKey] = obj.value;
//					createTemplateObject(subKey)
//					//////debugger
					
//				}
				
//				console.log("match", match);
//				//debugger

//			});
//			if (!data[residence]) {
//				data[residence] = [];
//			}
//			data[residence] =
//				Object.values(result).filter(item => {
//					const keys1 = Object.keys(newObj);
//					const keys2 = Object.keys(item);
//					return keys1.length === keys2.length;
//				});
//			return Object.values(result).filter(item => {
//				const keys1 = Object.keys(newObj);
//				const keys2 = Object.keys(item);
//				return keys1.length === keys2.length;
//			});
//		}
//		const combinedObjects = combineObjectsa(objectData);
//		console.log("Data with roleList:", combinedObjects);
//	} else {

//		$(formdata).each(function (index, obj) {

//			data[obj.name] = obj.value;

//		});
//	}
	
	
//	console.log("Resulting JSON:", data);
//	return data;

//}

function changeFormValueToObject(form) {
	const formData = $(form).serializeArray();
	const data = {};
	const objectData = [];
	const newObj = {};

	console.log("formData", formData);

	if (form.hasAttribute("data-insert-object")) {
		//combineObjects(formData, data, objectData, newObj);
		const output = convertToNestedStructure(formData);
		Object.assign(data, output);
	} else if (form.hasAttribute("data-insert-list")) {
		combineLists(formData, data, objectData);
		//Object.assign(data, output);
	} else if (form.hasAttribute("data-insert-object-list")) {
		combineObjectsa(formData, data, objectData, newObj);
		//Object.assign(data, output);
	} else {
		formData.forEach((obj) => {
			data[obj.name] = obj.value;
		});
	}

	console.log("Resulting JSON:", data);
	return data;
}

function createTemplateObject(propertyName, newObj) {
	if (!newObj[propertyName]) {
		newObj[propertyName] = "";
	}
}

function combineObjects(formData, data, objectData, newObj) {
	formData.forEach((obj) => {
		const residenceMatch = obj.name.match(/(\w+)\[(\d+)\]\.(\w+)/);
		const residenceMatchList = obj.name.match(/\[\](.+)/);
		//const matchMarge = obj.name.match(/(\w+)\[(\d+)\]\.(\w+)|\[\](.+)/);
		const matchMarge = obj.name.match(/(\w+)\[(\d+)\]\.\[\](\w+)/);
		console.log("residenceMatch", residenceMatch)
		console.log("residenceMatchList", residenceMatchList)
		console.log("matchMarge", matchMarge)
		if (residenceMatch) {
			objectData.push(obj);
		} else if (residenceMatchList) {
			objectData.push(obj);

		} else if (matchMarge) {
			objectData.push(obj);
		} else {
			data[obj.name] = obj.value;
		}
	});

	const result = {};
	const resultList = {};
	objectData.forEach((obj, index) => {
		const match = obj.name.match(/(\w+)\[(\d+)\]\.(\w+)/);
		const matchList = obj.name.match(/\[\](.+)/);
		const matchMarge = obj.name.match(/(\w+)\[(\d+)\]\.\[\](\w+)/);

		if (match) {
			const [_, key, index, subKey] = match;
			residence = key;
			result[index] = result[index] || {};
			result[index][subKey] = obj.value;
			createTemplateObject(subKey, newObj);
		} else if (matchList) {
			const key = matchList[1];
			resultList[key] = resultList[key] || [];
			if (obj.value !== null && obj.value !== "") {
				resultList[key].push(obj.value);
			} else {
				resultList[key] = null;
			}
			

		} else if (matchMarge) {
			const [_, key, index, subKey] = matchMarge;
			console.log(key)
			const resultListMarge = {};
			resultListMarge[index] = resultListMarge[index] || {};
			resultListMarge[index][subKey] = obj.value;
			console.log("resultListMarge",resultListMarge)
			//if (obj.value !== null && obj.value !== "") {
			//	resultList[key].push(obj.value);
			//} else {
			//	resultList[key] = null;
			//}
		}
	});
	if (!data[residence]) {
		data[residence] = [];
	}
	
	data[residence] = Object.values(result).filter((item) => {
		const keys1 = Object.keys(newObj);
		const keys2 = Object.keys(item);
		return keys1.length === keys2.length;
	});
	Object.assign(data, resultList);

}

function combineLists(formData, data, objectData) {
	formData.forEach((obj) => {
		const match = obj.name.match(/\[\](.+)/);
		if (match) {
			objectData.push(obj);
		} else {
			const trimmedValue = obj.value.trim();
			if (trimmedValue) {
				data[obj.name] = obj.value;
			}
		}
	});

	const result = {};
	objectData.forEach((obj) => {
		const match = obj.name.match(/\[\](.+)/);
		if (match) {
			const key = match[1];
			result[key] = result[key] || [];
			if (obj.value !== null && obj.value !== "") {
				result[key].push(obj.value);
			} else {
				result[key] = null;
			}
		}
	});

	Object.assign(data, result);
}

function combineObjectsa(formData, data, objectData, newObj) {
	formData.forEach((obj) => {
		const residenceMatch = obj.name.match(/(\w+)\[]\.(\w+)/);
		if (residenceMatch) {
			objectData.push(obj);
		} else {
			data[obj.name] = obj.value;
		}
	});

	const result = {};
	objectData.forEach((obj, index) => {
		const match = obj.name.match(/(\w+)\[]\.(\w+)/);
		if (match) {
			const [_, key, subKey] = match;
			result[index] = result[index] || {};
			result[index][subKey] = obj.value;
			createTemplateObject(subKey, newObj);
		}
	});

	const residence = Object.keys(result)[0];
	data[residence] = Object.values(result).filter((item) => {
		const keys1 = Object.keys(newObj);
		const keys2 = Object.keys(item);
		return keys1.length === keys2.length;
	});
	console.log("data", data);
	console.log("residence", residence);
}


function convertToNestedStructure(data) {
	const result = {};

	data.forEach(({ name, value }) => {
		// Check for indexed items like itemsAndItemOptions[0].itemId
		const indexedMatch = name.match(/(\w+)\[(\d*)\]\.(\w+)/);
		const dotSeparatedMatch = name.match(/(\w+)\.(\w+)/);
		const arrayMatch = name.match(/\[\](.+)/);

		if (indexedMatch) {
			const [, mainKey, index, subKey] = indexedMatch;

			if (!result[mainKey]) result[mainKey] = [];

			if (index === "") {
				// Empty brackets [] indicate appending to the array
				const lastElement = result[mainKey][result[mainKey].length - 1];
				if (lastElement && !lastElement.hasOwnProperty(subKey)) {
					lastElement[subKey] = value;
				} else {
					const newElement = { [subKey]: value };
					result[mainKey].push(newElement);
				}
			} else {
				if (!result[mainKey][index]) result[mainKey][index] = {};
				if (result[mainKey][index][subKey]) {
					if (Array.isArray(result[mainKey][index][subKey])) {
						result[mainKey][index][subKey].push(value);
					} else {
						result[mainKey][index][subKey] = [
							result[mainKey][index][subKey],
							value,
						];
					}
				} else {
					result[mainKey][index][subKey] = value;
				}
			}
		} else if (arrayMatch) {
			const [, mainKey, subKey] = arrayMatch;
			if (!result[mainKey]) result[mainKey] = [];
			if (subKey && value) {
				result[mainKey].push({ [subKey]: value });
			} else {
				result[mainKey].push(value);
			}
		} else if (dotSeparatedMatch) {
			const [, mainKey, subKey] = dotSeparatedMatch;
			if (!result[mainKey]) result[mainKey] = {};
			result[mainKey][subKey] = value;
		} else {
			if (result[name]) {
				if (Array.isArray(result[name])) {
					result[name].push(value);
				} else {
					result[name] = [result[name], value];
				}
			} else {
				result[name] = value;
			}
		}
	});

	return result;
}

function convertTextToArray(propertyNameMatch) {
	
	
	// Create an array and an element inside
	var myArray = [];
	var newElement = {};
	if (propertyNameMatch) {
		var propertyName = propertyNameMatch[0].substring(1); // remove the leading dot
		newElement[propertyName] = "string";
		console.log(propertyName)
	}

	return newElement;
}
function formDataMultipart($this) {
	var formElements = $this.find('input[name],select[name],textarea[name]');
	var formData = new FormData();
	
	formElements.each(function (index, obj) {


		//formData.append(obj.name, obj.value);
		if (obj.type === "file") {
			if (obj.files && obj.files[0]) {
				formData.append(obj.name, obj.files[0], obj.files[0].name);

			}
		} else {
			formData.append(obj.name, obj.value);
		}
		
		

	});
	
	return formData;
	
}

function formDataMultipartFile($this) {
	var formElements = $this.find('input[type="file"]');
	var formData = new FormData();
	var url;

	console.log("formElements", formElements)
	console.log("formElements", formElements)
	//////////debugger
	formElements.each(function (index, obj) {
		 url = $(obj).data('upload-url');
		if (obj.type === "file") {
			if (obj.name === "imageValueListChild") {
				for (let i = 0; i < obj.files.length; i++) {
					debugger
					formData.append(obj.name, obj.files[i], obj.files[i].name);
				}
			}
			else if (obj.files && obj.files[0]) {
				formData.append(obj.name, obj.files[0], obj.files[0].name);
				
				

			}
		}
		
	});
	if (url !== null) {
		passingFormDataMultipartRoute(url, formData);
	}

	

}
//function formDataMultipartListFile($this) {
//	var formElements = $this.find('input[type="file"]');
//	var formData = new FormData();
//	var url;

//	formElements.each(function (index, obj) {
//		url = $(obj).data('upload-url');
//		if (obj.type === "file" && obj.files) {
//			// Loop through all files in the file input
			
//		}
//	});

//	if (url !== null) {
//		passingFormDataMultipartRoute(url, formData);
//	}
//}

function passingDataRoute(urlForPostItem, userInfo) {
	const $form = $('[data-url="' + urlForPostItem + '"]'),
		$button = $form.find(':submit');
	

	////debugger
	$.ajax({
		headers: {
			"X-XSRF-TOKEN": $("[name='AntiforgeryFieldname']").val(),
			"Authorization": $("[name='AntiforgeryFieldname']").val()
		},
		type: 'POST',
		contentType: "application/json; charset=utf-8",
		url: urlForPostItem,
		dataType: "json",
		cache: false,
		data: JSON.stringify(userInfo),
		success: function (data) {
			
			if (data["url"] != null && data["url"] !== "upload_file" && data["url"] !== "model" && data["url"] !== "hide") {
					handleAjaxMessage(data);
				window.location.replace(data["url"]);
				console.log("url", data["url"]);
				debugger

			} else if (data["url"] != null && data["url"] === "upload_file" ) {
				console.log("upload_file", $('[data-url="' + this.url + '"]'));

				formDataMultipartFile($('[data-url="' + this.url + '"]'))
				}
			else if (data["url"] != null && data["url"] === "model") {
				const $formhere = $('[data-url="' + this.url + '"]'),
					$idModel = $formhere.parent().parent().parent().attr("id");

				if ($idModel) {
					$(`#${$idModel}`).modal("hide");
					//Clear form inputs
					$formhere[0].reset();
					$formhere.find("select").each(function (i, o) {
						const $set = $(o);
						$set.val(null).trigger('change');
						if ($set.hasClass('is-valid')) {
							$set.removeClass('is-valid');
						}
					});
				}
				const datatableReload = $.CustomPluginDatatablesV1.reloadDataTable();
				datatableReload.ajax.reload()
					handleAjaxMessage(data);

				}	
				
			handleAjaxMessage(data);

			removeLoadingFromButton($button);

			
		},
		error: function (req, status, err) {
			console.log('something went wrong', status, err, req);
			let errorMsg = "An unexpected error occurred.";

			if (req.status === 200) {
				errorMsg = "Received an unexpected HTML response. Please check the server for errors.";
			} else if (req.responseText && req.responseText.startsWith("<html")) {
				errorMsg = "Received an HTML response instead of JSON. Please check the server for errors.";
			} else {
				errorMsg = req.responseText || errorMsg;
			}

			$customToast.showToast({
				type: `Error ${req.status}`,
				text: errorMsg,
				icon: "error",
				color: "#ff4545",
				status: "error"
			});
			removeLoadingFromButton($button);
		}
	});
}
function passingDataRouteModel(model,dataForm) {
	$('.loading').addClass("without-background").show();
	$.ajax({
		url: `/Modal/getRenderModal/${model}`,
		method: 'GET',
		success: function (data) {
			$('.loading').removeClass("without-background").hide();
			$('body').append(data);
			$(`#${model}`).modal('show');
			const $formModal = $(`#${model}`).find("form");
			
			var formDataArray = $($formModal).serializeArray().filter(function (field) {
				// Exclude fields by name
				return field.name !== 'AntiforgeryFieldname';
			});

			$(formDataArray).each(function (index, obj) {
				const el = $(`[name="${obj.name}"]`);
				if (el.jHasAttribute("data-other-name")) {
					let oName = el.attr("data-other-name");
					//console.log("true", data[oName])
					el.val(dataForm[oName]);
					////console.log(data[])
				} else {
					dataForm.hasOwnProperty(obj.name) ? el.val(dataForm[obj.name]) : el.val(obj.value);


				}


			});
			
		}
	});
}


function passingFormDataMultipartRoute(urlForPostItem, userInfo) {
	const $form = $('[data-url="' + urlForPostItem + '"]'),
		$button = $form.find(':submit');
	//debugger
	console.log("userInfo", userInfo)
	$.ajax({
		headers: {
			"X-XSRF-TOKEN": $("[name='AntiforgeryFieldname']").val(),
			"Authorization": $("[name='AntiforgeryFieldname']").val()
		},
		url: urlForPostItem,
		data: userInfo,
		cache: false,
		contentType: false,
		processData: false,
		type: 'POST',
		success: function (data) {
			if (data["url"] != null && data["url"] != "hide_loading" && data["url"] !== "hide") {
				handleAjaxMessage(data);
				window.location.replace(data["url"]);
				
			} else if (data["url"] != null && data["url"] == "hide_loading") {
				$(".spinner-border.center-image-loading").hide()
				//$form.find('.preview').attr('src', e.target.result)
				handleAjaxMessage(data);
				const form = $('[data-url="' + this.url + '"]')
				console.log("upload_file", );
				console.log("upload_file", data);
				if (form.jHasAttribute('data-change-image')) {
					var CImage = $(form.data("change-image"));
					//[0].src = ;
						//attr('src',)
					CImage.attr('src', data['resObject'])
					console.log('CImage', CImage)
				}

			}
			
			console.log(data)
			
				handleAjaxMessage(data);
			
			
			removeLoadingFromButton($button);
			
		},
		error: function (req, status, err) {
			console.log('something went wrong', status, err + "   !!!!    " + JSON.stringify(req, null, 4));
			removeLoadingFromButton($button);
			$customToast.showToast({
				type: `خطأ ${req.status}`,
				text: req["responseText"],
				icon: "check",
				color: "#ff4545",
				status: "error",
			});
		}
	});





}
function changeAttToObject(el) {

	var objAttributes = {};
	var summernote = {};
	$(el).each(function () {

		$.each(this.attributes, function () {
			// this.attributes is not a plain object, but an array
			// of attribute nodes, which contain both the name and value
			//console.log("this.specified ", this.name === "class" && this.value.includes("requiredSummernote"))
			if (this.specified) {
				this.name === "name" && this.value === "dropdown-cantry" ? console.log(this.value) : '';
				this.name === "required" ? objAttributes[this.name] = true : ''
				this.name === "type" && this.value === "email" ||
					this.name === "name" && this.value === "email" ? objAttributes[this.value] = true : '';
				this.name === "name" && this.value === "email" ? objAttributes["isEmailValidWithRegex"] = true : '';
				this.name === "minlength" || this.name === "data-rule-minlength" ? objAttributes["minlength"] = this.value : '';
				this.name === "maxlength" || this.name === "data-rule-maxlength" ? objAttributes["maxlength"] = this.value : '';
				this.name === "data-rule-equalto" ? objAttributes["equalTo"] = this.value : '';
				this.name === "name" && this.value === "phoneNumber" || this.name === "type"
					&& this.value === "tel" ? objAttributes["isPhoneNumberValid"] = true : '';
				this.name === "class" && this.value.includes("permissin_box") ? objAttributes["permissin_box"] = true : '';
				this.name === "class" && this.value.includes("requiredSummernote") ? objAttributes["requiredSummernote"] = true: '';				
			}
		});
	});

	//console.log(objAttributes)

	return objAttributes
}
jQuery.validator.addMethod("requiredSummernote", function () {
	// true to pass validation
	// false to FAIL validation
	return !($(".requiredSummernote").summernote('isEmpty'));
},'Summernote field is required');
$.validator.addMethod("permissin_box", function (value, elem, param) {
	return $(".permissin_box:checkbox:checked").length > 0;
}, "You must select at least one!");
jQuery.validator.addMethod("isPhoneNumberValid", function (value, element) {

	var regexpPattern = /^(((\+|00)9677|0?7)[01378]\d{7}|((\+|00)967|0)[1-7]\d{6})/;
	// if (element.name ==="phoneNumber"){
	element.addEventListener('keypress', enforceFormatPhone);

	if (regexpPattern.test(value)) {
		// console.log("true")
		return true;   //  validation otherwise
	} else {
		return false;   // FAIL validation when REGEX matches
	};

}, "Please enter phone number content 77|78|73|71|70");
function enforceFormatPhone  (e)  {
	// Input must be of a valid number format or a modifier key, and not longer than ten digits
	// console.log(/[^\d]/g.test(event));
	var valid = ((e.which >= 65 && e.which <= 90) || (e.which >= 97 && e.which <= 122));
	console.log(valid)
	if (valid) {
		e.preventDefault();
		const phoneNumber = e.target.value.replace(/[^\d]/g, '');
		e.target.value = phoneNumber;

	} else {
		// e.target.addEventListener('keyup',formatToPhone);
	}
};

function addLoadingToButton(button) {
	if (button.hasClass('has-loading')) {
		var $button = $(button);
		$($button).attr("disabled", false);
		if ($($button).attr("disabled") == "disabled") {
			return false;
		}
		$($button).attr("disabled", true);

		const name = button.attr('data-last-name');
		$($button).html('<span class="spinner-border spinner-border-sm mr-1" role="status" aria-hidden="true"></span>' + `جاري ${name} ...`);

	}
}
function removeLoadingFromButton(button) {
	if (button.hasClass('has-loading')) {
		var $button = $(button);
		setTimeout(function () {
			$button.removeClass('active');
			$($button).attr("disabled", false);
			$($button).html($button.data("last-name"));


		}, 1000);
	}
	
}

var resultMessages = {
	1: "Success",
	2: "Inputs Error",
	3: "Privilege Error",
	4: "Not Found",
	5: "Internal Error",
	6: "Exception",
	7: "Block",
	8: "User Existing",
	9: "User Not Verified",
	10: "Verification Code is Error",
	11: "Exceeded Required Number",
	12: "Verification Code Has Expired",
	13: "Password Is Incorrect",
	14: "Please Change Your Password",
	15: "User Not Existing",
	16: "User Is Blocked",
	17: "Not Within Range",
	18: "No Drivers In Area",
	19: "User Does Not Have Permission",
	20: "Accept Order",
	21: "Not Accept Order",
	22: "Driver Is Null",
	23: "Driver Cannot Accept Multiple Orders",
	24: "Order Cancel",
	25: "User Info Not Complete",
	26: "User name Existing",
	0: "Error"
};




// Define a function to handle AJAX success
function handleAjaxMessage(data) {
	// Check the result code returned from the server
	////////debugger
	console.log("resultMessages", resultMessages.hasOwnProperty(data.resCode))
	console.log("resultMessages", data)
	if (data.resCode === 1) {
		if (data["url"] != null && data["url"] === "model" || data["url"] != null && data["url"] === "hide" || data["url"] == "hide_loading") {
			$customToast.showToast({
				type: "نجاح",
				text: "تمت العملية بنجاح",
				icon: "check",
				color: "#3ad29f",
				status: "success",
			});
		} else {
			sessionStorage.setItem('toastMessage', JSON.stringify({
				type: "نجاح",
				text: "تمت العملية بنجاح",
				icon: "check",
				color: "#3ad29f",
				status: "success",
			}));
		}
	} else {
		$customToast.showToast({
			type: "خطأ",
			text: data.resMsg,
			icon: "check",
			color: "#ff4545",
			status: "error",
		});
	}
	//switch (resultMessages.hasOwnProperty(data.resCode)) {
	//	case data.resCode===1: // Success
	//		console.log("Success:", data.resMsg);
	//		if (data["url"] != null && data["url"] === "model" || data["url"] != null && data["url"] === "hide" || data["url"] == "hide_loading") {
	//			$customToast.showToast({
	//				type: "نجاح",
	//				text: "Your changes have been saved",
	//				icon: "check",
	//				color: "#3ad29f",
	//				status: "success",
	//			});
	//		} else {
	//			sessionStorage.setItem('toastMessage', JSON.stringify({
	//				type: "نجاح",
	//				text: "Your changes have been saved",
	//				icon: "check",
	//				color: "#3ad29f",
	//				status: "success",
	//			}));
	//		}
			
			
	//		break;
	//	case resultMessages.hasOwnProperty(data.resCode): // InputsError
	//		console.log(resultMessages[data.resCode], data.resMsg);
	//		// Handle inputs error scenario
	//		$customToast.showToast({
	//			type: "خطأ",
	//			text: data.resMsg,
	//			icon: "check",
	//			color: "#ff4545",
	//			status: "error",
	//		});
	//		break;
	//	default:
	//		$customToast.showToast({
	//			type: "خطأ",
	//			text: data.resMsg,
	//			icon: "check",
	//			color: "#ff4545",
	//			status: "error",
	//		});
	//		console.log("Unhandled Result Code:", data.resCode);
	//		// Handle other result codes
	//		break;
	//}
	
	
}


//const password = document.getElementById('password');
//const togglePassword = document.getElementById('togglePassword');

//togglePassword.addEventListener('click', function () {
//	// Toggle the type attribute
//	const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
//	password.setAttribute('type', type);

//	// Toggle the eye / eye slash icon
//	this.classList.toggle('fe-eye');
//	this.classList.toggle('fe-eye-off');
//});
