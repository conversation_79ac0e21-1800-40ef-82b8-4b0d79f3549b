!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(function(){try{return require("moment")}catch(t){}}()):"function"==typeof define&&define.amd?define(["require"],function(t){return e(function(){try{return t("moment")}catch(t){}}())}):(t=t||self).Chart=e(t.moment)}(this,function(i){"use strict";i=i&&i.hasOwnProperty("default")?i.default:i;var t,u={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},d=(function(t){var d={};for(var e in u)u.hasOwnProperty(e)&&(d[u[e]]=e);var o=t.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(var n in o)if(o.hasOwnProperty(n)){if(!("channels"in o[n]))throw new Error("missing channels property: "+n);if(!("labels"in o[n]))throw new Error("missing channel labels property: "+n);if(o[n].labels.length!==o[n].channels)throw new Error("channel and label counts mismatch: "+n);var i=o[n].channels,a=o[n].labels;delete o[n].channels,delete o[n].labels,Object.defineProperty(o[n],"channels",{value:i}),Object.defineProperty(o[n],"labels",{value:a})}o.rgb.hsl=function(t){var e,n,i=t[0]/255,a=t[1]/255,r=t[2]/255,o=Math.min(i,a,r),s=Math.max(i,a,r),l=s-o;return s===o?e=0:i===s?e=(a-r)/l:a===s?e=2+(r-i)/l:r===s&&(e=4+(i-a)/l),(e=Math.min(60*e,360))<0&&(e+=360),n=(o+s)/2,[e,100*(s===o?0:n<=.5?l/(s+o):l/(2-s-o)),100*n]},o.rgb.hsv=function(t){var e,n,i,a,r,o=t[0]/255,s=t[1]/255,l=t[2]/255,d=Math.max(o,s,l),u=d-Math.min(o,s,l),h=function(t){return(d-t)/6/u+.5};return 0==u?a=r=0:(r=u/d,e=h(o),n=h(s),i=h(l),o===d?a=i-n:s===d?a=1/3+e-i:l===d&&(a=2/3+n-e),a<0?a+=1:1<a&&--a),[360*a,100*r,100*d]},o.rgb.hwb=function(t){var e=t[0],n=t[1],i=t[2];return[o.rgb.hsl(t)[0],1/255*Math.min(e,Math.min(n,i))*100,100*(i=1-1/255*Math.max(e,Math.max(n,i)))]},o.rgb.cmyk=function(t){var e,n=t[0]/255,i=t[1]/255,a=t[2]/255;return[100*((1-n-(e=Math.min(1-n,1-i,1-a)))/(1-e)||0),100*((1-i-e)/(1-e)||0),100*((1-a-e)/(1-e)||0),100*e]},o.rgb.keyword=function(t){var e=d[t];if(e)return e;var n,i,a,r,o,s=1/0;for(var l in u){u.hasOwnProperty(l)&&(r=u[l],i=t,a=r,(o=Math.pow(i[0]-a[0],2)+Math.pow(i[1]-a[1],2)+Math.pow(i[2]-a[2],2))<s&&(s=o,n=l))}return n},o.keyword.rgb=function(t){return u[t]},o.rgb.xyz=function(t){var e=t[0]/255,n=t[1]/255,i=t[2]/255;return[100*(.4124*(e=.04045<e?Math.pow((e+.055)/1.055,2.4):e/12.92)+.3576*(n=.04045<n?Math.pow((n+.055)/1.055,2.4):n/12.92)+.1805*(i=.04045<i?Math.pow((i+.055)/1.055,2.4):i/12.92)),100*(.2126*e+.7152*n+.0722*i),100*(.0193*e+.1192*n+.9505*i)]},o.rgb.lab=function(t){var e=o.rgb.xyz(t),n=e[0],i=e[1],a=e[2];return i/=100,a/=108.883,n=.008856<(n/=95.047)?Math.pow(n,1/3):7.787*n+16/116,[116*(i=.008856<i?Math.pow(i,1/3):7.787*i+16/116)-16,500*(n-i),200*(i-(a=.008856<a?Math.pow(a,1/3):7.787*a+16/116))]},o.hsl.rgb=function(t){var e,n,i,a,r,o=t[0]/360,s=t[1]/100,l=t[2]/100;if(0==s)return[r=255*l,r,r];e=2*l-(n=l<.5?l*(1+s):l+s-l*s),a=[0,0,0];for(var d=0;d<3;d++)(i=o+1/3*-(d-1))<0&&i++,1<i&&i--,r=6*i<1?e+6*(n-e)*i:2*i<1?n:3*i<2?e+(n-e)*(2/3-i)*6:e,a[d]=255*r;return a},o.hsl.hsv=function(t){var e=t[0],n=t[1]/100,i=t[2]/100,a=n,r=Math.max(i,.01);return n*=(i*=2)<=1?i:2-i,a*=r<=1?r:2-r,[e,100*(0==i?2*a/(r+a):2*n/(i+n)),(i+n)/2*100]},o.hsv.rgb=function(t){var e=t[0]/60,n=t[1]/100,i=t[2]/100,a=Math.floor(e)%6,r=e-Math.floor(e),o=255*i*(1-n),s=255*i*(1-n*r),l=255*i*(1-n*(1-r));switch(i*=255,a){case 0:return[i,l,o];case 1:return[s,i,o];case 2:return[o,i,l];case 3:return[o,s,i];case 4:return[l,o,i];case 5:return[i,o,s]}},o.hsv.hsl=function(t){var e,n,i,a=t[0],r=t[1]/100,o=t[2]/100,s=Math.max(o,.01);return i=(2-r)*o,n=r*s,[a,100*(n=(n/=(e=(2-r)*s)<=1?e:2-e)||0),100*(i/=2)]},o.hwb.rgb=function(t){var e,n,i,a,r,o,s,l=t[0]/360,d=t[1]/100,u=t[2]/100,h=d+u;switch(1<h&&(d/=h,u/=h),i=6*l-(e=Math.floor(6*l)),0!=(1&e)&&(i=1-i),a=d+i*((n=1-u)-d),e){default:case 6:case 0:r=n,o=a,s=d;break;case 1:r=a,o=n,s=d;break;case 2:r=d,o=n,s=a;break;case 3:r=d,o=a,s=n;break;case 4:r=a,o=d,s=n;break;case 5:r=n,o=d,s=a}return[255*r,255*o,255*s]},o.cmyk.rgb=function(t){var e=t[0]/100,n=t[1]/100,i=t[2]/100,a=t[3]/100;return[255*(1-Math.min(1,e*(1-a)+a)),255*(1-Math.min(1,n*(1-a)+a)),255*(1-Math.min(1,i*(1-a)+a))]},o.xyz.rgb=function(t){var e,n,i,a=t[0]/100,r=t[1]/100,o=t[2]/100;return n=-.9689*a+1.8758*r+.0415*o,i=.0557*a+-.204*r+1.057*o,e=.0031308<(e=3.2406*a+-1.5372*r+-.4986*o)?1.055*Math.pow(e,1/2.4)-.055:12.92*e,n=.0031308<n?1.055*Math.pow(n,1/2.4)-.055:12.92*n,i=.0031308<i?1.055*Math.pow(i,1/2.4)-.055:12.92*i,[255*(e=Math.min(Math.max(0,e),1)),255*(n=Math.min(Math.max(0,n),1)),255*(i=Math.min(Math.max(0,i),1))]},o.xyz.lab=function(t){var e=t[0],n=t[1],i=t[2];return n/=100,i/=108.883,e=.008856<(e/=95.047)?Math.pow(e,1/3):7.787*e+16/116,[116*(n=.008856<n?Math.pow(n,1/3):7.787*n+16/116)-16,500*(e-n),200*(n-(i=.008856<i?Math.pow(i,1/3):7.787*i+16/116))]},o.lab.xyz=function(t){var e,n,i,a=t[0];e=t[1]/500+(n=(a+16)/116),i=n-t[2]/200;var r=Math.pow(n,3),o=Math.pow(e,3),s=Math.pow(i,3);return n=.008856<r?r:(n-16/116)/7.787,e=.008856<o?o:(e-16/116)/7.787,i=.008856<s?s:(i-16/116)/7.787,[e*=95.047,n*=100,i*=108.883]},o.lab.lch=function(t){var e,n=t[0],i=t[1],a=t[2];return(e=360*Math.atan2(a,i)/2/Math.PI)<0&&(e+=360),[n,Math.sqrt(i*i+a*a),e]},o.lch.lab=function(t){var e,n=t[0],i=t[1];return e=t[2]/360*2*Math.PI,[n,i*Math.cos(e),i*Math.sin(e)]},o.rgb.ansi16=function(t){var e=t[0],n=t[1],i=t[2],a=1 in arguments?arguments[1]:o.rgb.hsv(t)[2];if(0===(a=Math.round(a/50)))return 30;var r=30+(Math.round(i/255)<<2|Math.round(n/255)<<1|Math.round(e/255));return 2===a&&(r+=60),r},o.hsv.ansi16=function(t){return o.rgb.ansi16(o.hsv.rgb(t),t[2])},o.rgb.ansi256=function(t){var e=t[0],n=t[1],i=t[2];return e===n&&n===i?e<8?16:248<e?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(n/255*5)+Math.round(i/255*5)},o.ansi16.rgb=function(t){var e=t%10;if(0===e||7===e)return 50<t&&(e+=3.5),[e=e/10.5*255,e,e];var n=.5*(1+~~(50<t));return[(1&e)*n*255,(e>>1&1)*n*255,(e>>2&1)*n*255]},o.ansi256.rgb=function(t){if(232<=t){var e=10*(t-232)+8;return[e,e,e]}var n;return t-=16,[Math.floor(t/36)/5*255,Math.floor((n=t%36)/6)/5*255,n%6/5*255]},o.rgb.hex=function(t){var e=(((255&Math.round(t[0]))<<16)+((255&Math.round(t[1]))<<8)+(255&Math.round(t[2]))).toString(16).toUpperCase();return"000000".substring(e.length)+e},o.hex.rgb=function(t){var e=t.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!e)return[0,0,0];var n=e[0];3===e[0].length&&(n=n.split("").map(function(t){return t+t}).join(""));var i=parseInt(n,16);return[i>>16&255,i>>8&255,255&i]},o.rgb.hcg=function(t){var e,n=t[0]/255,i=t[1]/255,a=t[2]/255,r=Math.max(Math.max(n,i),a),o=Math.min(Math.min(n,i),a),s=r-o;return e=s<=0?0:r===n?(i-a)/s%6:r===i?2+(a-n)/s:4+(n-i)/s+4,e/=6,[360*(e%=1),100*s,100*(s<1?o/(1-s):0)]},o.hsl.hcg=function(t){var e=t[1]/100,n=t[2]/100,i=1,a=0;return(i=n<.5?2*e*n:2*e*(1-n))<1&&(a=(n-.5*i)/(1-i)),[t[0],100*i,100*a]},o.hsv.hcg=function(t){var e=t[1]/100,n=t[2]/100,i=e*n,a=i<1?(n-i)/(1-i):0;return[t[0],100*i,100*a]},o.hcg.rgb=function(t){var e=t[0]/360,n=t[1]/100,i=t[2]/100;if(0==n)return[255*i,255*i,255*i];var a,r=[0,0,0],o=e%1*6,s=o%1,l=1-s;switch(Math.floor(o)){case 0:r[0]=1,r[1]=s,r[2]=0;break;case 1:r[0]=l,r[1]=1,r[2]=0;break;case 2:r[0]=0,r[1]=1,r[2]=s;break;case 3:r[0]=0,r[1]=l,r[2]=1;break;case 4:r[0]=s,r[1]=0,r[2]=1;break;default:r[0]=1,r[1]=0,r[2]=l}return a=(1-n)*i,[255*(n*r[0]+a),255*(n*r[1]+a),255*(n*r[2]+a)]},o.hcg.hsv=function(t){var e=t[1]/100,n=e+t[2]/100*(1-e),i=0<n?e/n:0;return[t[0],100*i,100*n]},o.hcg.hsl=function(t){var e=t[1]/100,n=t[2]/100*(1-e)+.5*e,i=0;return 0<n&&n<.5?i=e/(2*n):.5<=n&&n<1&&(i=e/(2*(1-n))),[t[0],100*i,100*n]},o.hcg.hwb=function(t){var e=t[1]/100,n=e+t[2]/100*(1-e);return[t[0],100*(n-e),100*(1-n)]},o.hwb.hcg=function(t){var e=t[1]/100,n=1-t[2]/100,i=n-e,a=i<1?(n-i)/(1-i):0;return[t[0],100*i,100*a]},o.apple.rgb=function(t){return[t[0]/65535*255,t[1]/65535*255,t[2]/65535*255]},o.rgb.apple=function(t){return[t[0]/255*65535,t[1]/255*65535,t[2]/255*65535]},o.gray.rgb=function(t){return[t[0]/100*255,t[0]/100*255,t[0]/100*255]},o.gray.hsl=o.gray.hsv=function(t){return[0,0,t[0]]},o.gray.hwb=function(t){return[0,100,t[0]]},o.gray.cmyk=function(t){return[0,0,0,t[0]]},o.gray.lab=function(t){return[t[0],0,0]},o.gray.hex=function(t){var e=255&Math.round(t[0]/100*255),n=((e<<16)+(e<<8)+e).toString(16).toUpperCase();return"000000".substring(n.length)+n},o.rgb.gray=function(t){return[(t[0]+t[1]+t[2])/3/255*100]}}(t={exports:{}}),t.exports);function l(t,e){for(var n=[e[t].parent,t],i=d[e[t].parent][t],a=e[t].parent;e[a].parent;)n.unshift(e[a].parent),i=function(e,n){return function(t){return n(e(t))}}(d[e[a].parent][a],i),a=e[a].parent;return i.conversion=n,i}d.rgb,d.hsl,d.hsv,d.hwb,d.cmyk,d.xyz,d.lab,d.lch,d.hex,d.keyword,d.ansi16,d.ansi256,d.hcg,d.apple,d.gray;var h={};Object.keys(d).forEach(function(o){h[o]={},Object.defineProperty(h[o],"channels",{value:d[o].channels}),Object.defineProperty(h[o],"labels",{value:d[o].labels});var s=function(){for(var t=function(t){var e=function(){for(var t={},e=Object.keys(d),n=e.length,i=0;i<n;i++)t[e[i]]={distance:-1,parent:null};return t}(),n=[t];for(e[t].distance=0;n.length;)for(var i=n.pop(),a=Object.keys(d[i]),r=a.length,o=0;o<r;o++){var s=a[o],l=e[s];-1===l.distance&&(l.distance=e[i].distance+1,l.parent=i,n.unshift(s))}return e}(o),e={},n=Object.keys(t),i=n.length,a=0;a<i;a++){var r=n[a];null!==t[r].parent&&(e[r]=l(r,t))}return e}();Object.keys(s).forEach(function(t){var e,a,n=s[t];function i(t){return null==t?t:(1<arguments.length&&(t=Array.prototype.slice.call(arguments)),e(t))}function r(t){if(null==t)return t;1<arguments.length&&(t=Array.prototype.slice.call(arguments));var e=a(t);if("object"==typeof e)for(var n=e.length,i=0;i<n;i++)e[i]=Math.round(e[i]);return e}h[o][t]=("conversion"in(a=n)&&(r.conversion=a.conversion),r),h[o][t].raw=("conversion"in(e=n)&&(i.conversion=e.conversion),i)})});var c=h,o={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},n={getRgba:a,getHsla:r,getRgb:function(t){var e=a(t);return e&&e.slice(0,3)},getHsl:function(t){var e=r(t);return e&&e.slice(0,3)},getHwb:s,getAlpha:function(t){var e=a(t);return(e=e||r(t))||(e=s(t))?e[3]:void 0},hexString:function(t,e){return e=void 0!==e&&3===t.length?e:t[3],"#"+v(t[0])+v(t[1])+v(t[2])+(0<=e&&e<1?v(Math.round(255*e)):"")},rgbString:function(t,e){return e<1||t[3]&&t[3]<1?f(t,e):"rgb("+t[0]+", "+t[1]+", "+t[2]+")"},rgbaString:f,percentString:function(t,e){return e<1||t[3]&&t[3]<1?g(t,e):"rgb("+Math.round(t[0]/255*100)+"%, "+Math.round(t[1]/255*100)+"%, "+Math.round(t[2]/255*100)+"%)"},percentaString:g,hslString:function(t,e){return e<1||t[3]&&t[3]<1?p(t,e):"hsl("+t[0]+", "+t[1]+"%, "+t[2]+"%)"},hslaString:p,hwbString:function(t,e){return void 0===e&&(e=void 0!==t[3]?t[3]:1),"hwb("+t[0]+", "+t[1]+"%, "+t[2]+"%"+(void 0!==e&&1!==e?", "+e:"")+")"},keyword:function(t){return e[t.slice(0,3)]}};function a(t){if(t){var e=[0,0,0],n=1,i=t.match(/^#([a-fA-F0-9]{3,4})$/i),a="";if(i){a=(i=i[1])[3];for(var r=0;r<e.length;r++)e[r]=parseInt(i[r]+i[r],16);a&&(n=Math.round(parseInt(a+a,16)/255*100)/100)}else if(i=t.match(/^#([a-fA-F0-9]{6}([a-fA-F0-9]{2})?)$/i)){for(a=i[2],i=i[1],r=0;r<e.length;r++)e[r]=parseInt(i.slice(2*r,2*r+2),16);a&&(n=Math.round(parseInt(a,16)/255*100)/100)}else if(i=t.match(/^rgba?\(\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/i)){for(r=0;r<e.length;r++)e[r]=parseInt(i[r+1]);n=parseFloat(i[4])}else if(i=t.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/i)){for(r=0;r<e.length;r++)e[r]=Math.round(2.55*parseFloat(i[r+1]));n=parseFloat(i[4])}else if(i=t.match(/(\w+)/)){if("transparent"==i[1])return[0,0,0,0];if(!(e=o[i[1]]))return}for(r=0;r<e.length;r++)e[r]=m(e[r],0,255);return n=n||0==n?m(n,0,1):1,e[3]=n,e}}function r(t){if(t){var e=t.match(/^hsla?\(\s*([+-]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)/);if(e){var n=parseFloat(e[4]);return[m(parseInt(e[1]),0,360),m(parseFloat(e[2]),0,100),m(parseFloat(e[3]),0,100),m(isNaN(n)?1:n,0,1)]}}}function s(t){if(t){var e=t.match(/^hwb\(\s*([+-]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)/);if(e){var n=parseFloat(e[4]);return[m(parseInt(e[1]),0,360),m(parseFloat(e[2]),0,100),m(parseFloat(e[3]),0,100),m(isNaN(n)?1:n,0,1)]}}}function f(t,e){return void 0===e&&(e=void 0!==t[3]?t[3]:1),"rgba("+t[0]+", "+t[1]+", "+t[2]+", "+e+")"}function g(t,e){return"rgba("+Math.round(t[0]/255*100)+"%, "+Math.round(t[1]/255*100)+"%, "+Math.round(t[2]/255*100)+"%, "+(e||t[3]||1)+")"}function p(t,e){return void 0===e&&(e=void 0!==t[3]?t[3]:1),"hsla("+t[0]+", "+t[1]+"%, "+t[2]+"%, "+e+")"}function m(t,e,n){return Math.min(Math.max(e,t),n)}function v(t){var e=t.toString(16).toUpperCase();return e.length<2?"0"+e:e}var e={};for(var b in o)e[o[b]]=b;var x=function(t){return t instanceof x?t:this instanceof x?(this.valid=!1,this.values={rgb:[0,0,0],hsl:[0,0,0],hsv:[0,0,0],hwb:[0,0,0],cmyk:[0,0,0,0],alpha:1},void("string"==typeof t?(e=n.getRgba(t))?this.setValues("rgb",e):(e=n.getHsla(t))?this.setValues("hsl",e):(e=n.getHwb(t))&&this.setValues("hwb",e):"object"==typeof t&&(void 0!==(e=t).r||void 0!==e.red?this.setValues("rgb",e):void 0!==e.l||void 0!==e.lightness?this.setValues("hsl",e):void 0!==e.v||void 0!==e.value?this.setValues("hsv",e):void 0!==e.w||void 0!==e.whiteness?this.setValues("hwb",e):void 0===e.c&&void 0===e.cyan||this.setValues("cmyk",e)))):new x(t);var e};x.prototype={isValid:function(){return this.valid},rgb:function(){return this.setSpace("rgb",arguments)},hsl:function(){return this.setSpace("hsl",arguments)},hsv:function(){return this.setSpace("hsv",arguments)},hwb:function(){return this.setSpace("hwb",arguments)},cmyk:function(){return this.setSpace("cmyk",arguments)},rgbArray:function(){return this.values.rgb},hslArray:function(){return this.values.hsl},hsvArray:function(){return this.values.hsv},hwbArray:function(){var t=this.values;return 1!==t.alpha?t.hwb.concat([t.alpha]):t.hwb},cmykArray:function(){return this.values.cmyk},rgbaArray:function(){var t=this.values;return t.rgb.concat([t.alpha])},hslaArray:function(){var t=this.values;return t.hsl.concat([t.alpha])},alpha:function(t){return void 0===t?this.values.alpha:(this.setValues("alpha",t),this)},red:function(t){return this.setChannel("rgb",0,t)},green:function(t){return this.setChannel("rgb",1,t)},blue:function(t){return this.setChannel("rgb",2,t)},hue:function(t){return t=t&&((t%=360)<0?360+t:t),this.setChannel("hsl",0,t)},saturation:function(t){return this.setChannel("hsl",1,t)},lightness:function(t){return this.setChannel("hsl",2,t)},saturationv:function(t){return this.setChannel("hsv",1,t)},whiteness:function(t){return this.setChannel("hwb",1,t)},blackness:function(t){return this.setChannel("hwb",2,t)},value:function(t){return this.setChannel("hsv",2,t)},cyan:function(t){return this.setChannel("cmyk",0,t)},magenta:function(t){return this.setChannel("cmyk",1,t)},yellow:function(t){return this.setChannel("cmyk",2,t)},black:function(t){return this.setChannel("cmyk",3,t)},hexString:function(){return n.hexString(this.values.rgb)},rgbString:function(){return n.rgbString(this.values.rgb,this.values.alpha)},rgbaString:function(){return n.rgbaString(this.values.rgb,this.values.alpha)},percentString:function(){return n.percentString(this.values.rgb,this.values.alpha)},hslString:function(){return n.hslString(this.values.hsl,this.values.alpha)},hslaString:function(){return n.hslaString(this.values.hsl,this.values.alpha)},hwbString:function(){return n.hwbString(this.values.hwb,this.values.alpha)},keyword:function(){return n.keyword(this.values.rgb,this.values.alpha)},rgbNumber:function(){var t=this.values.rgb;return t[0]<<16|t[1]<<8|t[2]},luminosity:function(){for(var t=this.values.rgb,e=[],n=0;n<t.length;n++){var i=t[n]/255;e[n]=i<=.03928?i/12.92:Math.pow((.055+i)/1.055,2.4)}return.2126*e[0]+.7152*e[1]+.0722*e[2]},contrast:function(t){var e=this.luminosity(),n=t.luminosity();return n<e?(e+.05)/(n+.05):(n+.05)/(e+.05)},level:function(t){var e=this.contrast(t);return 7.1<=e?"AAA":4.5<=e?"AA":""},dark:function(){var t=this.values.rgb;return(299*t[0]+587*t[1]+114*t[2])/1e3<128},light:function(){return!this.dark()},negate:function(){for(var t=[],e=0;e<3;e++)t[e]=255-this.values.rgb[e];return this.setValues("rgb",t),this},lighten:function(t){var e=this.values.hsl;return e[2]+=e[2]*t,this.setValues("hsl",e),this},darken:function(t){var e=this.values.hsl;return e[2]-=e[2]*t,this.setValues("hsl",e),this},saturate:function(t){var e=this.values.hsl;return e[1]+=e[1]*t,this.setValues("hsl",e),this},desaturate:function(t){var e=this.values.hsl;return e[1]-=e[1]*t,this.setValues("hsl",e),this},whiten:function(t){var e=this.values.hwb;return e[1]+=e[1]*t,this.setValues("hwb",e),this},blacken:function(t){var e=this.values.hwb;return e[2]+=e[2]*t,this.setValues("hwb",e),this},greyscale:function(){var t=this.values.rgb,e=.3*t[0]+.59*t[1]+.11*t[2];return this.setValues("rgb",[e,e,e]),this},clearer:function(t){var e=this.values.alpha;return this.setValues("alpha",e-e*t),this},opaquer:function(t){var e=this.values.alpha;return this.setValues("alpha",e+e*t),this},rotate:function(t){var e=this.values.hsl,n=(e[0]+t)%360;return e[0]=n<0?360+n:n,this.setValues("hsl",e),this},mix:function(t,e){var n=t,i=void 0===e?.5:e,a=2*i-1,r=this.alpha()-n.alpha(),o=(1+(a*r==-1?a:(a+r)/(1+a*r)))/2,s=1-o;return this.rgb(o*this.red()+s*n.red(),o*this.green()+s*n.green(),o*this.blue()+s*n.blue()).alpha(this.alpha()*i+n.alpha()*(1-i))},toJSON:function(){return this.rgb()},clone:function(){var t,e,n=new x,i=this.values,a=n.values;for(var r in i)i.hasOwnProperty(r)&&(t=i[r],"[object Array]"===(e={}.toString.call(t))?a[r]=t.slice(0):"[object Number]"===e?a[r]=t:console.error("unexpected color value:",t));return n}},x.prototype.spaces={rgb:["red","green","blue"],hsl:["hue","saturation","lightness"],hsv:["hue","saturation","value"],hwb:["hue","whiteness","blackness"],cmyk:["cyan","magenta","yellow","black"]},x.prototype.maxes={rgb:[255,255,255],hsl:[360,100,100],hsv:[360,100,100],hwb:[360,100,100],cmyk:[100,100,100,100]},x.prototype.getValues=function(t){for(var e=this.values,n={},i=0;i<t.length;i++)n[t.charAt(i)]=e[t][i];return 1!==e.alpha&&(n.a=e.alpha),n},x.prototype.setValues=function(t,e){var n,i=this.values,a=this.spaces,r=this.maxes,o=1;if(this.valid=!0,"alpha"===t)o=e;else if(e.length)i[t]=e.slice(0,t.length),o=e[t.length];else if(void 0!==e[t.charAt(0)]){for(l=0;l<t.length;l++)i[t][l]=e[t.charAt(l)];o=e.a}else if(void 0!==e[a[t][0]]){for(var s=a[t],l=0;l<t.length;l++)i[t][l]=e[s[l]];o=e.alpha}if(i.alpha=Math.max(0,Math.min(1,void 0===o?i.alpha:o)),"alpha"===t)return!1;for(l=0;l<t.length;l++)n=Math.max(0,Math.min(r[t][l],i[t][l])),i[t][l]=Math.round(n);for(var d in a)d!==t&&(i[d]=c[t][d](i[t]));return!0},x.prototype.setSpace=function(t,e){var n=e[0];return void 0===n?this.getValues(t):("number"==typeof n&&(n=Array.prototype.slice.call(e)),this.setValues(t,n),this)},x.prototype.setChannel=function(t,e,n){var i=this.values[t];return void 0===n?i[e]:(n===i[e]||(i[e]=n,this.setValues(t,i)),this)},"undefined"!=typeof window&&(window.Color=x);var y,_=x,k={noop:function(){},uid:(y=0,function(){return y++}),isNullOrUndef:function(t){return null==t},isArray:function(t){if(Array.isArray&&Array.isArray(t))return!0;var e=Object.prototype.toString.call(t);return"[object"===e.substr(0,7)&&"Array]"===e.substr(-6)},isObject:function(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)},isFinite:function(t){return("number"==typeof t||t instanceof Number)&&isFinite(t)},valueOrDefault:function(t,e){return void 0===t?e:t},valueAtIndexOrDefault:function(t,e,n){return k.valueOrDefault(k.isArray(t)?t[e]:t,n)},callback:function(t,e,n){if(t&&"function"==typeof t.call)return t.apply(n,e)},each:function(t,e,n,i){var a,r,o;if(k.isArray(t))if(r=t.length,i)for(a=r-1;0<=a;a--)e.call(n,t[a],a);else for(a=0;a<r;a++)e.call(n,t[a],a);else if(k.isObject(t))for(r=(o=Object.keys(t)).length,a=0;a<r;a++)e.call(n,t[o[a]],o[a])},arrayEquals:function(t,e){var n,i,a,r;if(!t||!e||t.length!==e.length)return!1;for(n=0,i=t.length;n<i;++n)if(a=t[n],r=e[n],a instanceof Array&&r instanceof Array){if(!k.arrayEquals(a,r))return!1}else if(a!==r)return!1;return!0},clone:function(t){if(k.isArray(t))return t.map(k.clone);if(k.isObject(t)){for(var e={},n=Object.keys(t),i=n.length,a=0;a<i;++a)e[n[a]]=k.clone(t[n[a]]);return e}return t},_merger:function(t,e,n,i){var a=e[t],r=n[t];k.isObject(a)&&k.isObject(r)?k.merge(a,r,i):e[t]=k.clone(r)},_mergerIf:function(t,e,n){var i=e[t],a=n[t];k.isObject(i)&&k.isObject(a)?k.mergeIf(i,a):e.hasOwnProperty(t)||(e[t]=k.clone(a))},merge:function(t,e,n){var i,a,r,o,s,l=k.isArray(e)?e:[e],d=l.length;if(!k.isObject(t))return t;for(i=(n=n||{}).merger||k._merger,a=0;a<d;++a)if(e=l[a],k.isObject(e))for(s=0,o=(r=Object.keys(e)).length;s<o;++s)i(r[s],t,e,n);return t},mergeIf:function(t,e){return k.merge(t,e,{merger:k._mergerIf})},extend:Object.assign||function(t){return k.merge(t,[].slice.call(arguments,1),{merger:function(t,e,n){e[t]=n[t]}})},inherits:function(t){function e(){this.constructor=i}var n=this,i=t&&t.hasOwnProperty("constructor")?t.constructor:function(){return n.apply(this,arguments)};return e.prototype=n.prototype,i.prototype=new e,i.extend=k.inherits,t&&k.extend(i.prototype,t),i.__super__=n.prototype,i},_deprecated:function(t,e,n,i){void 0!==e&&console.warn(t+': "'+n+'" is deprecated. Please use "'+i+'" instead')}},w=k;k.callCallback=k.callback,k.indexOf=function(t,e,n){return Array.prototype.indexOf.call(t,e,n)},k.getValueOrDefault=k.valueOrDefault,k.getValueAtIndexOrDefault=k.valueAtIndexOrDefault;var M={linear:function(t){return t},easeInQuad:function(t){return t*t},easeOutQuad:function(t){return-t*(t-2)},easeInOutQuad:function(t){return(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1)},easeInCubic:function(t){return t*t*t},easeOutCubic:function(t){return--t*t*t+1},easeInOutCubic:function(t){return(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},easeInQuart:function(t){return t*t*t*t},easeOutQuart:function(t){return-(--t*t*t*t-1)},easeInOutQuart:function(t){return(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},easeInQuint:function(t){return t*t*t*t*t},easeOutQuint:function(t){return--t*t*t*t*t+1},easeInOutQuint:function(t){return(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},easeInSine:function(t){return 1-Math.cos(t*(Math.PI/2))},easeOutSine:function(t){return Math.sin(t*(Math.PI/2))},easeInOutSine:function(t){return-.5*(Math.cos(Math.PI*t)-1)},easeInExpo:function(t){return 0===t?0:Math.pow(2,10*(t-1))},easeOutExpo:function(t){return 1===t?1:1-Math.pow(2,-10*t)},easeInOutExpo:function(t){return 0===t?0:1===t?1:(t/=.5)<1?.5*Math.pow(2,10*(t-1)):.5*(2-Math.pow(2,-10*--t))},easeInCirc:function(t){return 1<=t?t:-(Math.sqrt(1-t*t)-1)},easeOutCirc:function(t){return Math.sqrt(1- --t*t)},easeInOutCirc:function(t){return(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},easeInElastic:function(t){var e=1.70158,n=0,i=1;return 0===t?0:1===t?1:(n=n||.3,e=i<1?(i=1,n/4):n/(2*Math.PI)*Math.asin(1/i),-i*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/n))},easeOutElastic:function(t){var e=1.70158,n=0,i=1;return 0===t?0:1===t?1:(n=n||.3,e=i<1?(i=1,n/4):n/(2*Math.PI)*Math.asin(1/i),i*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/n)+1)},easeInOutElastic:function(t){var e=1.70158,n=0,i=1;return 0===t?0:2==(t/=.5)?1:(n=n||.45,e=i<1?(i=1,n/4):n/(2*Math.PI)*Math.asin(1/i),t<1?i*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/n)*-.5:i*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/n)*.5+1)},easeInBack:function(t){return t*t*(2.70158*t-1.70158)},easeOutBack:function(t){return--t*t*(2.70158*t********)+1},easeInOutBack:function(t){var e=1.70158;return(t/=.5)<1?t*t*((1+(e*=1.525))*t-e)*.5:.5*((t-=2)*t*((1+(e*=1.525))*t+e)+2)},easeInBounce:function(t){return 1-M.easeOutBounce(1-t)},easeOutBounce:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},easeInOutBounce:function(t){return t<.5?.5*M.easeInBounce(2*t):.5*M.easeOutBounce(2*t-1)+.5}},S={effects:M};w.easingEffects=M;var C=Math.PI,P=C/180,A=2*C,D=C/2,T=C/4,I=2*C/3,F={clear:function(t){t.ctx.clearRect(0,0,t.width,t.height)},roundedRect:function(t,e,n,i,a,r){var o,s,l,d,u;r?(s=e+(o=Math.min(r,a/2,i/2)),l=n+o,d=e+i-o,u=n+a-o,t.moveTo(e,l),s<d&&l<u?(t.arc(s,l,o,-C,-D),t.arc(d,l,o,-D,0),t.arc(d,u,o,0,D),t.arc(s,u,o,D,C)):s<d?(t.moveTo(s,n),t.arc(d,l,o,-D,D),t.arc(s,l,o,D,C+D)):l<u?(t.arc(s,l,o,-C,0),t.arc(s,u,o,0,C)):t.arc(s,l,o,-C,C),t.closePath(),t.moveTo(e,n)):t.rect(e,n,i,a)},drawPoint:function(t,e,n,i,a,r){var o,s,l,d,u,h=(r||0)*P;if(e&&"object"==typeof e&&("[object HTMLImageElement]"===(o=e.toString())||"[object HTMLCanvasElement]"===o))return t.save(),t.translate(i,a),t.rotate(h),t.drawImage(e,-e.width/2,-e.height/2,e.width,e.height),void t.restore();if(!(isNaN(n)||n<=0)){switch(t.beginPath(),e){default:t.arc(i,a,n,0,A),t.closePath();break;case"triangle":t.moveTo(i+Math.sin(h)*n,a-Math.cos(h)*n),h+=I,t.lineTo(i+Math.sin(h)*n,a-Math.cos(h)*n),h+=I,t.lineTo(i+Math.sin(h)*n,a-Math.cos(h)*n),t.closePath();break;case"rectRounded":d=n-(u=.516*n),s=Math.cos(h+T)*d,l=Math.sin(h+T)*d,t.arc(i-s,a-l,u,h-C,h-D),t.arc(i+l,a-s,u,h-D,h),t.arc(i+s,a+l,u,h,h+D),t.arc(i-l,a+s,u,h+D,h+C),t.closePath();break;case"rect":if(!r){d=Math.SQRT1_2*n,t.rect(i-d,a-d,2*d,2*d);break}h+=T;case"rectRot":s=Math.cos(h)*n,l=Math.sin(h)*n,t.moveTo(i-s,a-l),t.lineTo(i+l,a-s),t.lineTo(i+s,a+l),t.lineTo(i-l,a+s),t.closePath();break;case"crossRot":h+=T;case"cross":s=Math.cos(h)*n,l=Math.sin(h)*n,t.moveTo(i-s,a-l),t.lineTo(i+s,a+l),t.moveTo(i+l,a-s),t.lineTo(i-l,a+s);break;case"star":s=Math.cos(h)*n,l=Math.sin(h)*n,t.moveTo(i-s,a-l),t.lineTo(i+s,a+l),t.moveTo(i+l,a-s),t.lineTo(i-l,a+s),h+=T,s=Math.cos(h)*n,l=Math.sin(h)*n,t.moveTo(i-s,a-l),t.lineTo(i+s,a+l),t.moveTo(i+l,a-s),t.lineTo(i-l,a+s);break;case"line":s=Math.cos(h)*n,l=Math.sin(h)*n,t.moveTo(i-s,a-l),t.lineTo(i+s,a+l);break;case"dash":t.moveTo(i,a),t.lineTo(i+Math.cos(h)*n,a+Math.sin(h)*n)}t.fill(),t.stroke()}},_isPointInArea:function(t,e){return t.x>e.left-1e-6&&t.x<e.right+1e-6&&t.y>e.top-1e-6&&t.y<e.bottom+1e-6},clipArea:function(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()},unclipArea:function(t){t.restore()},lineTo:function(t,e,n,i){var a,r=n.steppedLine;r?("middle"===r?(a=(e.x+n.x)/2,t.lineTo(a,i?n.y:e.y),t.lineTo(a,i?e.y:n.y)):"after"===r&&!i||"after"!==r&&i?t.lineTo(e.x,n.y):t.lineTo(n.x,e.y),t.lineTo(n.x,n.y)):n.tension?t.bezierCurveTo(i?e.controlPointPreviousX:e.controlPointNextX,i?e.controlPointPreviousY:e.controlPointNextY,i?n.controlPointNextX:n.controlPointPreviousX,i?n.controlPointNextY:n.controlPointPreviousY,n.x,n.y):t.lineTo(n.x,n.y)}},L=F;w.clear=F.clear,w.drawRoundedRectangle=function(t){t.beginPath(),F.roundedRect.apply(F,arguments)};var O={_set:function(t,e){return w.merge(this[t]||(this[t]={}),e)}};O._set("global",{defaultColor:"rgba(0,0,0,0.1)",defaultFontColor:"#666",defaultFontFamily:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",defaultFontSize:12,defaultFontStyle:"normal",defaultLineHeight:1.2,showLines:!0});var N=O,R=w.valueOrDefault,z={toLineHeight:function(t,e){var n=(""+t).match(/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/);if(!n||"normal"===n[1])return 1.2*e;switch(t=+n[2],n[3]){case"px":return t;case"%":t/=100}return e*t},toPadding:function(t){var e,n,i,a;return w.isObject(t)?(e=+t.top||0,n=+t.right||0,i=+t.bottom||0,a=+t.left||0):e=n=i=a=+t||0,{top:e,right:n,bottom:i,left:a,height:e+i,width:a+n}},_parseFont:function(t){var e,n=N.global,i=R(t.fontSize,n.defaultFontSize),a={family:R(t.fontFamily,n.defaultFontFamily),lineHeight:w.options.toLineHeight(R(t.lineHeight,n.defaultLineHeight),i),size:i,style:R(t.fontStyle,n.defaultFontStyle),weight:null,string:""};return a.string=!(e=a)||w.isNullOrUndef(e.size)||w.isNullOrUndef(e.family)?null:(e.style?e.style+" ":"")+(e.weight?e.weight+" ":"")+e.size+"px "+e.family,a},resolve:function(t,e,n,i){for(var a,r=!0,o=0,s=t.length;o<s;++o)if(void 0!==(a=t[o])&&(void 0!==e&&"function"==typeof a&&(a=a(e),r=!1),void 0!==n&&w.isArray(a)&&(a=a[n],r=!1),void 0!==a))return i&&!r&&(i.cacheable=!1),a}},B={_factorize:function(t){for(var e=[],n=Math.sqrt(t),i=1;i<n;i++)t%i==0&&(e.push(i),e.push(t/i));return n===(0|n)&&e.push(n),e.sort(function(t,e){return t-e}).pop(),e},log10:Math.log10||function(t){var e=Math.log(t)*Math.LOG10E,n=Math.round(e);return t===Math.pow(10,n)?n:e}},E=B;w.log10=B.log10;var W=w,V=L,H=z,j=E,q={getRtlAdapter:function(t,e,n){return t?(i=e,a=n,{x:function(t){return i+i+a-t},setWidth:function(t){a=t},textAlign:function(t){return"center"===t?t:"right"===t?"left":"right"},xPlus:function(t,e){return t-e},leftForLtr:function(t,e){return t-e}}):{x:function(t){return t},setWidth:function(t){},textAlign:function(t){return t},xPlus:function(t,e){return t+e},leftForLtr:function(t,e){return t}};var i,a},overrideTextDirection:function(t,e){var n,i;"ltr"!==e&&"rtl"!==e||(i=[(n=t.canvas.style).getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",e,"important"),t.prevTextDirection=i)},restoreTextDirection:function(t){var e=t.prevTextDirection;void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}};W.easing=S,W.canvas=V,W.options=H,W.math=j,W.rtl=q;function U(t){W.extend(this,t),this.initialize.apply(this,arguments)}W.extend(U.prototype,{_type:void 0,initialize:function(){this.hidden=!1},pivot:function(){var t=this;return t._view||(t._view=W.extend({},t._model)),t._start={},t},transition:function(t){var e=this,n=e._model,i=e._start,a=e._view;return n&&1!==t?(a=a||(e._view={}),function(t,e,n,i){for(var a,r,o,s,l,d,u,h=Object.keys(n),c=0,f=h.length;c<f;++c)if(s=n[a=h[c]],e.hasOwnProperty(a)||(e[a]=s),(r=e[a])!==s&&"_"!==a[0]){if(t.hasOwnProperty(a)||(t[a]=r),(l=typeof s)==typeof(o=t[a]))if("string"==l){if((d=_(o)).valid&&(u=_(s)).valid){e[a]=u.mix(d,i).rgbString();continue}}else if(W.isFinite(o)&&W.isFinite(s)){e[a]=o+(s-o)*i;continue}e[a]=s}}(i=i||(e._start={}),a,n,t)):(e._view=W.extend({},n),e._start=null),e},tooltipPosition:function(){return{x:this._model.x,y:this._model.y}},hasValue:function(){return W.isNumber(this._model.x)&&W.isNumber(this._model.y)}}),U.extend=W.inherits;var Y=U,G=Y.extend({chart:null,currentStep:0,numSteps:60,easing:"",render:null,onAnimationProgress:null,onAnimationComplete:null}),X=G;Object.defineProperty(G.prototype,"animationObject",{get:function(){return this}}),Object.defineProperty(G.prototype,"chartInstance",{get:function(){return this.chart},set:function(t){this.chart=t}}),N._set("global",{animation:{duration:1e3,easing:"easeOutQuart",onProgress:W.noop,onComplete:W.noop}});var K={animations:[],request:null,addAnimation:function(t,e,n,i){var a,r,o=this.animations;for(e.chart=t,e.startTime=Date.now(),e.duration=n,i||(t.animating=!0),a=0,r=o.length;a<r;++a)if(o[a].chart===t)return void(o[a]=e);o.push(e),1===o.length&&this.requestAnimationFrame()},cancelAnimation:function(e){var t=W.findIndex(this.animations,function(t){return t.chart===e});-1!==t&&(this.animations.splice(t,1),e.animating=!1)},requestAnimationFrame:function(){var t=this;null===t.request&&(t.request=W.requestAnimFrame.call(window,function(){t.request=null,t.startDigest()}))},startDigest:function(){this.advance(),0<this.animations.length&&this.requestAnimationFrame()},advance:function(){for(var t,e,n,i,a=this.animations,r=0;r<a.length;)e=(t=a[r]).chart,n=t.numSteps,i=Math.floor((Date.now()-t.startTime)/t.duration*n)+1,t.currentStep=Math.min(i,n),W.callback(t.render,[e,t],e),W.callback(t.onAnimationProgress,[t],e),t.currentStep>=n?(W.callback(t.onAnimationComplete,[t],e),e.animating=!1,a.splice(r,1)):++r}},Z=W.options.resolve,$=["push","pop","shift","splice","unshift"];function J(e,t){var n,i,a=e._chartjs;a&&(-1!==(i=(n=a.listeners).indexOf(t))&&n.splice(i,1),0<n.length||($.forEach(function(t){delete e[t]}),delete e._chartjs))}function Q(t,e){this.initialize(t,e)}W.extend(Q.prototype,{datasetElementType:null,dataElementType:null,_datasetElementOptions:["backgroundColor","borderCapStyle","borderColor","borderDash","borderDashOffset","borderJoinStyle","borderWidth"],_dataElementOptions:["backgroundColor","borderColor","borderWidth","pointStyle"],initialize:function(t,e){var n=this;n.chart=t,n.index=e,n.linkScales(),n.addElements(),n._type=n.getMeta().type},updateIndex:function(t){this.index=t},linkScales:function(){var t=this.getMeta(),e=this.chart,n=e.scales,i=this.getDataset(),a=e.options.scales;null!==t.xAxisID&&t.xAxisID in n&&!i.xAxisID||(t.xAxisID=i.xAxisID||a.xAxes[0].id),null!==t.yAxisID&&t.yAxisID in n&&!i.yAxisID||(t.yAxisID=i.yAxisID||a.yAxes[0].id)},getDataset:function(){return this.chart.data.datasets[this.index]},getMeta:function(){return this.chart.getDatasetMeta(this.index)},getScaleForId:function(t){return this.chart.scales[t]},_getValueScaleId:function(){return this.getMeta().yAxisID},_getIndexScaleId:function(){return this.getMeta().xAxisID},_getValueScale:function(){return this.getScaleForId(this._getValueScaleId())},_getIndexScale:function(){return this.getScaleForId(this._getIndexScaleId())},reset:function(){this._update(!0)},destroy:function(){this._data&&J(this._data,this)},createMetaDataset:function(){var t=this.datasetElementType;return t&&new t({_chart:this.chart,_datasetIndex:this.index})},createMetaData:function(t){var e=this.dataElementType;return e&&new e({_chart:this.chart,_datasetIndex:this.index,_index:t})},addElements:function(){for(var t=this.getMeta(),e=this.getDataset().data||[],n=t.data,i=0,a=e.length;i<a;++i)n[i]=n[i]||this.createMetaData(i);t.dataset=t.dataset||this.createMetaDataset()},addElementAndReset:function(t){var e=this.createMetaData(t);this.getMeta().data.splice(t,0,e),this.updateElement(e,t,!0)},buildOrUpdateElements:function(){var a,t,e=this,n=e.getDataset(),i=n.data||(n.data=[]);e._data!==i&&(e._data&&J(e._data,e),i&&Object.isExtensible(i)&&(t=e,(a=i)._chartjs?a._chartjs.listeners.push(t):(Object.defineProperty(a,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),$.forEach(function(t){var n="onData"+t.charAt(0).toUpperCase()+t.slice(1),i=a[t];Object.defineProperty(a,t,{configurable:!0,enumerable:!1,value:function(){var e=Array.prototype.slice.call(arguments),t=i.apply(this,e);return W.each(a._chartjs.listeners,function(t){"function"==typeof t[n]&&t[n].apply(t,e)}),t}})}))),e._data=i),e.resyncElements()},_configure:function(){this._config=W.merge({},[this.chart.options.datasets[this._type],this.getDataset()],{merger:function(t,e,n){"_meta"!==t&&"data"!==t&&W._merger(t,e,n)}})},_update:function(t){this._configure(),this._cachedDataOpts=null,this.update(t)},update:W.noop,transition:function(t){for(var e=this.getMeta(),n=e.data||[],i=n.length,a=0;a<i;++a)n[a].transition(t);e.dataset&&e.dataset.transition(t)},draw:function(){var t=this.getMeta(),e=t.data||[],n=e.length,i=0;for(t.dataset&&t.dataset.draw();i<n;++i)e[i].draw()},getStyle:function(t){var e,n=this.getMeta(),i=n.dataset;return this._configure(),!1!==(e=i&&void 0===t?this._resolveDatasetElementOptions(i||{}):(t=t||0,this._resolveDataElementOptions(n.data[t]||{},t))).fill&&null!==e.fill||(e.backgroundColor=e.borderColor),e},_resolveDatasetElementOptions:function(t,e){for(var n,i,a=this,r=a.chart,o=a._config,s=t.custom||{},l=r.options.elements[a.datasetElementType.prototype._type]||{},d=a._datasetElementOptions,u={},h={chart:r,dataset:a.getDataset(),datasetIndex:a.index,hover:e},c=0,f=d.length;c<f;++c)n=d[c],i=e?"hover"+n.charAt(0).toUpperCase()+n.slice(1):n,u[n]=Z([s[i],o[i],l[i]],h);return u},_resolveDataElementOptions:function(t,e){var n=this,i=t&&t.custom,a=n._cachedDataOpts;if(a&&!i)return a;var r,o,s,l,d=n.chart,u=n._config,h=d.options.elements[n.dataElementType.prototype._type]||{},c=n._dataElementOptions,f={},g={chart:d,dataIndex:e,dataset:n.getDataset(),datasetIndex:n.index},p={cacheable:!i},i=i||{};if(W.isArray(c))for(o=0,s=c.length;o<s;++o)f[l=c[o]]=Z([i[l],u[l],h[l]],g,e,p);else for(o=0,s=(r=Object.keys(c)).length;o<s;++o)f[l=r[o]]=Z([i[l],u[c[l]],u[l],h[l]],g,e,p);return p.cacheable&&(n._cachedDataOpts=Object.freeze(f)),f},removeHoverStyle:function(t){W.merge(t._model,t.$previousStyle||{}),delete t.$previousStyle},setHoverStyle:function(t){var e=this.chart.data.datasets[t._datasetIndex],n=t._index,i=t.custom||{},a=t._model,r=W.getHoverColor;t.$previousStyle={backgroundColor:a.backgroundColor,borderColor:a.borderColor,borderWidth:a.borderWidth},a.backgroundColor=Z([i.hoverBackgroundColor,e.hoverBackgroundColor,r(a.backgroundColor)],void 0,n),a.borderColor=Z([i.hoverBorderColor,e.hoverBorderColor,r(a.borderColor)],void 0,n),a.borderWidth=Z([i.hoverBorderWidth,e.hoverBorderWidth,a.borderWidth],void 0,n)},_removeDatasetHoverStyle:function(){var t=this.getMeta().dataset;t&&this.removeHoverStyle(t)},_setDatasetHoverStyle:function(){var t,e,n,i,a,r,o=this.getMeta().dataset,s={};if(o){for(r=o._model,a=this._resolveDatasetElementOptions(o,!0),t=0,e=(i=Object.keys(a)).length;t<e;++t)s[n=i[t]]=r[n],r[n]=a[n];o.$previousStyle=s}},resyncElements:function(){var t=this.getMeta(),e=this.getDataset().data,n=t.data.length,i=e.length;i<n?t.data.splice(i,n-i):n<i&&this.insertElements(n,i-n)},insertElements:function(t,e){for(var n=0;n<e;++n)this.addElementAndReset(t+n)},onDataPush:function(){var t=arguments.length;this.insertElements(this.getDataset().data.length-t,t)},onDataPop:function(){this.getMeta().data.pop()},onDataShift:function(){this.getMeta().data.shift()},onDataSplice:function(t,e){this.getMeta().data.splice(t,e),this.insertElements(t,arguments.length-2)},onDataUnshift:function(){this.insertElements(0,arguments.length)}}),Q.extend=W.inherits;var tt=Q,et=2*Math.PI;function nt(t,e){var n=e.startAngle,i=e.endAngle,a=e.pixelMargin,r=a/e.outerRadius,o=e.x,s=e.y;t.beginPath(),t.arc(o,s,e.outerRadius,n-r,i+r),e.innerRadius>a?(r=a/e.innerRadius,t.arc(o,s,e.innerRadius-a,i+r,n-r,!0)):t.arc(o,s,a,i+Math.PI/2,n-Math.PI/2),t.closePath(),t.clip()}N._set("global",{elements:{arc:{backgroundColor:N.global.defaultColor,borderColor:"#fff",borderWidth:2,borderAlign:"center"}}});var it=Y.extend({_type:"arc",inLabelRange:function(t){var e=this._view;return!!e&&Math.pow(t-e.x,2)<Math.pow(e.radius+e.hoverRadius,2)},inRange:function(t,e){var n=this._view;if(n){for(var i=W.getAngleFromPoint(n,{x:t,y:e}),a=i.angle,r=i.distance,o=n.startAngle,s=n.endAngle;s<o;)s+=et;for(;s<a;)a-=et;for(;a<o;)a+=et;var l=o<=a&&a<=s,d=r>=n.innerRadius&&r<=n.outerRadius;return l&&d}return!1},getCenterPoint:function(){var t=this._view,e=(t.startAngle+t.endAngle)/2,n=(t.innerRadius+t.outerRadius)/2;return{x:t.x+Math.cos(e)*n,y:t.y+Math.sin(e)*n}},getArea:function(){var t=this._view;return Math.PI*((t.endAngle-t.startAngle)/(2*Math.PI))*(Math.pow(t.outerRadius,2)-Math.pow(t.innerRadius,2))},tooltipPosition:function(){var t=this._view,e=t.startAngle+(t.endAngle-t.startAngle)/2,n=(t.outerRadius-t.innerRadius)/2+t.innerRadius;return{x:t.x+Math.cos(e)*n,y:t.y+Math.sin(e)*n}},draw:function(){var t,e,n,i,r,a=this._chart.ctx,o=this._view,s="inner"===o.borderAlign?.33:0,l={x:o.x,y:o.y,innerRadius:o.innerRadius,outerRadius:Math.max(o.outerRadius-s,0),pixelMargin:s,startAngle:o.startAngle,endAngle:o.endAngle,fullCircles:Math.floor(o.circumference/et)};if(a.save(),a.fillStyle=o.backgroundColor,a.strokeStyle=o.borderColor,l.fullCircles){for(l.endAngle=l.startAngle+et,a.beginPath(),a.arc(l.x,l.y,l.outerRadius,l.startAngle,l.endAngle),a.arc(l.x,l.y,l.innerRadius,l.endAngle,l.startAngle,!0),a.closePath(),t=0;t<l.fullCircles;++t)a.fill();l.endAngle=l.startAngle+o.circumference%et}a.beginPath(),a.arc(l.x,l.y,l.outerRadius,l.startAngle,l.endAngle),a.arc(l.x,l.y,l.innerRadius,l.endAngle,l.startAngle,!0),a.closePath(),a.fill(),o.borderWidth&&(e=a,i=l,(r="inner"===(n=o).borderAlign)?(e.lineWidth=2*n.borderWidth,e.lineJoin="round"):(e.lineWidth=n.borderWidth,e.lineJoin="bevel"),i.fullCircles&&function(t,e,n){var i,a=n.endAngle;for(r&&(n.endAngle=n.startAngle+et,nt(t,n),n.endAngle=a,n.endAngle===n.startAngle&&n.fullCircles&&(n.endAngle+=et,n.fullCircles--)),t.beginPath(),t.arc(n.x,n.y,n.innerRadius,n.startAngle+et,n.startAngle,!0),i=0;i<n.fullCircles;++i)t.stroke();for(t.beginPath(),t.arc(n.x,n.y,e.outerRadius,n.startAngle,n.startAngle+et),i=0;i<n.fullCircles;++i)t.stroke()}(e,n,i),r&&nt(e,i),e.beginPath(),e.arc(i.x,i.y,n.outerRadius,i.startAngle,i.endAngle),e.arc(i.x,i.y,i.innerRadius,i.endAngle,i.startAngle,!0),e.closePath(),e.stroke()),a.restore()}}),at=W.valueOrDefault,rt=N.global.defaultColor;N._set("global",{elements:{line:{tension:.4,backgroundColor:rt,borderWidth:3,borderColor:rt,borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",capBezierPoints:!0,fill:!0}}});var ot=Y.extend({_type:"line",draw:function(){var t,e,n,i=this,a=i._view,r=i._chart.ctx,o=a.spanGaps,s=i._children.slice(),l=N.global,d=l.elements.line,u=-1,h=i._loop;if(s.length){if(i._loop){for(t=0;t<s.length;++t)if(e=W.previousItem(s,t),!s[t]._view.skip&&e._view.skip){s=s.slice(t).concat(s.slice(0,t)),h=o;break}h&&s.push(s[0])}for(r.save(),r.lineCap=a.borderCapStyle||d.borderCapStyle,r.setLineDash&&r.setLineDash(a.borderDash||d.borderDash),r.lineDashOffset=at(a.borderDashOffset,d.borderDashOffset),r.lineJoin=a.borderJoinStyle||d.borderJoinStyle,r.lineWidth=at(a.borderWidth,d.borderWidth),r.strokeStyle=a.borderColor||l.defaultColor,r.beginPath(),(n=s[0]._view).skip||(r.moveTo(n.x,n.y),u=0),t=1;t<s.length;++t)n=s[t]._view,e=-1===u?W.previousItem(s,t):s[u],n.skip||(u!==t-1&&!o||-1===u?r.moveTo(n.x,n.y):W.canvas.lineTo(r,e._view,n),u=t);h&&r.closePath(),r.stroke(),r.restore()}}}),st=W.valueOrDefault,lt=N.global.defaultColor;function dt(t){var e=this._view;return!!e&&Math.abs(t-e.x)<e.radius+e.hitRadius}N._set("global",{elements:{point:{radius:3,pointStyle:"circle",backgroundColor:lt,borderColor:lt,borderWidth:1,hitRadius:1,hoverRadius:4,hoverBorderWidth:1}}});var ut=Y.extend({_type:"point",inRange:function(t,e){var n=this._view;return!!n&&Math.pow(t-n.x,2)+Math.pow(e-n.y,2)<Math.pow(n.hitRadius+n.radius,2)},inLabelRange:dt,inXRange:dt,inYRange:function(t){var e=this._view;return!!e&&Math.abs(t-e.y)<e.radius+e.hitRadius},getCenterPoint:function(){var t=this._view;return{x:t.x,y:t.y}},getArea:function(){return Math.PI*Math.pow(this._view.radius,2)},tooltipPosition:function(){var t=this._view;return{x:t.x,y:t.y,padding:t.radius+t.borderWidth}},draw:function(t){var e=this._view,n=this._chart.ctx,i=e.pointStyle,a=e.rotation,r=e.radius,o=e.x,s=e.y,l=N.global,d=l.defaultColor;e.skip||void 0!==t&&!W.canvas._isPointInArea(e,t)||(n.strokeStyle=e.borderColor||d,n.lineWidth=st(e.borderWidth,l.elements.point.borderWidth),n.fillStyle=e.backgroundColor||d,W.canvas.drawPoint(n,i,r,o,s,a))}}),ht=N.global.defaultColor;function ct(t){return t&&void 0!==t.width}function ft(t){var e,n,i,a,r=ct(t)?(a=t.width/2,e=t.x-a,n=t.x+a,i=Math.min(t.y,t.base),Math.max(t.y,t.base)):(a=t.height/2,e=Math.min(t.x,t.base),n=Math.max(t.x,t.base),i=t.y-a,t.y+a);return{left:e,top:i,right:n,bottom:r}}function gt(t,e,n){return t===e?n:t===n?e:t}function pt(t,e,n){var i=null===e,a=null===n,r=!(!t||i&&a)&&ft(t);return r&&(i||e>=r.left&&e<=r.right)&&(a||n>=r.top&&n<=r.bottom)}N._set("global",{elements:{rectangle:{backgroundColor:ht,borderColor:ht,borderSkipped:"bottom",borderWidth:0}}});var mt=Y.extend({_type:"rectangle",draw:function(){var t,e,n,i,a,r,o,s,l,d,u,h,c,f,g,p,m=this._chart.ctx,v=this._view,b=(c=ft(t=v),f=c.right-c.left,g=c.bottom-c.top,n=f/2,i=g/2,u=(e=t).borderWidth,l=e.borderSkipped,d={},l&&(e.horizontal?e.base>e.x&&(l=gt(l,"left","right")):e.base<e.y&&(l=gt(l,"bottom","top")),d[l]=!0),h=d,W.isObject(u)?(a=+u.top||0,r=+u.right||0,o=+u.bottom||0,s=+u.left||0):a=r=o=s=+u||0,p={t:h.top||a<0?0:i<a?i:a,r:h.right||r<0?0:n<r?n:r,b:h.bottom||o<0?0:i<o?i:o,l:h.left||s<0?0:n<s?n:s},{outer:{x:c.left,y:c.top,w:f,h:g},inner:{x:c.left+p.l,y:c.top+p.t,w:f-p.l-p.r,h:g-p.t-p.b}}),x=b.outer,y=b.inner;m.fillStyle=v.backgroundColor,m.fillRect(x.x,x.y,x.w,x.h),x.w===y.w&&x.h===y.h||(m.save(),m.beginPath(),m.rect(x.x,x.y,x.w,x.h),m.clip(),m.fillStyle=v.borderColor,m.rect(y.x,y.y,y.w,y.h),m.fill("evenodd"),m.restore())},height:function(){var t=this._view;return t.base-t.y},inRange:function(t,e){return pt(this._view,t,e)},inLabelRange:function(t,e){var n=this._view;return ct(n)?pt(n,t,null):pt(n,null,e)},inXRange:function(t){return pt(this._view,t,null)},inYRange:function(t){return pt(this._view,null,t)},getCenterPoint:function(){var t,e=this._view,n=ct(e)?(t=e.x,(e.y+e.base)/2):(t=(e.x+e.base)/2,e.y);return{x:t,y:n}},getArea:function(){var t=this._view;return ct(t)?t.width*Math.abs(t.y-t.base):t.height*Math.abs(t.x-t.base)},tooltipPosition:function(){var t=this._view;return{x:t.x,y:t.y}}}),vt={},bt=ot,xt=ut,yt=mt;vt.Arc=it,vt.Line=bt,vt.Point=xt,vt.Rectangle=yt;var _t=W._deprecated,kt=W.valueOrDefault;N._set("bar",{hover:{mode:"label"},scales:{xAxes:[{type:"category",offset:!0,gridLines:{offsetGridLines:!0}}],yAxes:[{type:"linear"}]}}),N._set("global",{datasets:{bar:{categoryPercentage:.8,barPercentage:.9}}});var wt=tt.extend({dataElementType:vt.Rectangle,_dataElementOptions:["backgroundColor","borderColor","borderSkipped","borderWidth","barPercentage","barThickness","categoryPercentage","maxBarThickness","minBarLength"],initialize:function(){var t,e,n=this;tt.prototype.initialize.apply(n,arguments),(t=n.getMeta()).stack=n.getDataset().stack,t.bar=!0,e=n._getIndexScale().options,_t("bar chart",e.barPercentage,"scales.[x/y]Axes.barPercentage","dataset.barPercentage"),_t("bar chart",e.barThickness,"scales.[x/y]Axes.barThickness","dataset.barThickness"),_t("bar chart",e.categoryPercentage,"scales.[x/y]Axes.categoryPercentage","dataset.categoryPercentage"),_t("bar chart",n._getValueScale().options.minBarLength,"scales.[x/y]Axes.minBarLength","dataset.minBarLength"),_t("bar chart",e.maxBarThickness,"scales.[x/y]Axes.maxBarThickness","dataset.maxBarThickness")},update:function(t){var e,n,i=this.getMeta().data;for(this._ruler=this.getRuler(),e=0,n=i.length;e<n;++e)this.updateElement(i[e],e,t)},updateElement:function(t,e,n){var i=this,a=i.getMeta(),r=i.getDataset(),o=i._resolveDataElementOptions(t,e);t._xScale=i.getScaleForId(a.xAxisID),t._yScale=i.getScaleForId(a.yAxisID),t._datasetIndex=i.index,t._index=e,t._model={backgroundColor:o.backgroundColor,borderColor:o.borderColor,borderSkipped:o.borderSkipped,borderWidth:o.borderWidth,datasetLabel:r.label,label:i.chart.data.labels[e]},W.isArray(r.data[e])&&(t._model.borderSkipped=null),i._updateElementGeometry(t,e,n,o),t.pivot()},_updateElementGeometry:function(t,e,n,i){var a=this,r=t._model,o=a._getValueScale(),s=o.getBasePixel(),l=o.isHorizontal(),d=a._ruler||a.getRuler(),u=a.calculateBarValuePixels(a.index,e,i),h=a.calculateBarIndexPixels(a.index,e,d,i);r.horizontal=l,r.base=n?s:u.base,r.x=l?n?s:u.head:h.center,r.y=l?h.center:n?s:u.head,r.height=l?h.size:void 0,r.width=l?void 0:h.size},_getStacks:function(t){for(var e,n=this._getIndexScale(),i=n._getMatchingVisibleMetas(this._type),a=n.options.stacked,r=i.length,o=[],s=0;s<r&&(e=i[s],(!1===a||-1===o.indexOf(e.stack)||void 0===a&&void 0===e.stack)&&o.push(e.stack),e.index!==t);++s);return o},getStackCount:function(){return this._getStacks().length},getStackIndex:function(t,e){var n=this._getStacks(t),i=void 0!==e?n.indexOf(e):-1;return-1===i?n.length-1:i},getRuler:function(){for(var t=this._getIndexScale(),e=[],n=0,i=this.getMeta().data.length;n<i;++n)e.push(t.getPixelForValue(null,n,this.index));return{pixels:e,start:t._startPixel,end:t._endPixel,stackCount:this.getStackCount(),scale:t}},calculateBarValuePixels:function(t,e,n){var i,a,r,o,s,l,d,u=this.chart,h=this._getValueScale(),c=h.isHorizontal(),f=u.data.datasets,g=h._getMatchingVisibleMetas(this._type),p=h._parseValue(f[t].data[e]),m=n.minBarLength,v=h.options.stacked,b=this.getMeta().stack,x=void 0===p.start?0:0<=p.max&&0<=p.min?p.min:p.max,y=void 0===p.start?p.end:0<=p.max&&0<=p.min?p.max-p.min:p.min-p.max,_=g.length;if(v||void 0===v&&void 0!==b)for(i=0;i<_&&(a=g[i]).index!==t;++i)a.stack===b&&(r=void 0===(d=h._parseValue(f[a.index].data[e])).start?d.end:0<=d.min&&0<=d.max?d.max:d.min,(p.min<0&&r<0||0<=p.max&&0<r)&&(x+=r));return o=h.getPixelForValue(x),l=(s=h.getPixelForValue(x+y))-o,void 0!==m&&Math.abs(l)<m&&(l=m,s=0<=y&&!c||y<0&&c?o-m:o+m),{size:l,base:o,head:s,center:s+l/2}},calculateBarIndexPixels:function(t,e,n,i){var a,r,o,s,l,d,u,h,c,f,g,p,m,v,b,x,y,_="flex"===i.barThickness?(g=i,m=(f=n).pixels,v=m[e],b=0<e?m[e-1]:null,x=e<m.length-1?m[e+1]:null,y=g.categoryPercentage,null===b&&(b=v-(null===x?f.end-f.start:x-v)),null===x&&(x=v+v-b),p=v-(v-Math.min(b,x))/2*y,{chunk:Math.abs(x-b)/2*y/f.stackCount,ratio:g.barPercentage,start:p}):(a=e,r=n,l=(o=i).barThickness,d=r.stackCount,u=r.pixels[a],h=W.isNullOrUndef(l)?function(t,e){for(var n,i,a=t._length,r=1,o=e.length;r<o;++r)a=Math.min(a,Math.abs(e[r]-e[r-1]));for(r=0,o=t.getTicks().length;r<o;++r)i=t.getPixelForTick(r),a=0<r?Math.min(a,Math.abs(i-n)):a,n=i;return a}(r.scale,r.pixels):-1,c=W.isNullOrUndef(l)?(s=h*o.categoryPercentage,o.barPercentage):(s=l*d,1),{chunk:s/d,ratio:c,start:u-s/2}),k=this.getStackIndex(t,this.getMeta().stack),w=_.start+_.chunk*k+_.chunk/2,M=Math.min(kt(i.maxBarThickness,1/0),_.chunk*_.ratio);return{base:w-M/2,head:w+M/2,center:w,size:M}},draw:function(){var t=this.chart,e=this._getValueScale(),n=this.getMeta().data,i=this.getDataset(),a=n.length,r=0;for(W.canvas.clipArea(t.ctx,t.chartArea);r<a;++r){var o=e._parseValue(i.data[r]);isNaN(o.min)||isNaN(o.max)||n[r].draw()}W.canvas.unclipArea(t.ctx)},_resolveDataElementOptions:function(){var t=W.extend({},tt.prototype._resolveDataElementOptions.apply(this,arguments)),e=this._getIndexScale().options,n=this._getValueScale().options;return t.barPercentage=kt(e.barPercentage,t.barPercentage),t.barThickness=kt(e.barThickness,t.barThickness),t.categoryPercentage=kt(e.categoryPercentage,t.categoryPercentage),t.maxBarThickness=kt(e.maxBarThickness,t.maxBarThickness),t.minBarLength=kt(n.minBarLength,t.minBarLength),t}}),Mt=W.valueOrDefault,St=W.options.resolve;N._set("bubble",{hover:{mode:"single"},scales:{xAxes:[{type:"linear",position:"bottom",id:"x-axis-0"}],yAxes:[{type:"linear",position:"left",id:"y-axis-0"}]},tooltips:{callbacks:{title:function(){return""},label:function(t,e){var n=e.datasets[t.datasetIndex].label||"",i=e.datasets[t.datasetIndex].data[t.index];return n+": ("+t.xLabel+", "+t.yLabel+", "+i.r+")"}}}});var Ct=tt.extend({dataElementType:vt.Point,_dataElementOptions:["backgroundColor","borderColor","borderWidth","hoverBackgroundColor","hoverBorderColor","hoverBorderWidth","hoverRadius","hitRadius","pointStyle","rotation"],update:function(n){var i=this,t=i.getMeta().data;W.each(t,function(t,e){i.updateElement(t,e,n)})},updateElement:function(t,e,n){var i=this,a=i.getMeta(),r=t.custom||{},o=i.getScaleForId(a.xAxisID),s=i.getScaleForId(a.yAxisID),l=i._resolveDataElementOptions(t,e),d=i.getDataset().data[e],u=i.index,h=n?o.getPixelForDecimal(.5):o.getPixelForValue("object"==typeof d?d:NaN,e,u),c=n?s.getBasePixel():s.getPixelForValue(d,e,u);t._xScale=o,t._yScale=s,t._options=l,t._datasetIndex=u,t._index=e,t._model={backgroundColor:l.backgroundColor,borderColor:l.borderColor,borderWidth:l.borderWidth,hitRadius:l.hitRadius,pointStyle:l.pointStyle,rotation:l.rotation,radius:n?0:l.radius,skip:r.skip||isNaN(h)||isNaN(c),x:h,y:c},t.pivot()},setHoverStyle:function(t){var e=t._model,n=t._options,i=W.getHoverColor;t.$previousStyle={backgroundColor:e.backgroundColor,borderColor:e.borderColor,borderWidth:e.borderWidth,radius:e.radius},e.backgroundColor=Mt(n.hoverBackgroundColor,i(n.backgroundColor)),e.borderColor=Mt(n.hoverBorderColor,i(n.borderColor)),e.borderWidth=Mt(n.hoverBorderWidth,n.borderWidth),e.radius=n.radius+n.hoverRadius},_resolveDataElementOptions:function(t,e){var n=this,i=n.chart,a=n.getDataset(),r=t.custom||{},o=a.data[e]||{},s=tt.prototype._resolveDataElementOptions.apply(n,arguments),l={chart:i,dataIndex:e,dataset:a,datasetIndex:n.index};return n._cachedDataOpts===s&&(s=W.extend({},s)),s.radius=St([r.radius,o.r,n._config.radius,i.options.elements.point.radius],l,e),s}}),Pt=W.valueOrDefault,At=Math.PI,Dt=2*At,Tt=At/2;N._set("doughnut",{animation:{animateRotate:!0,animateScale:!1},hover:{mode:"single"},legendCallback:function(t){var e,n,i,a=document.createElement("ul"),r=t.data,o=r.datasets,s=r.labels;if(a.setAttribute("class",t.id+"-legend"),o.length)for(n=o[e=0].data.length;e<n;++e)(i=a.appendChild(document.createElement("li"))).appendChild(document.createElement("span")).style.backgroundColor=o[0].backgroundColor[e],s[e]&&i.appendChild(document.createTextNode(s[e]));return a.outerHTML},legend:{labels:{generateLabels:function(a){var r=a.data;return r.labels.length&&r.datasets.length?r.labels.map(function(t,e){var n=a.getDatasetMeta(0),i=n.controller.getStyle(e);return{text:t,fillStyle:i.backgroundColor,strokeStyle:i.borderColor,lineWidth:i.borderWidth,hidden:isNaN(r.datasets[0].data[e])||n.data[e].hidden,index:e}}):[]}},onClick:function(t,e){for(var n,i=e.index,a=this.chart,r=0,o=(a.data.datasets||[]).length;r<o;++r)(n=a.getDatasetMeta(r)).data[i]&&(n.data[i].hidden=!n.data[i].hidden);a.update()}},cutoutPercentage:50,rotation:-Tt,circumference:Dt,tooltips:{callbacks:{title:function(){return""},label:function(t,e){var n=e.labels[t.index],i=": "+e.datasets[t.datasetIndex].data[t.index];return W.isArray(n)?(n=n.slice())[0]+=i:n+=i,n}}}});var It=tt.extend({dataElementType:vt.Arc,linkScales:W.noop,_dataElementOptions:["backgroundColor","borderColor","borderWidth","borderAlign","hoverBackgroundColor","hoverBorderColor","hoverBorderWidth"],getRingIndex:function(t){for(var e=0,n=0;n<t;++n)this.chart.isDatasetVisible(n)&&++e;return e},update:function(t){var e,n,i,a,r,o,s,l,d,u,h,c,f,g,p,m,v,b=this,x=b.chart,y=x.chartArea,_=x.options,k=1,w=1,M=0,S=0,C=b.getMeta(),P=C.data,A=_.cutoutPercentage/100||0,D=_.circumference,T=b._getRingWeight(b.index);for(D<Dt&&(r=_.rotation%Dt,o=(r+=At<=r?-Dt:r<-At?Dt:0)+D,s=Math.cos(r),l=Math.sin(r),d=Math.cos(o),u=Math.sin(o),h=r<=0&&0<=o||Dt<=o,c=r<=Tt&&Tt<=o||Dt+Tt<=o,f=r<=-Tt&&-Tt<=o||At+Tt<=o,g=r===-At||At<=o?-1:Math.min(s,s*A,d,d*A),p=f?-1:Math.min(l,l*A,u,u*A),k=((m=h?1:Math.max(s,s*A,d,d*A))-g)/2,w=((v=c?1:Math.max(l,l*A,u,u*A))-p)/2,M=-(m+g)/2,S=-(v+p)/2),i=0,a=P.length;i<a;++i)P[i]._options=b._resolveDataElementOptions(P[i],i);for(x.borderWidth=b.getMaxBorderWidth(),e=(y.right-y.left-x.borderWidth)/k,n=(y.bottom-y.top-x.borderWidth)/w,x.outerRadius=Math.max(Math.min(e,n)/2,0),x.innerRadius=Math.max(x.outerRadius*A,0),x.radiusLength=(x.outerRadius-x.innerRadius)/(b._getVisibleDatasetWeightTotal()||1),x.offsetX=M*x.outerRadius,x.offsetY=S*x.outerRadius,C.total=b.calculateTotal(),b.outerRadius=x.outerRadius-x.radiusLength*b._getRingWeightOffset(b.index),b.innerRadius=Math.max(b.outerRadius-x.radiusLength*T,0),i=0,a=P.length;i<a;++i)b.updateElement(P[i],i,t)},updateElement:function(t,e,n){var i=this,a=i.chart,r=a.chartArea,o=a.options,s=o.animation,l=(r.left+r.right)/2,d=(r.top+r.bottom)/2,u=o.rotation,h=o.rotation,c=i.getDataset(),f=n&&s.animateRotate||t.hidden?0:i.calculateCircumference(c.data[e])*(o.circumference/Dt),g=n&&s.animateScale?0:i.innerRadius,p=n&&s.animateScale?0:i.outerRadius,m=t._options||{};W.extend(t,{_datasetIndex:i.index,_index:e,_model:{backgroundColor:m.backgroundColor,borderColor:m.borderColor,borderWidth:m.borderWidth,borderAlign:m.borderAlign,x:l+a.offsetX,y:d+a.offsetY,startAngle:u,endAngle:h,circumference:f,outerRadius:p,innerRadius:g,label:W.valueAtIndexOrDefault(c.label,e,a.data.labels[e])}});var v=t._model;n&&s.animateRotate||(v.startAngle=0===e?o.rotation:i.getMeta().data[e-1]._model.endAngle,v.endAngle=v.startAngle+v.circumference),t.pivot()},calculateTotal:function(){var n,i=this.getDataset(),t=this.getMeta(),a=0;return W.each(t.data,function(t,e){n=i.data[e],isNaN(n)||t.hidden||(a+=Math.abs(n))}),a},calculateCircumference:function(t){var e=this.getMeta().total;return 0<e&&!isNaN(t)?Dt*(Math.abs(t)/e):0},getMaxBorderWidth:function(t){var e,n,i,a,r,o,s,l,d=0,u=this.chart;if(!t)for(e=0,n=u.data.datasets.length;e<n;++e)if(u.isDatasetVisible(e)){t=(i=u.getDatasetMeta(e)).data,e!==this.index&&(r=i.controller);break}if(!t)return 0;for(e=0,n=t.length;e<n;++e)a=t[e],"inner"!==(o=r?(r._configure(),r._resolveDataElementOptions(a,e)):a._options).borderAlign&&(s=o.borderWidth,d=(l=o.hoverBorderWidth)>(d=d<s?s:d)?l:d);return d},setHoverStyle:function(t){var e=t._model,n=t._options,i=W.getHoverColor;t.$previousStyle={backgroundColor:e.backgroundColor,borderColor:e.borderColor,borderWidth:e.borderWidth},e.backgroundColor=Pt(n.hoverBackgroundColor,i(n.backgroundColor)),e.borderColor=Pt(n.hoverBorderColor,i(n.borderColor)),e.borderWidth=Pt(n.hoverBorderWidth,n.borderWidth)},_getRingWeightOffset:function(t){for(var e=0,n=0;n<t;++n)this.chart.isDatasetVisible(n)&&(e+=this._getRingWeight(n));return e},_getRingWeight:function(t){return Math.max(Pt(this.chart.data.datasets[t].weight,1),0)},_getVisibleDatasetWeightTotal:function(){return this._getRingWeightOffset(this.chart.data.datasets.length)}});N._set("horizontalBar",{hover:{mode:"index",axis:"y"},scales:{xAxes:[{type:"linear",position:"bottom"}],yAxes:[{type:"category",position:"left",offset:!0,gridLines:{offsetGridLines:!0}}]},elements:{rectangle:{borderSkipped:"left"}},tooltips:{mode:"index",axis:"y"}}),N._set("global",{datasets:{horizontalBar:{categoryPercentage:.8,barPercentage:.9}}});var Ft=wt.extend({_getValueScaleId:function(){return this.getMeta().xAxisID},_getIndexScaleId:function(){return this.getMeta().yAxisID}}),Lt=W.valueOrDefault,Ot=W.options.resolve,Rt=W.canvas._isPointInArea;function zt(t,e){var n=t&&t.options.ticks||{},i=n.reverse,a=void 0===n.min?e:0,r=void 0===n.max?e:0;return{start:i?r:a,end:i?a:r}}N._set("line",{showLines:!0,spanGaps:!1,hover:{mode:"label"},scales:{xAxes:[{type:"category",id:"x-axis-0"}],yAxes:[{type:"linear",id:"y-axis-0"}]}});var Nt=tt.extend({datasetElementType:vt.Line,dataElementType:vt.Point,_datasetElementOptions:["backgroundColor","borderCapStyle","borderColor","borderDash","borderDashOffset","borderJoinStyle","borderWidth","cubicInterpolationMode","fill"],_dataElementOptions:{backgroundColor:"pointBackgroundColor",borderColor:"pointBorderColor",borderWidth:"pointBorderWidth",hitRadius:"pointHitRadius",hoverBackgroundColor:"pointHoverBackgroundColor",hoverBorderColor:"pointHoverBorderColor",hoverBorderWidth:"pointHoverBorderWidth",hoverRadius:"pointHoverRadius",pointStyle:"pointStyle",radius:"pointRadius",rotation:"pointRotation"},update:function(t){var e,n,i=this,a=i.getMeta(),r=a.dataset,o=a.data||[],s=i.chart.options,l=i._config,d=i._showLine=Lt(l.showLine,s.showLines);for(i._xScale=i.getScaleForId(a.xAxisID),i._yScale=i.getScaleForId(a.yAxisID),d&&(void 0!==l.tension&&void 0===l.lineTension&&(l.lineTension=l.tension),r._scale=i._yScale,r._datasetIndex=i.index,r._children=o,r._model=i._resolveDatasetElementOptions(r),r.pivot()),e=0,n=o.length;e<n;++e)i.updateElement(o[e],e,t);for(d&&0!==r._model.tension&&i.updateBezierControlPoints(),e=0,n=o.length;e<n;++e)o[e].pivot()},updateElement:function(t,e,n){var i=this,a=i.getMeta(),r=t.custom||{},o=i.getDataset(),s=i.index,l=o.data[e],d=i._xScale,u=i._yScale,h=a.dataset._model,c=i._resolveDataElementOptions(t,e),f=d.getPixelForValue("object"==typeof l?l:NaN,e,s),g=n?u.getBasePixel():i.calculatePointY(l,e,s);t._xScale=d,t._yScale=u,t._options=c,t._datasetIndex=s,t._index=e,t._model={x:f,y:g,skip:r.skip||isNaN(f)||isNaN(g),radius:c.radius,pointStyle:c.pointStyle,rotation:c.rotation,backgroundColor:c.backgroundColor,borderColor:c.borderColor,borderWidth:c.borderWidth,tension:Lt(r.tension,h?h.tension:0),steppedLine:!!h&&h.steppedLine,hitRadius:c.hitRadius}},_resolveDatasetElementOptions:function(t){var e,n,i,a,r,o,s,l,d,u,h,c=this,f=c._config,g=t.custom||{},p=c.chart.options,m=p.elements.line,v=tt.prototype._resolveDatasetElementOptions.apply(c,arguments);return v.spanGaps=Lt(f.spanGaps,p.spanGaps),v.tension=Lt(f.lineTension,m.tension),v.steppedLine=Ot([g.steppedLine,f.steppedLine,m.stepped]),v.clip=(e=Lt(f.clip,(o=c._xScale,s=c._yScale,l=v.borderWidth,u=zt(o,d=l/2),{top:(h=zt(s,d)).end,right:u.end,bottom:h.start,left:u.start})),W.isObject(e)?(n=e.top,i=e.right,a=e.bottom,r=e.left):n=i=a=r=e,{top:n,right:i,bottom:a,left:r}),v},calculatePointY:function(t,e,n){var i,a,r,o,s,l,d,u=this.chart,h=this._yScale,c=0,f=0;if(h.options.stacked){for(s=+h.getRightValue(t),d=(l=u._getSortedVisibleDatasetMetas()).length,i=0;i<d&&(r=l[i]).index!==n;++i)a=u.data.datasets[r.index],"line"===r.type&&r.yAxisID===h.id&&((o=+h.getRightValue(a.data[e]))<0?f+=o||0:c+=o||0);return s<0?h.getPixelForValue(f+s):h.getPixelForValue(c+s)}return h.getPixelForValue(t)},updateBezierControlPoints:function(){var t,e,n,i,a=this.chart,r=this.getMeta(),o=r.dataset._model,s=a.chartArea,l=r.data||[];function d(t,e,n){return Math.max(Math.min(t,n),e)}if(o.spanGaps&&(l=l.filter(function(t){return!t._model.skip})),"monotone"===o.cubicInterpolationMode)W.splineCurveMonotone(l);else for(t=0,e=l.length;t<e;++t)n=l[t]._model,i=W.splineCurve(W.previousItem(l,t)._model,n,W.nextItem(l,t)._model,o.tension),n.controlPointPreviousX=i.previous.x,n.controlPointPreviousY=i.previous.y,n.controlPointNextX=i.next.x,n.controlPointNextY=i.next.y;if(a.options.elements.line.capBezierPoints)for(t=0,e=l.length;t<e;++t)n=l[t]._model,Rt(n,s)&&(0<t&&Rt(l[t-1]._model,s)&&(n.controlPointPreviousX=d(n.controlPointPreviousX,s.left,s.right),n.controlPointPreviousY=d(n.controlPointPreviousY,s.top,s.bottom)),t<l.length-1&&Rt(l[t+1]._model,s)&&(n.controlPointNextX=d(n.controlPointNextX,s.left,s.right),n.controlPointNextY=d(n.controlPointNextY,s.top,s.bottom)))},draw:function(){var t,e=this.chart,n=this.getMeta(),i=n.data||[],a=e.chartArea,r=e.canvas,o=0,s=i.length;for(this._showLine&&(t=n.dataset._model.clip,W.canvas.clipArea(e.ctx,{left:!1===t.left?0:a.left-t.left,right:!1===t.right?r.width:a.right+t.right,top:!1===t.top?0:a.top-t.top,bottom:!1===t.bottom?r.height:a.bottom+t.bottom}),n.dataset.draw(),W.canvas.unclipArea(e.ctx));o<s;++o)i[o].draw(a)},setHoverStyle:function(t){var e=t._model,n=t._options,i=W.getHoverColor;t.$previousStyle={backgroundColor:e.backgroundColor,borderColor:e.borderColor,borderWidth:e.borderWidth,radius:e.radius},e.backgroundColor=Lt(n.hoverBackgroundColor,i(n.backgroundColor)),e.borderColor=Lt(n.hoverBorderColor,i(n.borderColor)),e.borderWidth=Lt(n.hoverBorderWidth,n.borderWidth),e.radius=Lt(n.hoverRadius,n.radius)}}),Bt=W.options.resolve;N._set("polarArea",{scale:{type:"radialLinear",angleLines:{display:!1},gridLines:{circular:!0},pointLabels:{display:!1},ticks:{beginAtZero:!0}},animation:{animateRotate:!0,animateScale:!0},startAngle:-.5*Math.PI,legendCallback:function(t){var e,n,i,a=document.createElement("ul"),r=t.data,o=r.datasets,s=r.labels;if(a.setAttribute("class",t.id+"-legend"),o.length)for(n=o[e=0].data.length;e<n;++e)(i=a.appendChild(document.createElement("li"))).appendChild(document.createElement("span")).style.backgroundColor=o[0].backgroundColor[e],s[e]&&i.appendChild(document.createTextNode(s[e]));return a.outerHTML},legend:{labels:{generateLabels:function(a){var r=a.data;return r.labels.length&&r.datasets.length?r.labels.map(function(t,e){var n=a.getDatasetMeta(0),i=n.controller.getStyle(e);return{text:t,fillStyle:i.backgroundColor,strokeStyle:i.borderColor,lineWidth:i.borderWidth,hidden:isNaN(r.datasets[0].data[e])||n.data[e].hidden,index:e}}):[]}},onClick:function(t,e){for(var n,i=e.index,a=this.chart,r=0,o=(a.data.datasets||[]).length;r<o;++r)(n=a.getDatasetMeta(r)).data[i].hidden=!n.data[i].hidden;a.update()}},tooltips:{callbacks:{title:function(){return""},label:function(t,e){return e.labels[t.index]+": "+t.yLabel}}}});var Et=tt.extend({dataElementType:vt.Arc,linkScales:W.noop,_dataElementOptions:["backgroundColor","borderColor","borderWidth","borderAlign","hoverBackgroundColor","hoverBorderColor","hoverBorderWidth"],_getIndexScaleId:function(){return this.chart.scale.id},_getValueScaleId:function(){return this.chart.scale.id},update:function(t){var e,n,i,a=this,r=a.getDataset(),o=a.getMeta(),s=a.chart.options.startAngle||0,l=a._starts=[],d=a._angles=[],u=o.data;for(a._updateRadius(),o.count=a.countVisibleElements(),e=0,n=r.data.length;e<n;e++)l[e]=s,i=a._computeAngle(e),s+=d[e]=i;for(e=0,n=u.length;e<n;++e)u[e]._options=a._resolveDataElementOptions(u[e],e),a.updateElement(u[e],e,t)},_updateRadius:function(){var t=this,e=t.chart,n=e.chartArea,i=e.options,a=Math.min(n.right-n.left,n.bottom-n.top);e.outerRadius=Math.max(a/2,0),e.innerRadius=Math.max(i.cutoutPercentage?e.outerRadius/100*i.cutoutPercentage:1,0),e.radiusLength=(e.outerRadius-e.innerRadius)/e.getVisibleDatasetCount(),t.outerRadius=e.outerRadius-e.radiusLength*t.index,t.innerRadius=t.outerRadius-e.radiusLength},updateElement:function(t,e,n){var i=this,a=i.chart,r=i.getDataset(),o=a.options,s=o.animation,l=a.scale,d=a.data.labels,u=l.xCenter,h=l.yCenter,c=o.startAngle,f=t.hidden?0:l.getDistanceFromCenterForValue(r.data[e]),g=i._starts[e],p=g+(t.hidden?0:i._angles[e]),m=s.animateScale?0:l.getDistanceFromCenterForValue(r.data[e]),v=t._options||{};W.extend(t,{_datasetIndex:i.index,_index:e,_scale:l,_model:{backgroundColor:v.backgroundColor,borderColor:v.borderColor,borderWidth:v.borderWidth,borderAlign:v.borderAlign,x:u,y:h,innerRadius:0,outerRadius:n?m:f,startAngle:n&&s.animateRotate?c:g,endAngle:n&&s.animateRotate?c:p,label:W.valueAtIndexOrDefault(d,e,d[e])}}),t.pivot()},countVisibleElements:function(){var n=this.getDataset(),t=this.getMeta(),i=0;return W.each(t.data,function(t,e){isNaN(n.data[e])||t.hidden||i++}),i},setHoverStyle:function(t){var e=t._model,n=t._options,i=W.getHoverColor,a=W.valueOrDefault;t.$previousStyle={backgroundColor:e.backgroundColor,borderColor:e.borderColor,borderWidth:e.borderWidth},e.backgroundColor=a(n.hoverBackgroundColor,i(n.backgroundColor)),e.borderColor=a(n.hoverBorderColor,i(n.borderColor)),e.borderWidth=a(n.hoverBorderWidth,n.borderWidth)},_computeAngle:function(t){var e=this,n=this.getMeta().count,i=e.getDataset(),a=e.getMeta();if(isNaN(i.data[t])||a.data[t].hidden)return 0;var r={chart:e.chart,dataIndex:t,dataset:i,datasetIndex:e.index};return Bt([e.chart.options.elements.arc.angle,2*Math.PI/n],r,t)}});N._set("pie",W.clone(N.doughnut)),N._set("pie",{cutoutPercentage:0});var Wt=It,Vt=W.valueOrDefault;N._set("radar",{spanGaps:!1,scale:{type:"radialLinear"},elements:{line:{fill:"start",tension:0}}});var Ht=tt.extend({datasetElementType:vt.Line,dataElementType:vt.Point,linkScales:W.noop,_datasetElementOptions:["backgroundColor","borderWidth","borderColor","borderCapStyle","borderDash","borderDashOffset","borderJoinStyle","fill"],_dataElementOptions:{backgroundColor:"pointBackgroundColor",borderColor:"pointBorderColor",borderWidth:"pointBorderWidth",hitRadius:"pointHitRadius",hoverBackgroundColor:"pointHoverBackgroundColor",hoverBorderColor:"pointHoverBorderColor",hoverBorderWidth:"pointHoverBorderWidth",hoverRadius:"pointHoverRadius",pointStyle:"pointStyle",radius:"pointRadius",rotation:"pointRotation"},_getIndexScaleId:function(){return this.chart.scale.id},_getValueScaleId:function(){return this.chart.scale.id},update:function(t){var e,n,i=this,a=i.getMeta(),r=a.dataset,o=a.data||[],s=i.chart.scale,l=i._config;for(void 0!==l.tension&&void 0===l.lineTension&&(l.lineTension=l.tension),r._scale=s,r._datasetIndex=i.index,r._children=o,r._loop=!0,r._model=i._resolveDatasetElementOptions(r),r.pivot(),e=0,n=o.length;e<n;++e)i.updateElement(o[e],e,t);for(i.updateBezierControlPoints(),e=0,n=o.length;e<n;++e)o[e].pivot()},updateElement:function(t,e,n){var i=this,a=t.custom||{},r=i.getDataset(),o=i.chart.scale,s=o.getPointPositionForValue(e,r.data[e]),l=i._resolveDataElementOptions(t,e),d=i.getMeta().dataset._model,u=n?o.xCenter:s.x,h=n?o.yCenter:s.y;t._scale=o,t._options=l,t._datasetIndex=i.index,t._index=e,t._model={x:u,y:h,skip:a.skip||isNaN(u)||isNaN(h),radius:l.radius,pointStyle:l.pointStyle,rotation:l.rotation,backgroundColor:l.backgroundColor,borderColor:l.borderColor,borderWidth:l.borderWidth,tension:Vt(a.tension,d?d.tension:0),hitRadius:l.hitRadius}},_resolveDatasetElementOptions:function(){var t=this._config,e=this.chart.options,n=tt.prototype._resolveDatasetElementOptions.apply(this,arguments);return n.spanGaps=Vt(t.spanGaps,e.spanGaps),n.tension=Vt(t.lineTension,e.elements.line.tension),n},updateBezierControlPoints:function(){var t,e,n,i,a=this.getMeta(),r=this.chart.chartArea,o=a.data||[];function s(t,e,n){return Math.max(Math.min(t,n),e)}for(a.dataset._model.spanGaps&&(o=o.filter(function(t){return!t._model.skip})),t=0,e=o.length;t<e;++t)n=o[t]._model,i=W.splineCurve(W.previousItem(o,t,!0)._model,n,W.nextItem(o,t,!0)._model,n.tension),n.controlPointPreviousX=s(i.previous.x,r.left,r.right),n.controlPointPreviousY=s(i.previous.y,r.top,r.bottom),n.controlPointNextX=s(i.next.x,r.left,r.right),n.controlPointNextY=s(i.next.y,r.top,r.bottom)},setHoverStyle:function(t){var e=t._model,n=t._options,i=W.getHoverColor;t.$previousStyle={backgroundColor:e.backgroundColor,borderColor:e.borderColor,borderWidth:e.borderWidth,radius:e.radius},e.backgroundColor=Vt(n.hoverBackgroundColor,i(n.backgroundColor)),e.borderColor=Vt(n.hoverBorderColor,i(n.borderColor)),e.borderWidth=Vt(n.hoverBorderWidth,n.borderWidth),e.radius=Vt(n.hoverRadius,n.radius)}});N._set("scatter",{hover:{mode:"single"},scales:{xAxes:[{id:"x-axis-1",type:"linear",position:"bottom"}],yAxes:[{id:"y-axis-1",type:"linear",position:"left"}]},tooltips:{callbacks:{title:function(){return""},label:function(t){return"("+t.xLabel+", "+t.yLabel+")"}}}}),N._set("global",{datasets:{scatter:{showLine:!1}}});var jt={bar:wt,bubble:Ct,doughnut:It,horizontalBar:Ft,line:Nt,polarArea:Et,pie:Wt,radar:Ht,scatter:Nt};function qt(t,e){return t.native?{x:t.x,y:t.y}:W.getRelativePosition(t,e)}function Ut(t,e){for(var n,i,a,r,o=t._getSortedVisibleDatasetMetas(),s=0,l=o.length;s<l;++s)for(i=0,a=(n=o[s].data).length;i<a;++i)(r=n[i])._view.skip||e(r)}function Yt(t,e){var n=[];return Ut(t,function(t){t.inRange(e.x,e.y)&&n.push(t)}),n}function Gt(t,i,a,r){var o=Number.POSITIVE_INFINITY,s=[];return Ut(t,function(t){var e,n;a&&!t.inRange(i.x,i.y)||(e=t.getCenterPoint(),(n=r(i,e))<o?(s=[t],o=n):n===o&&s.push(t))}),s}function Xt(t){var a=-1!==t.indexOf("x"),r=-1!==t.indexOf("y");return function(t,e){var n=a?Math.abs(t.x-e.x):0,i=r?Math.abs(t.y-e.y):0;return Math.sqrt(Math.pow(n,2)+Math.pow(i,2))}}function Kt(t,e,n){var i=qt(e,t);n.axis=n.axis||"x";var a=Xt(n.axis),r=n.intersect?Yt(t,i):Gt(t,i,!1,a),o=[];return r.length?(t._getSortedVisibleDatasetMetas().forEach(function(t){var e=t.data[r[0]._index];e&&!e._view.skip&&o.push(e)}),o):[]}var Zt={modes:{single:function(t,e){var n=qt(e,t),i=[];return Ut(t,function(t){return t.inRange(n.x,n.y)&&(i.push(t),i)}),i.slice(0,1)},label:Kt,index:Kt,dataset:function(t,e,n){var i=qt(e,t);n.axis=n.axis||"xy";var a=Xt(n.axis),r=n.intersect?Yt(t,i):Gt(t,i,!1,a);return 0<r.length&&(r=t.getDatasetMeta(r[0]._datasetIndex).data),r},"x-axis":function(t,e){return Kt(t,e,{intersect:!1})},point:function(t,e){return Yt(t,qt(e,t))},nearest:function(t,e,n){var i=qt(e,t);n.axis=n.axis||"xy";var a=Xt(n.axis);return Gt(t,i,n.intersect,a)},x:function(t,e,n){var i=qt(e,t),a=[],r=!1;return Ut(t,function(t){t.inXRange(i.x)&&a.push(t),t.inRange(i.x,i.y)&&(r=!0)}),n.intersect&&!r&&(a=[]),a},y:function(t,e,n){var i=qt(e,t),a=[],r=!1;return Ut(t,function(t){t.inYRange(i.y)&&a.push(t),t.inRange(i.x,i.y)&&(r=!0)}),n.intersect&&!r&&(a=[]),a}}},$t=W.extend;function Jt(t,e){return W.where(t,function(t){return t.pos===e})}function Qt(t,a){return t.sort(function(t,e){var n=a?e:t,i=a?t:e;return n.weight===i.weight?n.index-i.index:n.weight-i.weight})}function te(t,e,n,i){return Math.max(t[n],e[n])+Math.max(t[i],e[i])}function ee(t,e){var n,i=e.maxPadding;return n={left:0,top:0,right:0,bottom:0},(t?["left","right"]:["top","bottom"]).forEach(function(t){n[t]=Math.max(e[t],i[t])}),n}function ne(t,e,n){for(var i,a,r,o,s,l,d,u,h,c,f,g,p=[],m=0,v=t.length;m<v;++m)(a=(i=t[m]).box).update(i.width||e.w,i.height||e.h,ee(i.horizontal,e)),s=e,l=n,g=f=c=h=u=void 0,f=(d=i).box,g=s.maxPadding,d.size&&(s[d.pos]-=d.size),d.size=d.horizontal?f.height:f.width,s[d.pos]+=d.size,f.getPadding&&(c=f.getPadding(),g.top=Math.max(g.top,c.top),g.left=Math.max(g.left,c.left),g.bottom=Math.max(g.bottom,c.bottom),g.right=Math.max(g.right,c.right)),u=l.outerWidth-te(g,s,"left","right"),h=l.outerHeight-te(g,s,"top","bottom"),u===s.w&&h===s.h||(s.w=u,s.h=h,d.horizontal?u===s.w:h===s.h)||(o=!0,p.length&&(r=!0)),a.fullWidth||p.push(i);return r&&ne(p,e,n)||o}function ie(t,e,n){for(var i,a,r=n.padding,o=e.x,s=e.y,l=0,d=t.length;l<d;++l)a=(i=t[l]).box,i.horizontal?(a.left=a.fullWidth?r.left:e.left,a.right=a.fullWidth?n.outerWidth-r.right:e.left+e.w,a.top=s,a.bottom=s+a.height,a.width=a.right-a.left,s=a.bottom):(a.left=o,a.right=o+a.width,a.top=e.top,a.bottom=e.top+e.h,a.height=a.bottom-a.top,o=a.right);e.x=o,e.y=s}N._set("global",{layout:{padding:{top:0,right:0,bottom:0,left:0}}});var ae,re={defaults:{},addBox:function(t,e){t.boxes||(t.boxes=[]),e.fullWidth=e.fullWidth||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw:function(){e.draw.apply(e,arguments)}}]},t.boxes.push(e)},removeBox:function(t,e){var n=t.boxes?t.boxes.indexOf(e):-1;-1!==n&&t.boxes.splice(n,1)},configure:function(t,e,n){for(var i,a=["fullWidth","position","weight"],r=a.length,o=0;o<r;++o)i=a[o],n.hasOwnProperty(i)&&(e[i]=n[i])},update:function(n,t,e){var i,a,r,o,s,l,d,u,h,c,f,g,p,m,v,b;function x(t){var e=Math.max(f[t]-c[t],0);return c[t]+=e,e}n&&(i=n.options.layout||{},r=t-(a=W.options.toPadding(i.padding)).width,o=e-a.height,g=function(t){for(var e,n=[],i=0,a=(t||[]).length;i<a;++i)e=t[i],n.push({index:i,box:e,pos:e.position,horizontal:e.isHorizontal(),weight:e.weight});return n}(n.boxes),p=Qt(Jt(g,"left"),!0),m=Qt(Jt(g,"right")),v=Qt(Jt(g,"top"),!0),b=Qt(Jt(g,"bottom")),l=(s={leftAndTop:p.concat(v),rightAndBottom:m.concat(b),chartArea:Jt(g,"chartArea"),vertical:p.concat(m),horizontal:v.concat(b)}).vertical,d=s.horizontal,u=Object.freeze({outerWidth:t,outerHeight:e,padding:a,availableWidth:r,vBoxMaxWidth:r/2/l.length,hBoxMaxHeight:o/2}),h=$t({maxPadding:$t({},a),w:r,h:o,x:a.left,y:a.top},a),function(t,e){for(var n,i=0,a=t.length;i<a;++i)(n=t[i]).width=n.horizontal?n.box.fullWidth&&e.availableWidth:e.vBoxMaxWidth,n.height=n.horizontal&&e.hBoxMaxHeight}(l.concat(d),u),ne(l,h,u),ne(d,h,u)&&ne(l,h,u),f=(c=h).maxPadding,c.y+=x("top"),c.x+=x("left"),x("right"),x("bottom"),ie(s.leftAndTop,h,u),h.x+=h.w,h.y+=h.h,ie(s.rightAndBottom,h,u),n.chartArea={left:h.left,top:h.top,right:h.left+h.w,bottom:h.top+h.h},W.each(s.chartArea,function(t){var e=t.box;$t(e,n.chartArea),e.update(h.w,h.h)}))}},oe=(ae=Object.freeze({__proto__:null,default:"@keyframes chartjs-render-animation{from{opacity:.99}to{opacity:1}}.chartjs-render-monitor{animation:chartjs-render-animation 1ms}.chartjs-size-monitor,.chartjs-size-monitor-expand,.chartjs-size-monitor-shrink{position:absolute;direction:ltr;left:0;top:0;right:0;bottom:0;overflow:hidden;pointer-events:none;visibility:hidden;z-index:-1}.chartjs-size-monitor-expand>div{position:absolute;width:1000000px;height:1000000px;left:0;top:0}.chartjs-size-monitor-shrink>div{position:absolute;width:200%;height:200%;left:0;top:0}"}))&&ae.default||ae,se="$chartjs",le="chartjs-size-monitor",de="chartjs-render-monitor",ue=["animationstart","webkitAnimationStart"],he={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"};function ce(t,e){var n=W.getStyle(t,e),i=n&&n.match(/^(\d+)(\.\d+)?px$/);return i?Number(i[1]):void 0}var fe=!!function(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("e",null,e)}catch(t){}return t}()&&{passive:!0};function ge(t,e,n){t.addEventListener(e,n,fe)}function pe(t,e,n){t.removeEventListener(e,n,fe)}function me(t,e,n,i,a){return{type:t,chart:e,native:a||null,x:void 0!==n?n:null,y:void 0!==i?i:null}}function ve(t){var e=document.createElement("div");return e.className=t||"",e}function be(n,i,a){var t,e,r,o,s,l,d,u=n[se]||(n[se]={}),h=u.resizer=function(t){var e=ve(le),n=ve(le+"-expand"),i=ve(le+"-shrink");n.appendChild(ve()),i.appendChild(ve()),e.appendChild(n),e.appendChild(i),e._reset=function(){n.scrollLeft=1e6,n.scrollTop=1e6,i.scrollLeft=1e6,i.scrollTop=1e6};function a(){e._reset(),t()}return ge(n,"scroll",a.bind(n,"expand")),ge(i,"scroll",a.bind(i,"shrink")),e}((r=!(t=function(){var t,e;u.resizer&&(e=(t=a.options.maintainAspectRatio&&n.parentNode)?t.clientWidth:0,i(me("resize",a)),t&&t.clientWidth<e&&a.canvas&&i(me("resize",a)))}),function(){o=Array.prototype.slice.call(arguments),e=e||this,r||(r=!0,W.requestAnimFrame.call(window,function(){r=!1,t.apply(e,o)}))}));l=(s=n)[se]||(s[se]={}),d=l.renderProxy=function(t){"chartjs-render-animation"===t.animationName&&function(){if(u.resizer){var t=n.parentNode;t&&t!==h.parentNode&&t.insertBefore(h,t.firstChild),h._reset()}}()},W.each(ue,function(t){ge(s,t,d)}),l.reflow=!!s.offsetParent,s.classList.add(de)}var xe={disableCSSInjection:!1,_enabled:"undefined"!=typeof window&&"undefined"!=typeof document,_ensureLoaded:function(t){var e,n,i,a,r;this.disableCSSInjection||(e=t.getRootNode?t.getRootNode():document,n=e.host?e:document.head,i=oe,(r=n[se]||(n[se]={})).containsStyles||(r.containsStyles=!0,i="/* Chart.js */\n"+i,(a=document.createElement("style")).setAttribute("type","text/css"),a.appendChild(document.createTextNode(i)),n.appendChild(a)))},acquireContext:function(t,e){"string"==typeof t?t=document.getElementById(t):t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas);var n,i,a,r,o,s,l,d=t&&t.getContext&&t.getContext("2d");return d&&d.canvas===t?(this._ensureLoaded(t),i=e,o=(n=t).style,s=n.getAttribute("height"),l=n.getAttribute("width"),n[se]={initial:{height:s,width:l,style:{display:o.display,height:o.height,width:o.width}}},o.display=o.display||"block",null!==l&&""!==l||void 0!==(a=ce(n,"width"))&&(n.width=a),null!==s&&""!==s||(""===n.style.height?n.height=n.width/(i.options.aspectRatio||2):(r=ce(n,"height"),void 0!==a&&(n.height=r))),d):null},releaseContext:function(t){var n,i=t.canvas;i[se]&&(n=i[se].initial,["height","width"].forEach(function(t){var e=n[t];W.isNullOrUndef(e)?i.removeAttribute(t):i.setAttribute(t,e)}),W.each(n.style||{},function(t,e){i.style[e]=t}),i.width=i.width,delete i[se])},addEventListener:function(r,t,o){var e,n=r.canvas;"resize"!==t?ge(n,t,((e=o[se]||(o[se]={})).proxies||(e.proxies={}))[r.id+"_"+t]=function(t){var e,n,i,a;o((n=r,i=he[(e=t).type]||e.type,a=W.getRelativePosition(e,n),me(i,n,a.x,a.y,e)))}):be(n,o,r)},removeEventListener:function(t,e,n){var i,a,r,o,s,l,d,u=t.canvas;"resize"!==e?(i=((n[se]||{}).proxies||{})[t.id+"_"+e])&&pe(u,e,i):(l=(a=u)[se]||{},d=l.resizer,delete l.resizer,o=(r=a)[se]||{},(s=o.renderProxy)&&(W.each(ue,function(t){pe(r,t,s)}),delete o.renderProxy),r.classList.remove(de),d&&d.parentNode&&d.parentNode.removeChild(d))}};W.addEvent=ge,W.removeEvent=pe;var ye=xe._enabled?xe:{acquireContext:function(t){return t&&t.canvas&&(t=t.canvas),t&&t.getContext("2d")||null}},_e=W.extend({initialize:function(){},acquireContext:function(){},releaseContext:function(){},addEventListener:function(){},removeEventListener:function(){}},ye);N._set("global",{plugins:{}});var ke={_plugins:[],_cacheId:0,register:function(t){var e=this._plugins;[].concat(t).forEach(function(t){-1===e.indexOf(t)&&e.push(t)}),this._cacheId++},unregister:function(t){var n=this._plugins;[].concat(t).forEach(function(t){var e=n.indexOf(t);-1!==e&&n.splice(e,1)}),this._cacheId++},clear:function(){this._plugins=[],this._cacheId++},count:function(){return this._plugins.length},getAll:function(){return this._plugins},notify:function(t,e,n){for(var i,a,r,o,s=this.descriptors(t),l=s.length,d=0;d<l;++d)if("function"==typeof(o=(a=(i=s[d]).plugin)[e])&&((r=[t].concat(n||[])).push(i.options),!1===o.apply(a,r)))return!1;return!0},descriptors:function(t){var e=t.$plugins||(t.$plugins={});if(e.id===this._cacheId)return e.descriptors;var i=[],a=[],n=t&&t.config||{},r=n.options&&n.options.plugins||{};return this._plugins.concat(n.plugins||[]).forEach(function(t){var e,n;-1===i.indexOf(t)&&(e=t.id,!1!==(n=r[e])&&(!0===n&&(n=W.clone(N.global.plugins[e])),i.push(t),a.push({plugin:t,options:n||{}})))}),e.descriptors=a,e.id=this._cacheId,a},_invalidate:function(t){delete t.$plugins}},we={constructors:{},defaults:{},registerScaleType:function(t,e,n){this.constructors[t]=e,this.defaults[t]=W.clone(n)},getScaleConstructor:function(t){return this.constructors.hasOwnProperty(t)?this.constructors[t]:void 0},getScaleDefaults:function(t){return this.defaults.hasOwnProperty(t)?W.merge({},[N.scale,this.defaults[t]]):{}},updateScaleDefaults:function(t,e){this.defaults.hasOwnProperty(t)&&(this.defaults[t]=W.extend(this.defaults[t],e))},addScalesToLayout:function(e){W.each(e.scales,function(t){t.fullWidth=t.options.fullWidth,t.position=t.options.position,t.weight=t.options.weight,re.addBox(e,t)})}},Me=W.valueOrDefault,Se=W.rtl.getRtlAdapter;N._set("global",{tooltips:{enabled:!0,custom:null,mode:"nearest",position:"average",intersect:!0,backgroundColor:"rgba(0,0,0,0.8)",titleFontStyle:"bold",titleSpacing:2,titleMarginBottom:6,titleFontColor:"#fff",titleAlign:"left",bodySpacing:2,bodyFontColor:"#fff",bodyAlign:"left",footerFontStyle:"bold",footerSpacing:2,footerMarginTop:6,footerFontColor:"#fff",footerAlign:"left",yPadding:6,xPadding:6,caretPadding:2,caretSize:5,cornerRadius:6,multiKeyBackground:"#fff",displayColors:!0,borderColor:"rgba(0,0,0,0)",borderWidth:0,callbacks:{beforeTitle:W.noop,title:function(t,e){var n,i="",a=e.labels,r=a?a.length:0;return 0<t.length&&((n=t[0]).label?i=n.label:n.xLabel?i=n.xLabel:0<r&&n.index<r&&(i=a[n.index])),i},afterTitle:W.noop,beforeBody:W.noop,beforeLabel:W.noop,label:function(t,e){var n=e.datasets[t.datasetIndex].label||"";return n&&(n+=": "),W.isNullOrUndef(t.value)?n+=t.yLabel:n+=t.value,n},labelColor:function(t,e){var n=e.getDatasetMeta(t.datasetIndex).data[t.index]._view;return{borderColor:n.borderColor,backgroundColor:n.backgroundColor}},labelTextColor:function(){return this._options.bodyFontColor},afterLabel:W.noop,afterBody:W.noop,beforeFooter:W.noop,footer:W.noop,afterFooter:W.noop}}});var Ce={average:function(t){if(!t.length)return!1;for(var e=0,n=0,i=0,a=0,r=t.length;a<r;++a){var o,s=t[a];s&&s.hasValue()&&(e+=(o=s.tooltipPosition()).x,n+=o.y,++i)}return{x:e/i,y:n/i}},nearest:function(t,e){for(var n,i,a=e.x,r=e.y,o=Number.POSITIVE_INFINITY,s=0,l=t.length;s<l;++s){var d,u,h=t[s];h&&h.hasValue()&&(d=h.getCenterPoint(),(u=W.distanceBetweenPoints(e,d))<o&&(o=u,n=h))}return n&&(a=(i=n.tooltipPosition()).x,r=i.y),{x:a,y:r}}};function Pe(t,e){return e&&(W.isArray(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function Ae(t){return("string"==typeof t||t instanceof String)&&-1<t.indexOf("\n")?t.split("\n"):t}function De(t){var e=N.global;return{xPadding:t.xPadding,yPadding:t.yPadding,xAlign:t.xAlign,yAlign:t.yAlign,rtl:t.rtl,textDirection:t.textDirection,bodyFontColor:t.bodyFontColor,_bodyFontFamily:Me(t.bodyFontFamily,e.defaultFontFamily),_bodyFontStyle:Me(t.bodyFontStyle,e.defaultFontStyle),_bodyAlign:t.bodyAlign,bodyFontSize:Me(t.bodyFontSize,e.defaultFontSize),bodySpacing:t.bodySpacing,titleFontColor:t.titleFontColor,_titleFontFamily:Me(t.titleFontFamily,e.defaultFontFamily),_titleFontStyle:Me(t.titleFontStyle,e.defaultFontStyle),titleFontSize:Me(t.titleFontSize,e.defaultFontSize),_titleAlign:t.titleAlign,titleSpacing:t.titleSpacing,titleMarginBottom:t.titleMarginBottom,footerFontColor:t.footerFontColor,_footerFontFamily:Me(t.footerFontFamily,e.defaultFontFamily),_footerFontStyle:Me(t.footerFontStyle,e.defaultFontStyle),footerFontSize:Me(t.footerFontSize,e.defaultFontSize),_footerAlign:t.footerAlign,footerSpacing:t.footerSpacing,footerMarginTop:t.footerMarginTop,caretSize:t.caretSize,cornerRadius:t.cornerRadius,backgroundColor:t.backgroundColor,opacity:0,legendColorBackground:t.multiKeyBackground,displayColors:t.displayColors,borderColor:t.borderColor,borderWidth:t.borderWidth}}function Te(t,e){return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-t.xPadding:t.x+t.xPadding}function Ie(t){return Pe([],Ae(t))}var Fe=Y.extend({initialize:function(){this._model=De(this._options),this._lastActive=[]},getTitle:function(){var t=this._options.callbacks,e=t.beforeTitle.apply(this,arguments),n=t.title.apply(this,arguments),i=t.afterTitle.apply(this,arguments),a=Pe(a=[],Ae(e));return a=Pe(a,Ae(n)),Pe(a,Ae(i))},getBeforeBody:function(){return Ie(this._options.callbacks.beforeBody.apply(this,arguments))},getBody:function(t,n){var i=this,a=i._options.callbacks,r=[];return W.each(t,function(t){var e={before:[],lines:[],after:[]};Pe(e.before,Ae(a.beforeLabel.call(i,t,n))),Pe(e.lines,a.label.call(i,t,n)),Pe(e.after,Ae(a.afterLabel.call(i,t,n))),r.push(e)}),r},getAfterBody:function(){return Ie(this._options.callbacks.afterBody.apply(this,arguments))},getFooter:function(){var t=this._options.callbacks,e=t.beforeFooter.apply(this,arguments),n=t.footer.apply(this,arguments),i=t.afterFooter.apply(this,arguments),a=Pe(a=[],Ae(e));return a=Pe(a,Ae(n)),Pe(a,Ae(i))},update:function(t){var e,n,i,a,r,o,s,l,d,u,h,c,f,g,p,m,v,b,x,y,_,k=this,w=k._options,M=k._model,S=k._model=De(w),C=k._active,P=k._data,A={xAlign:M.xAlign,yAlign:M.yAlign},D={x:M.x,y:M.y},T={width:M.width,height:M.height},I={x:M.caretX,y:M.caretY};if(C.length){S.opacity=1;for(var F=[],L=[],I=Ce[w.position].call(k,C,k._eventPosition),O=[],R=0,z=C.length;R<z;++R)O.push((l=s=o=i=n=void 0,n=(e=C[R])._xScale,i=e._yScale||e._scale,a=e._index,r=e._datasetIndex,s=(o=e._chart.getDatasetMeta(r).controller)._getIndexScale(),l=o._getValueScale(),{xLabel:n?n.getLabelForIndex(a,r):"",yLabel:i?i.getLabelForIndex(a,r):"",label:s?""+s.getLabelForIndex(a,r):"",value:l?""+l.getLabelForIndex(a,r):"",index:a,datasetIndex:r,x:e._model.x,y:e._model.y}));w.filter&&(O=O.filter(function(t){return w.filter(t,P)})),w.itemSort&&(O=O.sort(function(t,e){return w.itemSort(t,e,P)})),W.each(O,function(t){F.push(w.callbacks.labelColor.call(k,t,k._chart)),L.push(w.callbacks.labelTextColor.call(k,t,k._chart))}),S.title=k.getTitle(O,P),S.beforeBody=k.getBeforeBody(O,P),S.body=k.getBody(O,P),S.afterBody=k.getAfterBody(O,P),S.footer=k.getFooter(O,P),S.x=I.x,S.y=I.y,S.caretPadding=w.caretPadding,S.labelColors=F,S.labelTextColors=L,S.dataPoints=O,T=function(t,e){var n=t._chart.ctx,i=2*e.yPadding,a=0,r=e.body,o=r.reduce(function(t,e){return t+e.before.length+e.lines.length+e.after.length},0);o+=e.beforeBody.length+e.afterBody.length;var s=e.title.length,l=e.footer.length,d=e.titleFontSize,u=e.bodyFontSize,h=e.footerFontSize;i+=s*d,i+=s?(s-1)*e.titleSpacing:0,i+=s?e.titleMarginBottom:0,i+=o*u,i+=o?(o-1)*e.bodySpacing:0,i+=l?e.footerMarginTop:0,i+=l*h,i+=l?(l-1)*e.footerSpacing:0;function c(t){a=Math.max(a,n.measureText(t).width+f)}var f=0;return n.font=W.fontString(d,e._titleFontStyle,e._titleFontFamily),W.each(e.title,c),n.font=W.fontString(u,e._bodyFontStyle,e._bodyFontFamily),W.each(e.beforeBody.concat(e.afterBody),c),f=e.displayColors?u+2:0,W.each(r,function(t){W.each(t.before,c),W.each(t.lines,c),W.each(t.after,c)}),f=0,n.font=W.fontString(h,e._footerFontStyle,e._footerFontFamily),W.each(e.footer,c),{width:a+=2*e.xPadding,height:i}}(this,S),d=S,h=A=function(t,e){var n,i=t._model,a=t._chart,r=t._chart.chartArea,o="center",s="center";i.y<e.height?s="top":i.y>a.height-e.height&&(s="bottom");var l=(r.left+r.right)/2,d=(r.top+r.bottom)/2,u="center"===s?(n=function(t){return t<=l},function(t){return l<t}):(n=function(t){return t<=e.width/2},function(t){return t>=a.width-e.width/2}),h=function(t){return t+e.width+i.caretSize+i.caretPadding>a.width},c=function(t){return t-e.width-i.caretSize-i.caretPadding<0},f=function(t){return t<=d?"top":"bottom"};n(i.x)?(o="left",h(i.x)&&(o="center",s=f(i.y))):u(i.x)&&(o="right",c(i.x)&&(o="center",s=f(i.y)));var g=t._options;return{xAlign:g.xAlign?g.xAlign:o,yAlign:g.yAlign?g.yAlign:s}}(this,u=T),c=k._chart,f=d.x,g=d.y,p=d.caretSize,m=d.caretPadding,v=d.cornerRadius,b=h.xAlign,x=h.yAlign,y=p+m,_=v+m,"right"===b?f-=u.width:"center"===b&&((f-=u.width/2)+u.width>c.width&&(f=c.width-u.width),f<0&&(f=0)),"top"===x?g+=y:g-="bottom"===x?u.height+y:u.height/2,"center"===x?"left"===b?f+=y:"right"===b&&(f-=y):"left"===b?f-=_:"right"===b&&(f+=_),D={x:f,y:g}}else S.opacity=0;return S.xAlign=A.xAlign,S.yAlign=A.yAlign,S.x=D.x,S.y=D.y,S.width=T.width,S.height=T.height,S.caretX=I.x,S.caretY=I.y,k._model=S,t&&w.custom&&w.custom.call(k,S),k},drawCaret:function(t,e){var n=this._chart.ctx,i=this._view,a=this.getCaretPosition(t,e,i);n.lineTo(a.x1,a.y1),n.lineTo(a.x2,a.y2),n.lineTo(a.x3,a.y3)},getCaretPosition:function(t,e,n){var i,a,r,o,s,l,d,u=n.caretSize,h=n.cornerRadius,c=n.xAlign,f=n.yAlign,g=t.x,p=t.y,m=e.width,v=e.height;return"center"===f?(r=p+v/2,o="left"===c?(i=(d=g)-u,l=d,a=r+u,r-u):(i=(d=g+m)+u,l=d,a=r-u,r+u)):(l=(d="left"===c?(i=g+h+u)-u:"right"===c?(i=g+m-h-u)-u:(i=n.caretX)-u,i+u),"top"===f?(r=(a=p)-u,o=a):(r=(a=p+v)+u,o=a,s=l,l=d,d=s)),{x1:d,x2:i,x3:l,y1:a,y2:r,y3:o}},drawTitle:function(t,e,n){var i,a,r,o=e.title,s=o.length;if(s){var l=Se(e.rtl,e.x,e.width);for(t.x=Te(e,e._titleAlign),n.textAlign=l.textAlign(e._titleAlign),n.textBaseline="middle",i=e.titleFontSize,a=e.titleSpacing,n.fillStyle=e.titleFontColor,n.font=W.fontString(i,e._titleFontStyle,e._titleFontFamily),r=0;r<s;++r)n.fillText(o[r],l.x(t.x),t.y+i/2),t.y+=i+a,r+1===s&&(t.y+=e.titleMarginBottom-a)}},drawBody:function(e,t,n){function i(t){n.fillText(t,y.x(e.x+b),e.y+f/2),e.y+=f+g}var a,r,o,s,l,d,u,h,c,f=t.bodyFontSize,g=t.bodySpacing,p=t._bodyAlign,m=t.body,v=t.displayColors,b=0,x=v?Te(t,"left"):0,y=Se(t.rtl,t.x,t.width),_=y.textAlign(p);for(n.textAlign=p,n.textBaseline="middle",n.font=W.fontString(f,t._bodyFontStyle,t._bodyFontFamily),e.x=Te(t,_),n.fillStyle=t.bodyFontColor,W.each(t.beforeBody,i),b=v&&"right"!==_?"center"===p?f/2+1:f+2:0,l=0,u=m.length;l<u;++l){for(a=m[l],r=t.labelTextColors[l],o=t.labelColors[l],n.fillStyle=r,W.each(a.before,i),d=0,h=(s=a.lines).length;d<h;++d){v&&(c=y.x(x),n.fillStyle=t.legendColorBackground,n.fillRect(y.leftForLtr(c,f),e.y,f,f),n.lineWidth=1,n.strokeStyle=o.borderColor,n.strokeRect(y.leftForLtr(c,f),e.y,f,f),n.fillStyle=o.backgroundColor,n.fillRect(y.leftForLtr(y.xPlus(c,1),f-2),e.y+1,f-2,f-2),n.fillStyle=r),i(s[d])}W.each(a.after,i)}b=0,W.each(t.afterBody,i),e.y-=g},drawFooter:function(t,e,n){var i,a,r=e.footer,o=r.length;if(o){var s=Se(e.rtl,e.x,e.width);for(t.x=Te(e,e._footerAlign),t.y+=e.footerMarginTop,n.textAlign=s.textAlign(e._footerAlign),n.textBaseline="middle",i=e.footerFontSize,n.fillStyle=e.footerFontColor,n.font=W.fontString(i,e._footerFontStyle,e._footerFontFamily),a=0;a<o;++a)n.fillText(r[a],s.x(t.x),t.y+i/2),t.y+=i+e.footerSpacing}},drawBackground:function(t,e,n,i){n.fillStyle=e.backgroundColor,n.strokeStyle=e.borderColor,n.lineWidth=e.borderWidth;var a=e.xAlign,r=e.yAlign,o=t.x,s=t.y,l=i.width,d=i.height,u=e.cornerRadius;n.beginPath(),n.moveTo(o+u,s),"top"===r&&this.drawCaret(t,i),n.lineTo(o+l-u,s),n.quadraticCurveTo(o+l,s,o+l,s+u),"center"===r&&"right"===a&&this.drawCaret(t,i),n.lineTo(o+l,s+d-u),n.quadraticCurveTo(o+l,s+d,o+l-u,s+d),"bottom"===r&&this.drawCaret(t,i),n.lineTo(o+u,s+d),n.quadraticCurveTo(o,s+d,o,s+d-u),"center"===r&&"left"===a&&this.drawCaret(t,i),n.lineTo(o,s+u),n.quadraticCurveTo(o,s,o+u,s),n.closePath(),n.fill(),0<e.borderWidth&&n.stroke()},draw:function(){var t,e,n,i,a=this._chart.ctx,r=this._view;0!==r.opacity&&(t={width:r.width,height:r.height},e={x:r.x,y:r.y},n=Math.abs(r.opacity<.001)?0:r.opacity,i=r.title.length||r.beforeBody.length||r.body.length||r.afterBody.length||r.footer.length,this._options.enabled&&i&&(a.save(),a.globalAlpha=n,this.drawBackground(e,r,a,t),e.y+=r.yPadding,W.rtl.overrideTextDirection(a,r.textDirection),this.drawTitle(e,r,a),this.drawBody(e,r,a),this.drawFooter(e,r,a),W.rtl.restoreTextDirection(a,r.textDirection),a.restore()))},handleEvent:function(t){var e,n=this,i=n._options;return n._lastActive=n._lastActive||[],"mouseout"===t.type?n._active=[]:(n._active=n._chart.getElementsAtEventForMode(t,i.mode,i),i.reverse&&n._active.reverse()),(e=!W.arrayEquals(n._active,n._lastActive))&&(n._lastActive=n._active,(i.enabled||i.custom)&&(n._eventPosition={x:t.x,y:t.y},n.update(!0),n.pivot())),e}});Fe.positioners=Ce;var Le=W.valueOrDefault;function Oe(){return W.merge({},[].slice.call(arguments),{merger:function(t,e,n,i){if("xAxes"===t||"yAxes"===t){var a,r,o,s=n[t].length;for(e[t]||(e[t]=[]),a=0;a<s;++a)o=n[t][a],r=Le(o.type,"xAxes"===t?"category":"linear"),a>=e[t].length&&e[t].push({}),!e[t][a].type||o.type&&o.type!==e[t][a].type?W.merge(e[t][a],[we.getScaleDefaults(r),o]):W.merge(e[t][a],o)}else W._merger(t,e,n,i)}})}function Re(){return W.merge({},[].slice.call(arguments),{merger:function(t,e,n,i){var a=e[t]||{},r=n[t];"scales"===t?e[t]=Oe(a,r):"scale"===t?e[t]=W.merge(a,[we.getScaleDefaults(r.type),r]):W._merger(t,e,n,i)}})}function ze(t,e,n){for(var i,a=function(t){return t.id===i};i=e+n++,0<=W.findIndex(t,a););return i}function Ne(t){return"top"===t||"bottom"===t}function Be(n,i){return function(t,e){return t[n]===e[n]?t[i]-e[i]:t[n]-e[n]}}N._set("global",{elements:{},events:["mousemove","mouseout","click","touchstart","touchmove"],hover:{onHover:null,mode:"nearest",intersect:!0,animationDuration:400},onClick:null,maintainAspectRatio:!0,responsive:!0,responsiveAnimationDuration:0});function Ee(t,e){return this.construct(t,e),this}W.extend(Ee.prototype,{construct:function(t,e){var n,i,a=this;(i=(n=(n=e)||{}).data=n.data||{}).datasets=i.datasets||[],i.labels=i.labels||[],n.options=Re(N.global,N[n.type],n.options||{}),e=n;var r=_e.acquireContext(t,e),o=r&&r.canvas,s=o&&o.height,l=o&&o.width;a.id=W.uid(),a.ctx=r,a.canvas=o,a.config=e,a.width=l,a.height=s,a.aspectRatio=s?l/s:null,a.options=e.options,a._bufferedRender=!1,a._layers=[],(a.chart=a).controller=a,Ee.instances[a.id]=a,Object.defineProperty(a,"data",{get:function(){return a.config.data},set:function(t){a.config.data=t}}),r&&o?(a.initialize(),a.update()):console.error("Failed to create chart: can't acquire context from the given item")},initialize:function(){var t=this;return ke.notify(t,"beforeInit"),W.retinaScale(t,t.options.devicePixelRatio),t.bindEvents(),t.options.responsive&&t.resize(!0),t.initToolTip(),ke.notify(t,"afterInit"),t},clear:function(){return W.canvas.clear(this),this},stop:function(){return K.cancelAnimation(this),this},resize:function(t){var e,n=this,i=n.options,a=n.canvas,r=i.maintainAspectRatio&&n.aspectRatio||null,o=Math.max(0,Math.floor(W.getMaximumWidth(a))),s=Math.max(0,Math.floor(r?o/r:W.getMaximumHeight(a)));n.width===o&&n.height===s||(a.width=n.width=o,a.height=n.height=s,a.style.width=o+"px",a.style.height=s+"px",W.retinaScale(n,i.devicePixelRatio),t)||(e={width:o,height:s},ke.notify(n,"resize",[e]),i.onResize&&i.onResize(n,e),n.stop(),n.update({duration:i.responsiveAnimationDuration}))},ensureScalesHaveIDs:function(){var t=this.options,n=t.scales||{},e=t.scale;W.each(n.xAxes,function(t,e){t.id||(t.id=ze(n.xAxes,"x-axis-",e))}),W.each(n.yAxes,function(t,e){t.id||(t.id=ze(n.yAxes,"y-axis-",e))}),e&&(e.id=e.id||"scale")},buildOrUpdateScales:function(){var o=this,t=o.options,s=o.scales||{},e=[],l=Object.keys(s).reduce(function(t,e){return t[e]=!1,t},{});t.scales&&(e=e.concat((t.scales.xAxes||[]).map(function(t){return{options:t,dtype:"category",dposition:"bottom"}}),(t.scales.yAxes||[]).map(function(t){return{options:t,dtype:"linear",dposition:"left"}}))),t.scale&&e.push({options:t.scale,dtype:"radialLinear",isDefault:!0,dposition:"chartArea"}),W.each(e,function(t){var e=t.options,n=e.id,i=Le(e.type,t.dtype);Ne(e.position)!==Ne(t.dposition)&&(e.position=t.dposition),l[n]=!0;var a=null;if(n in s&&s[n].type===i)(a=s[n]).options=e,a.ctx=o.ctx,a.chart=o;else{var r=we.getScaleConstructor(i);if(!r)return;a=new r({id:n,type:i,options:e,ctx:o.ctx,chart:o}),s[a.id]=a}a.mergeTicksOptions(),t.isDefault&&(o.scale=a)}),W.each(l,function(t,e){t||delete s[e]}),o.scales=s,we.addScalesToLayout(this)},buildOrUpdateControllers:function(){for(var t=this,e=[],n=t.data.datasets,i=0,a=n.length;i<a;i++){var r=n[i],o=t.getDatasetMeta(i),s=r.type||t.config.type;if(o.type&&o.type!==s&&(t.destroyDatasetMeta(i),o=t.getDatasetMeta(i)),o.type=s,o.order=r.order||0,o.index=i,o.controller)o.controller.updateIndex(i),o.controller.linkScales();else{var l=jt[o.type];if(void 0===l)throw new Error('"'+o.type+'" is not a chart type.');o.controller=new l(t,i),e.push(o.controller)}}return e},resetElements:function(){var n=this;W.each(n.data.datasets,function(t,e){n.getDatasetMeta(e).controller.reset()},n)},reset:function(){this.resetElements(),this.tooltip.initialize()},update:function(t){var e,n,i=this;if(t&&"object"==typeof t||(t={duration:t,lazy:arguments[1]}),n=(e=i).options,W.each(e.scales,function(t){re.removeBox(e,t)}),n=Re(N.global,N[e.config.type],n),e.options=e.config.options=n,e.ensureScalesHaveIDs(),e.buildOrUpdateScales(),e.tooltip._options=n.tooltips,e.tooltip.initialize(),ke._invalidate(i),!1!==ke.notify(i,"beforeUpdate")){i.tooltip._data=i.data;for(var a=i.buildOrUpdateControllers(),r=0,o=i.data.datasets.length;r<o;r++)i.getDatasetMeta(r).controller.buildOrUpdateElements();i.updateLayout(),i.options.animation&&i.options.animation.duration&&W.each(a,function(t){t.reset()}),i.updateDatasets(),i.tooltip.initialize(),i.lastActive=[],ke.notify(i,"afterUpdate"),i._layers.sort(Be("z","_idx")),i._bufferedRender?i._bufferedRequest={duration:t.duration,easing:t.easing,lazy:t.lazy}:i.render(t)}},updateLayout:function(){var e=this;!1!==ke.notify(e,"beforeLayout")&&(re.update(this,this.width,this.height),e._layers=[],W.each(e.boxes,function(t){t._configure&&t._configure(),e._layers.push.apply(e._layers,t._layers())},e),e._layers.forEach(function(t,e){t._idx=e}),ke.notify(e,"afterScaleUpdate"),ke.notify(e,"afterLayout"))},updateDatasets:function(){if(!1!==ke.notify(this,"beforeDatasetsUpdate")){for(var t=0,e=this.data.datasets.length;t<e;++t)this.updateDataset(t);ke.notify(this,"afterDatasetsUpdate")}},updateDataset:function(t){var e=this.getDatasetMeta(t),n={meta:e,index:t};!1!==ke.notify(this,"beforeDatasetUpdate",[n])&&(e.controller._update(),ke.notify(this,"afterDatasetUpdate",[n]))},render:function(t){var e=this;t&&"object"==typeof t||(t={duration:t,lazy:arguments[1]});var n=e.options.animation,i=Le(t.duration,n&&n.duration),a=t.lazy;if(!1!==ke.notify(e,"beforeRender")){var r,o=function(t){ke.notify(e,"afterRender"),W.callback(n&&n.onComplete,[t],e)};return n&&i?(r=new X({numSteps:i/16.66,easing:t.easing||n.easing,render:function(t,e){var n=W.easing.effects[e.easing],i=e.currentStep,a=i/e.numSteps;t.draw(n(a),a,i)},onAnimationProgress:n.onProgress,onAnimationComplete:o}),K.addAnimation(e,r,i,a)):(e.draw(),o(new X({numSteps:0,chart:e}))),e}},draw:function(t){var e,n,i=this;if(i.clear(),W.isNullOrUndef(t)&&(t=1),i.transition(t),!(i.width<=0||i.height<=0)&&!1!==ke.notify(i,"beforeDraw",[t])){for(n=i._layers,e=0;e<n.length&&n[e].z<=0;++e)n[e].draw(i.chartArea);for(i.drawDatasets(t);e<n.length;++e)n[e].draw(i.chartArea);i._drawTooltip(t),ke.notify(i,"afterDraw",[t])}},transition:function(t){for(var e=0,n=(this.data.datasets||[]).length;e<n;++e)this.isDatasetVisible(e)&&this.getDatasetMeta(e).controller.transition(t);this.tooltip.transition(t)},_getSortedDatasetMetas:function(t){for(var e=[],n=0,i=(this.data.datasets||[]).length;n<i;++n)t&&!this.isDatasetVisible(n)||e.push(this.getDatasetMeta(n));return e.sort(Be("order","index")),e},_getSortedVisibleDatasetMetas:function(){return this._getSortedDatasetMetas(!0)},drawDatasets:function(t){var e,n;if(!1!==ke.notify(this,"beforeDatasetsDraw",[t])){for(n=(e=this._getSortedVisibleDatasetMetas()).length-1;0<=n;--n)this.drawDataset(e[n],t);ke.notify(this,"afterDatasetsDraw",[t])}},drawDataset:function(t,e){var n={meta:t,index:t.index,easingValue:e};!1!==ke.notify(this,"beforeDatasetDraw",[n])&&(t.controller.draw(e),ke.notify(this,"afterDatasetDraw",[n]))},_drawTooltip:function(t){var e=this.tooltip,n={tooltip:e,easingValue:t};!1!==ke.notify(this,"beforeTooltipDraw",[n])&&(e.draw(),ke.notify(this,"afterTooltipDraw",[n]))},getElementAtEvent:function(t){return Zt.modes.single(this,t)},getElementsAtEvent:function(t){return Zt.modes.label(this,t,{intersect:!0})},getElementsAtXAxis:function(t){return Zt.modes["x-axis"](this,t,{intersect:!0})},getElementsAtEventForMode:function(t,e,n){var i=Zt.modes[e];return"function"==typeof i?i(this,t,n):[]},getDatasetAtEvent:function(t){return Zt.modes.dataset(this,t,{intersect:!0})},getDatasetMeta:function(t){var e=this.data.datasets[t];return e._meta||(e._meta={}),e._meta[this.id]||(e._meta[this.id]={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e.order||0,index:t})},getVisibleDatasetCount:function(){for(var t=0,e=0,n=this.data.datasets.length;e<n;++e)this.isDatasetVisible(e)&&t++;return t},isDatasetVisible:function(t){var e=this.getDatasetMeta(t);return"boolean"==typeof e.hidden?!e.hidden:!this.data.datasets[t].hidden},generateLegend:function(){return this.options.legendCallback(this)},destroyDatasetMeta:function(t){var e=this.id,n=this.data.datasets[t],i=n._meta&&n._meta[e];i&&(i.controller.destroy(),delete n._meta[e])},destroy:function(){var t,e,n=this,i=n.canvas;for(n.stop(),t=0,e=n.data.datasets.length;t<e;++t)n.destroyDatasetMeta(t);i&&(n.unbindEvents(),W.canvas.clear(n),_e.releaseContext(n.ctx),n.canvas=null,n.ctx=null),ke.notify(n,"destroy"),delete Ee.instances[n.id]},toBase64Image:function(){return this.canvas.toDataURL.apply(this.canvas,arguments)},initToolTip:function(){var t=this;t.tooltip=new Fe({_chart:t,_chartInstance:t,_data:t.data,_options:t.options.tooltips},t)},bindEvents:function(){var e=this,n=e._listeners={},i=function(){e.eventHandler.apply(e,arguments)};W.each(e.options.events,function(t){_e.addEventListener(e,t,i),n[t]=i}),e.options.responsive&&(i=function(){e.resize()},_e.addEventListener(e,"resize",i),n.resize=i)},unbindEvents:function(){var n=this,t=n._listeners;t&&(delete n._listeners,W.each(t,function(t,e){_e.removeEventListener(n,e,t)}))},updateHoverStyle:function(t,e,n){for(var i,a=n?"set":"remove",r=0,o=t.length;r<o;++r)(i=t[r])&&this.getDatasetMeta(i._datasetIndex).controller[a+"HoverStyle"](i);"dataset"===e&&this.getDatasetMeta(t[0]._datasetIndex).controller["_"+a+"DatasetHoverStyle"]()},eventHandler:function(t){var e=this,n=e.tooltip;if(!1!==ke.notify(e,"beforeEvent",[t])){e._bufferedRender=!0,e._bufferedRequest=null;var i=e.handleEvent(t);n&&(i=n._start?n.handleEvent(t):i|n.handleEvent(t)),ke.notify(e,"afterEvent",[t]);var a=e._bufferedRequest;return a?e.render(a):i&&!e.animating&&(e.stop(),e.render({duration:e.options.hover.animationDuration,lazy:!0})),e._bufferedRender=!1,e._bufferedRequest=null,e}},handleEvent:function(t){var e,n=this,i=n.options||{},a=i.hover;return n.lastActive=n.lastActive||[],"mouseout"===t.type?n.active=[]:n.active=n.getElementsAtEventForMode(t,a.mode,a),W.callback(i.onHover||i.hover.onHover,[t.native,n.active],n),"mouseup"!==t.type&&"click"!==t.type||i.onClick&&i.onClick.call(n,t.native,n.active),n.lastActive.length&&n.updateHoverStyle(n.lastActive,a.mode,!1),n.active.length&&a.mode&&n.updateHoverStyle(n.active,a.mode,!0),e=!W.arrayEquals(n.active,n.lastActive),n.lastActive=n.active,e}}),Ee.instances={};var We=Ee;function Ve(){throw new Error("This method is not implemented: either no adapter can be found or an incomplete integration was provided.")}function He(t){this.options=t||{}}(Ee.Controller=Ee).types={},W.configMerge=Re,W.scaleMerge=Oe,W.extend(He.prototype,{formats:Ve,parse:Ve,format:Ve,add:Ve,diff:Ve,startOf:Ve,endOf:Ve,_create:function(t){return t}}),He.override=function(t){W.extend(He.prototype,t)};var je={_date:He},qe={formatters:{values:function(t){return W.isArray(t)?t:""+t},linear:function(t,e,n){var i=3<n.length?n[2]-n[1]:n[1]-n[0];1<Math.abs(i)&&t!==Math.floor(t)&&(i=t-Math.floor(t));var a,r,o,s=W.log10(Math.abs(i));return 0!==t?Math.max(Math.abs(n[0]),Math.abs(n[n.length-1]))<1e-4?(a=W.log10(Math.abs(t)),r=Math.floor(a)-Math.floor(s),r=Math.max(Math.min(r,20),0),t.toExponential(r)):(o=-1*Math.floor(s),o=Math.max(Math.min(o,20),0),t.toFixed(o)):"0"},logarithmic:function(t,e,n){var i=t/Math.pow(10,Math.floor(W.log10(t)));return 0===t?"0":1==i||2==i||5==i||0===e||e===n.length-1?t.toExponential():""}}},Ue=W.isArray,Ye=W.isNullOrUndef,Ge=W.valueOrDefault,Xe=W.valueAtIndexOrDefault;function Ke(t){return t.drawTicks?t.tickMarkLength:0}function Ze(t){var e,n;return t.display?(e=W.options._parseFont(t),n=W.options.toPadding(t.padding),e.lineHeight+n.height):0}function $e(t,e){return W.extend(W.options._parseFont({fontFamily:Ge(e.fontFamily,t.fontFamily),fontSize:Ge(e.fontSize,t.fontSize),fontStyle:Ge(e.fontStyle,t.fontStyle),lineHeight:Ge(e.lineHeight,t.lineHeight)}),{color:W.options.resolve([e.fontColor,t.fontColor,N.global.defaultFontColor])})}function Je(t){var e=$e(t,t.minor);return{minor:e,major:t.major.enabled?$e(t,t.major):e}}function Qe(t){for(var e,n=[],i=0,a=t.length;i<a;++i)void 0!==(e=t[i])._index&&n.push(e);return n}function tn(t,e,n,i){var a,r,o,s,l=Ge(n,0),d=Math.min(Ge(i,t.length),t.length),u=0;for(e=Math.ceil(e),i&&(e=(a=i-n)/Math.floor(a/e)),s=l;s<0;)u++,s=Math.round(l+u*e);for(r=Math.max(l,0);r<d;r++)o=t[r],r===s?(o._index=r,u++,s=Math.round(l+u*e)):delete o.label}N._set("scale",{display:!0,position:"left",offset:!1,gridLines:{display:!0,color:"rgba(0,0,0,0.1)",lineWidth:1,drawBorder:!0,drawOnChartArea:!0,drawTicks:!0,tickMarkLength:10,zeroLineWidth:1,zeroLineColor:"rgba(0,0,0,0.25)",zeroLineBorderDash:[],zeroLineBorderDashOffset:0,offsetGridLines:!1,borderDash:[],borderDashOffset:0},scaleLabel:{display:!1,labelString:"",padding:{top:4,bottom:4}},ticks:{beginAtZero:!1,minRotation:0,maxRotation:50,mirror:!1,padding:0,reverse:!1,display:!0,autoSkip:!0,autoSkipPadding:0,labelOffset:0,callback:qe.formatters.values,minor:{},major:{}}});var en=Y.extend({zeroLineIndex:0,getPadding:function(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}},getTicks:function(){return this._ticks},_getLabels:function(){var t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]},mergeTicksOptions:function(){},beforeUpdate:function(){W.callback(this.options.beforeUpdate,[this])},update:function(t,e,n){var i,a,r,o,s,l=this,d=l.options.ticks,u=d.sampleSize;if(l.beforeUpdate(),l.maxWidth=t,l.maxHeight=e,l.margins=W.extend({left:0,right:0,top:0,bottom:0},n),l._ticks=null,l.ticks=null,l._labelSizes=null,l._maxLabelLines=0,l.longestLabelWidth=0,l.longestTextCache=l.longestTextCache||{},l._gridLineItems=null,l._labelItems=null,l.beforeSetDimensions(),l.setDimensions(),l.afterSetDimensions(),l.beforeDataLimits(),l.determineDataLimits(),l.afterDataLimits(),l.beforeBuildTicks(),o=l.buildTicks()||[],(!(o=l.afterBuildTicks(o)||o)||!o.length)&&l.ticks)for(o=[],i=0,a=l.ticks.length;i<a;++i)o.push({value:l.ticks[i],major:!1});return s=u<(l._ticks=o).length,r=l._convertTicksToLabels(s?function(t){for(var e=[],n=t.length/u,i=0,a=t.length;i<a;i+=n)e.push(t[Math.floor(i)]);return e}(o):o),l._configure(),l.beforeCalculateTickRotation(),l.calculateTickRotation(),l.afterCalculateTickRotation(),l.beforeFit(),l.fit(),l.afterFit(),l._ticksToDraw=d.display&&(d.autoSkip||"auto"===d.source)?l._autoSkip(o):o,s&&(r=l._convertTicksToLabels(l._ticksToDraw)),l.ticks=r,l.afterUpdate(),l.minSize},_configure:function(){var t,e,n=this,i=n.options.ticks.reverse;n.isHorizontal()?(t=n.left,e=n.right):(t=n.top,e=n.bottom,i=!i),n._startPixel=t,n._endPixel=e,n._reversePixels=i,n._length=e-t},afterUpdate:function(){W.callback(this.options.afterUpdate,[this])},beforeSetDimensions:function(){W.callback(this.options.beforeSetDimensions,[this])},setDimensions:function(){var t=this;t.isHorizontal()?(t.width=t.maxWidth,t.left=0,t.right=t.width):(t.height=t.maxHeight,t.top=0,t.bottom=t.height),t.paddingLeft=0,t.paddingTop=0,t.paddingRight=0,t.paddingBottom=0},afterSetDimensions:function(){W.callback(this.options.afterSetDimensions,[this])},beforeDataLimits:function(){W.callback(this.options.beforeDataLimits,[this])},determineDataLimits:W.noop,afterDataLimits:function(){W.callback(this.options.afterDataLimits,[this])},beforeBuildTicks:function(){W.callback(this.options.beforeBuildTicks,[this])},buildTicks:W.noop,afterBuildTicks:function(t){var e=this;return Ue(t)&&t.length?W.callback(e.options.afterBuildTicks,[e,t]):(e.ticks=W.callback(e.options.afterBuildTicks,[e,e.ticks])||e.ticks,t)},beforeTickToLabelConversion:function(){W.callback(this.options.beforeTickToLabelConversion,[this])},convertTicksToLabels:function(){var t=this.options.ticks;this.ticks=this.ticks.map(t.userCallback||t.callback,this)},afterTickToLabelConversion:function(){W.callback(this.options.afterTickToLabelConversion,[this])},beforeCalculateTickRotation:function(){W.callback(this.options.beforeCalculateTickRotation,[this])},calculateTickRotation:function(){var t,e,n,i,a,r,o,s=this,l=s.options,d=l.ticks,u=s.getTicks().length,h=d.minRotation||0,c=d.maxRotation,f=h;!s._isVisible()||!d.display||c<=h||u<=1||!s.isHorizontal()?s.labelRotation=h:(e=(t=s._getLabelSizes()).widest.width,n=t.highest.height-t.highest.offset,i=Math.min(s.maxWidth,s.chart.width-e),e+6>(l.offset?s.maxWidth/u:i/(u-1))&&(a=i/(u-(l.offset?.5:1)),r=s.maxHeight-Ke(l.gridLines)-d.padding-Ze(l.scaleLabel),o=Math.sqrt(e*e+n*n),f=W.toDegrees(Math.min(Math.asin(Math.min((t.highest.height+6)/a,1)),Math.asin(Math.min(r/o,1))-Math.asin(n/o))),f=Math.max(h,Math.min(c,f))),s.labelRotation=f)},afterCalculateTickRotation:function(){W.callback(this.options.afterCalculateTickRotation,[this])},beforeFit:function(){W.callback(this.options.beforeFit,[this])},fit:function(){var t,e,n,i,a,r,o,s,l,d,u,h,c,f,g,p,m,v,b=this,x=b.minSize={width:0,height:0},y=b.chart,_=b.options,k=_.ticks,w=_.scaleLabel,M=_.gridLines,S=b._isVisible(),C="bottom"===_.position,P=b.isHorizontal();P?x.width=b.maxWidth:S&&(x.width=Ke(M)+Ze(w)),P?S&&(x.height=Ke(M)+Ze(w)):x.height=b.maxHeight,k.display&&S&&(t=Je(k),n=(e=b._getLabelSizes()).first,i=e.last,a=e.widest,r=e.highest,o=.4*t.minor.lineHeight,s=k.padding,P?(l=0!==b.labelRotation,d=W.toRadians(b.labelRotation),u=Math.cos(d),c=(h=Math.sin(d))*a.width+u*(r.height-(l?r.offset:0))+(l?0:o),x.height=Math.min(b.maxHeight,x.height+c+s),g=b.getPixelForTick(0)-b.left,p=b.right-b.getPixelForTick(b.getTicks().length-1),m=l?(f=C?u*n.width+h*n.offset:h*(n.height-n.offset),C?h*(i.height-i.offset):u*i.width+h*i.offset):(f=n.width/2,i.width/2),b.paddingLeft=Math.max((f-g)*b.width/(b.width-g),0)+3,b.paddingRight=Math.max((m-p)*b.width/(b.width-p),0)+3):(v=k.mirror?0:a.width+s+o,x.width=Math.min(b.maxWidth,x.width+v),b.paddingTop=n.height/2,b.paddingBottom=i.height/2)),b.handleMargins(),P?(b.width=b._length=y.width-b.margins.left-b.margins.right,b.height=x.height):(b.width=x.width,b.height=b._length=y.height-b.margins.top-b.margins.bottom)},handleMargins:function(){var t=this;t.margins&&(t.margins.left=Math.max(t.paddingLeft,t.margins.left),t.margins.top=Math.max(t.paddingTop,t.margins.top),t.margins.right=Math.max(t.paddingRight,t.margins.right),t.margins.bottom=Math.max(t.paddingBottom,t.margins.bottom))},afterFit:function(){W.callback(this.options.afterFit,[this])},isHorizontal:function(){var t=this.options.position;return"top"===t||"bottom"===t},isFullWidth:function(){return this.options.fullWidth},getRightValue:function(t){if(Ye(t))return NaN;if(("number"==typeof t||t instanceof Number)&&!isFinite(t))return NaN;if(t)if(this.isHorizontal()){if(void 0!==t.x)return this.getRightValue(t.x)}else if(void 0!==t.y)return this.getRightValue(t.y);return t},_convertTicksToLabels:function(t){var e,n,i,a=this;for(a.ticks=t.map(function(t){return t.value}),a.beforeTickToLabelConversion(),e=a.convertTicksToLabels(t)||a.ticks,a.afterTickToLabelConversion(),n=0,i=t.length;n<i;++n)t[n].label=e[n];return e},_getLabelSizes:function(){var t=this,e=t._labelSizes;return e||(t._labelSizes=e=function(t,e,n,i){for(var a,r,o,s,l,d,u,h,c,f,g,p,m,v=n.length,b=[],x=[],y=[],_=0;_<v;++_){if(o=n[_].label,s=n[_].major?e.major:e.minor,t.font=l=s.string,d=i[l]=i[l]||{data:{},gc:[]},u=s.lineHeight,h=c=0,Ye(o)||Ue(o)){if(Ue(o))for(a=0,r=o.length;a<r;++a)f=o[a],Ye(f)||Ue(f)||(h=W.measureText(t,d.data,d.gc,h,f),c+=u)}else h=W.measureText(t,d.data,d.gc,h,o),c=u;b.push(h),x.push(c),y.push(u/2)}function k(t){return{width:b[t]||0,height:x[t]||0,offset:y[t]||0}}return m=v,W.each(i,function(t){var e,n=t.gc,i=n.length/2;if(m<i){for(e=0;e<i;++e)delete t.data[n[e]];n.splice(0,i)}}),g=b.indexOf(Math.max.apply(null,b)),p=x.indexOf(Math.max.apply(null,x)),{first:k(0),last:k(v-1),widest:k(g),highest:k(p)}}(t.ctx,Je(t.options.ticks),t.getTicks(),t.longestTextCache),t.longestLabelWidth=e.widest.width),e},_parseValue:function(t){var e,n,i,a=Ue(t)?(e=+this.getRightValue(t[0]),n=+this.getRightValue(t[1]),i=Math.min(e,n),Math.max(e,n)):(e=void 0,n=t=+this.getRightValue(t),i=t);return{min:i,max:a,start:e,end:n}},_getScaleLabel:function(t){var e=this._parseValue(t);return void 0!==e.start?"["+e.start+", "+e.end+"]":+this.getRightValue(t)},getLabelForIndex:W.noop,getPixelForValue:W.noop,getValueForPixel:W.noop,getPixelForTick:function(t){var e=this.options.offset,n=this._ticks.length,i=1/Math.max(n-(e?0:1),1);return t<0||n-1<t?null:this.getPixelForDecimal(t*i+(e?i/2:0))},getPixelForDecimal:function(t){return this._reversePixels&&(t=1-t),this._startPixel+t*this._length},getDecimalForPixel:function(t){var e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e},getBasePixel:function(){return this.getPixelForValue(this.getBaseValue())},getBaseValue:function(){var t=this.min,e=this.max;return this.beginAtZero?0:t<0&&e<0?e:0<t&&0<e?t:0},_autoSkip:function(t){var e,n,i,a,r=this.options.ticks,o=this._length,s=r.maxTicksLimit||o/this._tickSize()+1,l=r.major.enabled?function(t){for(var e=[],n=0,i=t.length;n<i;n++)t[n].major&&e.push(n);return e}(t):[],d=l.length,u=l[0],h=l[d-1];if(s<d)return function(t,e,n){var i,a,r=0,o=e[0];for(n=Math.ceil(n),i=0;i<t.length;i++)a=t[i],i===o?(a._index=i,o=e[++r*n]):delete a.label}(t,l,d/s),Qe(t);if(i=function(t,e){var n,i,a,r,o=function(t){var e,n,i=t.length;if(i<2)return!1;for(n=t[0],e=1;e<i;++e)if(t[e]-t[e-1]!==n)return!1;return n}(l),s=(t.length-1)/e;if(!o)return Math.max(s,1);for(a=0,r=(n=W.math._factorize(o)).length-1;a<r;a++)if((i=n[a])>s)return i;return Math.max(s,1)}(t,s),0<d){for(e=0,n=d-1;e<n;e++)tn(t,i,l[e],l[e+1]);return a=1<d?(h-u)/(d-1):null,tn(t,i,W.isNullOrUndef(a)?0:u-a,u),tn(t,i,h,W.isNullOrUndef(a)?t.length:h+a),Qe(t)}return tn(t,i),Qe(t)},_tickSize:function(){var t=this.options.ticks,e=W.toRadians(this.labelRotation),n=Math.abs(Math.cos(e)),i=Math.abs(Math.sin(e)),a=this._getLabelSizes(),r=t.autoSkipPadding||0,o=a?a.widest.width+r:0,s=a?a.highest.height+r:0;return this.isHorizontal()?o*i<s*n?o/n:s/i:s*i<o*n?s/n:o/i},_isVisible:function(){var t,e,n,i=this.chart,a=this.options.display;if("auto"!==a)return!!a;for(t=0,e=i.data.datasets.length;t<e;++t)if(i.isDatasetVisible(t)&&((n=i.getDatasetMeta(t)).xAxisID===this.id||n.yAxisID===this.id))return!0;return!1},_computeGridLineItems:function(t){function e(t){return F(y,t,T)}var n,i,a,r,o,s,l,d,u,h,c,f,g,p,m,v,b,x=this,y=x.chart,_=x.options,k=_.gridLines,w=_.position,M=k.offsetGridLines,S=x.isHorizontal(),C=x._ticksToDraw,P=C.length+(M?1:0),A=Ke(k),D=[],T=k.drawBorder?Xe(k.lineWidth,0,0):0,I=T/2,F=W._alignPixel;for("top"===w?(n=e(x.bottom),l=x.bottom-A,u=n-I,c=e(t.top)+I,g=t.bottom):"bottom"===w?(n=e(x.top),c=t.top,g=e(t.bottom)-I,l=n+I,u=x.top+A):"left"===w?(n=e(x.right),s=x.right-A,d=n-I,h=e(t.left)+I,f=t.right):(n=e(x.left),h=t.left,f=e(t.right)-I,s=n+I,d=x.left+A),i=0;i<P;++i)a=C[i]||{},Ye(a.label)&&i<C.length||(b=i===x.zeroLineIndex&&_.offset===M?(p=k.zeroLineWidth,m=k.zeroLineColor,v=k.zeroLineBorderDash||[],k.zeroLineBorderDashOffset||0):(p=Xe(k.lineWidth,i,1),m=Xe(k.color,i,"rgba(0,0,0,0.1)"),v=k.borderDash||[],k.borderDashOffset||0),void 0!==(r=function(t,e,n){var i,a=t.getTicks().length,r=Math.min(e,a-1),o=t.getPixelForTick(r),s=t._startPixel,l=t._endPixel;if(!(n&&(i=1===a?Math.max(o-s,l-o):0===e?(t.getPixelForTick(1)-o)/2:(o-t.getPixelForTick(r-1))/2,(o+=r<e?i:-i)<s-1e-6||l+1e-6<o)))return o}(x,a._index||i,M))&&(o=F(y,r,p),S?s=d=h=f=o:l=u=c=g=o,D.push({tx1:s,ty1:l,tx2:d,ty2:u,x1:h,y1:c,x2:f,y2:g,width:p,color:m,borderDash:v,borderDashOffset:b})));return D.ticksLength=P,D.borderValue=n,D},_computeLabelItems:function(){for(var t,e,n,i,a,r,o,s,l,d=this,u=d.options,h=u.ticks,c=u.position,f=h.mirror,g=d.isHorizontal(),p=d._ticksToDraw,m=Je(h),v=h.padding,b=Ke(u.gridLines),x=-W.toRadians(d.labelRotation),y=[],_="top"===c?(i=d.bottom-b-v,x?"left":"center"):"bottom"===c?(i=d.top+b+v,x?"right":"center"):"left"===c?(n=d.right-(f?0:b)-v,f?"left":"right"):(n=d.left+(f?0:b)+v,f?"right":"left"),k=0,w=p.length;k<w;++k)e=(t=p[k]).label,Ye(e)||(a=d.getPixelForTick(t._index||k)+h.labelOffset,o=(r=t.major?m.major:m.minor).lineHeight,s=Ue(e)?e.length:1,l=g?(n=a,"top"===c?((x?1:.5)-s)*o:(x?0:.5)*o):(i=a,(1-s)*o/2),y.push({x:n,y:i,rotation:x,label:e,font:r,textOffset:l,textAlign:_}));return y},_drawGrid:function(t){var e=this,n=e.options.gridLines;if(n.display){for(var i,a,r,o,s,l,d,u,h,c,f=e.ctx,g=e.chart,p=W._alignPixel,m=n.drawBorder?Xe(n.lineWidth,0,0):0,v=e._gridLineItems||(e._gridLineItems=e._computeGridLineItems(t)),b=0,x=v.length;b<x;++b)i=(r=v[b]).width,a=r.color,i&&a&&(f.save(),f.lineWidth=i,f.strokeStyle=a,f.setLineDash&&(f.setLineDash(r.borderDash),f.lineDashOffset=r.borderDashOffset),f.beginPath(),n.drawTicks&&(f.moveTo(r.tx1,r.ty1),f.lineTo(r.tx2,r.ty2)),n.drawOnChartArea&&(f.moveTo(r.x1,r.y1),f.lineTo(r.x2,r.y2)),f.stroke(),f.restore());m&&(u=m,h=Xe(n.lineWidth,v.ticksLength-1,1),c=v.borderValue,e.isHorizontal()?(o=p(g,e.left,u)-u/2,s=p(g,e.right,h)+h/2,l=d=c):(l=p(g,e.top,u)-u/2,d=p(g,e.bottom,h)+h/2,o=s=c),f.lineWidth=m,f.strokeStyle=Xe(n.color,0),f.beginPath(),f.moveTo(o,l),f.lineTo(s,d),f.stroke())}},_drawLabels:function(){var t=this;if(t.options.ticks.display)for(var e,n,i,a,r,o,s=t.ctx,l=t._labelItems||(t._labelItems=t._computeLabelItems()),d=0,u=l.length;d<u;++d){if(a=(i=l[d]).font,s.save(),s.translate(i.x,i.y),s.rotate(i.rotation),s.font=a.string,s.fillStyle=a.color,s.textBaseline="middle",s.textAlign=i.textAlign,r=i.label,o=i.textOffset,Ue(r))for(e=0,n=r.length;e<n;++e)s.fillText(""+r[e],0,o),o+=a.lineHeight;else s.fillText(r,0,o);s.restore()}},_drawTitle:function(){var t,e,n,i,a,r,o,s,l,d=this,u=d.ctx,h=d.options,c=h.scaleLabel;c.display&&(t=Ge(c.fontColor,N.global.defaultFontColor),e=W.options._parseFont(c),n=W.options.toPadding(c.padding),i=e.lineHeight/2,a=h.position,l=0,d.isHorizontal()?(o=d.left+d.width/2,s="bottom"===a?d.bottom-i-n.bottom:d.top+i+n.top):(o=(r="left"===a)?d.left+i+n.top:d.right-i-n.top,s=d.top+d.height/2,l=r?-.5*Math.PI:.5*Math.PI),u.save(),u.translate(o,s),u.rotate(l),u.textAlign="center",u.textBaseline="middle",u.fillStyle=t,u.font=e.string,u.fillText(c.labelString,0,0),u.restore())},draw:function(t){this._isVisible()&&(this._drawGrid(t),this._drawTitle(),this._drawLabels())},_layers:function(){var t=this,e=t.options,n=e.ticks&&e.ticks.z||0,i=e.gridLines&&e.gridLines.z||0;return t._isVisible()&&n!==i&&t.draw===t._draw?[{z:i,draw:function(){t._drawGrid.apply(t,arguments),t._drawTitle.apply(t,arguments)}},{z:n,draw:function(){t._drawLabels.apply(t,arguments)}}]:[{z:n,draw:function(){t.draw.apply(t,arguments)}}]},_getMatchingVisibleMetas:function(e){var n=this,i=n.isHorizontal();return n.chart._getSortedVisibleDatasetMetas().filter(function(t){return(!e||t.type===e)&&(i?t.xAxisID===n.id:t.yAxisID===n.id)})}});en.prototype._draw=en.prototype.draw;var nn=en,an=W.isNullOrUndef,rn=nn.extend({determineDataLimits:function(){var t,e=this,n=e._getLabels(),i=e.options.ticks,a=i.min,r=i.max,o=0,s=n.length-1;void 0!==a&&0<=(t=n.indexOf(a))&&(o=t),void 0!==r&&0<=(t=n.indexOf(r))&&(s=t),e.minIndex=o,e.maxIndex=s,e.min=n[o],e.max=n[s]},buildTicks:function(){var t=this._getLabels(),e=this.minIndex,n=this.maxIndex;this.ticks=0===e&&n===t.length-1?t:t.slice(e,n+1)},getLabelForIndex:function(t,e){var n=this.chart;return n.getDatasetMeta(e).controller._getValueScaleId()===this.id?this.getRightValue(n.data.datasets[e].data[t]):this._getLabels()[t]},_configure:function(){var t=this,e=t.options.offset,n=t.ticks;nn.prototype._configure.call(t),t.isHorizontal()||(t._reversePixels=!t._reversePixels),n&&(t._startValue=t.minIndex-(e?.5:0),t._valueRange=Math.max(n.length-(e?0:1),1))},getPixelForValue:function(t,e,n){var i,a,r,o=this;return an(e)||an(n)||(t=o.chart.data.datasets[n].data[e]),an(t)||(i=o.isHorizontal()?t.x:t.y),(void 0!==i||void 0!==t&&isNaN(e))&&(a=o._getLabels(),t=W.valueOrDefault(i,t),e=-1!==(r=a.indexOf(t))?r:e,isNaN(e)&&(e=t)),o.getPixelForDecimal((e-o._startValue)/o._valueRange)},getPixelForTick:function(t){var e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t],t+this.minIndex)},getValueForPixel:function(t){var e=Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange);return Math.min(Math.max(e,0),this.ticks.length-1)},getBasePixel:function(){return this.bottom}});rn._defaults={position:"bottom"};var on=W.noop,sn=W.isNullOrUndef,ln=nn.extend({getRightValue:function(t){return"string"==typeof t?+t:nn.prototype.getRightValue.call(this,t)},handleTickRangeOptions:function(){var t,e,n=this,i=n.options.ticks;i.beginAtZero&&(t=W.sign(n.min),e=W.sign(n.max),t<0&&e<0?n.max=0:0<t&&0<e&&(n.min=0));var a=void 0!==i.min||void 0!==i.suggestedMin,r=void 0!==i.max||void 0!==i.suggestedMax;void 0!==i.min?n.min=i.min:void 0!==i.suggestedMin&&(null===n.min?n.min=i.suggestedMin:n.min=Math.min(n.min,i.suggestedMin)),void 0!==i.max?n.max=i.max:void 0!==i.suggestedMax&&(null===n.max?n.max=i.suggestedMax:n.max=Math.max(n.max,i.suggestedMax)),a!=r&&n.min>=n.max&&(a?n.max=n.min+1:n.min=n.max-1),n.min===n.max&&(n.max++,i.beginAtZero||n.min--)},getTickLimit:function(){var t,e=this.options.ticks,n=e.stepSize,i=e.maxTicksLimit;return n?t=Math.ceil(this.max/n)-Math.floor(this.min/n)+1:(t=this._computeTickLimit(),i=i||11),i&&(t=Math.min(i,t)),t},_computeTickLimit:function(){return Number.POSITIVE_INFINITY},handleDirectionalChanges:on,buildTicks:function(){var p=this,t=p.options.ticks,e=p.getTickLimit(),m={maxTicks:Math.max(2,e),min:t.min,max:t.max,precision:t.precision,stepSize:W.valueOrDefault(t.fixedStepSize,t.stepSize)},n=p.ticks=function(){var t,e,n,i,a=[],r=m.stepSize,o=r||1,s=m.maxTicks-1,l=m.min,d=m.max,u=m.precision,h=p.min,c=p.max,f=W.niceNum((c-h)/s/o)*o;if(f<1e-14&&sn(l)&&sn(d))return[h,c];(i=Math.ceil(c/f)-Math.floor(h/f))>s&&(f=W.niceNum(i*f/s/o)*o),r||sn(u)?t=Math.pow(10,W._decimalPlaces(f)):(t=Math.pow(10,u),f=Math.ceil(f*t)/t),e=Math.floor(h/f)*f,n=Math.ceil(c/f)*f,r&&(!sn(l)&&W.almostWhole(l/f,f/1e3)&&(e=l),!sn(d)&&W.almostWhole(d/f,f/1e3)&&(n=d)),i=(n-e)/f,i=W.almostEquals(i,Math.round(i),f/1e3)?Math.round(i):Math.ceil(i),e=Math.round(e*t)/t,n=Math.round(n*t)/t,a.push(sn(l)?e:l);for(var g=1;g<i;++g)a.push(Math.round((e+g*f)*t)/t);return a.push(sn(d)?n:d),a}();p.handleDirectionalChanges(),p.max=W.max(n),p.min=W.min(n),t.reverse?(n.reverse(),p.start=p.max,p.end=p.min):(p.start=p.min,p.end=p.max)},convertTicksToLabels:function(){var t=this;t.ticksAsNumbers=t.ticks.slice(),t.zeroLineIndex=t.ticks.indexOf(0),nn.prototype.convertTicksToLabels.call(t)},_configure:function(){var t,e=this,n=e.getTicks(),i=e.min,a=e.max;nn.prototype._configure.call(e),e.options.offset&&n.length&&(i-=t=(a-i)/Math.max(n.length-1,1)/2,a+=t),e._startValue=i,e._endValue=a,e._valueRange=a-i}}),dn={position:"left",ticks:{callback:qe.formatters.linear}};var un=ln.extend({determineDataLimits:function(){var t,e,n,i,a=this,r=a.options,o=a.chart.data.datasets,s=a._getMatchingVisibleMetas(),l=r.stacked,d={},u=s.length;if(a.min=Number.POSITIVE_INFINITY,a.max=Number.NEGATIVE_INFINITY,void 0===l)for(t=0;!l&&t<u;++t)l=void 0!==(e=s[t]).stack;for(t=0;t<u;++t)n=o[(e=s[t]).index].data,l?function(t,e,n,i){for(var a,r,o,s,l=t.options,d=(r=e,o=l.stacked,s=[n.type,void 0===o&&void 0===n.stack?n.index:"",n.stack].join("."),void 0===r[s]&&(r[s]={pos:[],neg:[]}),r[s]),u=d.pos,h=d.neg,c=i.length,f=0;f<c;++f)a=t._parseValue(i[f]),isNaN(a.min)||isNaN(a.max)||n.data[f].hidden||(u[f]=u[f]||0,h[f]=h[f]||0,l.relativePoints?u[f]=100:a.min<0||a.max<0?h[f]+=a.min:u[f]+=a.max)}(a,d,e,n):function(t,e,n){for(var i,a=n.length,r=0;r<a;++r)i=t._parseValue(n[r]),isNaN(i.min)||isNaN(i.max)||e.data[r].hidden||(t.min=Math.min(t.min,i.min),t.max=Math.max(t.max,i.max))}(a,e,n);W.each(d,function(t){i=t.pos.concat(t.neg),a.min=Math.min(a.min,W.min(i)),a.max=Math.max(a.max,W.max(i))}),a.min=W.isFinite(a.min)&&!isNaN(a.min)?a.min:0,a.max=W.isFinite(a.max)&&!isNaN(a.max)?a.max:1,a.handleTickRangeOptions()},_computeTickLimit:function(){var t;return this.isHorizontal()?Math.ceil(this.width/40):(t=W.options._parseFont(this.options.ticks),Math.ceil(this.height/t.lineHeight))},handleDirectionalChanges:function(){this.isHorizontal()||this.ticks.reverse()},getLabelForIndex:function(t,e){return this._getScaleLabel(this.chart.data.datasets[e].data[t])},getPixelForValue:function(t){return this.getPixelForDecimal((this.getRightValue(t)-this._startValue)/this._valueRange)},getValueForPixel:function(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange},getPixelForTick:function(t){var e=this.ticksAsNumbers;return t<0||t>e.length-1?null:this.getPixelForValue(e[t])}});un._defaults=dn;var hn=W.valueOrDefault,cn=W.math.log10,fn={position:"left",ticks:{callback:qe.formatters.logarithmic}};function gn(t,e){return W.isFinite(t)&&0<=t?t:e}var pn=nn.extend({determineDataLimits:function(){var t,e,n,i,a=this,r=a.options,o=a.chart,s=o.data.datasets,l=a.isHorizontal();function d(t){return l?t.xAxisID===a.id:t.yAxisID===a.id}a.min=Number.POSITIVE_INFINITY,a.max=Number.NEGATIVE_INFINITY,a.minNotZero=Number.POSITIVE_INFINITY;var u=r.stacked;if(void 0===u)for(c=0;c<s.length;c++)if(t=o.getDatasetMeta(c),o.isDatasetVisible(c)&&d(t)&&void 0!==t.stack){u=!0;break}if(r.stacked||u){for(var h={},c=0;c<s.length;c++){var f=[(t=o.getDatasetMeta(c)).type,void 0===r.stacked&&void 0===t.stack?c:"",t.stack].join(".");if(o.isDatasetVisible(c)&&d(t))for(void 0===h[f]&&(h[f]=[]),n=0,i=(e=s[c].data).length;n<i;n++){var g=h[f],p=a._parseValue(e[n]);isNaN(p.min)||isNaN(p.max)||t.data[n].hidden||p.min<0||p.max<0||(g[n]=g[n]||0,g[n]+=p.max)}}W.each(h,function(t){var e,n;0<t.length&&(e=W.min(t),n=W.max(t),a.min=Math.min(a.min,e),a.max=Math.max(a.max,n))})}else for(c=0;c<s.length;c++)if(t=o.getDatasetMeta(c),o.isDatasetVisible(c)&&d(t))for(n=0,i=(e=s[c].data).length;n<i;n++)p=a._parseValue(e[n]),isNaN(p.min)||isNaN(p.max)||t.data[n].hidden||p.min<0||p.max<0||(a.min=Math.min(p.min,a.min),a.max=Math.max(p.max,a.max),0!==p.min&&(a.minNotZero=Math.min(p.min,a.minNotZero)));a.min=W.isFinite(a.min)?a.min:null,a.max=W.isFinite(a.max)?a.max:null,a.minNotZero=W.isFinite(a.minNotZero)?a.minNotZero:null,this.handleTickRangeOptions()},handleTickRangeOptions:function(){var t=this,e=t.options.ticks;t.min=gn(e.min,t.min),t.max=gn(e.max,t.max),t.min===t.max&&(0!==t.min&&null!==t.min?(t.min=Math.pow(10,Math.floor(cn(t.min))-1),t.max=Math.pow(10,Math.floor(cn(t.max))+1)):(t.min=1,t.max=10)),null===t.min&&(t.min=Math.pow(10,Math.floor(cn(t.max))-1)),null===t.max&&(t.max=0!==t.min?Math.pow(10,Math.floor(cn(t.min))+1):10),null===t.minNotZero&&(0<t.min?t.minNotZero=t.min:t.max<1?t.minNotZero=Math.pow(10,Math.floor(cn(t.max))):t.minNotZero=1)},buildTicks:function(){var t=this,e=t.options.ticks,n=!t.isHorizontal(),i={min:gn(e.min),max:gn(e.max)},a=t.ticks=function(t,e){var n,i,a=[],r=hn(t.min,Math.pow(10,Math.floor(cn(e.min)))),o=Math.floor(cn(e.max)),s=Math.ceil(e.max/Math.pow(10,o));0===r?(n=Math.floor(cn(e.minNotZero)),i=Math.floor(e.minNotZero/Math.pow(10,n)),a.push(r),r=i*Math.pow(10,n)):(n=Math.floor(cn(r)),i=Math.floor(r/Math.pow(10,n)));for(var l=n<0?Math.pow(10,Math.abs(n)):1;a.push(r),10==++i&&(i=1,l=0<=++n?1:l),r=Math.round(i*Math.pow(10,n)*l)/l,n<o||n===o&&i<s;);var d=hn(t.max,r);return a.push(d),a}(i,t);t.max=W.max(a),t.min=W.min(a),e.reverse?(n=!n,t.start=t.max,t.end=t.min):(t.start=t.min,t.end=t.max),n&&a.reverse()},convertTicksToLabels:function(){this.tickValues=this.ticks.slice(),nn.prototype.convertTicksToLabels.call(this)},getLabelForIndex:function(t,e){return this._getScaleLabel(this.chart.data.datasets[e].data[t])},getPixelForTick:function(t){var e=this.tickValues;return t<0||t>e.length-1?null:this.getPixelForValue(e[t])},_getFirstTickValue:function(t){var e=Math.floor(cn(t));return Math.floor(t/Math.pow(10,e))*Math.pow(10,e)},_configure:function(){var t=this,e=t.min,n=0;nn.prototype._configure.call(t),0===e&&(e=t._getFirstTickValue(t.minNotZero),n=hn(t.options.ticks.fontSize,N.global.defaultFontSize)/t._length),t._startValue=cn(e),t._valueOffset=n,t._valueRange=(cn(t.max)-cn(e))/(1-n)},getPixelForValue:function(t){var e=this,n=0;return(t=+e.getRightValue(t))>e.min&&0<t&&(n=(cn(t)-e._startValue)/e._valueRange+e._valueOffset),e.getPixelForDecimal(n)},getValueForPixel:function(t){var e=this,n=e.getDecimalForPixel(t);return 0===n&&0===e.min?0:Math.pow(10,e._startValue+(n-e._valueOffset)*e._valueRange)}});pn._defaults=fn;var mn=W.valueOrDefault,vn=W.valueAtIndexOrDefault,bn=W.options.resolve,xn={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,color:"rgba(0,0,0,0.1)",lineWidth:1,borderDash:[],borderDashOffset:0},gridLines:{circular:!1},ticks:{showLabelBackdrop:!0,backdropColor:"rgba(255,255,255,0.75)",backdropPaddingY:2,backdropPaddingX:2,callback:qe.formatters.linear},pointLabels:{display:!0,fontSize:10,callback:function(t){return t}}};function yn(t){var e=t.ticks;return e.display&&t.display?mn(e.fontSize,N.global.defaultFontSize)+2*e.backdropPaddingY:0}function _n(t,e,n,i,a){return t===i||t===a?{start:e-n/2,end:e+n/2}:t<i||a<t?{start:e-n,end:e}:{start:e,end:e+n}}function kn(t){return W.isNumber(t)?t:0}var wn=ln.extend({setDimensions:function(){var t=this;t.width=t.maxWidth,t.height=t.maxHeight,t.paddingTop=yn(t.options)/2,t.xCenter=Math.floor(t.width/2),t.yCenter=Math.floor((t.height-t.paddingTop)/2),t.drawingArea=Math.min(t.height-t.paddingTop,t.width)/2},determineDataLimits:function(){var a=this,n=a.chart,r=Number.POSITIVE_INFINITY,o=Number.NEGATIVE_INFINITY;W.each(n.data.datasets,function(t,e){var i;n.isDatasetVisible(e)&&(i=n.getDatasetMeta(e),W.each(t.data,function(t,e){var n=+a.getRightValue(t);isNaN(n)||i.data[e].hidden||(r=Math.min(n,r),o=Math.max(n,o))}))}),a.min=r===Number.POSITIVE_INFINITY?0:r,a.max=o===Number.NEGATIVE_INFINITY?0:o,a.handleTickRangeOptions()},_computeTickLimit:function(){return Math.ceil(this.drawingArea/yn(this.options))},convertTicksToLabels:function(){var e=this;ln.prototype.convertTicksToLabels.call(e),e.pointLabels=e.chart.data.labels.map(function(){var t=W.callback(e.options.pointLabels.callback,arguments,e);return t||0===t?t:""})},getLabelForIndex:function(t,e){return+this.getRightValue(this.chart.data.datasets[e].data[t])},fit:function(){var t=this.options;t.display&&t.pointLabels.display?function(t){var e,n,i=W.options._parseFont(t.options.pointLabels),a={l:0,r:t.width,t:0,b:t.height-t.paddingTop},r={};t.ctx.font=i.string,t._pointLabelSizes=[];for(var o,s,l,d=t.chart.data.labels.length,u=0;u<d;u++){n=t.getPointPosition(u,t.drawingArea+5),o=t.ctx,s=i.lineHeight,l=t.pointLabels[u],e=W.isArray(l)?{w:W.longestText(o,o.font,l),h:l.length*s}:{w:o.measureText(l).width,h:s},t._pointLabelSizes[u]=e;var h=t.getIndexAngle(u),c=W.toDegrees(h)%360,f=_n(c,n.x,e.w,0,180),g=_n(c,n.y,e.h,90,270);f.start<a.l&&(a.l=f.start,r.l=h),f.end>a.r&&(a.r=f.end,r.r=h),g.start<a.t&&(a.t=g.start,r.t=h),g.end>a.b&&(a.b=g.end,r.b=h)}t.setReductions(t.drawingArea,a,r)}(this):this.setCenterPoint(0,0,0,0)},setReductions:function(t,e,n){var i=this,a=e.l/Math.sin(n.l),r=Math.max(e.r-i.width,0)/Math.sin(n.r),o=-e.t/Math.cos(n.t),s=-Math.max(e.b-(i.height-i.paddingTop),0)/Math.cos(n.b),a=kn(a),r=kn(r),o=kn(o),s=kn(s);i.drawingArea=Math.min(Math.floor(t-(a+r)/2),Math.floor(t-(o+s)/2)),i.setCenterPoint(a,r,o,s)},setCenterPoint:function(t,e,n,i){var a=this,r=a.width-e-a.drawingArea,o=t+a.drawingArea,s=n+a.drawingArea,l=a.height-a.paddingTop-i-a.drawingArea;a.xCenter=Math.floor((o+r)/2+a.left),a.yCenter=Math.floor((s+l)/2+a.top+a.paddingTop)},getIndexAngle:function(t){var e=this.chart,n=(t*(360/e.data.labels.length)+((e.options||{}).startAngle||0))%360;return(n<0?360+n:n)*Math.PI*2/360},getDistanceFromCenterForValue:function(t){var e=this;if(W.isNullOrUndef(t))return NaN;var n=e.drawingArea/(e.max-e.min);return e.options.ticks.reverse?(e.max-t)*n:(t-e.min)*n},getPointPosition:function(t,e){var n=this.getIndexAngle(t)-Math.PI/2;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter}},getPointPositionForValue:function(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))},getBasePosition:function(t){var e=this.min,n=this.max;return this.getPointPositionForValue(t||0,this.beginAtZero?0:e<0&&n<0?n:0<e&&0<n?e:0)},_drawGrid:function(){var t,n,e,i=this,a=i.ctx,r=i.options,o=r.gridLines,s=r.angleLines,l=mn(s.lineWidth,o.lineWidth),d=mn(s.color,o.color);if(r.pointLabels.display&&function(t){var e=t.ctx,n=t.options,i=n.pointLabels,a=yn(n),r=t.getDistanceFromCenterForValue(n.ticks.reverse?t.min:t.max),o=W.options._parseFont(i);e.save(),e.font=o.string,e.textBaseline="middle";for(var s,l,d,u,h=t.chart.data.labels.length-1;0<=h;h--){var c=0===h?a/2:0,f=t.getPointPosition(h,r+c+5),g=vn(i.fontColor,h,N.global.defaultFontColor);e.fillStyle=g;var p=t.getIndexAngle(h),m=W.toDegrees(p);e.textAlign=0===(u=m)||180===u?"center":u<180?"left":"right",s=m,l=t._pointLabelSizes[h],d=f,90===s||270===s?d.y-=l.h/2:(270<s||s<90)&&(d.y-=l.h),function(t,e,n,i){var a,r,o=n.y+i/2;if(W.isArray(e))for(a=0,r=e.length;a<r;++a)t.fillText(e[a],n.x,o),o+=i;else t.fillText(e,n.x,o)}(e,t.pointLabels[h],f,o.lineHeight)}e.restore()}(i),o.display&&W.each(i.ticks,function(t,e){0!==e&&(n=i.getDistanceFromCenterForValue(i.ticksAsNumbers[e]),function(t,e,n,i){var a,r=t.ctx,o=e.circular,s=t.chart.data.labels.length,l=vn(e.color,i-1),d=vn(e.lineWidth,i-1);if((o||s)&&l&&d){if(r.save(),r.strokeStyle=l,r.lineWidth=d,r.setLineDash&&(r.setLineDash(e.borderDash||[]),r.lineDashOffset=e.borderDashOffset||0),r.beginPath(),o)r.arc(t.xCenter,t.yCenter,n,0,2*Math.PI);else{a=t.getPointPosition(0,n),r.moveTo(a.x,a.y);for(var u=1;u<s;u++)a=t.getPointPosition(u,n),r.lineTo(a.x,a.y)}r.closePath(),r.stroke(),r.restore()}}(i,o,n,e))}),s.display&&l&&d){for(a.save(),a.lineWidth=l,a.strokeStyle=d,a.setLineDash&&(a.setLineDash(bn([s.borderDash,o.borderDash,[]])),a.lineDashOffset=bn([s.borderDashOffset,o.borderDashOffset,0])),t=i.chart.data.labels.length-1;0<=t;t--)n=i.getDistanceFromCenterForValue(r.ticks.reverse?i.min:i.max),e=i.getPointPosition(t,n),a.beginPath(),a.moveTo(i.xCenter,i.yCenter),a.lineTo(e.x,e.y),a.stroke();a.restore()}},_drawLabels:function(){var n,i,t,a,r,o=this,s=o.ctx,l=o.options.ticks;l.display&&(t=o.getIndexAngle(0),a=W.options._parseFont(l),r=mn(l.fontColor,N.global.defaultFontColor),s.save(),s.font=a.string,s.translate(o.xCenter,o.yCenter),s.rotate(t),s.textAlign="center",s.textBaseline="middle",W.each(o.ticks,function(t,e){0===e&&!l.reverse||(n=o.getDistanceFromCenterForValue(o.ticksAsNumbers[e]),l.showLabelBackdrop&&(i=s.measureText(t).width,s.fillStyle=l.backdropColor,s.fillRect(-i/2-l.backdropPaddingX,-n-a.size/2-l.backdropPaddingY,i+2*l.backdropPaddingX,a.size+2*l.backdropPaddingY)),s.fillStyle=r,s.fillText(t,0,-n))}),s.restore())},_drawTitle:W.noop});wn._defaults=xn;var Mn=W._deprecated,Sn=W.options.resolve,Cn=W.valueOrDefault,Pn=Number.MIN_SAFE_INTEGER||-9007199254740991,An=Number.MAX_SAFE_INTEGER||9007199254740991,Dn={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Tn=Object.keys(Dn);function In(t,e){return t-e}function Fn(t){return W.valueOrDefault(t.time.min,t.ticks.min)}function Ln(t){return W.valueOrDefault(t.time.max,t.ticks.max)}function On(t,e,n,i){var a=function(t,e,n){for(var i,a,r,o=0,s=t.length-1;0<=o&&o<=s;){if(a=t[(i=o+s>>1)-1]||null,r=t[i],!a)return{lo:null,hi:r};if(r[e]<n)o=1+i;else{if(!(a[e]>n))return{lo:a,hi:r};s=i-1}}return{lo:r,hi:null}}(t,e,n),r=a.lo?a.hi?a.lo:t[t.length-2]:t[0],o=a.lo?a.hi?a.hi:t[t.length-1]:t[1],s=o[e]-r[e],l=s?(n-r[e])/s:0,d=(o[i]-r[i])*l;return r[i]+d}function Rn(t,e){var n=t._adapter,i=t.options.time,a=i.parser,r=a||i.format,o=e;return"function"==typeof a&&(o=a(o)),W.isFinite(o)||(o="string"==typeof r?n.parse(o,r):n.parse(o)),null!==o?+o:(a||"function"!=typeof r||(o=r(e),W.isFinite(o)||(o=n.parse(o))),o)}function zn(t,e){if(W.isNullOrUndef(e))return null;var n=t.options.time,i=Rn(t,t.getRightValue(e));return null===i||n.round&&(i=+t._adapter.startOf(i,n.round)),i}function Nn(t,e,n,i){for(var a,r,o=Tn.length,s=Tn.indexOf(t);s<o-1;++s)if(r=(a=Dn[Tn[s]]).steps?a.steps:An,a.common&&Math.ceil((n-e)/(r*a.size))<=i)return Tn[s];return Tn[o-1]}function Bn(l,t,e){for(var n,i=[],a={},r=t.length,o=0;o<r;++o)a[n=t[o]]=o,i.push({value:n,major:!1});return 0!==r&&e?function(t,e,n){for(var i,a=l._adapter,r=+a.startOf(t[0].value,n),o=t[t.length-1].value,s=r;s<=o;s=+a.add(s,1,n))0<=(i=e[s])&&(t[i].major=!0);return t}(i,a,e):i}var En=nn.extend({initialize:function(){this.mergeTicksOptions(),nn.prototype.initialize.call(this)},update:function(){var t=this.options,e=t.time||(t.time={}),n=this._adapter=new je._date(t.adapters.date);return Mn("time scale",e.format,"time.format","time.parser"),Mn("time scale",e.min,"time.min","ticks.min"),Mn("time scale",e.max,"time.max","ticks.max"),W.mergeIf(e.displayFormats,n.formats()),nn.prototype.update.apply(this,arguments)},getRightValue:function(t){return t&&void 0!==t.t&&(t=t.t),nn.prototype.getRightValue.call(this,t)},determineDataLimits:function(){for(var t,e,n,i,a,r=this,o=r.chart,s=r._adapter,l=r.options,d=l.time.unit||"day",u=An,h=Pn,c=[],f=[],g=[],p=r._getLabels(),m=0,v=p.length;m<v;++m)g.push(zn(r,p[m]));for(m=0,v=(o.data.datasets||[]).length;m<v;++m)if(o.isDatasetVisible(m))if(n=o.data.datasets[m].data,W.isObject(n[0]))for(f[m]=[],t=0,e=n.length;t<e;++t)i=zn(r,n[t]),c.push(i),f[m][t]=i;else f[m]=g.slice(0),a||(c=c.concat(g),a=!0);else f[m]=[];g.length&&(u=Math.min(u,g[0]),h=Math.max(h,g[g.length-1])),c.length&&(c=1<v?function(t){for(var e,n={},i=[],a=0,r=t.length;a<r;++a)n[e=t[a]]||(n[e]=!0,i.push(e));return i}(c).sort(In):c.sort(In),u=Math.min(u,c[0]),h=Math.max(h,c[c.length-1])),u=zn(r,Fn(l))||u,h=zn(r,Ln(l))||h,u=u===An?+s.startOf(Date.now(),d):u,h=h===Pn?+s.endOf(Date.now(),d)+1:h,r.min=Math.min(u,h),r.max=Math.max(u+1,h),r._table=[],r._timestamps={data:c,datasets:f,labels:g}},buildTicks:function(){var t,e,n,i,a,r,o,s,l,h=this,d=h.min,u=h.max,c=h.options,f=c.ticks,g=c.time,p=h._timestamps,m=[],v=h.getLabelCapacity(d),b=f.source,x=c.distribution,p="data"===b||"auto"===b&&"series"===x?p.data:"labels"===b?p.labels:function(t,e){var n,i=h._adapter,a=h.options,r=a.time,o=r.unit||Nn(r.minUnit,t,e,v),s=Sn([r.stepSize,r.unitStepSize,1]),l="week"===o&&r.isoWeekday,d=t,u=[];if(l&&(d=+i.startOf(d,"isoWeek",l)),d=+i.startOf(d,l?"day":o),i.diff(e,t,o)>1e5*s)throw t+" and "+e+" are too far apart with stepSize of "+s+" "+o;for(n=d;n<e;n=+i.add(n,s,o))u.push(n);return n!==e&&"ticks"!==a.bounds||u.push(n),u}(d,u);for("ticks"===c.bounds&&p.length&&(d=p[0],u=p[p.length-1]),d=zn(h,Fn(c))||d,u=zn(h,Ln(c))||u,t=0,e=p.length;t<e;++t)(n=p[t])>=d&&n<=u&&m.push(n);return h.min=d,h.max=u,h._unit=g.unit||(f.autoSkip?Nn(g.minUnit,h.min,h.max,v):function(t,e,n,i,a){for(var r,o=Tn.length-1;o>=Tn.indexOf(n);o--)if(r=Tn[o],Dn[r].common&&t._adapter.diff(a,i,r)>=e-1)return r;return Tn[n?Tn.indexOf(n):0]}(h,m.length,g.minUnit,h.min,h.max)),h._majorUnit=f.major.enabled&&"year"!==h._unit?function(t){for(var e=Tn.indexOf(t)+1,n=Tn.length;e<n;++e)if(Dn[Tn[e]].common)return Tn[e]}(h._unit):void 0,h._table=function(t,e,n){if("linear"===x||!t.length)return[{time:e,pos:0},{time:n,pos:1}];for(var i,a,r,o=[],s=[e],l=0,d=t.length;l<d;++l)(a=t[l])>e&&a<n&&s.push(a);for(s.push(n),l=0,d=s.length;l<d;++l)r=s[l+1],i=s[l-1],a=s[l],void 0!==i&&void 0!==r&&Math.round((r+i)/2)===a||o.push({time:a,pos:l/(d-1)});return o}(h._timestamps.data,d,u),h._offsets=(i=h._table,a=m,l=s=0,c.offset&&a.length&&(r=On(i,"time",a[0],"pos"),s=1===a.length?1-r:(On(i,"time",a[1],"pos")-r)/2,o=On(i,"time",a[a.length-1],"pos"),l=1===a.length?o:(o-On(i,"time",a[a.length-2],"pos"))/2),{start:s,end:l,factor:1/(s+1+l)}),f.reverse&&m.reverse(),Bn(h,m,h._majorUnit)},getLabelForIndex:function(t,e){var n=this,i=n._adapter,a=n.chart.data,r=n.options.time,o=a.labels&&t<a.labels.length?a.labels[t]:"",s=a.datasets[e].data[t];return W.isObject(s)&&(o=n.getRightValue(s)),r.tooltipFormat?i.format(Rn(n,o),r.tooltipFormat):"string"==typeof o?o:i.format(Rn(n,o),r.displayFormats.datetime)},tickFormatFunction:function(t,e,n,i){var a=this._adapter,r=this.options,o=r.time.displayFormats,s=o[this._unit],l=this._majorUnit,d=o[l],u=n[e],h=r.ticks,c=l&&d&&u&&u.major,f=a.format(t,i||(c?d:s)),g=c?h.major:h.minor,p=Sn([g.callback,g.userCallback,h.callback,h.userCallback]);return p?p(f,e,n):f},convertTicksToLabels:function(t){for(var e=[],n=0,i=t.length;n<i;++n)e.push(this.tickFormatFunction(t[n].value,n,t));return e},getPixelForOffset:function(t){var e=this._offsets,n=On(this._table,"time",t,"pos");return this.getPixelForDecimal((e.start+n)*e.factor)},getPixelForValue:function(t,e,n){var i=null;if(void 0!==e&&void 0!==n&&(i=this._timestamps.datasets[n][e]),null===i&&(i=zn(this,t)),null!==i)return this.getPixelForOffset(i)},getPixelForTick:function(t){var e=this.getTicks();return 0<=t&&t<e.length?this.getPixelForOffset(e[t].value):null},getValueForPixel:function(t){var e=this._offsets,n=this.getDecimalForPixel(t)/e.factor-e.end,i=On(this._table,"pos",n,"time");return this._adapter._create(i)},_getLabelSize:function(t){var e=this.options.ticks,n=this.ctx.measureText(t).width,i=W.toRadians(this.isHorizontal()?e.maxRotation:e.minRotation),a=Math.cos(i),r=Math.sin(i),o=Cn(e.fontSize,N.global.defaultFontSize);return{w:n*a+o*r,h:n*r+o*a}},getLabelWidth:function(t){return this._getLabelSize(t).w},getLabelCapacity:function(t){var e=this,n=e.options.time,i=n.displayFormats,a=i[n.unit]||i.millisecond,r=e.tickFormatFunction(t,0,Bn(e,[t],e._majorUnit),a),o=e._getLabelSize(r),s=Math.floor(e.isHorizontal()?e.width/o.w:e.height/o.h);return e.options.offset&&s--,0<s?s:1}});En._defaults={position:"bottom",distribution:"linear",bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,displayFormat:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{autoSkip:!1,source:"auto",major:{enabled:!1}}};var Wn={category:rn,linear:un,logarithmic:pn,radialLinear:wn,time:En},Vn={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};je._date.override("function"==typeof i?{_id:"moment",formats:function(){return Vn},parse:function(t,e){return"string"==typeof t&&"string"==typeof e?t=i(t,e):t instanceof i||(t=i(t)),t.isValid()?t.valueOf():null},format:function(t,e){return i(t).format(e)},add:function(t,e,n){return i(t).add(e,n).valueOf()},diff:function(t,e,n){return i(t).diff(i(e),n)},startOf:function(t,e,n){return t=i(t),"isoWeek"===e?t.isoWeekday(n).valueOf():t.startOf(e).valueOf()},endOf:function(t,e){return i(t).endOf(e).valueOf()},_create:function(t){return i(t)}}:{}),N._set("global",{plugins:{filler:{propagate:!0}}});var Hn={dataset:function(t){var e=t.fill,n=t.chart,i=n.getDatasetMeta(e),a=i&&n.isDatasetVisible(e)&&i.dataset._children||[],r=a.length||0;return r?function(t,e){return e<r&&a[e]._view||null}:null},boundary:function(t){var n=t.boundary,e=n?n.x:null,i=n?n.y:null;return W.isArray(n)?function(t,e){return n[e]}:function(t){return{x:null===e?t.x:e,y:null===i?t.y:i}}}};function jn(t){return t&&!t.skip}function qn(t,e,n,i,a){var r,o,s,l;if(i&&a){for(t.moveTo(e[0].x,e[0].y),r=1;r<i;++r)W.canvas.lineTo(t,e[r-1],e[r]);if(void 0===n[0].angle)for(t.lineTo(n[a-1].x,n[a-1].y),r=a-1;0<r;--r)W.canvas.lineTo(t,n[r],n[r-1],!0);else for(o=n[0].cx,s=n[0].cy,l=Math.sqrt(Math.pow(n[0].x-o,2)+Math.pow(n[0].y-s,2)),r=a-1;0<r;--r)t.arc(o,s,l,n[r].angle,n[r-1].angle,!0)}}var Un={id:"filler",afterDatasetsUpdate:function(t,e){for(var n,i,a,r,o,s,l=(t.data.datasets||[]).length,d=e.propagate,u=[],h=0;h<l;++h)a=null,(i=(n=t.getDatasetMeta(h)).dataset)&&i._model&&i instanceof vt.Line&&(a={visible:t.isDatasetVisible(h),fill:function(t,e,n){var i,a=t._model||{},r=a.fill;if(void 0===r&&(r=!!a.backgroundColor),!1===r||null===r)return!1;if(!0===r)return"origin";if(i=parseFloat(r,10),isFinite(i)&&Math.floor(i)===i)return"-"!==r[0]&&"+"!==r[0]||(i=e+i),!(i===e||i<0||n<=i)&&i;switch(r){case"bottom":return"start";case"top":return"end";case"zero":return"origin";case"origin":case"start":case"end":return r;default:return!1}}(i,h,l),chart:t,el:i}),n.$filler=a,u.push(a);for(h=0;h<l;++h)(a=u[h])&&(a.fill=function(t,e,n){var i,a=t[e].fill,r=[e];if(!n)return a;for(;!1!==a&&-1===r.indexOf(a);){if(!isFinite(a))return a;if(!(i=t[a]))return!1;if(i.visible)return a;r.push(a),a=i.fill}return!1}(u,h,d),a.boundary=function(u){return((u.el._scale||{}).getPointPositionForValue?function(){var t,e,n,i,a,r=u.el._scale,o=r.options,s=r.chart.data.labels.length,l=u.fill,d=[];if(!s)return null;for(t=o.ticks.reverse?r.max:r.min,e=o.ticks.reverse?r.min:r.max,n=r.getPointPositionForValue(0,t),i=0;i<s;++i)a="start"===l||"end"===l?r.getPointPositionForValue(i,"start"===l?t:e):r.getBasePosition(i),o.gridLines.circular&&(a.cx=n.x,a.cy=n.y,a.angle=r.getIndexAngle(i)-Math.PI/2),d.push(a);return d}:function(){var t,e=u.el._model||{},n=u.el._scale||{},i=u.fill,a=null;if(isFinite(i))return null;if("start"===i?a=void 0===e.scaleBottom?n.bottom:e.scaleBottom:"end"===i?a=void 0===e.scaleTop?n.top:e.scaleTop:void 0!==e.scaleZero?a=e.scaleZero:n.getBasePixel&&(a=n.getBasePixel()),null!=a){if(void 0!==a.x&&void 0!==a.y)return a;if(W.isFinite(a))return{x:(t=n.isHorizontal())?a:null,y:t?null:a}}return null})()}(a),a.mapper=(s=o=void 0,o=(r=a).fill,!(s="dataset")===o?null:(isFinite(o)||(s="boundary"),Hn[s](r))))},beforeDatasetsDraw:function(t){for(var e,n,i,a,r,o,s=t._getSortedVisibleDatasetMetas(),l=t.ctx,d=s.length-1;0<=d;--d)(e=s[d].$filler)&&e.visible&&(i=(n=e.el)._view,a=n._children||[],r=e.mapper,o=i.backgroundColor||N.global.defaultColor,r&&o&&a.length&&(W.canvas.clipArea(l,t.chartArea),function(t,e,n,i,a,r){var o,s,l,d,u,h,c,f,g=e.length,p=i.spanGaps,m=[],v=[],b=0,x=0;for(t.beginPath(),o=0,s=g;o<s;++o)u=n(d=e[l=o%g]._view,l,i),h=jn(d),c=jn(u),r&&void 0===f&&h&&(s=g+(f=o+1)),h&&c?(b=m.push(d),x=v.push(u)):b&&x&&(p?(h&&m.push(d),c&&v.push(u)):(qn(t,m,v,b,x),b=x=0,m=[],v=[]));qn(t,m,v,b,x),t.closePath(),t.fillStyle=a,t.fill()}(l,a,r,i,o,n._loop),W.canvas.unclipArea(l)))}},Yn=W.rtl.getRtlAdapter,Gn=W.noop,Xn=W.valueOrDefault;function Kn(t,e){return t.usePointStyle&&t.boxWidth>e?e:t.boxWidth}N._set("global",{legend:{display:!0,position:"top",align:"center",fullWidth:!0,reverse:!1,weight:1e3,onClick:function(t,e){var n=e.datasetIndex,i=this.chart,a=i.getDatasetMeta(n);a.hidden=null===a.hidden?!i.data.datasets[n].hidden:null,i.update()},onHover:null,onLeave:null,labels:{boxWidth:40,padding:10,generateLabels:function(n){var i=n.data.datasets,t=n.options.legend||{},a=t.labels&&t.labels.usePointStyle;return n._getSortedDatasetMetas().map(function(t){var e=t.controller.getStyle(a?0:void 0);return{text:i[t.index].label,fillStyle:e.backgroundColor,hidden:!n.isDatasetVisible(t.index),lineCap:e.borderCapStyle,lineDash:e.borderDash,lineDashOffset:e.borderDashOffset,lineJoin:e.borderJoinStyle,lineWidth:e.borderWidth,strokeStyle:e.borderColor,pointStyle:e.pointStyle,rotation:e.rotation,datasetIndex:t.index}},this)}}},legendCallback:function(t){var e,n,i,a=document.createElement("ul"),r=t.data.datasets;for(a.setAttribute("class",t.id+"-legend"),e=0,n=r.length;e<n;e++)(i=a.appendChild(document.createElement("li"))).appendChild(document.createElement("span")).style.backgroundColor=r[e].backgroundColor,r[e].label&&i.appendChild(document.createTextNode(r[e].label));return a.outerHTML}});var Zn=Y.extend({initialize:function(t){W.extend(this,t),this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1},beforeUpdate:Gn,update:function(t,e,n){var i=this;return i.beforeUpdate(),i.maxWidth=t,i.maxHeight=e,i.margins=n,i.beforeSetDimensions(),i.setDimensions(),i.afterSetDimensions(),i.beforeBuildLabels(),i.buildLabels(),i.afterBuildLabels(),i.beforeFit(),i.fit(),i.afterFit(),i.afterUpdate(),i.minSize},afterUpdate:Gn,beforeSetDimensions:Gn,setDimensions:function(){var t=this;t.isHorizontal()?(t.width=t.maxWidth,t.left=0,t.right=t.width):(t.height=t.maxHeight,t.top=0,t.bottom=t.height),t.paddingLeft=0,t.paddingTop=0,t.paddingRight=0,t.paddingBottom=0,t.minSize={width:0,height:0}},afterSetDimensions:Gn,beforeBuildLabels:Gn,buildLabels:function(){var e=this,n=e.options.labels||{},t=W.callback(n.generateLabels,[e.chart],e)||[];n.filter&&(t=t.filter(function(t){return n.filter(t,e.chart.data)})),e.options.reverse&&t.reverse(),e.legendItems=t},afterBuildLabels:Gn,beforeFit:Gn,fit:function(){var i,a,r,o,s,l,d,u,t=this,e=t.options,h=e.labels,n=e.display,c=t.ctx,f=W.options._parseFont(h),g=f.size,p=t.legendHitBoxes=[],m=t.minSize,v=t.isHorizontal();v?(m.width=t.maxWidth,m.height=n?10:0):(m.width=n?10:0,m.height=t.maxHeight),n?(c.font=f.string,v?(i=t.lineWidths=[0],a=0,c.textAlign="left",c.textBaseline="middle",W.each(t.legendItems,function(t,e){var n=Kn(h,g)+g/2+c.measureText(t.text).width;(0===e||i[i.length-1]+n+2*h.padding>m.width)&&(a+=g+h.padding,i[i.length-(0<e?0:1)]=0),p[e]={left:0,top:0,width:n,height:g},i[i.length-1]+=n+h.padding}),m.height+=a):(r=h.padding,o=t.columnWidths=[],s=t.columnHeights=[],l=h.padding,u=d=0,W.each(t.legendItems,function(t,e){var n=Kn(h,g)+g/2+c.measureText(t.text).width;0<e&&u+g+2*r>m.height&&(l+=d+h.padding,o.push(d),s.push(u),u=d=0),d=Math.max(d,n),u+=g+r,p[e]={left:0,top:0,width:n,height:g}}),l+=d,o.push(d),s.push(u),m.width+=l),t.width=m.width,t.height=m.height):t.width=m.width=t.height=m.height=0},afterFit:Gn,isHorizontal:function(){return"top"===this.options.position||"bottom"===this.options.position},draw:function(){var y,_,t,e,k,w,M,S,C,P,A,D=this,n=D.options,T=n.labels,i=N.global,I=i.defaultColor,F=i.elements.line,L=D.height,O=D.columnHeights,R=D.width,z=D.lineWidths;n.display&&(y=Yn(n.rtl,D.left,D.minSize.width),_=D.ctx,t=Xn(T.fontColor,i.defaultFontColor),e=W.options._parseFont(T),k=e.size,_.textAlign=y.textAlign("left"),_.textBaseline="middle",_.lineWidth=.5,_.strokeStyle=t,_.fillStyle=t,_.font=e.string,w=Kn(T,k),M=D.legendHitBoxes,S=function(t,e){switch(n.align){case"start":return T.padding;case"end":return t-e;default:return(t-e+T.padding)/2}},C=D.isHorizontal(),P=C?{x:D.left+S(R,z[0]),y:D.top+T.padding,line:0}:{x:D.left+T.padding,y:D.top+S(L,O[0]),line:0},W.rtl.overrideTextDirection(D.ctx,n.textDirection),A=k+T.padding,W.each(D.legendItems,function(t,e){var n=_.measureText(t.text).width,i=w+k/2+n,a=P.x,r=P.y;y.setWidth(D.minSize.width),C?0<e&&a+i+T.padding>D.left+D.minSize.width&&(r=P.y+=A,P.line++,a=P.x=D.left+S(R,z[P.line])):0<e&&r+A>D.top+D.minSize.height&&(a=P.x=a+D.columnWidths[P.line]+T.padding,P.line++,r=P.y=D.top+S(L,O[P.line]));var o,s,l,d,u,h,c,f,g,p,m,v,b,x=y.x(a);c=x,f=r,g=t,isNaN(w)||w<=0||(_.save(),p=Xn(g.lineWidth,F.borderWidth),_.fillStyle=Xn(g.fillStyle,I),_.lineCap=Xn(g.lineCap,F.borderCapStyle),_.lineDashOffset=Xn(g.lineDashOffset,F.borderDashOffset),_.lineJoin=Xn(g.lineJoin,F.borderJoinStyle),_.lineWidth=p,_.strokeStyle=Xn(g.strokeStyle,I),_.setLineDash&&_.setLineDash(Xn(g.lineDash,F.borderDash)),T&&T.usePointStyle?(m=w*Math.SQRT2/2,v=y.xPlus(c,w/2),b=f+k/2,W.canvas.drawPoint(_,g.pointStyle,m,v,b,g.rotation)):(_.fillRect(y.leftForLtr(c,w),f,w,k),0!==p&&_.strokeRect(y.leftForLtr(c,w),f,w,k)),_.restore()),M[e].left=y.leftForLtr(x,M[e].width),M[e].top=r,o=r,s=t,l=n,d=k/2,u=y.xPlus(x,w+d),h=o+d,_.fillText(s.text,u,h),s.hidden&&(_.beginPath(),_.lineWidth=2,_.moveTo(u,h),_.lineTo(y.xPlus(u,l),h),_.stroke()),C?P.x+=i+T.padding:P.y+=A}),W.rtl.restoreTextDirection(D.ctx,n.textDirection))},_getLegendItemAt:function(t,e){var n,i,a,r=this;if(t>=r.left&&t<=r.right&&e>=r.top&&e<=r.bottom)for(a=r.legendHitBoxes,n=0;n<a.length;++n)if(t>=(i=a[n]).left&&t<=i.left+i.width&&e>=i.top&&e<=i.top+i.height)return r.legendItems[n];return null},handleEvent:function(t){var e,n=this,i=n.options,a="mouseup"===t.type?"click":t.type;if("mousemove"===a){if(!i.onHover&&!i.onLeave)return}else{if("click"!==a)return;if(!i.onClick)return}e=n._getLegendItemAt(t.x,t.y),"click"===a?e&&i.onClick&&i.onClick.call(n,t.native,e):(i.onLeave&&e!==n._hoveredItem&&(n._hoveredItem&&i.onLeave.call(n,t.native,n._hoveredItem),n._hoveredItem=e),i.onHover&&e&&i.onHover.call(n,t.native,e))}});function $n(t,e){var n=new Zn({ctx:t.ctx,options:e,chart:t});re.configure(t,n,e),re.addBox(t,n),t.legend=n}var Jn={id:"legend",_element:Zn,beforeInit:function(t){var e=t.options.legend;e&&$n(t,e)},beforeUpdate:function(t){var e=t.options.legend,n=t.legend;e?(W.mergeIf(e,N.global.legend),n?(re.configure(t,n,e),n.options=e):$n(t,e)):n&&(re.removeBox(t,n),delete t.legend)},afterEvent:function(t,e){var n=t.legend;n&&n.handleEvent(e)}},Qn=W.noop;N._set("global",{title:{display:!1,fontStyle:"bold",fullWidth:!0,padding:10,position:"top",text:"",weight:2e3}});var ti=Y.extend({initialize:function(t){W.extend(this,t),this.legendHitBoxes=[]},beforeUpdate:Qn,update:function(t,e,n){var i=this;return i.beforeUpdate(),i.maxWidth=t,i.maxHeight=e,i.margins=n,i.beforeSetDimensions(),i.setDimensions(),i.afterSetDimensions(),i.beforeBuildLabels(),i.buildLabels(),i.afterBuildLabels(),i.beforeFit(),i.fit(),i.afterFit(),i.afterUpdate(),i.minSize},afterUpdate:Qn,beforeSetDimensions:Qn,setDimensions:function(){var t=this;t.isHorizontal()?(t.width=t.maxWidth,t.left=0,t.right=t.width):(t.height=t.maxHeight,t.top=0,t.bottom=t.height),t.paddingLeft=0,t.paddingTop=0,t.paddingRight=0,t.paddingBottom=0,t.minSize={width:0,height:0}},afterSetDimensions:Qn,beforeBuildLabels:Qn,buildLabels:Qn,afterBuildLabels:Qn,beforeFit:Qn,fit:function(){var t,e=this,n=e.options,i=e.minSize={},a=e.isHorizontal();n.display?(t=(W.isArray(n.text)?n.text.length:1)*W.options._parseFont(n).lineHeight+2*n.padding,e.width=i.width=a?e.maxWidth:t,e.height=i.height=a?t:e.maxHeight):e.width=i.width=e.height=i.height=0},afterFit:Qn,isHorizontal:function(){var t=this.options.position;return"top"===t||"bottom"===t},draw:function(){var t=this,e=t.ctx,n=t.options;if(n.display){var i,a,r,o=W.options._parseFont(n),s=o.lineHeight,l=s/2+n.padding,d=0,u=t.top,h=t.left,c=t.bottom,f=t.right;e.fillStyle=W.valueOrDefault(n.fontColor,N.global.defaultFontColor),e.font=o.string,t.isHorizontal()?(a=h+(f-h)/2,r=u+l,i=f-h):(a="left"===n.position?h+l:f-l,r=u+(c-u)/2,i=c-u,d=Math.PI*("left"===n.position?-.5:.5)),e.save(),e.translate(a,r),e.rotate(d),e.textAlign="center",e.textBaseline="middle";var g=n.text;if(W.isArray(g))for(var p=0,m=0;m<g.length;++m)e.fillText(g[m],0,p,i),p+=s;else e.fillText(g,0,0,i);e.restore()}}});function ei(t,e){var n=new ti({ctx:t.ctx,options:e,chart:t});re.configure(t,n,e),re.addBox(t,n),t.titleBlock=n}var ni={},ii=Un,ai=Jn,ri={id:"title",_element:ti,beforeInit:function(t){var e=t.options.title;e&&ei(t,e)},beforeUpdate:function(t){var e=t.options.title,n=t.titleBlock;e?(W.mergeIf(e,N.global.title),n?(re.configure(t,n,e),n.options=e):ei(t,e)):n&&(re.removeBox(t,n),delete t.titleBlock)}};for(var oi in ni.filler=ii,ni.legend=ai,ni.title=ri,(We.helpers=W).where=function(t,e){if(W.isArray(t)&&Array.prototype.filter)return t.filter(e);var n=[];return W.each(t,function(t){e(t)&&n.push(t)}),n},W.findIndex=Array.prototype.findIndex?function(t,e,n){return t.findIndex(e,n)}:function(t,e,n){n=void 0===n?t:n;for(var i=0,a=t.length;i<a;++i)if(e.call(n,t[i],i,t))return i;return-1},W.findNextWhere=function(t,e,n){W.isNullOrUndef(n)&&(n=-1);for(var i=n+1;i<t.length;i++){var a=t[i];if(e(a))return a}},W.findPreviousWhere=function(t,e,n){W.isNullOrUndef(n)&&(n=t.length);for(var i=n-1;0<=i;i--){var a=t[i];if(e(a))return a}},W.isNumber=function(t){return!isNaN(parseFloat(t))&&isFinite(t)},W.almostEquals=function(t,e,n){return Math.abs(t-e)<n},W.almostWhole=function(t,e){var n=Math.round(t);return n-e<=t&&t<=n+e},W.max=function(t){return t.reduce(function(t,e){return isNaN(e)?t:Math.max(t,e)},Number.NEGATIVE_INFINITY)},W.min=function(t){return t.reduce(function(t,e){return isNaN(e)?t:Math.min(t,e)},Number.POSITIVE_INFINITY)},W.sign=Math.sign?function(t){return Math.sign(t)}:function(t){return 0==(t=+t)||isNaN(t)?t:0<t?1:-1},W.toRadians=function(t){return t*(Math.PI/180)},W.toDegrees=function(t){return t*(180/Math.PI)},W._decimalPlaces=function(t){if(W.isFinite(t)){for(var e=1,n=0;Math.round(t*e)/e!==t;)e*=10,n++;return n}},W.getAngleFromPoint=function(t,e){var n=e.x-t.x,i=e.y-t.y,a=Math.sqrt(n*n+i*i),r=Math.atan2(i,n);return r<-.5*Math.PI&&(r+=2*Math.PI),{angle:r,distance:a}},W.distanceBetweenPoints=function(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))},W.aliasPixel=function(t){return t%2==0?0:.5},W._alignPixel=function(t,e,n){var i=t.currentDevicePixelRatio,a=n/2;return Math.round((e-a)*i)/i+a},W.splineCurve=function(t,e,n,i){var a=t.skip?e:t,r=e,o=n.skip?e:n,s=Math.sqrt(Math.pow(r.x-a.x,2)+Math.pow(r.y-a.y,2)),l=Math.sqrt(Math.pow(o.x-r.x,2)+Math.pow(o.y-r.y,2)),d=s/(s+l),u=l/(s+l),h=i*(d=isNaN(d)?0:d),c=i*(u=isNaN(u)?0:u);return{previous:{x:r.x-h*(o.x-a.x),y:r.y-h*(o.y-a.y)},next:{x:r.x+c*(o.x-a.x),y:r.y+c*(o.y-a.y)}}},W.EPSILON=Number.EPSILON||1e-14,W.splineCurveMonotone=function(t){for(var e,n,i,a,r,o,s,l,d,u=(t||[]).map(function(t){return{model:t._model,deltaK:0,mK:0}}),h=u.length,c=0;c<h;++c){(e=u[c]).model.skip||(d=0<c?u[c-1]:null,(n=c<h-1?u[c+1]:null)&&!n.model.skip&&(l=n.model.x-e.model.x,e.deltaK=0!=l?(n.model.y-e.model.y)/l:0),!d||d.model.skip?e.mK=e.deltaK:!n||n.model.skip?e.mK=d.deltaK:this.sign(d.deltaK)!==this.sign(e.deltaK)?e.mK=0:e.mK=(d.deltaK+e.deltaK)/2)}for(c=0;c<h-1;++c)e=u[c],n=u[c+1],e.model.skip||n.model.skip||(W.almostEquals(e.deltaK,0,this.EPSILON)?e.mK=n.mK=0:(i=e.mK/e.deltaK,a=n.mK/e.deltaK,(o=Math.pow(i,2)+Math.pow(a,2))<=9||(r=3/Math.sqrt(o),e.mK=i*r*e.deltaK,n.mK=a*r*e.deltaK)));for(c=0;c<h;++c)(e=u[c]).model.skip||(d=0<c?u[c-1]:null,n=c<h-1?u[c+1]:null,d&&!d.model.skip&&(s=(e.model.x-d.model.x)/3,e.model.controlPointPreviousX=e.model.x-s,e.model.controlPointPreviousY=e.model.y-s*e.mK),n&&!n.model.skip&&(s=(n.model.x-e.model.x)/3,e.model.controlPointNextX=e.model.x+s,e.model.controlPointNextY=e.model.y+s*e.mK))},W.nextItem=function(t,e,n){return n?e>=t.length-1?t[0]:t[e+1]:e>=t.length-1?t[t.length-1]:t[e+1]},W.previousItem=function(t,e,n){return n?e<=0?t[t.length-1]:t[e-1]:e<=0?t[0]:t[e-1]},W.niceNum=function(t,e){var n=Math.floor(W.log10(t)),i=t/Math.pow(10,n);return(e?i<1.5?1:i<3?2:i<7?5:10:i<=1?1:i<=2?2:i<=5?5:10)*Math.pow(10,n)},W.requestAnimFrame="undefined"==typeof window?function(t){t()}:window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t){return window.setTimeout(t,1e3/60)},W.getRelativePosition=function(t,e){var n,i=t.originalEvent||t,a=t.target||t.srcElement,r=a.getBoundingClientRect(),o=i.touches,s=o&&0<o.length?(n=o[0].clientX,o[0].clientY):(n=i.clientX,i.clientY),l=parseFloat(W.getStyle(a,"padding-left")),d=parseFloat(W.getStyle(a,"padding-top")),u=parseFloat(W.getStyle(a,"padding-right")),h=parseFloat(W.getStyle(a,"padding-bottom")),c=r.right-r.left-l-u,f=r.bottom-r.top-d-h;return{x:n=Math.round((n-r.left-l)/c*a.width/e.currentDevicePixelRatio),y:s=Math.round((s-r.top-d)/f*a.height/e.currentDevicePixelRatio)}},W.getConstraintWidth=function(t){return di(t,"max-width","clientWidth")},W.getConstraintHeight=function(t){return di(t,"max-height","clientHeight")},W._calculatePadding=function(t,e,n){return-1<(e=W.getStyle(t,e)).indexOf("%")?n*parseInt(e,10)/100:parseInt(e,10)},W._getParentNode=function(t){var e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e},W.getMaximumWidth=function(t){var e=W._getParentNode(t);if(!e)return t.clientWidth;var n=e.clientWidth,i=n-W._calculatePadding(e,"padding-left",n)-W._calculatePadding(e,"padding-right",n),a=W.getConstraintWidth(t);return isNaN(a)?i:Math.min(i,a)},W.getMaximumHeight=function(t){var e=W._getParentNode(t);if(!e)return t.clientHeight;var n=e.clientHeight,i=n-W._calculatePadding(e,"padding-top",n)-W._calculatePadding(e,"padding-bottom",n),a=W.getConstraintHeight(t);return isNaN(a)?i:Math.min(i,a)},W.getStyle=function(t,e){return t.currentStyle?t.currentStyle[e]:document.defaultView.getComputedStyle(t,null).getPropertyValue(e)},W.retinaScale=function(t,e){var n,i,a,r=t.currentDevicePixelRatio=e||"undefined"!=typeof window&&window.devicePixelRatio||1;1!==r&&(n=t.canvas,i=t.height,a=t.width,n.height=i*r,n.width=a*r,t.ctx.scale(r,r),n.style.height||n.style.width||(n.style.height=i+"px",n.style.width=a+"px"))},W.fontString=function(t,e,n){return e+" "+t+"px "+n},W.longestText=function(t,e,n,i){var a=(i=i||{}).data=i.data||{},r=i.garbageCollect=i.garbageCollect||[];i.font!==e&&(a=i.data={},r=i.garbageCollect=[],i.font=e),t.font=e;for(var o,s,l,d,u=0,h=n.length,c=0;c<h;c++)if(null!=(l=n[c])&&!0!==W.isArray(l))u=W.measureText(t,a,r,u,l);else if(W.isArray(l))for(o=0,s=l.length;o<s;o++)null==(d=l[o])||W.isArray(d)||(u=W.measureText(t,a,r,u,d));var f=r.length/2;if(f>n.length){for(c=0;c<f;c++)delete a[r[c]];r.splice(0,f)}return u},W.measureText=function(t,e,n,i,a){var r=e[a];return r||(r=e[a]=t.measureText(a).width,n.push(a)),i<r&&(i=r),i},W.numberOfLabelLines=function(t){var e=1;return W.each(t,function(t){W.isArray(t)&&t.length>e&&(e=t.length)}),e},W.color=_?function(t){return t instanceof CanvasGradient&&(t=N.global.defaultColor),_(t)}:function(t){return console.error("Color.js not found!"),t},W.getHoverColor=function(t){return t instanceof CanvasPattern||t instanceof CanvasGradient?t:W.color(t).saturate(.5).darken(.1).rgbString()},We._adapters=je,We.Animation=X,We.animationService=K,We.controllers=jt,We.DatasetController=tt,We.defaults=N,We.Element=Y,We.elements=vt,We.Interaction=Zt,We.layouts=re,We.platform=_e,We.plugins=ke,We.Scale=nn,We.scaleService=we,We.Ticks=qe,We.Tooltip=Fe,We.helpers.each(Wn,function(t,e){We.scaleService.registerScaleType(e,t,t._defaults)}),ni)ni.hasOwnProperty(oi)&&We.plugins.register(ni[oi]);function si(t,e,n){var i;return"string"==typeof t?(i=parseInt(t,10),-1!==t.indexOf("%")&&(i=i/100*e.parentNode[n])):i=t,i}function li(t){return null!=t&&"none"!==t}function di(t,e,n){var i=document.defaultView,a=W._getParentNode(t),r=i.getComputedStyle(t)[e],o=i.getComputedStyle(a)[e],s=li(r),l=li(o),d=Number.POSITIVE_INFINITY;return s||l?Math.min(s?si(r,t,n):d,l?si(o,a,n):d):"none"}We.platform.initialize();var ui=We;return"undefined"!=typeof window&&(window.Chart=We),(We.Chart=We).Legend=ni.legend._element,We.Title=ni.title._element,We.pluginService=We.plugins,We.PluginBase=We.Element.extend({}),We.canvasHelpers=We.helpers.canvas,We.layoutService=We.layouts,We.LinearScaleBase=ln,We.helpers.each(["Bar","Bubble","Doughnut","Line","PolarArea","Radar","Scatter"],function(n){We[n]=function(t,e){return new We(t,We.helpers.merge(e||{},{type:n.charAt(0).toLowerCase()+n.slice(1)}))}}),ui});