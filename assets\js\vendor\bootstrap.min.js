!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],e):e((t=t||self).bootstrap={},t.j<PERSON><PERSON>y,t.Pop<PERSON>)}(this,function(t,g,u){"use strict";function i(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function s(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}function e(e,t){var n,i=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),i.push.apply(i,n)),i}function r(o){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?e(Object(s),!0).forEach(function(t){var e,n,i;e=o,i=s[n=t],n in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(s)):e(Object(s)).forEach(function(t){Object.defineProperty(o,t,Object.getOwnPropertyDescriptor(s,t))})}return o}g=g&&Object.prototype.hasOwnProperty.call(g,"default")?g.default:g,u=u&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u;var m={TRANSITION_END:"bsTransitionEnd",getUID:function(t){for(;t+=~~(1e6*Math.random()),document.getElementById(t););return t},getSelectorFromElement:function(t){var e,n=t.getAttribute("data-target");n&&"#"!==n||(n=(e=t.getAttribute("href"))&&"#"!==e?e.trim():"");try{return document.querySelector(n)?n:null}catch(t){return null}},getTransitionDurationFromElement:function(t){if(!t)return 0;var e=g(t).css("transition-duration"),n=g(t).css("transition-delay"),i=parseFloat(e),o=parseFloat(n);return i||o?(e=e.split(",")[0],n=n.split(",")[0],1e3*(parseFloat(e)+parseFloat(n))):0},reflow:function(t){return t.offsetHeight},triggerTransitionEnd:function(t){g(t).trigger("transitionend")},supportsTransitionEnd:function(){return Boolean("transitionend")},isElement:function(t){return(t[0]||t).nodeType},typeCheckConfig:function(t,e,n){for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)){var o=n[i],s=e[i],r=s&&m.isElement(s)?"element":null===s||void 0===s?""+s:{}.toString.call(s).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(o).test(r))throw new Error(t.toUpperCase()+': Option "'+i+'" provided type "'+r+'" but expected type "'+o+'".')}},findShadowRoot:function(t){if(!document.documentElement.attachShadow)return null;if("function"!=typeof t.getRootNode)return t instanceof ShadowRoot?t:t.parentNode?m.findShadowRoot(t.parentNode):null;var e=t.getRootNode();return e instanceof ShadowRoot?e:null},jQueryDetection:function(){if(void 0===g)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var t=g.fn.jquery.split(" ")[0].split(".");if(t[0]<2&&t[1]<9||1===t[0]&&9===t[1]&&t[2]<1||4<=t[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};m.jQueryDetection(),g.fn.emulateTransitionEnd=function(t){var e=this,n=!1;return g(this).one(m.TRANSITION_END,function(){n=!0}),setTimeout(function(){n||m.triggerTransitionEnd(e)},t),this},g.event.special[m.TRANSITION_END]={bindType:"transitionend",delegateType:"transitionend",handle:function(t){if(g(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}};var n,o="alert",a=g.fn[o],l=((n=c.prototype).close=function(t){var e=this._element;t&&(e=this._getRootElement(t)),this._triggerCloseEvent(e).isDefaultPrevented()||this._removeElement(e)},n.dispose=function(){g.removeData(this._element,"bs.alert"),this._element=null},n._getRootElement=function(t){var e=m.getSelectorFromElement(t),n=!1;return e&&(n=document.querySelector(e)),n=n||g(t).closest(".alert")[0]},n._triggerCloseEvent=function(t){var e=g.Event("close.bs.alert");return g(t).trigger(e),e},n._removeElement=function(e){var t,n=this;g(e).removeClass("show"),g(e).hasClass("fade")?(t=m.getTransitionDurationFromElement(e),g(e).one(m.TRANSITION_END,function(t){return n._destroyElement(e,t)}).emulateTransitionEnd(t)):this._destroyElement(e)},n._destroyElement=function(t){g(t).detach().trigger("closed.bs.alert").remove()},c._jQueryInterface=function(n){return this.each(function(){var t=g(this),e=t.data("bs.alert");e||(e=new c(this),t.data("bs.alert",e)),"close"===n&&e[n](this)})},c._handleDismiss=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},s(c,null,[{key:"VERSION",get:function(){return"4.5.0"}}]),c);function c(t){this._element=t}g(document).on("click.bs.alert.data-api",'[data-dismiss="alert"]',l._handleDismiss(new l)),g.fn[o]=l._jQueryInterface,g.fn[o].Constructor=l,g.fn[o].noConflict=function(){return g.fn[o]=a,l._jQueryInterface};var h,d=g.fn.button,f=((h=p.prototype).toggle=function(){var t,e,n=!0,i=!0,o=g(this._element).closest('[data-toggle="buttons"]')[0];!o||(t=this._element.querySelector('input:not([type="hidden"])'))&&("radio"===t.type&&(t.checked&&this._element.classList.contains("active")?n=!1:(e=o.querySelector(".active"))&&g(e).removeClass("active")),n&&("checkbox"!==t.type&&"radio"!==t.type||(t.checked=!this._element.classList.contains("active")),g(t).trigger("change")),t.focus(),i=!1),this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(i&&this._element.setAttribute("aria-pressed",!this._element.classList.contains("active")),n&&g(this._element).toggleClass("active"))},h.dispose=function(){g.removeData(this._element,"bs.button"),this._element=null},p._jQueryInterface=function(e){return this.each(function(){var t=g(this).data("bs.button");t||(t=new p(this),g(this).data("bs.button",t)),"toggle"===e&&t[e]()})},s(p,null,[{key:"VERSION",get:function(){return"4.5.0"}}]),p);function p(t){this._element=t}g(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(t){var e=t.target,n=e;if(g(e).hasClass("btn")||(e=g(e).closest(".btn")[0]),!e||e.hasAttribute("disabled")||e.classList.contains("disabled"))t.preventDefault();else{var i=e.querySelector('input:not([type="hidden"])');if(i&&(i.hasAttribute("disabled")||i.classList.contains("disabled")))return void t.preventDefault();"LABEL"===n.tagName&&i&&"checkbox"===i.type&&t.preventDefault(),f._jQueryInterface.call(g(e),"toggle")}}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(t){var e=g(t.target).closest(".btn")[0];g(e).toggleClass("focus",/^focus(in)?$/.test(t.type))}),g(window).on("load.bs.button.data-api",function(){for(var t=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),e=0,n=t.length;e<n;e++){var i=t[e],o=i.querySelector('input:not([type="hidden"])');o.checked||o.hasAttribute("checked")?i.classList.add("active"):i.classList.remove("active")}for(var s=0,r=(t=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;s<r;s++){var a=t[s];"true"===a.getAttribute("aria-pressed")?a.classList.add("active"):a.classList.remove("active")}}),g.fn.button=f._jQueryInterface,g.fn.button.Constructor=f,g.fn.button.noConflict=function(){return g.fn.button=d,f._jQueryInterface};var _,v="carousel",b=g.fn[v],y={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},E={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},w={TOUCH:"touch",PEN:"pen"},T=((_=C.prototype).next=function(){this._isSliding||this._slide("next")},_.nextWhenVisible=function(){!document.hidden&&g(this._element).is(":visible")&&"hidden"!==g(this._element).css("visibility")&&this.next()},_.prev=function(){this._isSliding||this._slide("prev")},_.pause=function(t){t||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(m.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},_.cycle=function(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},_.to=function(t){var e=this;this._activeElement=this._element.querySelector(".active.carousel-item");var n=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||t<0))if(this._isSliding)g(this._element).one("slid.bs.carousel",function(){return e.to(t)});else{if(n===t)return this.pause(),void this.cycle();var i=n<t?"next":"prev";this._slide(i,this._items[t])}},_.dispose=function(){g(this._element).off(".bs.carousel"),g.removeData(this._element,"bs.carousel"),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},_._getConfig=function(t){return t=r(r({},y),t),m.typeCheckConfig(v,t,E),t},_._handleSwipe=function(){var t,e=Math.abs(this.touchDeltaX);e<=40||(t=e/this.touchDeltaX,(this.touchDeltaX=0)<t&&this.prev(),t<0&&this.next())},_._addEventListeners=function(){var e=this;this._config.keyboard&&g(this._element).on("keydown.bs.carousel",function(t){return e._keydown(t)}),"hover"===this._config.pause&&g(this._element).on("mouseenter.bs.carousel",function(t){return e.pause(t)}).on("mouseleave.bs.carousel",function(t){return e.cycle(t)}),this._config.touch&&this._addTouchEventListeners()},_._addTouchEventListeners=function(){var t,e,n=this;this._touchSupported&&(t=function(t){n._pointerEvent&&w[t.originalEvent.pointerType.toUpperCase()]?n.touchStartX=t.originalEvent.clientX:n._pointerEvent||(n.touchStartX=t.originalEvent.touches[0].clientX)},e=function(t){n._pointerEvent&&w[t.originalEvent.pointerType.toUpperCase()]&&(n.touchDeltaX=t.originalEvent.clientX-n.touchStartX),n._handleSwipe(),"hover"===n._config.pause&&(n.pause(),n.touchTimeout&&clearTimeout(n.touchTimeout),n.touchTimeout=setTimeout(function(t){return n.cycle(t)},500+n._config.interval))},g(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel",function(t){return t.preventDefault()}),this._pointerEvent?(g(this._element).on("pointerdown.bs.carousel",t),g(this._element).on("pointerup.bs.carousel",e),this._element.classList.add("pointer-event")):(g(this._element).on("touchstart.bs.carousel",t),g(this._element).on("touchmove.bs.carousel",function(t){var e;(e=t).originalEvent.touches&&1<e.originalEvent.touches.length?n.touchDeltaX=0:n.touchDeltaX=e.originalEvent.touches[0].clientX-n.touchStartX}),g(this._element).on("touchend.bs.carousel",e)))},_._keydown=function(t){if(!/input|textarea/i.test(t.target.tagName))switch(t.which){case 37:t.preventDefault(),this.prev();break;case 39:t.preventDefault(),this.next()}},_._getItemIndex=function(t){return this._items=t&&t.parentNode?[].slice.call(t.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(t)},_._getItemByDirection=function(t,e){var n="next"===t,i="prev"===t,o=this._getItemIndex(e),s=this._items.length-1;if((i&&0===o||n&&o===s)&&!this._config.wrap)return e;var r=(o+("prev"===t?-1:1))%this._items.length;return-1==r?this._items[this._items.length-1]:this._items[r]},_._triggerSlideEvent=function(t,e){var n=this._getItemIndex(t),i=this._getItemIndex(this._element.querySelector(".active.carousel-item")),o=g.Event("slide.bs.carousel",{relatedTarget:t,direction:e,from:i,to:n});return g(this._element).trigger(o),o},_._setActiveIndicatorElement=function(t){var e,n;this._indicatorsElement&&(e=[].slice.call(this._indicatorsElement.querySelectorAll(".active")),g(e).removeClass("active"),(n=this._indicatorsElement.children[this._getItemIndex(t)])&&g(n).addClass("active"))},_._slide=function(t,e){var n,i,o,s,r,a=this,l=this._element.querySelector(".active.carousel-item"),c=this._getItemIndex(l),h=e||l&&this._getItemByDirection(t,l),u=this._getItemIndex(h),d=Boolean(this._interval),f="next"===t?(n="carousel-item-left",i="carousel-item-next","left"):(n="carousel-item-right",i="carousel-item-prev","right");h&&g(h).hasClass("active")?this._isSliding=!1:!this._triggerSlideEvent(h,f).isDefaultPrevented()&&l&&h&&(this._isSliding=!0,d&&this.pause(),this._setActiveIndicatorElement(h),o=g.Event("slid.bs.carousel",{relatedTarget:h,direction:f,from:c,to:u}),g(this._element).hasClass("slide")?(g(h).addClass(i),m.reflow(h),g(l).addClass(n),g(h).addClass(n),(s=parseInt(h.getAttribute("data-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=s):this._config.interval=this._config.defaultInterval||this._config.interval,r=m.getTransitionDurationFromElement(l),g(l).one(m.TRANSITION_END,function(){g(h).removeClass(n+" "+i).addClass("active"),g(l).removeClass("active "+i+" "+n),a._isSliding=!1,setTimeout(function(){return g(a._element).trigger(o)},0)}).emulateTransitionEnd(r)):(g(l).removeClass("active"),g(h).addClass("active"),this._isSliding=!1,g(this._element).trigger(o)),d&&this.cycle())},C._jQueryInterface=function(i){return this.each(function(){var t=g(this).data("bs.carousel"),e=r(r({},y),g(this).data());"object"==typeof i&&(e=r(r({},e),i));var n="string"==typeof i?i:e.slide;if(t||(t=new C(this,e),g(this).data("bs.carousel",t)),"number"==typeof i)t.to(i);else if("string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}else e.interval&&e.ride&&(t.pause(),t.cycle())})},C._dataApiClickHandler=function(t){var e,n,i,o=m.getSelectorFromElement(this);!o||(e=g(o)[0])&&g(e).hasClass("carousel")&&(n=r(r({},g(e).data()),g(this).data()),(i=this.getAttribute("data-slide-to"))&&(n.interval=!1),C._jQueryInterface.call(g(e),n),i&&g(e).data("bs.carousel").to(i),t.preventDefault())},s(C,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return y}}]),C);function C(t,e){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(e),this._element=t,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}g(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",T._dataApiClickHandler),g(window).on("load.bs.carousel.data-api",function(){for(var t=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),e=0,n=t.length;e<n;e++){var i=g(t[e]);T._jQueryInterface.call(i,i.data())}}),g.fn[v]=T._jQueryInterface,g.fn[v].Constructor=T,g.fn[v].noConflict=function(){return g.fn[v]=b,T._jQueryInterface};var S,D="collapse",k=g.fn[D],N={toggle:!0,parent:""},A={toggle:"boolean",parent:"(string|element)"},I=((S=O.prototype).toggle=function(){g(this._element).hasClass("show")?this.hide():this.show()},S.show=function(){var t,e,n,i,o,s,r=this;this._isTransitioning||g(this._element).hasClass("show")||(this._parent&&0===(t=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(t){return"string"==typeof r._config.parent?t.getAttribute("data-parent")===r._config.parent:t.classList.contains("collapse")})).length&&(t=null),t&&(e=g(t).not(this._selector).data("bs.collapse"))&&e._isTransitioning)||(n=g.Event("show.bs.collapse"),g(this._element).trigger(n),n.isDefaultPrevented()||(t&&(O._jQueryInterface.call(g(t).not(this._selector),"hide"),e||g(t).data("bs.collapse",null)),i=this._getDimension(),g(this._element).removeClass("collapse").addClass("collapsing"),this._element.style[i]=0,this._triggerArray.length&&g(this._triggerArray).removeClass("collapsed").attr("aria-expanded",!0),this.setTransitioning(!0),o="scroll"+(i[0].toUpperCase()+i.slice(1)),s=m.getTransitionDurationFromElement(this._element),g(this._element).one(m.TRANSITION_END,function(){g(r._element).removeClass("collapsing").addClass("collapse show"),r._element.style[i]="",r.setTransitioning(!1),g(r._element).trigger("shown.bs.collapse")}).emulateTransitionEnd(s),this._element.style[i]=this._element[o]+"px"))},S.hide=function(){var t=this;if(!this._isTransitioning&&g(this._element).hasClass("show")){var e=g.Event("hide.bs.collapse");if(g(this._element).trigger(e),!e.isDefaultPrevented()){var n=this._getDimension();this._element.style[n]=this._element.getBoundingClientRect()[n]+"px",m.reflow(this._element),g(this._element).addClass("collapsing").removeClass("collapse show");var i=this._triggerArray.length;if(0<i)for(var o=0;o<i;o++){var s=this._triggerArray[o],r=m.getSelectorFromElement(s);null!==r&&(g([].slice.call(document.querySelectorAll(r))).hasClass("show")||g(s).addClass("collapsed").attr("aria-expanded",!1))}this.setTransitioning(!0),this._element.style[n]="";var a=m.getTransitionDurationFromElement(this._element);g(this._element).one(m.TRANSITION_END,function(){t.setTransitioning(!1),g(t._element).removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")}).emulateTransitionEnd(a)}}},S.setTransitioning=function(t){this._isTransitioning=t},S.dispose=function(){g.removeData(this._element,"bs.collapse"),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},S._getConfig=function(t){return(t=r(r({},N),t)).toggle=Boolean(t.toggle),m.typeCheckConfig(D,t,A),t},S._getDimension=function(){return g(this._element).hasClass("width")?"width":"height"},S._getParent=function(){var t,n=this;m.isElement(this._config.parent)?(t=this._config.parent,void 0!==this._config.parent.jquery&&(t=this._config.parent[0])):t=document.querySelector(this._config.parent);var e='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',i=[].slice.call(t.querySelectorAll(e));return g(i).each(function(t,e){n._addAriaAndCollapsedClass(O._getTargetFromElement(e),[e])}),t},S._addAriaAndCollapsedClass=function(t,e){var n=g(t).hasClass("show");e.length&&g(e).toggleClass("collapsed",!n).attr("aria-expanded",n)},O._getTargetFromElement=function(t){var e=m.getSelectorFromElement(t);return e?document.querySelector(e):null},O._jQueryInterface=function(i){return this.each(function(){var t=g(this),e=t.data("bs.collapse"),n=r(r(r({},N),t.data()),"object"==typeof i&&i?i:{});if(!e&&n.toggle&&"string"==typeof i&&/show|hide/.test(i)&&(n.toggle=!1),e||(e=new O(this,n),t.data("bs.collapse",e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}})},s(O,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return N}}]),O);function O(e,t){this._isTransitioning=!1,this._element=e,this._config=this._getConfig(t),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'));for(var n=[].slice.call(document.querySelectorAll('[data-toggle="collapse"]')),i=0,o=n.length;i<o;i++){var s=n[i],r=m.getSelectorFromElement(s),a=[].slice.call(document.querySelectorAll(r)).filter(function(t){return t===e});null!==r&&0<a.length&&(this._selector=r,this._triggerArray.push(s))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}g(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(t){"A"===t.currentTarget.tagName&&t.preventDefault();var n=g(this),e=m.getSelectorFromElement(this),i=[].slice.call(document.querySelectorAll(e));g(i).each(function(){var t=g(this),e=t.data("bs.collapse")?"toggle":n.data();I._jQueryInterface.call(t,e)})}),g.fn[D]=I._jQueryInterface,g.fn[D].Constructor=I,g.fn[D].noConflict=function(){return g.fn[D]=k,I._jQueryInterface};var j,P="dropdown",x=g.fn[P],L=new RegExp("38|40|27"),R={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},q={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},F=((j=Q.prototype).toggle=function(){var t;this._element.disabled||g(this._element).hasClass("disabled")||(t=g(this._menu).hasClass("show"),Q._clearMenus(),t||this.show(!0))},j.show=function(t){if(void 0===t&&(t=!1),!(this._element.disabled||g(this._element).hasClass("disabled")||g(this._menu).hasClass("show"))){var e={relatedTarget:this._element},n=g.Event("show.bs.dropdown",e),i=Q._getParentFromElement(this._element);if(g(i).trigger(n),!n.isDefaultPrevented()){if(!this._inNavbar&&t){if(void 0===u)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");var o=this._element;"parent"===this._config.reference?o=i:m.isElement(this._config.reference)&&(o=this._config.reference,void 0!==this._config.reference.jquery&&(o=this._config.reference[0])),"scrollParent"!==this._config.boundary&&g(i).addClass("position-static"),this._popper=new u(o,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===g(i).closest(".navbar-nav").length&&g(document.body).children().on("mouseover",null,g.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),g(this._menu).toggleClass("show"),g(i).toggleClass("show").trigger(g.Event("shown.bs.dropdown",e))}}},j.hide=function(){var t,e,n;this._element.disabled||g(this._element).hasClass("disabled")||!g(this._menu).hasClass("show")||(t={relatedTarget:this._element},e=g.Event("hide.bs.dropdown",t),n=Q._getParentFromElement(this._element),g(n).trigger(e),e.isDefaultPrevented()||(this._popper&&this._popper.destroy(),g(this._menu).toggleClass("show"),g(n).toggleClass("show").trigger(g.Event("hidden.bs.dropdown",t))))},j.dispose=function(){g.removeData(this._element,"bs.dropdown"),g(this._element).off(".bs.dropdown"),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},j.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},j._addEventListeners=function(){var e=this;g(this._element).on("click.bs.dropdown",function(t){t.preventDefault(),t.stopPropagation(),e.toggle()})},j._getConfig=function(t){return t=r(r(r({},this.constructor.Default),g(this._element).data()),t),m.typeCheckConfig(P,t,this.constructor.DefaultType),t},j._getMenuElement=function(){var t;return this._menu||(t=Q._getParentFromElement(this._element))&&(this._menu=t.querySelector(".dropdown-menu")),this._menu},j._getPlacement=function(){var t=g(this._element.parentNode),e="bottom-start";return t.hasClass("dropup")?e=g(this._menu).hasClass("dropdown-menu-right")?"top-end":"top-start":t.hasClass("dropright")?e="right-start":t.hasClass("dropleft")?e="left-start":g(this._menu).hasClass("dropdown-menu-right")&&(e="bottom-end"),e},j._detectNavbar=function(){return 0<g(this._element).closest(".navbar").length},j._getOffset=function(){var e=this,t={};return"function"==typeof this._config.offset?t.fn=function(t){return t.offsets=r(r({},t.offsets),e._config.offset(t.offsets,e._element)||{}),t}:t.offset=this._config.offset,t},j._getPopperConfig=function(){var t={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(t.modifiers.applyStyle={enabled:!1}),r(r({},t),this._config.popperConfig)},Q._jQueryInterface=function(e){return this.each(function(){var t=g(this).data("bs.dropdown");if(t||(t=new Q(this,"object"==typeof e?e:null),g(this).data("bs.dropdown",t)),"string"==typeof e){if(void 0===t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},Q._clearMenus=function(t){if(!t||3!==t.which&&("keyup"!==t.type||9===t.which))for(var e=[].slice.call(document.querySelectorAll('[data-toggle="dropdown"]')),n=0,i=e.length;n<i;n++){var o,s,r=Q._getParentFromElement(e[n]),a=g(e[n]).data("bs.dropdown"),l={relatedTarget:e[n]};t&&"click"===t.type&&(l.clickEvent=t),a&&(o=a._menu,!g(r).hasClass("show")||t&&("click"===t.type&&/input|textarea/i.test(t.target.tagName)||"keyup"===t.type&&9===t.which)&&g.contains(r,t.target)||(s=g.Event("hide.bs.dropdown",l),g(r).trigger(s),s.isDefaultPrevented()||("ontouchstart"in document.documentElement&&g(document.body).children().off("mouseover",null,g.noop),e[n].setAttribute("aria-expanded","false"),a._popper&&a._popper.destroy(),g(o).removeClass("show"),g(r).removeClass("show").trigger(g.Event("hidden.bs.dropdown",l)))))}},Q._getParentFromElement=function(t){var e,n=m.getSelectorFromElement(t);return n&&(e=document.querySelector(n)),e||t.parentNode},Q._dataApiKeydownHandler=function(t){if(!(/input|textarea/i.test(t.target.tagName)?32===t.which||27!==t.which&&(40!==t.which&&38!==t.which||g(t.target).closest(".dropdown-menu").length):!L.test(t.which))&&!this.disabled&&!g(this).hasClass("disabled")){var e=Q._getParentFromElement(this),n=g(e).hasClass("show");if(n||27!==t.which){if(t.preventDefault(),t.stopPropagation(),!n||n&&(27===t.which||32===t.which))return 27===t.which&&g(e.querySelector('[data-toggle="dropdown"]')).trigger("focus"),void g(this).trigger("click");var i,o=[].slice.call(e.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(t){return g(t).is(":visible")});0!==o.length&&(i=o.indexOf(t.target),38===t.which&&0<i&&i--,40===t.which&&i<o.length-1&&i++,i<0&&(i=0),o[i].focus())}}},s(Q,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return R}},{key:"DefaultType",get:function(){return q}}]),Q);function Q(t,e){this._element=t,this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}g(document).on("keydown.bs.dropdown.data-api",'[data-toggle="dropdown"]',F._dataApiKeydownHandler).on("keydown.bs.dropdown.data-api",".dropdown-menu",F._dataApiKeydownHandler).on("click.bs.dropdown.data-api keyup.bs.dropdown.data-api",F._clearMenus).on("click.bs.dropdown.data-api",'[data-toggle="dropdown"]',function(t){t.preventDefault(),t.stopPropagation(),F._jQueryInterface.call(g(this),"toggle")}).on("click.bs.dropdown.data-api",".dropdown form",function(t){t.stopPropagation()}),g.fn[P]=F._jQueryInterface,g.fn[P].Constructor=F,g.fn[P].noConflict=function(){return g.fn[P]=x,F._jQueryInterface};var B,H=g.fn.modal,U={backdrop:!0,keyboard:!0,focus:!0,show:!0},M={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},W=((B=V.prototype).toggle=function(t){return this._isShown?this.hide():this.show(t)},B.show=function(t){var e,n=this;this._isShown||this._isTransitioning||(g(this._element).hasClass("fade")&&(this._isTransitioning=!0),e=g.Event("show.bs.modal",{relatedTarget:t}),g(this._element).trigger(e),this._isShown||e.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),g(this._element).on("click.dismiss.bs.modal",'[data-dismiss="modal"]',function(t){return n.hide(t)}),g(this._dialog).on("mousedown.dismiss.bs.modal",function(){g(n._element).one("mouseup.dismiss.bs.modal",function(t){g(t.target).is(n._element)&&(n._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return n._showElement(t)})))},B.hide=function(t){var e,n,i,o=this;t&&t.preventDefault(),this._isShown&&!this._isTransitioning&&(e=g.Event("hide.bs.modal"),g(this._element).trigger(e),this._isShown&&!e.isDefaultPrevented()&&(this._isShown=!1,(n=g(this._element).hasClass("fade"))&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),g(document).off("focusin.bs.modal"),g(this._element).removeClass("show"),g(this._element).off("click.dismiss.bs.modal"),g(this._dialog).off("mousedown.dismiss.bs.modal"),n?(i=m.getTransitionDurationFromElement(this._element),g(this._element).one(m.TRANSITION_END,function(t){return o._hideModal(t)}).emulateTransitionEnd(i)):this._hideModal()))},B.dispose=function(){[window,this._element,this._dialog].forEach(function(t){return g(t).off(".bs.modal")}),g(document).off("focusin.bs.modal"),g.removeData(this._element,"bs.modal"),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},B.handleUpdate=function(){this._adjustDialog()},B._getConfig=function(t){return t=r(r({},U),t),m.typeCheckConfig("modal",t,M),t},B._triggerBackdropTransition=function(){var t=this;if("static"===this._config.backdrop){var e=g.Event("hidePrevented.bs.modal");if(g(this._element).trigger(e),e.defaultPrevented)return;this._element.classList.add("modal-static");var n=m.getTransitionDurationFromElement(this._element);g(this._element).one(m.TRANSITION_END,function(){t._element.classList.remove("modal-static")}).emulateTransitionEnd(n),this._element.focus()}else this.hide()},B._showElement=function(t){var e=this,n=g(this._element).hasClass("fade"),i=this._dialog?this._dialog.querySelector(".modal-body"):null;function o(){e._config.focus&&e._element.focus(),e._isTransitioning=!1,g(e._element).trigger(r)}this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),g(this._dialog).hasClass("modal-dialog-scrollable")&&i?i.scrollTop=0:this._element.scrollTop=0,n&&m.reflow(this._element),g(this._element).addClass("show"),this._config.focus&&this._enforceFocus();var s,r=g.Event("shown.bs.modal",{relatedTarget:t});n?(s=m.getTransitionDurationFromElement(this._dialog),g(this._dialog).one(m.TRANSITION_END,o).emulateTransitionEnd(s)):o()},B._enforceFocus=function(){var e=this;g(document).off("focusin.bs.modal").on("focusin.bs.modal",function(t){document!==t.target&&e._element!==t.target&&0===g(e._element).has(t.target).length&&e._element.focus()})},B._setEscapeEvent=function(){var e=this;this._isShown?g(this._element).on("keydown.dismiss.bs.modal",function(t){e._config.keyboard&&27===t.which?(t.preventDefault(),e.hide()):e._config.keyboard||27!==t.which||e._triggerBackdropTransition()}):this._isShown||g(this._element).off("keydown.dismiss.bs.modal")},B._setResizeEvent=function(){var e=this;this._isShown?g(window).on("resize.bs.modal",function(t){return e.handleUpdate(t)}):g(window).off("resize.bs.modal")},B._hideModal=function(){var t=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._isTransitioning=!1,this._showBackdrop(function(){g(document.body).removeClass("modal-open"),t._resetAdjustments(),t._resetScrollbar(),g(t._element).trigger("hidden.bs.modal")})},B._removeBackdrop=function(){this._backdrop&&(g(this._backdrop).remove(),this._backdrop=null)},B._showBackdrop=function(t){var e,n,i=this,o=g(this._element).hasClass("fade")?"fade":"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",o&&this._backdrop.classList.add(o),g(this._backdrop).appendTo(document.body),g(this._element).on("click.dismiss.bs.modal",function(t){i._ignoreBackdropClick?i._ignoreBackdropClick=!1:t.target===t.currentTarget&&i._triggerBackdropTransition()}),o&&m.reflow(this._backdrop),g(this._backdrop).addClass("show"),!t)return;if(!o)return void t();var s=m.getTransitionDurationFromElement(this._backdrop);g(this._backdrop).one(m.TRANSITION_END,t).emulateTransitionEnd(s)}else!this._isShown&&this._backdrop?(g(this._backdrop).removeClass("show"),e=function(){i._removeBackdrop(),t&&t()},g(this._element).hasClass("fade")?(n=m.getTransitionDurationFromElement(this._backdrop),g(this._backdrop).one(m.TRANSITION_END,e).emulateTransitionEnd(n)):e()):t&&t()},B._adjustDialog=function(){var t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},B._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},B._checkScrollbar=function(){var t=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(t.left+t.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},B._setScrollbar=function(){var t,e,n,i,o=this;this._isBodyOverflowing&&(t=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top")),e=[].slice.call(document.querySelectorAll(".sticky-top")),g(t).each(function(t,e){var n=e.style.paddingRight,i=g(e).css("padding-right");g(e).data("padding-right",n).css("padding-right",parseFloat(i)+o._scrollbarWidth+"px")}),g(e).each(function(t,e){var n=e.style.marginRight,i=g(e).css("margin-right");g(e).data("margin-right",n).css("margin-right",parseFloat(i)-o._scrollbarWidth+"px")}),n=document.body.style.paddingRight,i=g(document.body).css("padding-right"),g(document.body).data("padding-right",n).css("padding-right",parseFloat(i)+this._scrollbarWidth+"px")),g(document.body).addClass("modal-open")},B._resetScrollbar=function(){var t=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top"));g(t).each(function(t,e){var n=g(e).data("padding-right");g(e).removeData("padding-right"),e.style.paddingRight=n||""});var e=[].slice.call(document.querySelectorAll(".sticky-top"));g(e).each(function(t,e){var n=g(e).data("margin-right");void 0!==n&&g(e).css("margin-right",n).removeData("margin-right")});var n=g(document.body).data("padding-right");g(document.body).removeData("padding-right"),document.body.style.paddingRight=n||""},B._getScrollbarWidth=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e},V._jQueryInterface=function(n,i){return this.each(function(){var t=g(this).data("bs.modal"),e=r(r(r({},U),g(this).data()),"object"==typeof n&&n?n:{});if(t||(t=new V(this,e),g(this).data("bs.modal",t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n](i)}else e.show&&t.show(i)})},s(V,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return U}}]),V);function V(t,e){this._config=this._getConfig(e),this._element=t,this._dialog=t.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}g(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(t){var e,n=this,i=m.getSelectorFromElement(this);i&&(e=document.querySelector(i));var o=g(e).data("bs.modal")?"toggle":r(r({},g(e).data()),g(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||t.preventDefault();var s=g(e).one("show.bs.modal",function(t){t.isDefaultPrevented()||s.one("hidden.bs.modal",function(){g(n).is(":visible")&&n.focus()})});W._jQueryInterface.call(g(e),o,this)}),g.fn.modal=W._jQueryInterface,g.fn.modal.Constructor=W,g.fn.modal.noConflict=function(){return g.fn.modal=H,W._jQueryInterface};var z=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],K=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,X=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function Y(t,s,e){if(0===t.length)return t;if(e&&"function"==typeof e)return e(t);for(var n=(new window.DOMParser).parseFromString(t,"text/html"),r=Object.keys(s),a=[].slice.call(n.body.querySelectorAll("*")),i=0,o=a.length;i<o;i++)!function(t){var e=a[t],n=e.nodeName.toLowerCase();if(-1===r.indexOf(e.nodeName.toLowerCase()))return e.parentNode.removeChild(e);var i=[].slice.call(e.attributes),o=[].concat(s["*"]||[],s[n]||[]);i.forEach(function(t){!function(t,e){var n=t.nodeName.toLowerCase();if(-1!==e.indexOf(n))return-1===z.indexOf(n)||Boolean(t.nodeValue.match(K)||t.nodeValue.match(X));for(var i=e.filter(function(t){return t instanceof RegExp}),o=0,s=i.length;o<s;o++)if(n.match(i[o]))return 1}(t,o)&&e.removeAttribute(t.nodeName)})}(i);return n.body.innerHTML}var $,J="tooltip",G=g.fn[J],Z=new RegExp("(^|\\s)bs-tooltip\\S+","g"),tt=["sanitize","whiteList","sanitizeFn"],et={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},nt={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},it={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:{"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},popperConfig:null},ot={HIDE:"hide.bs.tooltip",HIDDEN:"hidden.bs.tooltip",SHOW:"show.bs.tooltip",SHOWN:"shown.bs.tooltip",INSERTED:"inserted.bs.tooltip",CLICK:"click.bs.tooltip",FOCUSIN:"focusin.bs.tooltip",FOCUSOUT:"focusout.bs.tooltip",MOUSEENTER:"mouseenter.bs.tooltip",MOUSELEAVE:"mouseleave.bs.tooltip"},st=(($=rt.prototype).enable=function(){this._isEnabled=!0},$.disable=function(){this._isEnabled=!1},$.toggleEnabled=function(){this._isEnabled=!this._isEnabled},$.toggle=function(t){if(this._isEnabled)if(t){var e=this.constructor.DATA_KEY,n=g(t.currentTarget).data(e);n||(n=new this.constructor(t.currentTarget,this._getDelegateConfig()),g(t.currentTarget).data(e,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)}else{if(g(this.getTipElement()).hasClass("show"))return void this._leave(null,this);this._enter(null,this)}},$.dispose=function(){clearTimeout(this._timeout),g.removeData(this.element,this.constructor.DATA_KEY),g(this.element).off(this.constructor.EVENT_KEY),g(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&g(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},$.show=function(){var e=this;if("none"===g(this.element).css("display"))throw new Error("Please use show on visible elements");var t=g.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){g(this.element).trigger(t);var n=m.findShadowRoot(this.element),i=g.contains(null!==n?n:this.element.ownerDocument.documentElement,this.element);if(t.isDefaultPrevented()||!i)return;var o=this.getTipElement(),s=m.getUID(this.constructor.NAME);o.setAttribute("id",s),this.element.setAttribute("aria-describedby",s),this.setContent(),this.config.animation&&g(o).addClass("fade");var r="function"==typeof this.config.placement?this.config.placement.call(this,o,this.element):this.config.placement,a=this._getAttachment(r);this.addAttachmentClass(a);var l=this._getContainer();g(o).data(this.constructor.DATA_KEY,this),g.contains(this.element.ownerDocument.documentElement,this.tip)||g(o).appendTo(l),g(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new u(this.element,o,this._getPopperConfig(a)),g(o).addClass("show"),"ontouchstart"in document.documentElement&&g(document.body).children().on("mouseover",null,g.noop);var c,h=function(){e.config.animation&&e._fixTransition();var t=e._hoverState;e._hoverState=null,g(e.element).trigger(e.constructor.Event.SHOWN),"out"===t&&e._leave(null,e)};g(this.tip).hasClass("fade")?(c=m.getTransitionDurationFromElement(this.tip),g(this.tip).one(m.TRANSITION_END,h).emulateTransitionEnd(c)):h()}},$.hide=function(t){function e(){"show"!==i._hoverState&&o.parentNode&&o.parentNode.removeChild(o),i._cleanTipClass(),i.element.removeAttribute("aria-describedby"),g(i.element).trigger(i.constructor.Event.HIDDEN),null!==i._popper&&i._popper.destroy(),t&&t()}var n,i=this,o=this.getTipElement(),s=g.Event(this.constructor.Event.HIDE);g(this.element).trigger(s),s.isDefaultPrevented()||(g(o).removeClass("show"),"ontouchstart"in document.documentElement&&g(document.body).children().off("mouseover",null,g.noop),this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,g(this.tip).hasClass("fade")?(n=m.getTransitionDurationFromElement(o),g(o).one(m.TRANSITION_END,e).emulateTransitionEnd(n)):e(),this._hoverState="")},$.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},$.isWithContent=function(){return Boolean(this.getTitle())},$.addAttachmentClass=function(t){g(this.getTipElement()).addClass("bs-tooltip-"+t)},$.getTipElement=function(){return this.tip=this.tip||g(this.config.template)[0],this.tip},$.setContent=function(){var t=this.getTipElement();this.setElementContent(g(t.querySelectorAll(".tooltip-inner")),this.getTitle()),g(t).removeClass("fade show")},$.setElementContent=function(t,e){"object"!=typeof e||!e.nodeType&&!e.jquery?this.config.html?(this.config.sanitize&&(e=Y(e,this.config.whiteList,this.config.sanitizeFn)),t.html(e)):t.text(e):this.config.html?g(e).parent().is(t)||t.empty().append(e):t.text(g(e).text())},$.getTitle=function(){return this.element.getAttribute("data-original-title")||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},$._getPopperConfig=function(t){var e=this;return r(r({},{placement:t,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){return e._handlePopperPlacementChange(t)}}),this.config.popperConfig)},$._getOffset=function(){var e=this,t={};return"function"==typeof this.config.offset?t.fn=function(t){return t.offsets=r(r({},t.offsets),e.config.offset(t.offsets,e.element)||{}),t}:t.offset=this.config.offset,t},$._getContainer=function(){return!1===this.config.container?document.body:m.isElement(this.config.container)?g(this.config.container):g(document).find(this.config.container)},$._getAttachment=function(t){return nt[t.toUpperCase()]},$._setListeners=function(){var i=this;this.config.trigger.split(" ").forEach(function(t){var e,n;"click"===t?g(i.element).on(i.constructor.Event.CLICK,i.config.selector,function(t){return i.toggle(t)}):"manual"!==t&&(e="hover"===t?i.constructor.Event.MOUSEENTER:i.constructor.Event.FOCUSIN,n="hover"===t?i.constructor.Event.MOUSELEAVE:i.constructor.Event.FOCUSOUT,g(i.element).on(e,i.config.selector,function(t){return i._enter(t)}).on(n,i.config.selector,function(t){return i._leave(t)}))}),this._hideModalHandler=function(){i.element&&i.hide()},g(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=r(r({},this.config),{},{trigger:"manual",selector:""}):this._fixTitle()},$._fixTitle=function(){var t=typeof this.element.getAttribute("data-original-title");!this.element.getAttribute("title")&&"string"==t||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},$._enter=function(t,e){var n=this.constructor.DATA_KEY;(e=e||g(t.currentTarget).data(n))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),g(t.currentTarget).data(n,e)),t&&(e._activeTrigger["focusin"===t.type?"focus":"hover"]=!0),g(e.getTipElement()).hasClass("show")||"show"===e._hoverState?e._hoverState="show":(clearTimeout(e._timeout),e._hoverState="show",e.config.delay&&e.config.delay.show?e._timeout=setTimeout(function(){"show"===e._hoverState&&e.show()},e.config.delay.show):e.show())},$._leave=function(t,e){var n=this.constructor.DATA_KEY;(e=e||g(t.currentTarget).data(n))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),g(t.currentTarget).data(n,e)),t&&(e._activeTrigger["focusout"===t.type?"focus":"hover"]=!1),e._isWithActiveTrigger()||(clearTimeout(e._timeout),e._hoverState="out",e.config.delay&&e.config.delay.hide?e._timeout=setTimeout(function(){"out"===e._hoverState&&e.hide()},e.config.delay.hide):e.hide())},$._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},$._getConfig=function(t){var e=g(this.element).data();return Object.keys(e).forEach(function(t){-1!==tt.indexOf(t)&&delete e[t]}),"number"==typeof(t=r(r(r({},this.constructor.Default),e),"object"==typeof t&&t?t:{})).delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),m.typeCheckConfig(J,t,this.constructor.DefaultType),t.sanitize&&(t.template=Y(t.template,t.whiteList,t.sanitizeFn)),t},$._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},$._cleanTipClass=function(){var t=g(this.getTipElement()),e=t.attr("class").match(Z);null!==e&&e.length&&t.removeClass(e.join(""))},$._handlePopperPlacementChange=function(t){this.tip=t.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(t.placement))},$._fixTransition=function(){var t=this.getTipElement(),e=this.config.animation;null===t.getAttribute("x-placement")&&(g(t).removeClass("fade"),this.config.animation=!1,this.hide(),this.show(),this.config.animation=e)},rt._jQueryInterface=function(n){return this.each(function(){var t=g(this).data("bs.tooltip"),e="object"==typeof n&&n;if((t||!/dispose|hide/.test(n))&&(t||(t=new rt(this,e),g(this).data("bs.tooltip",t)),"string"==typeof n)){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},s(rt,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return it}},{key:"NAME",get:function(){return J}},{key:"DATA_KEY",get:function(){return"bs.tooltip"}},{key:"Event",get:function(){return ot}},{key:"EVENT_KEY",get:function(){return".bs.tooltip"}},{key:"DefaultType",get:function(){return et}}]),rt);function rt(t,e){if(void 0===u)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners()}g.fn[J]=st._jQueryInterface,g.fn[J].Constructor=st,g.fn[J].noConflict=function(){return g.fn[J]=G,st._jQueryInterface};var at="popover",lt=g.fn[at],ct=new RegExp("(^|\\s)bs-popover\\S+","g"),ht=r(r({},st.Default),{},{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),ut=r(r({},st.DefaultType),{},{content:"(string|element|function)"}),dt={HIDE:"hide.bs.popover",HIDDEN:"hidden.bs.popover",SHOW:"show.bs.popover",SHOWN:"shown.bs.popover",INSERTED:"inserted.bs.popover",CLICK:"click.bs.popover",FOCUSIN:"focusin.bs.popover",FOCUSOUT:"focusout.bs.popover",MOUSEENTER:"mouseenter.bs.popover",MOUSELEAVE:"mouseleave.bs.popover"},ft=function(t){var e,n;function i(){return t.apply(this,arguments)||this}n=t,(e=i).prototype=Object.create(n.prototype),(e.prototype.constructor=e).__proto__=n;var o=i.prototype;return o.isWithContent=function(){return this.getTitle()||this._getContent()},o.addAttachmentClass=function(t){g(this.getTipElement()).addClass("bs-popover-"+t)},o.getTipElement=function(){return this.tip=this.tip||g(this.config.template)[0],this.tip},o.setContent=function(){var t=g(this.getTipElement());this.setElementContent(t.find(".popover-header"),this.getTitle());var e=this._getContent();"function"==typeof e&&(e=e.call(this.element)),this.setElementContent(t.find(".popover-body"),e),t.removeClass("fade show")},o._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},o._cleanTipClass=function(){var t=g(this.getTipElement()),e=t.attr("class").match(ct);null!==e&&0<e.length&&t.removeClass(e.join(""))},i._jQueryInterface=function(n){return this.each(function(){var t=g(this).data("bs.popover"),e="object"==typeof n?n:null;if((t||!/dispose|hide/.test(n))&&(t||(t=new i(this,e),g(this).data("bs.popover",t)),"string"==typeof n)){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},s(i,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return ht}},{key:"NAME",get:function(){return at}},{key:"DATA_KEY",get:function(){return"bs.popover"}},{key:"Event",get:function(){return dt}},{key:"EVENT_KEY",get:function(){return".bs.popover"}},{key:"DefaultType",get:function(){return ut}}]),i}(st);g.fn[at]=ft._jQueryInterface,g.fn[at].Constructor=ft,g.fn[at].noConflict=function(){return g.fn[at]=lt,ft._jQueryInterface};var gt,mt="scrollspy",pt=g.fn[mt],_t={offset:10,method:"auto",target:""},vt={offset:"number",method:"string",target:"(string|element)"},bt=((gt=yt.prototype).refresh=function(){var e=this,t=this._scrollElement===this._scrollElement.window?"offset":"position",o="auto"===this._config.method?t:this._config.method,s="position"===o?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(t){var e,n=m.getSelectorFromElement(t);if(n&&(e=document.querySelector(n)),e){var i=e.getBoundingClientRect();if(i.width||i.height)return[g(e)[o]().top+s,n]}return null}).filter(function(t){return t}).sort(function(t,e){return t[0]-e[0]}).forEach(function(t){e._offsets.push(t[0]),e._targets.push(t[1])})},gt.dispose=function(){g.removeData(this._element,"bs.scrollspy"),g(this._scrollElement).off(".bs.scrollspy"),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},gt._getConfig=function(t){var e;return"string"!=typeof(t=r(r({},_t),"object"==typeof t&&t?t:{})).target&&m.isElement(t.target)&&((e=g(t.target).attr("id"))||(e=m.getUID(mt),g(t.target).attr("id",e)),t.target="#"+e),m.typeCheckConfig(mt,t,vt),t},gt._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},gt._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},gt._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},gt._process=function(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),n=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),n<=t){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&t<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var o=this._offsets.length;o--;)this._activeTarget!==this._targets[o]&&t>=this._offsets[o]&&(void 0===this._offsets[o+1]||t<this._offsets[o+1])&&this._activate(this._targets[o])}},gt._activate=function(e){this._activeTarget=e,this._clear();var t=this._selector.split(",").map(function(t){return t+'[data-target="'+e+'"],'+t+'[href="'+e+'"]'}),n=g([].slice.call(document.querySelectorAll(t.join(","))));n.hasClass("dropdown-item")?(n.closest(".dropdown").find(".dropdown-toggle").addClass("active"),n.addClass("active")):(n.addClass("active"),n.parents(".nav, .list-group").prev(".nav-link, .list-group-item").addClass("active"),n.parents(".nav, .list-group").prev(".nav-item").children(".nav-link").addClass("active")),g(this._scrollElement).trigger("activate.bs.scrollspy",{relatedTarget:e})},gt._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(t){return t.classList.contains("active")}).forEach(function(t){return t.classList.remove("active")})},yt._jQueryInterface=function(e){return this.each(function(){var t=g(this).data("bs.scrollspy");if(t||(t=new yt(this,"object"==typeof e&&e),g(this).data("bs.scrollspy",t)),"string"==typeof e){if(void 0===t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},s(yt,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return _t}}]),yt);function yt(t,e){var n=this;this._element=t,this._scrollElement="BODY"===t.tagName?window:t,this._config=this._getConfig(e),this._selector=this._config.target+" .nav-link,"+this._config.target+" .list-group-item,"+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,g(this._scrollElement).on("scroll.bs.scrollspy",function(t){return n._process(t)}),this.refresh(),this._process()}g(window).on("load.bs.scrollspy.data-api",function(){for(var t=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),e=t.length;e--;){var n=g(t[e]);bt._jQueryInterface.call(n,n.data())}}),g.fn[mt]=bt._jQueryInterface,g.fn[mt].Constructor=bt,g.fn[mt].noConflict=function(){return g.fn[mt]=pt,bt._jQueryInterface};var Et,wt=g.fn.tab,Tt=((Et=Ct.prototype).show=function(){var t,e,n,i,o,s,r,a,l=this;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&g(this._element).hasClass("active")||g(this._element).hasClass("disabled")||(e=g(this._element).closest(".nav, .list-group")[0],n=m.getSelectorFromElement(this._element),e&&(i="UL"===e.nodeName||"OL"===e.nodeName?"> li > .active":".active",o=(o=g.makeArray(g(e).find(i)))[o.length-1]),s=g.Event("hide.bs.tab",{relatedTarget:this._element}),r=g.Event("show.bs.tab",{relatedTarget:o}),o&&g(o).trigger(s),g(this._element).trigger(r),r.isDefaultPrevented()||s.isDefaultPrevented()||(n&&(t=document.querySelector(n)),this._activate(this._element,e),a=function(){var t=g.Event("hidden.bs.tab",{relatedTarget:l._element}),e=g.Event("shown.bs.tab",{relatedTarget:o});g(o).trigger(t),g(l._element).trigger(e)},t?this._activate(t,t.parentNode,a):a()))},Et.dispose=function(){g.removeData(this._element,"bs.tab"),this._element=null},Et._activate=function(t,e,n){function i(){return s._transitionComplete(t,r,n)}var o,s=this,r=(!e||"UL"!==e.nodeName&&"OL"!==e.nodeName?g(e).children(".active"):g(e).find("> li > .active"))[0],a=n&&r&&g(r).hasClass("fade");r&&a?(o=m.getTransitionDurationFromElement(r),g(r).removeClass("show").one(m.TRANSITION_END,i).emulateTransitionEnd(o)):i()},Et._transitionComplete=function(t,e,n){var i,o,s;e&&(g(e).removeClass("active"),(i=g(e.parentNode).find("> .dropdown-menu .active")[0])&&g(i).removeClass("active"),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!1)),g(t).addClass("active"),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),m.reflow(t),t.classList.contains("fade")&&t.classList.add("show"),t.parentNode&&g(t.parentNode).hasClass("dropdown-menu")&&((o=g(t).closest(".dropdown")[0])&&(s=[].slice.call(o.querySelectorAll(".dropdown-toggle")),g(s).addClass("active")),t.setAttribute("aria-expanded",!0)),n&&n()},Ct._jQueryInterface=function(n){return this.each(function(){var t=g(this),e=t.data("bs.tab");if(e||(e=new Ct(this),t.data("bs.tab",e)),"string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}})},s(Ct,null,[{key:"VERSION",get:function(){return"4.5.0"}}]),Ct);function Ct(t){this._element=t}g(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(t){t.preventDefault(),Tt._jQueryInterface.call(g(this),"show")}),g.fn.tab=Tt._jQueryInterface,g.fn.tab.Constructor=Tt,g.fn.tab.noConflict=function(){return g.fn.tab=wt,Tt._jQueryInterface};var St,Dt=g.fn.toast,kt={animation:"boolean",autohide:"boolean",delay:"number"},Nt={animation:!0,autohide:!0,delay:500},At=((St=It.prototype).show=function(){var t,e,n=this,i=g.Event("show.bs.toast");g(this._element).trigger(i),i.isDefaultPrevented()||(this._config.animation&&this._element.classList.add("fade"),t=function(){n._element.classList.remove("showing"),n._element.classList.add("show"),g(n._element).trigger("shown.bs.toast"),n._config.autohide&&(n._timeout=setTimeout(function(){n.hide()},n._config.delay))},this._element.classList.remove("hide"),m.reflow(this._element),this._element.classList.add("showing"),this._config.animation?(e=m.getTransitionDurationFromElement(this._element),g(this._element).one(m.TRANSITION_END,t).emulateTransitionEnd(e)):t())},St.hide=function(){var t;this._element.classList.contains("show")&&(t=g.Event("hide.bs.toast"),g(this._element).trigger(t),t.isDefaultPrevented()||this._close())},St.dispose=function(){clearTimeout(this._timeout),this._timeout=null,this._element.classList.contains("show")&&this._element.classList.remove("show"),g(this._element).off("click.dismiss.bs.toast"),g.removeData(this._element,"bs.toast"),this._element=null,this._config=null},St._getConfig=function(t){return t=r(r(r({},Nt),g(this._element).data()),"object"==typeof t&&t?t:{}),m.typeCheckConfig("toast",t,this.constructor.DefaultType),t},St._setListeners=function(){var t=this;g(this._element).on("click.dismiss.bs.toast",'[data-dismiss="toast"]',function(){return t.hide()})},St._close=function(){function t(){n._element.classList.add("hide"),g(n._element).trigger("hidden.bs.toast")}var e,n=this;this._element.classList.remove("show"),this._config.animation?(e=m.getTransitionDurationFromElement(this._element),g(this._element).one(m.TRANSITION_END,t).emulateTransitionEnd(e)):t()},It._jQueryInterface=function(n){return this.each(function(){var t=g(this),e=t.data("bs.toast");if(e||(e=new It(this,"object"==typeof n&&n),t.data("bs.toast",e)),"string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n](this)}})},s(It,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"DefaultType",get:function(){return kt}},{key:"Default",get:function(){return Nt}}]),It);function It(t,e){this._element=t,this._config=this._getConfig(e),this._timeout=null,this._setListeners()}g.fn.toast=At._jQueryInterface,g.fn.toast.Constructor=At,g.fn.toast.noConflict=function(){return g.fn.toast=Dt,At._jQueryInterface},t.Alert=l,t.Button=f,t.Carousel=T,t.Collapse=I,t.Dropdown=F,t.Modal=W,t.Popover=ft,t.Scrollspy=bt,t.Tab=Tt,t.Toast=At,t.Tooltip=st,t.Util=m,Object.defineProperty(t,"__esModule",{value:!0})});