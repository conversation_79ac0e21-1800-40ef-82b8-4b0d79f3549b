!function(){function a(n){return n&&(n.ownerDocument||n.document||n).documentElement}function T(n){return n&&(n.ownerDocument&&n.ownerDocument.defaultView||n.document&&n||n.defaultView)}function o(n,t){return n<t?-1:t<n?1:t<=n?0:NaN}function c(n){return null===n?NaN:+n}function f(n){return!isNaN(n)}function n(u){return{left:function(n,t,e,r){for(arguments.length<3&&(e=0),arguments.length<4&&(r=n.length);e<r;){var i=e+r>>>1;u(n[i],t)<0?e=1+i:r=i}return e},right:function(n,t,e,r){for(arguments.length<3&&(e=0),arguments.length<4&&(r=n.length);e<r;){var i=e+r>>>1;0<u(n[i],t)?r=i:e=1+i}return e}}}function l(n){return n.length}function t(n,t){for(var e in t)Object.defineProperty(n.prototype,e,{value:t[e],enumerable:!1})}function m(){this._=Object.create(null)}function e(n){return(n+="")===uu||n[0]===ou?ou+n:n}function r(n){return(n+="")[0]===ou?n.slice(1):n}function i(n){return e(n)in this._}function u(n){return(n=e(n))in this._&&delete this._[n]}function s(){var n=[];for(var t in this._)n.push(r(t));return n}function h(){var n=0;for(var t in this._)++n;return n}function p(){for(var n in this._)return!1;return!0}function g(){this._=Object.create(null)}function R(n){return n}function d(n,t){if(t in n)return t;t=t.charAt(0).toUpperCase()+t.slice(1);for(var e=0,r=au.length;e<r;++e){var i=au[e]+t;if(i in n)return i}}function y(){}function v(){}function M(i){function n(){for(var n,t=u,e=-1,r=t.length;++e<r;)(n=t[e].on)&&n.apply(this,arguments);return i}var u=[],o=new m;return n.on=function(n,t){var e,r=o.get(n);return arguments.length<2?r&&r.on:(r&&(r.on=null,u=u.slice(0,e=u.indexOf(r)).concat(u.slice(e+1)),o.remove(n)),t&&u.push(o.set(n,{on:t})),i)},n}function D(){Bi.event.preventDefault()}function x(){for(var n,t=Bi.event;n=t.sourceEvent;)t=n;return t}function P(i){for(var u=new v,n=0,t=arguments.length;++n<t;)u[arguments[n]]=M(u);return u.of=function(e,r){return function(n){try{var t=n.sourceEvent=Bi.event;n.target=i,Bi.event=n,u[n.type].apply(e,r)}finally{Bi.event=t}}},u}function b(n){return cu(n,pu),n}function _(n){return"function"==typeof n?n:function(){return fu(n,this)}}function w(n){return"function"==typeof n?n:function(){return su(n,this)}}function S(t,e){return t=Bi.ns.qualify(t),null==e?t.local?function(){this.removeAttributeNS(t.space,t.local)}:function(){this.removeAttribute(t)}:"function"==typeof e?t.local?function(){var n=e.apply(this,arguments);null==n?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,n)}:function(){var n=e.apply(this,arguments);null==n?this.removeAttribute(t):this.setAttribute(t,n)}:t.local?function(){this.setAttributeNS(t.space,t.local,e)}:function(){this.setAttribute(t,e)}}function k(n){return n.trim().replace(/\s+/g," ")}function N(n){return new RegExp("(?:^|\\s+)"+Bi.requote(n)+"(?:\\s+|$)","g")}function E(n){return(n+"").trim().split(/^|\s+/)}function A(e,r){var i=(e=E(e).map(C)).length;return"function"==typeof r?function(){for(var n=-1,t=r.apply(this,arguments);++n<i;)e[n](this,t)}:function(){for(var n=-1;++n<i;)e[n](this,r)}}function C(r){var i=N(r);return function(n,t){if(e=n.classList)return t?e.add(r):e.remove(r);var e=n.getAttribute("class")||"";t?(i.lastIndex=0,i.test(e)||n.setAttribute("class",k(e+" "+r))):n.setAttribute("class",k(e.replace(i," ")))}}function z(t,e,r){return null==e?function(){this.style.removeProperty(t)}:"function"==typeof e?function(){var n=e.apply(this,arguments);null==n?this.style.removeProperty(t):this.style.setProperty(t,n,r)}:function(){this.style.setProperty(t,e,r)}}function L(t,e){return null==e?function(){delete this[t]}:"function"==typeof e?function(){var n=e.apply(this,arguments);null==n?delete this[t]:this[t]=n}:function(){this[t]=e}}function q(e){return"function"==typeof e?e:(e=Bi.ns.qualify(e)).local?function(){return this.ownerDocument.createElementNS(e.space,e.local)}:function(){var n=this.ownerDocument,t=this.namespaceURI;return t===gu&&n.documentElement.namespaceURI===gu?n.createElement(e):n.createElementNS(t,e)}}function U(){var n=this.parentNode;n&&n.removeChild(this)}function j(n){return{__data__:n}}function F(n){return function(){return hu(this,n)}}function H(n,t){for(var e=0,r=n.length;e<r;e++)for(var i,u=n[e],o=0,a=u.length;o<a;o++)(i=u[o])&&t(i,o,e);return n}function O(n){return cu(n,vu),n}function I(i,t,e){function r(){var n=this[u];n&&(this.removeEventListener(i,n,n.$),delete this[u])}var u="__on"+i,n=i.indexOf("."),o=Y;0<n&&(i=i.slice(0,n));var a=yu.get(i);return a&&(i=a,o=Z),n?t?function(){var n=o(t,Ji(arguments));r.call(this),this.addEventListener(i,this[u]=n,n.$=e),n._=t}:r:t?y:function(){var n,t,e=new RegExp("^__on([^.]+)"+Bi.requote(i)+"$");for(var r in this){(n=r.match(e))&&(t=this[r],this.removeEventListener(n[1],t,t.$),delete this[r])}}}function Y(e,r){return function(n){var t=Bi.event;Bi.event=n,r[0]=this.__data__;try{e.apply(this,r)}finally{Bi.event=t}}}function Z(n,t){var e=Y(n,t);return function(n){var t=n.relatedTarget;t&&(t===this||8&t.compareDocumentPosition(this))||e.call(this,n)}}function V(n){var e,r,i=".dragsuppress-"+ ++Mu,u="click"+i,o=Bi.select(T(n)).on("touchmove"+i,D).on("dragstart"+i,D).on("selectstart"+i,D);return null==mu&&(mu=!("onselectstart"in n)&&d(n.style,"userSelect")),mu&&(e=a(n).style,r=e[mu],e[mu]="none"),function(n){var t;o.on(i,null),mu&&(e[mu]=r),n&&(t=function(){o.on(u,null)},o.on(u,function(){D(),t()},!0),setTimeout(t,0))}}function X(n,t){t.changedTouches&&(t=t.changedTouches[0]);var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r,i,u=e.createSVGPoint();return xu<0&&(((r=T(n)).scrollX||r.scrollY)&&(i=(e=Bi.select("body").append("svg").style({position:"absolute",top:0,left:0,margin:0,padding:0,border:"none"},"important"))[0][0].getScreenCTM(),xu=!(i.f||i.e),e.remove())),xu?(u.x=t.pageX,u.y=t.pageY):(u.x=t.clientX,u.y=t.clientY),[(u=u.matrixTransform(n.getScreenCTM().inverse())).x,u.y]}var o=n.getBoundingClientRect();return[t.clientX-o.left-n.clientLeft,t.clientY-o.top-n.clientTop]}function $(){return Bi.event.changedTouches[0].identifier}function B(n){return 0<n?1:n<0?-1:0}function W(n,t,e){return(t[0]-n[0])*(e[1]-n[1])-(t[1]-n[1])*(e[0]-n[0])}function J(n){return 1<n?0:n<-1?wu:Math.acos(n)}function G(n){return 1<n?Nu:n<-1?-Nu:Math.asin(n)}function K(n){return((n=Math.exp(n))+1/n)/2}function Q(n){return(n=Math.sin(n/2))*n}function nn(){}function tn(n,t,e){return this instanceof tn?(this.h=+n,this.s=+t,void(this.l=+e)):arguments.length<2?n instanceof tn?new tn(n.h,n.s,n.l):vn(""+n,yn,tn):new tn(n,t,e)}function en(n,t,e){function r(n){return Math.round(255*(360<(t=n)?t-=360:t<0&&(t+=360),t<60?i+(u-i)*t/60:t<180?u:t<240?i+(u-i)*(240-t)/60:i));var t}var i,u;return n=isNaN(n)?0:(n%=360)<0?n+360:n,t=isNaN(t)||t<0?0:1<t?1:t,i=2*(e=e<0?0:1<e?1:e)-(u=e<=.5?e*(1+t):e+t-e*t),new hn(r(n+120),r(n),r(n-120))}function rn(n,t,e){return this instanceof rn?(this.h=+n,this.c=+t,void(this.l=+e)):arguments.length<2?n instanceof rn?new rn(n.h,n.c,n.l):ln(n instanceof on?n.l:(n=mn((n=Bi.rgb(n)).r,n.g,n.b)).l,n.a,n.b):new rn(n,t,e)}function un(n,t,e){return isNaN(n)&&(n=0),isNaN(t)&&(t=0),new on(e,Math.cos(n*=Eu)*t,Math.sin(n)*t)}function on(n,t,e){return this instanceof on?(this.l=+n,this.a=+t,void(this.b=+e)):arguments.length<2?n instanceof on?new on(n.l,n.a,n.b):n instanceof rn?un(n.h,n.c,n.l):mn((n=hn(n)).r,n.g,n.b):new on(n,t,e)}function an(n,t,e){var r=(n+16)/116,i=r+t/500,u=r-e/200;return new hn(sn(3.2404542*(i=cn(i)*Pu)-1.5371385*(r=cn(r)*Uu)-.4985314*(u=cn(u)*ju)),sn(-.969266*i+1.8760108*r+.041556*u),sn(.0556434*i-.2040259*r+1.0572252*u))}function ln(n,t,e){return 0<n?new rn(Math.atan2(e,t)*Au,Math.sqrt(t*t+e*e),n):new rn(NaN,NaN,n)}function cn(n){return.206893034<n?n*n*n:(n-4/29)/7.787037}function fn(n){return.008856<n?Math.pow(n,1/3):7.787037*n+4/29}function sn(n){return Math.round(255*(n<=.00304?12.92*n:1.055*Math.pow(n,1/2.4)-.055))}function hn(n,t,e){return this instanceof hn?(this.r=~~n,this.g=~~t,void(this.b=~~e)):arguments.length<2?n instanceof hn?new hn(n.r,n.g,n.b):vn(""+n,hn,en):new hn(n,t,e)}function pn(n){return new hn(n>>16,n>>8&255,255&n)}function gn(n){return pn(n)+""}function dn(n){return n<16?"0"+Math.max(0,n).toString(16):Math.min(255,n).toString(16)}function vn(n,t,e){var r,i,u,o=0,a=0,l=0;if(r=/([a-z]+)\((.*)\)/.exec(n=n.toLowerCase()))switch(i=r[2].split(","),r[1]){case"hsl":return e(parseFloat(i[0]),parseFloat(i[1])/100,parseFloat(i[2])/100);case"rgb":return t(xn(i[0]),xn(i[1]),xn(i[2]))}return(u=Ou.get(n))?t(u.r,u.g,u.b):(null==n||"#"!==n.charAt(0)||isNaN(u=parseInt(n.slice(1),16))||(4===n.length?(o=(3840&u)>>4,o|=o>>4,a=240&u,a|=a>>4,l=15&u,l|=l<<4):7===n.length&&(o=(16711680&u)>>16,a=(65280&u)>>8,l=255&u)),t(o,a,l))}function yn(n,t,e){var r,i,u=Math.min(n/=255,t/=255,e/=255),o=Math.max(n,t,e),a=o-u,l=(o+u)/2;return a?(i=l<.5?a/(o+u):a/(2-o-u),r=n==o?(t-e)/a+(t<e?6:0):t==o?(e-n)/a+2:(n-t)/a+4,r*=60):(r=NaN,i=0<l&&l<1?0:r),new tn(r,i,l)}function mn(n,t,e){var r=fn((.4124564*(n=Mn(n))+.3575761*(t=Mn(t))+.1804375*(e=Mn(e)))/Pu),i=fn((.2126729*n+.7151522*t+.072175*e)/Uu);return on(116*i-16,500*(r-i),200*(i-fn((.0193339*n+.119192*t+.9503041*e)/ju)))}function Mn(n){return(n/=255)<=.04045?n/12.92:Math.pow((n+.055)/1.055,2.4)}function xn(n){var t=parseFloat(n);return"%"===n.charAt(n.length-1)?Math.round(2.55*t):t}function bn(n){return"function"==typeof n?n:function(){return n}}function _n(r){return function(n,t,e){return 2===arguments.length&&"function"==typeof t&&(e=t,t=null),wn(n,t,r,e)}}function wn(i,u,o,n){function t(){var n,t,e,r=f.status;if(!r&&((e=(t=f).responseType)&&"text"!==e?t.response:t.responseText)||200<=r&&r<300||304===r){try{n=o.call(a,f)}catch(n){return void l.error.call(a,n)}l.load.call(a,n)}else l.error.call(a,f)}var e,a={},l=Bi.dispatch("beforesend","progress","load","error"),c={},f=new XMLHttpRequest,s=null;return!this.XDomainRequest||"withCredentials"in f||!/^(http(s)?:)?\/\//.test(i)||(f=new XDomainRequest),"onload"in f?f.onload=f.onerror=t:f.onreadystatechange=function(){3<f.readyState&&t()},f.onprogress=function(n){var t=Bi.event;Bi.event=n;try{l.progress.call(a,f)}finally{Bi.event=t}},a.header=function(n,t){return n=(n+"").toLowerCase(),arguments.length<2?c[n]:(null==t?delete c[n]:c[n]=t+"",a)},a.mimeType=function(n){return arguments.length?(u=null==n?null:n+"",a):u},a.responseType=function(n){return arguments.length?(s=n,a):s},a.response=function(n){return o=n,a},["get","post"].forEach(function(n){a[n]=function(){return a.send.apply(a,[n].concat(Ji(arguments)))}}),a.send=function(n,t,e){if(2===arguments.length&&"function"==typeof t&&(e=t,t=null),f.open(n,i,!0),null==u||"accept"in c||(c.accept=u+",*/*"),f.setRequestHeader)for(var r in c)f.setRequestHeader(r,c[r]);return null!=u&&f.overrideMimeType&&f.overrideMimeType(u),null!=s&&(f.responseType=s),null!=e&&a.on("error",e).on("load",function(n){e(null,n)}),l.beforesend.call(a,f),f.send(null==t?null:t),a},a.abort=function(){return f.abort(),a},Bi.rebind(a,l,"on"),null==n?a:a.get(1===(e=n).length?function(n,t){e(null==n?t:null)}:e)}function Sn(n,t,e){var r=arguments.length;r<2&&(t=0),r<3&&(e=Date.now());var i={c:n,t:e+t,n:null};return Yu?Yu.n=i:Iu=i,Yu=i,Zu||(Vu=clearTimeout(Vu),Zu=1,Xu(kn)),i}function kn(){var n=Nn(),t=En()-n;24<t?(isFinite(t)&&(clearTimeout(Vu),Vu=setTimeout(kn,t)),Zu=0):(Zu=1,Xu(kn))}function Nn(){for(var n=Date.now(),t=Iu;t;)n>=t.t&&t.c(n-t.t)&&(t.c=null),t=t.n;return n}function En(){for(var n,t=Iu,e=1/0;t;)t=t.c?(t.t<e&&(e=t.t),(n=t).n):n?n.n=t.n:Iu=t.n;return Yu=n,e}function An(n,t){return t-(n?Math.ceil(Math.log(n)/Math.LN10):1)}function Cn(n){return n+""}function zn(){this._=new Date(1<arguments.length?Date.UTC.apply(this,arguments):arguments[0])}function Ln(r,u,o){function n(n){var t=r(n),e=i(t,1);return n-t<e-n?t:e}function a(n){return u(n=r(new Gu(n-1)),1),n}function i(n,t){return u(n=new Gu(+n),t),n}function l(n,t,e){var r=a(n),i=[];if(1<e)for(;r<t;)o(r)%e||i.push(new Date(+r)),u(r,1);else for(;r<t;)i.push(new Date(+r)),u(r,1);return i}(r.floor=r).round=n,r.ceil=a,r.offset=i,r.range=l;var t=r.utc=qn(r);return(t.floor=t).round=qn(n),t.ceil=qn(a),t.offset=qn(i),t.range=function(n,t,e){try{var r=new(Gu=zn);return r._=n,l(r,t,e)}finally{Gu=Date}},r}function qn(r){return function(n,t){try{var e=new(Gu=zn);return e._=n,r(e,t)._}finally{Gu=Date}}}function Tn(n){function r(a){function n(n){for(var t,e,r,i=[],u=-1,o=0;++u<l;)37===a.charCodeAt(u)&&(i.push(a.slice(o,u)),null!=(e=Qu[t=a.charAt(++u)])&&(t=a.charAt(++u)),(r=x[t])&&(t=r(n,null==e?"e"===t?" ":"0":e)),i.push(t),o=u+1);return i.push(a.slice(o,u)),i.join("")}var l=a.length;return n.parse=function(n){var t={y:1900,m:0,d:1,H:0,M:0,S:0,L:0,Z:null};if(i(t,a,n,0)!=n.length)return null;"p"in t&&(t.H=t.H%12+12*t.p);var e=null!=t.Z&&Gu!==zn,r=new(e?zn:Gu);return"j"in t?r.setFullYear(t.y,0,t.j):"W"in t||"U"in t?("w"in t||(t.w="W"in t?1:0),r.setFullYear(t.y,0,1),r.setFullYear(t.y,0,"W"in t?(t.w+6)%7+7*t.W-(r.getDay()+5)%7:t.w+7*t.U-(r.getDay()+6)%7)):r.setFullYear(t.y,t.m,t.d),r.setHours(t.H+(t.Z/100|0),t.M+t.Z%100,t.S,t.L),e?r._:r},n.toString=function(){return a},n}function i(n,t,e,r){for(var i,u,o,a=0,l=t.length,c=e.length;a<l;){if(c<=r)return-1;if(37===(i=t.charCodeAt(a++))){if(o=t.charAt(a++),!(u=b[o in Qu?t.charAt(a++):o])||(r=u(n,e,r))<0)return-1}else if(i!=e.charCodeAt(r++))return-1}return r}var t=n.dateTime,e=n.date,u=n.time,o=n.periods,a=n.days,l=n.shortDays,c=n.months,f=n.shortMonths;r.multi=(r.utc=function(n){function t(n){try{var t=new(Gu=zn);return t._=n,e(t)}finally{Gu=Date}}var e=r(n);return t.parse=function(n){try{Gu=zn;var t=e.parse(n);return t&&t._}finally{Gu=Date}},t.toString=e.toString,t}).multi=Kn;var s=Bi.map(),h=Dn(a),p=Pn(a),g=Dn(l),d=Pn(l),v=Dn(c),y=Pn(c),m=Dn(f),M=Pn(f);o.forEach(function(n,t){s.set(n.toLowerCase(),t)});var x={a:function(n){return l[n.getDay()]},A:function(n){return a[n.getDay()]},b:function(n){return f[n.getMonth()]},B:function(n){return c[n.getMonth()]},c:r(t),d:function(n,t){return Rn(n.getDate(),t,2)},e:function(n,t){return Rn(n.getDate(),t,2)},H:function(n,t){return Rn(n.getHours(),t,2)},I:function(n,t){return Rn(n.getHours()%12||12,t,2)},j:function(n,t){return Rn(1+Ju.dayOfYear(n),t,3)},L:function(n,t){return Rn(n.getMilliseconds(),t,3)},m:function(n,t){return Rn(n.getMonth()+1,t,2)},M:function(n,t){return Rn(n.getMinutes(),t,2)},p:function(n){return o[+(12<=n.getHours())]},S:function(n,t){return Rn(n.getSeconds(),t,2)},U:function(n,t){return Rn(Ju.sundayOfYear(n),t,2)},w:function(n){return n.getDay()},W:function(n,t){return Rn(Ju.mondayOfYear(n),t,2)},x:r(e),X:r(u),y:function(n,t){return Rn(n.getFullYear()%100,t,2)},Y:function(n,t){return Rn(n.getFullYear()%1e4,t,4)},Z:Jn,"%":function(){return"%"}},b={a:function(n,t,e){g.lastIndex=0;var r=g.exec(t.slice(e));return r?(n.w=d.get(r[0].toLowerCase()),e+r[0].length):-1},A:function(n,t,e){h.lastIndex=0;var r=h.exec(t.slice(e));return r?(n.w=p.get(r[0].toLowerCase()),e+r[0].length):-1},b:function(n,t,e){m.lastIndex=0;var r=m.exec(t.slice(e));return r?(n.m=M.get(r[0].toLowerCase()),e+r[0].length):-1},B:function(n,t,e){v.lastIndex=0;var r=v.exec(t.slice(e));return r?(n.m=y.get(r[0].toLowerCase()),e+r[0].length):-1},c:function(n,t,e){return i(n,x.c.toString(),t,e)},d:Zn,e:Zn,H:Xn,I:Xn,j:Vn,L:Wn,m:Yn,M:$n,p:function(n,t,e){var r=s.get(t.slice(e,e+=2).toLowerCase());return null==r?-1:(n.p=r,e)},S:Bn,U:jn,w:Un,W:Fn,x:function(n,t,e){return i(n,x.x.toString(),t,e)},X:function(n,t,e){return i(n,x.X.toString(),t,e)},y:On,Y:Hn,Z:In,"%":Gn};return r}function Rn(n,t,e){var r=n<0?"-":"",i=(r?-n:n)+"",u=i.length;return r+(u<e?new Array(e-u+1).join(t)+i:i)}function Dn(n){return new RegExp("^(?:"+n.map(Bi.requote).join("|")+")","i")}function Pn(n){for(var t=new m,e=-1,r=n.length;++e<r;)t.set(n[e].toLowerCase(),e);return t}function Un(n,t,e){no.lastIndex=0;var r=no.exec(t.slice(e,e+1));return r?(n.w=+r[0],e+r[0].length):-1}function jn(n,t,e){no.lastIndex=0;var r=no.exec(t.slice(e));return r?(n.U=+r[0],e+r[0].length):-1}function Fn(n,t,e){no.lastIndex=0;var r=no.exec(t.slice(e));return r?(n.W=+r[0],e+r[0].length):-1}function Hn(n,t,e){no.lastIndex=0;var r=no.exec(t.slice(e,e+4));return r?(n.y=+r[0],e+r[0].length):-1}function On(n,t,e){no.lastIndex=0;var r,i=no.exec(t.slice(e,e+2));return i?(n.y=(r=+i[0])+(68<r?1900:2e3),e+i[0].length):-1}function In(n,t,e){return/^[+-]\d{4}$/.test(t=t.slice(e,e+5))?(n.Z=-t,e+5):-1}function Yn(n,t,e){no.lastIndex=0;var r=no.exec(t.slice(e,e+2));return r?(n.m=r[0]-1,e+r[0].length):-1}function Zn(n,t,e){no.lastIndex=0;var r=no.exec(t.slice(e,e+2));return r?(n.d=+r[0],e+r[0].length):-1}function Vn(n,t,e){no.lastIndex=0;var r=no.exec(t.slice(e,e+3));return r?(n.j=+r[0],e+r[0].length):-1}function Xn(n,t,e){no.lastIndex=0;var r=no.exec(t.slice(e,e+2));return r?(n.H=+r[0],e+r[0].length):-1}function $n(n,t,e){no.lastIndex=0;var r=no.exec(t.slice(e,e+2));return r?(n.M=+r[0],e+r[0].length):-1}function Bn(n,t,e){no.lastIndex=0;var r=no.exec(t.slice(e,e+2));return r?(n.S=+r[0],e+r[0].length):-1}function Wn(n,t,e){no.lastIndex=0;var r=no.exec(t.slice(e,e+3));return r?(n.L=+r[0],e+r[0].length):-1}function Jn(n){var t=n.getTimezoneOffset(),e=0<t?"-":"+",r=iu(t)/60|0,i=iu(t)%60;return e+Rn(r,"0",2)+Rn(i,"0",2)}function Gn(n,t,e){to.lastIndex=0;var r=to.exec(t.slice(e,e+1));return r?e+r[0].length:-1}function Kn(r){for(var n=r.length,t=-1;++t<n;)r[t][0]=this(r[t][0]);return function(n){for(var t=0,e=r[t];!e[1](n);)e=r[++t];return e[0](n)}}function Qn(){}function nt(n,t,e){var r=e.s=n+t,i=r-n,u=r-i;e.t=n-u+(t-i)}function tt(n,t){n&&uo.hasOwnProperty(n.type)&&uo[n.type](n,t)}function et(n,t,e){var r,i=-1,u=n.length-e;for(t.lineStart();++i<u;)r=n[i],t.point(r[0],r[1],r[2]);t.lineEnd()}function rt(n,t){var e=-1,r=n.length;for(t.polygonStart();++e<r;)et(n[e],t,1);t.polygonEnd()}function it(){function e(n,t){t=t*Eu/2+wu/4;var e=(n*=Eu)-f,r=0<=e?1:-1,i=r*e,u=Math.cos(t),o=Math.sin(t),a=h*o,l=s*u+a*Math.cos(i),c=a*r*Math.sin(i);xo.add(Math.atan2(c,l)),f=n,s=u,h=o}var r,i,f,s,h;bo.point=function(n,t){bo.point=e,f=(r=n)*Eu,s=Math.cos(t=(i=t)*Eu/2+wu/4),h=Math.sin(t)},bo.lineEnd=function(){e(r,i)}}function ut(n){var t=n[0],e=n[1],r=Math.cos(e);return[r*Math.cos(t),r*Math.sin(t),Math.sin(e)]}function ot(n,t){return n[0]*t[0]+n[1]*t[1]+n[2]*t[2]}function at(n,t){return[n[1]*t[2]-n[2]*t[1],n[2]*t[0]-n[0]*t[2],n[0]*t[1]-n[1]*t[0]]}function lt(n,t){n[0]+=t[0],n[1]+=t[1],n[2]+=t[2]}function ct(n,t){return[n[0]*t,n[1]*t,n[2]*t]}function ft(n){var t=Math.sqrt(n[0]*n[0]+n[1]*n[1]+n[2]*n[2]);n[0]/=t,n[1]/=t,n[2]/=t}function st(n){return[Math.atan2(n[1],n[0]),G(n[2])]}function ht(n,t){return iu(n[0]-t[0])<bu&&iu(n[1]-t[1])<bu}function pt(n,t){n*=Eu;var e=Math.cos(t*=Eu);gt(e*Math.cos(n),e*Math.sin(n),Math.sin(t))}function gt(n,t,e){Ro+=(n-Ro)/++qo,Do+=(t-Do)/qo,Po+=(e-Po)/qo}function dt(){function r(n,t){n*=Eu;var e=Math.cos(t*=Eu),r=e*Math.cos(n),i=e*Math.sin(n),u=Math.sin(t),o=Math.atan2(Math.sqrt((o=l*u-c*i)*o+(o=c*r-a*u)*o+(o=a*i-l*r)*o),a*r+l*i+c*u);To+=o,Uo+=o*(a+(a=r)),jo+=o*(l+(l=i)),Fo+=o*(c+(c=u)),gt(a,l,c)}var a,l,c;Yo.point=function(n,t){n*=Eu;var e=Math.cos(t*=Eu);a=e*Math.cos(n),l=e*Math.sin(n),c=Math.sin(t),Yo.point=r,gt(a,l,c)}}function vt(){Yo.point=pt}function yt(){function r(n,t){n*=Eu;var e=Math.cos(t*=Eu),r=e*Math.cos(n),i=e*Math.sin(n),u=Math.sin(t),o=g*u-d*i,a=d*r-p*u,l=p*i-g*r,c=Math.sqrt(o*o+a*a+l*l),f=p*r+g*i+d*u,s=c&&-J(f)/c,h=Math.atan2(c,f);Ho+=s*o,Oo+=s*a,Io+=s*l,To+=h,Uo+=h*(p+(p=r)),jo+=h*(g+(g=i)),Fo+=h*(d+(d=u)),gt(p,g,d)}var i,u,p,g,d;Yo.point=function(n,t){i=n,u=t,Yo.point=r,n*=Eu;var e=Math.cos(t*=Eu);p=e*Math.cos(n),g=e*Math.sin(n),d=Math.sin(t),gt(p,g,d)},Yo.lineEnd=function(){r(i,u),Yo.lineEnd=vt,Yo.point=pt}}function mt(e,r){function n(n,t){return n=e(n,t),r(n[0],n[1])}return e.invert&&r.invert&&(n.invert=function(n,t){return(n=r.invert(n,t))&&e.invert(n[0],n[1])}),n}function Mt(){return!0}function xt(n,t,e,r,a){var l=[],c=[];if(n.forEach(function(n){if(!((t=n.length-1)<=0)){var t,e=n[0],r=n[t];if(ht(e,r)){a.lineStart();for(var i=0;i<t;++i)a.point((e=n[i])[0],e[1]);return void a.lineEnd()}var u=new _t(e,n,null,!0),o=new _t(e,null,u,!1);u.o=o,l.push(u),c.push(o),u=new _t(r,n,null,!1),o=new _t(r,null,u,!0),u.o=o,l.push(u),c.push(o)}}),c.sort(t),bt(l),bt(c),l.length){for(var i=0,u=e,o=c.length;i<o;++i)c[i].e=u=!u;for(var f,s,h=l[0];;){for(var p=h,g=!0;p.v;)if((p=p.n)===h)return;f=p.z,a.lineStart();do{if(p.v=p.o.v=!0,p.e){if(g)for(i=0,o=f.length;i<o;++i)a.point((s=f[i])[0],s[1]);else r(p.x,p.n.x,1,a);p=p.n}else{if(g)for(i=(f=p.p.z).length-1;0<=i;--i)a.point((s=f[i])[0],s[1]);else r(p.x,p.p.x,-1,a);p=p.p}f=(p=p.o).z,g=!g}while(!p.v);a.lineEnd()}}}function bt(n){if(t=n.length){for(var t,e,r=0,i=n[0];++r<t;)i.n=e=n[r],e.p=i,i=e;i.n=e=n[0],e.p=i}}function _t(n,t,e,r){this.x=n,this.z=t,this.o=e,this.e=r,this.v=!1,this.n=this.p=null}function wt(m,M,x,b){return function(r,o){function t(n,t){var e=r(n,t);m(n=e[0],t=e[1])&&o.point(n,t)}function n(n,t){var e=r(n,t);h.point(e[0],e[1])}function e(){g.point=n,h.lineStart()}function i(){g.point=t,h.lineEnd()}function a(n,t){s.push([n,t]);var e=r(n,t);v.point(e[0],e[1])}function u(){v.lineStart(),s=[]}function l(){a(s[0][0],s[0][1]),v.lineEnd();var n,t=v.clean(),e=d.buffer(),r=e.length;if(s.pop(),f.push(s),s=null,r)if(1&t){var i,u=-1;if(0<(r=(n=e[0]).length-1)){for(y||(o.polygonStart(),y=!0),o.lineStart();++u<r;)o.point((i=n[u])[0],i[1]);o.lineEnd()}}else 1<r&&2&t&&e.push(e.pop().concat(e.shift())),c.push(e.filter(St))}var c,f,s,h=M(o),p=r.invert(b[0],b[1]),g={point:t,lineStart:e,lineEnd:i,polygonStart:function(){g.point=a,g.lineStart=u,g.lineEnd=l,c=[],f=[]},polygonEnd:function(){g.point=t,g.lineStart=e,g.lineEnd=i,c=Bi.merge(c);var n=function(n,t){var e=n[0],r=n[1],i=[Math.sin(e),-Math.cos(e),0],u=0,o=0;xo.reset();for(var a=0,l=t.length;a<l;++a){var c=t[a],f=c.length;if(f)for(var s=c[0],h=s[0],p=s[1]/2+wu/4,g=Math.sin(p),d=Math.cos(p),v=1;;){v===f&&(v=0);var y,m,M,x=(n=c[v])[0],b=n[1]/2+wu/4,_=Math.sin(b),w=Math.cos(b),S=x-h,k=0<=S?1:-1,N=k*S,E=wu<N,A=g*_;if(xo.add(Math.atan2(A*k*Math.sin(N),d*w+A*Math.cos(N))),u+=E?S+k*Su:S,E^e<=h^e<=x&&(ft(y=at(ut(s),ut(n))),ft(m=at(i,y)),((M=(E^0<=S?-1:1)*G(m[2]))<r||r===M&&(y[0]||y[1]))&&(o+=E^0<=S?1:-1)),!v++)break;h=x,g=_,d=w,s=n}}return(u<-bu||u<bu&&xo<-bu)^1&o}(p,f);c.length?(y||(o.polygonStart(),y=!0),xt(c,Nt,n,x,o)):n&&(y||(o.polygonStart(),y=!0),o.lineStart(),x(null,null,1,o),o.lineEnd()),y&&(o.polygonEnd(),y=!1),c=f=null},sphere:function(){o.polygonStart(),o.lineStart(),x(null,null,1,o),o.lineEnd(),o.polygonEnd()}},d=kt(),v=M(d),y=!1;return g}}function St(n){return 1<n.length}function kt(){var e,t=[];return{lineStart:function(){t.push(e=[])},point:function(n,t){e.push([n,t])},lineEnd:y,buffer:function(){var n=t;return t=[],e=null,n},rejoin:function(){1<t.length&&t.push(t.pop().concat(t.shift()))}}}function Nt(n,t){return((n=n.x)[0]<0?n[1]-Nu-bu:Nu-n[1])-((t=t.x)[0]<0?t[1]-Nu-bu:Nu-t[1])}function Et(i){function p(n,t){return Math.cos(n)*Math.cos(t)>N}function g(n,t,e){var r=[1,0,0],i=at(ut(n),ut(t)),u=ot(i,i),o=i[0],a=u-o*o;if(!a)return!e&&n;var l=N*u/a,c=-N*o/a,f=at(r,i),s=ct(r,l);lt(s,ct(i,c));var h=f,p=ot(s,h),g=ot(h,h),d=p*p-g*(ot(s,s)-1);if(!(d<0)){var v=Math.sqrt(d),y=ct(h,(-p-v)/g);if(lt(y,s),y=st(y),!e)return y;var m,M=n[0],x=t[0],b=n[1],_=t[1];x<M&&(m=M,M=x,x=m);var w=x-M,S=iu(w-wu)<bu;if(!S&&_<b&&(m=b,b=_,_=m),S||w<bu?S?0<b+_^y[1]<(iu(y[0]-M)<bu?b:_):b<=y[1]&&y[1]<=_:wu<w^(M<=y[0]&&y[0]<=x)){var k=ct(h,(-p+v)/g);return lt(k,s),[y,st(k)]}}}function d(n,t){var e=v?i:wu-i,r=0;return n<-e?r|=1:e<n&&(r|=2),t<-e?r|=4:e<t&&(r|=8),r}var N=Math.cos(i),v=0<N,y=iu(N)>bu;return wt(p,function(a){var l,c,f,s,h;return{lineStart:function(){s=f=!1,h=1},point:function(n,t){var e,r,i=[n,t],u=p(n,t),o=v?u?0:d(n,t):u?d(n+(n<0?wu:-wu),t):0;!l&&(s=f=u)&&a.lineStart(),u!==f&&(e=g(l,i),(ht(l,e)||ht(i,e))&&(i[0]+=bu,i[1]+=bu,u=p(i[0],i[1]))),u!==f?(h=0,u?(a.lineStart(),e=g(i,l),a.point(e[0],e[1])):(e=g(l,i),a.point(e[0],e[1]),a.lineEnd()),l=e):y&&l&&v^u&&(o&c||!(r=g(i,l,!0))||(h=0,v?(a.lineStart(),a.point(r[0][0],r[0][1]),a.point(r[1][0],r[1][1]),a.lineEnd()):(a.point(r[1][0],r[1][1]),a.lineEnd(),a.lineStart(),a.point(r[0][0],r[0][1])))),!u||l&&ht(l,i)||a.point(i[0],i[1]),l=i,f=u,c=o},lineEnd:function(){f&&a.lineEnd(),l=null},clean:function(){return h|(s&&f)<<1}}},Kt(i,6*Eu),v?[0,-i]:[-wu,i-wu])}function At(f,s,h,p){return function(n){var t=n.a,e=n.b,r=t.x,i=t.y,u=0,o=1,a=e.x-r,l=e.y-i,c=f-r;if(a||!(0<c)){if(c/=a,a<0){if(c<u)return;c<o&&(o=c)}else if(0<a){if(o<c)return;u<c&&(u=c)}if(c=h-r,a||!(c<0)){if(c/=a,a<0){if(o<c)return;u<c&&(u=c)}else if(0<a){if(c<u)return;c<o&&(o=c)}if(c=s-i,l||!(0<c)){if(c/=l,l<0){if(c<u)return;c<o&&(o=c)}else if(0<l){if(o<c)return;u<c&&(u=c)}if(c=p-i,l||!(c<0)){if(c/=l,l<0){if(o<c)return;u<c&&(u=c)}else if(0<l){if(c<u)return;c<o&&(o=c)}return 0<u&&(n.a={x:r+u*a,y:i+u*l}),o<1&&(n.b={x:r+o*a,y:i+o*l}),n}}}}}}function Ct(x,b,_,w){function S(n,t){return iu(n[0]-x)<bu?0<t?0:3:iu(n[0]-_)<bu?0<t?2:1:iu(n[1]-b)<bu?0<t?1:0:0<t?3:2}function k(n,t){return N(n.x,t.x)}function N(n,t){var e=S(n,1),r=S(t,1);return e!==r?e-r:0===e?t[1]-n[1]:1===e?n[0]-t[0]:2===e?n[1]-t[1]:t[0]-n[0]}return function(i){function r(n,t,e,r){var i=0,u=0;if(null==n||(i=S(n,e))!==(u=S(t,e))||N(n,t)<0^0<e)for(;r.point(0===i||3===i?x:_,1<i?w:b),(i=(i+e+4)%4)!==u;);else r.point(t[0],t[1])}function u(n,t){return x<=n&&n<=_&&b<=t&&t<=w}function n(n,t){u(n,t)&&i.point(n,t)}function t(n,t){var e,r=u(n=Math.max(-Vo,Math.min(Vo,n)),t=Math.max(-Vo,Math.min(Vo,t)));f&&a.push([n,t]),d?(l=n,c=t,d=!1,(s=r)&&(i.lineStart(),i.point(n,t))):r&&g?i.point(n,t):m(e={a:{x:h,y:p},b:{x:n,y:t}})?(g||(i.lineStart(),i.point(e.a.x,e.a.y)),i.point(e.b.x,e.b.y),r||i.lineEnd(),v=!1):r&&(i.lineStart(),i.point(n,t),v=!1),h=n,p=t,g=r}var o,f,a,l,c,s,h,p,g,d,v,y=i,e=kt(),m=At(x,b,_,w),M={point:n,lineStart:function(){M.point=t,f&&f.push(a=[]),g=!(d=!0),h=p=NaN},lineEnd:function(){o&&(t(l,c),s&&g&&e.rejoin(),o.push(e.buffer())),M.point=n,g&&i.lineEnd()},polygonStart:function(){i=e,o=[],f=[],v=!0},polygonEnd:function(){i=y,o=Bi.merge(o);var n=function(n){for(var t=0,e=f.length,r=n[1],i=0;i<e;++i)for(var u,o=1,a=f[i],l=a.length,c=a[0];o<l;++o)u=a[o],c[1]<=r?u[1]>r&&0<W(c,u,n)&&++t:u[1]<=r&&W(c,u,n)<0&&--t,c=u;return 0!==t}([x,w]),t=v&&n,e=o.length;(t||e)&&(i.polygonStart(),t&&(i.lineStart(),r(null,null,1,i),i.lineEnd()),e&&xt(o,k,n,r,i),i.polygonEnd()),o=f=a=null}};return M}}function zt(n){var t=0,e=wu/3,r=Zt(n),i=r(t,e);return i.parallels=function(n){return arguments.length?r(t=n[0]*wu/180,e=n[1]*wu/180):[t/wu*180,e/wu*180]},i}function Lt(n,t){function e(n,t){var e=Math.sqrt(u-2*i*Math.sin(t))/i;return[e*Math.sin(n*=i),o-e*Math.cos(n)]}var r=Math.sin(n),i=(r+Math.sin(t))/2,u=1+r*(2*i-r),o=Math.sqrt(u)/i;return e.invert=function(n,t){var e=o-t;return[Math.atan2(n,e)/i,G((u-(n*n+e*e)*i*i)/(2*i))]},e}function qt(){function e(n,t){$o+=o*n-u*t,u=n,o=t}var r,i,u,o;Ko.point=function(n,t){Ko.point=e,r=u=n,i=o=t},Ko.lineEnd=function(){e(r,i)}}function Tt(){function n(n,t){o.push("M",n,",",t,u)}function t(n,t){o.push("M",n,",",t),a.point=e}function e(n,t){o.push("L",n,",",t)}function r(){a.point=n}function i(){o.push("Z")}var u=Rt(4.5),o=[],a={point:n,lineStart:function(){a.point=t},lineEnd:r,polygonStart:function(){a.lineEnd=i},polygonEnd:function(){a.lineEnd=r,a.point=n},pointRadius:function(n){return u=Rt(n),a},result:function(){if(o.length){var n=o.join("");return o=[],n}}};return a}function Rt(n){return"m0,"+n+"a"+n+","+n+" 0 1,1 0,"+-2*n+"a"+n+","+n+" 0 1,1 0,"+2*n+"z"}function Dt(n,t){Ro+=n,Do+=t,++Po}function Pt(){function e(n,t){var e=n-u,r=t-o,i=Math.sqrt(e*e+r*r);Uo+=i*(u+n)/2,jo+=i*(o+t)/2,Fo+=i,Dt(u=n,o=t)}var u,o;na.point=function(n,t){na.point=e,Dt(u=n,o=t)}}function Ut(){na.point=Dt}function jt(){function e(n,t){var e=n-u,r=t-o,i=Math.sqrt(e*e+r*r);Uo+=i*(u+n)/2,jo+=i*(o+t)/2,Fo+=i,Ho+=(i=o*n-u*t)*(u+n),Oo+=i*(o+t),Io+=3*i,Dt(u=n,o=t)}var r,i,u,o;na.point=function(n,t){na.point=e,Dt(r=u=n,i=o=t)},na.lineEnd=function(){e(r,i)}}function Ft(e){function n(n,t){e.moveTo(n+o,t),e.arc(n,t,o,0,Su)}function t(n,t){e.moveTo(n,t),a.point=r}function r(n,t){e.lineTo(n,t)}function i(){a.point=n}function u(){e.closePath()}var o=4.5,a={point:n,lineStart:function(){a.point=t},lineEnd:i,polygonStart:function(){a.lineEnd=u},polygonEnd:function(){a.lineEnd=i,a.point=n},pointRadius:function(n){return o=n,a},result:y};return a}function Ht(C){function t(n){return(b?function(i){function n(n,t){n=C(n,t),i.point(n[0],n[1])}function t(){d=NaN,x.point=e,i.lineStart()}function e(n,t){var e=ut([n,t]),r=C(n,t);z(d,v,g,y,m,M,d=r[0],v=r[1],g=n,y=e[0],m=e[1],M=e[2],b,i),i.point(d,v)}function r(){x.point=n,i.lineEnd()}function u(){t(),x.point=o,x.lineEnd=a}function o(n,t){e(l=n,t),c=d,f=v,s=y,h=m,p=M,x.point=e}function a(){z(d,v,g,y,m,M,c,f,l,s,h,p,b,i),x.lineEnd=r,r()}var l,c,f,s,h,p,g,d,v,y,m,M,x={point:n,lineStart:t,lineEnd:r,polygonStart:function(){i.polygonStart(),x.lineStart=u},polygonEnd:function(){i.polygonEnd(),x.lineStart=t}};return x}:function(e){return It(e,function(n,t){n=C(n,t),e.point(n[0],n[1])})})(n)}function z(n,t,e,r,i,u,o,a,l,c,f,s,h,p){var g,d,v,y,m,M,x,b,_,w,S,k,N=o-n,E=a-t,A=N*N+E*E;4*L<A&&h--&&(g=r+c,d=i+f,v=u+s,y=Math.sqrt(g*g+d*d+v*v),m=Math.asin(v/=y),M=iu(iu(v)-1)<bu||iu(e-l)<bu?(e+l)/2:Math.atan2(d,g),b=(x=C(M,m))[0],_=x[1],(L<(k=E*(w=b-n)-N*(S=_-t))*k/A||.3<iu((N*w+E*S)/A-.5)||r*c+i*f+u*s<q)&&(z(n,t,e,r,i,u,b,_,M,g/=y,d/=y,v,h,p),p.point(b,_),z(b,_,M,g,d,v,o,a,l,c,f,s,h,p)))}var L=.5,q=Math.cos(30*Eu),b=16;return t.precision=function(n){return arguments.length?(b=0<(L=n*n)&&16,t):Math.sqrt(L)},t}function Ot(n){this.stream=n}function It(n,t){return{point:t,sphere:function(){n.sphere()},lineStart:function(){n.lineStart()},lineEnd:function(){n.lineEnd()},polygonStart:function(){n.polygonStart()},polygonEnd:function(){n.polygonEnd()}}}function Yt(n){return Zt(function(){return n})()}function Zt(n){function t(n){return[(n=a(n[0]*Eu,n[1]*Eu))[0]*h+l,c-n[1]*h]}function e(n){return(n=a.invert((n[0]-l)/h,(c-n[1])/h))&&[n[0]*Au,n[1]*Au]}function r(){a=mt(o=Bt(y,m,M),u);var n=u(d,v);return l=p-n[0]*h,c=g+n[1]*h,i()}function i(){return f&&(f.valid=!1,f=null),t}var u,o,a,l,c,f,s=Ht(function(n,t){return[(n=u(n,t))[0]*h+l,c-n[1]*h]}),h=150,p=480,g=250,d=0,v=0,y=0,m=0,M=0,x=Zo,b=R,_=null,w=null;return t.stream=function(n){return f&&(f.valid=!1),(f=Vt(x(o,s(b(n))))).valid=!0,f},t.clipAngle=function(n){return arguments.length?(x=null==n?(_=n,Zo):Et((_=+n)*Eu),i()):_},t.clipExtent=function(n){return arguments.length?(b=(w=n)?Ct(n[0][0],n[0][1],n[1][0],n[1][1]):R,i()):w},t.scale=function(n){return arguments.length?(h=+n,r()):h},t.translate=function(n){return arguments.length?(p=+n[0],g=+n[1],r()):[p,g]},t.center=function(n){return arguments.length?(d=n[0]%360*Eu,v=n[1]%360*Eu,r()):[d*Au,v*Au]},t.rotate=function(n){return arguments.length?(y=n[0]%360*Eu,m=n[1]%360*Eu,M=2<n.length?n[2]%360*Eu:0,r()):[y*Au,m*Au,M*Au]},Bi.rebind(t,s,"precision"),function(){return u=n.apply(this,arguments),t.invert=u.invert&&e,r()}}function Vt(e){return It(e,function(n,t){e.point(n*Eu,t*Eu)})}function Xt(n,t){return[n,t]}function $t(n,t){return[wu<n?n-Su:n<-wu?n+Su:n,t]}function Bt(n,t,e){return n?t||e?mt(Jt(n),Gt(t,e)):Jt(n):t||e?Gt(t,e):$t}function Wt(e){return function(n,t){return[wu<(n+=e)?n-Su:n<-wu?n+Su:n,t]}}function Jt(n){var t=Wt(n);return t.invert=Wt(-n),t}function Gt(n,t){function e(n,t){var e=Math.cos(t),r=Math.cos(n)*e,i=Math.sin(n)*e,u=Math.sin(t),o=u*a+r*l;return[Math.atan2(i*c-o*f,r*a-u*l),G(o*c+i*f)]}var a=Math.cos(n),l=Math.sin(n),c=Math.cos(t),f=Math.sin(t);return e.invert=function(n,t){var e=Math.cos(t),r=Math.cos(n)*e,i=Math.sin(n)*e,u=Math.sin(t),o=u*c-i*f;return[Math.atan2(i*c+u*f,r*a+o*l),G(o*a-r*l)]},e}function Kt(a,l){var c=Math.cos(a),f=Math.sin(a);return function(n,t,e,r){var i=e*l;null!=n?(n=Qt(c,n),t=Qt(c,t),(0<e?n<t:t<n)&&(n+=e*Su)):(n=a+e*Su,t=a-.5*i);for(var u,o=n;0<e?t<o:o<t;o-=i)r.point((u=st([c,-f*Math.cos(o),-f*Math.sin(o)]))[0],u[1])}}function Qt(n,t){var e=ut(t);e[0]-=n,ft(e);var r=J(-e[1]);return((-e[2]<0?-r:r)+2*Math.PI-bu)%(2*Math.PI)}function ne(n,t,e){var r=Bi.range(n,t-bu,e).concat(t);return function(t){return r.map(function(n){return[t,n]})}}function te(n,t,e){var r=Bi.range(n,t-bu,e).concat(t);return function(t){return r.map(function(n){return[n,t]})}}function ee(n){return n.source}function re(n){return n.target}function ie(u,o){function n(n,t){var e=Math.cos(n),r=Math.cos(t),i=u(e*r);return[i*r*Math.sin(n),i*Math.sin(t)]}return n.invert=function(n,t){var e=Math.sqrt(n*n+t*t),r=o(e),i=Math.sin(r),u=Math.cos(r);return[Math.atan2(n*i,e*u),Math.asin(e&&t*i/e)]},n}function ue(n,t){function e(n,t){0<o?t<-Nu+bu&&(t=-Nu+bu):Nu-bu<t&&(t=Nu-bu);var e=o/Math.pow(r(t),u);return[e*Math.sin(u*n),o-e*Math.cos(u*n)]}function r(n){return Math.tan(wu/4+n/2)}var i=Math.cos(n),u=n===t?Math.sin(n):Math.log(i/Math.cos(t))/Math.log(r(t)/r(n)),o=i*Math.pow(r(n),u)/u;return u?(e.invert=function(n,t){var e=o-t,r=B(u)*Math.sqrt(n*n+e*e);return[Math.atan2(n,e)/u,2*Math.atan(Math.pow(o/r,1/u))-Nu]},e):ae}function oe(n,t){function e(n,t){var e=u-t;return[e*Math.sin(i*n),u-e*Math.cos(i*n)]}var r=Math.cos(n),i=n===t?Math.sin(n):(r-Math.cos(t))/(t-n),u=r/i+n;return iu(i)<bu?Xt:(e.invert=function(n,t){var e=u-t;return[Math.atan2(n,e)/i,u-B(i)*Math.sqrt(n*n+e*e)]},e)}function ae(n,t){return[n,Math.log(Math.tan(wu/4+t/2))]}function le(n){var i,u=Yt(n),o=u.scale,a=u.translate,l=u.clipExtent;return u.scale=function(){var n=o.apply(u,arguments);return n===u?i?u.clipExtent(null):u:n},u.translate=function(){var n=a.apply(u,arguments);return n===u?i?u.clipExtent(null):u:n},u.clipExtent=function(n){var t,e,r=l.apply(u,arguments);return r===u?(i=null==n)&&(t=wu*o(),e=a(),l([[e[0]-t,e[1]-t],[e[0]+t,e[1]+t]])):i&&(r=null),r},u.clipExtent(null)}function ce(n,t){return[Math.log(Math.tan(wu/4+t/2)),-n]}function fe(n){return n[0]}function se(n){return n[1]}function he(n){for(var t=n.length,e=[0,1],r=2,i=2;i<t;i++){for(;1<r&&W(n[e[r-2]],n[e[r-1]],n[i])<=0;)--r;e[r++]=i}return e.slice(0,r)}function pe(n,t){return n[0]-t[0]||n[1]-t[1]}function ge(n,t,e){return(e[0]-t[0])*(n[1]-t[1])<(e[1]-t[1])*(n[0]-t[0])}function de(n,t,e,r){var i=n[0],u=e[0],o=t[0]-i,a=r[0]-u,l=n[1],c=e[1],f=t[1]-l,s=r[1]-c,h=(a*(l-c)-s*(i-u))/(s*o-a*f);return[i+h*o,l+h*f]}function ve(n){var t=n[0],e=n[n.length-1];return!(t[0]-e[0]||t[1]-e[1])}function ye(){Re(this),this.edge=this.site=this.circle=null}function me(n){var t=ga.pop()||new ye;return t.site=n,t}function Me(n){Ee(n),sa.remove(n),ga.push(n),Re(n)}function xe(n){for(var t,e,r,i,u=n.x,o=n.y,a=sa._;a;)if(r=be(a,o)-u,bu<r)a=a.L;else{if(i=u-function(n,t){var e=n.N;if(e)return be(e,t);var r=n.site;return r.y===t?r.x:1/0}(a,o),!(bu<i)){-bu<r?(t=a.P,e=a):-bu<i?e=(t=a).N:t=e=a;break}if(!a.R){t=a;break}a=a.R}var l=me(n);if(sa.insert(t,l),t||e){if(t===e)return Ee(t),e=me(t.site),sa.insert(l,e),l.edge=e.edge=ze(t.site,l.site),Ne(t),Ne(e),0;if(!e)return l.edge=ze(t.site,l.site),0;Ee(t),Ee(e);var c=t.site,f=c.x,s=c.y,h=n.x-f,p=n.y-s,g=e.site,d=g.x-f,v=g.y-s,y=2*(h*v-p*d),m=h*h+p*p,M=d*d+v*v,x={x:(v*m-p*M)/y+f,y:(h*M-d*m)/y+s};Le(e.edge,c,g,x),l.edge=ze(c,n,null,x),e.edge=ze(n,g,null,x),Ne(t),Ne(e)}}function be(n,t){var e=n.site,r=e.x,i=e.y,u=i-t;if(!u)return r;var o=n.P;if(!o)return-1/0;var a=(e=o.site).x,l=e.y,c=l-t;if(!c)return a;var f=a-r,s=1/u-1/c,h=f/c;return s?(-h+Math.sqrt(h*h-2*s*(f*f/(-2*c)-l+c/2+i-u/2)))/s+r:(r+a)/2}function _e(n){this.site=n,this.edges=[]}function we(n){for(var t,e,r,i,u,o,a,l,c,f,s,h,p,g,d=n[0][0],v=n[1][0],y=n[0][1],m=n[1][1],M=fa,x=M.length;x--;)if((u=M[x])&&u.prepare())for(l=(a=u.edges).length,o=0;o<l;)r=(f=a[o].end()).x,i=f.y,t=(c=a[++o%l].start()).x,e=c.y,(iu(r-t)>bu||iu(i-e)>bu)&&(a.splice(o,0,new qe((s=u.site,h=f,p=iu(r-d)<bu&&bu<m-i?{x:d,y:iu(t-d)<bu?e:m}:iu(i-m)<bu&&bu<v-r?{x:iu(e-m)<bu?t:v,y:m}:iu(r-v)<bu&&bu<i-y?{x:v,y:iu(t-v)<bu?e:y}:iu(i-y)<bu&&bu<r-d?{x:iu(e-y)<bu?t:d,y:y}:null,g=void 0,(g=new Ce(s,null)).a=h,g.b=p,ca.push(g),g),u.site,null)),++l)}function Se(n,t){return t.angle-n.angle}function ke(){Re(this),this.x=this.y=this.arc=this.site=this.cy=null}function Ne(n){var t=n.P,e=n.N;if(t&&e){var r=t.site,i=n.site,u=e.site;if(r!==u){var o=i.x,a=i.y,l=r.x-o,c=r.y-a,f=u.x-o,s=2*(l*(v=u.y-a)-c*f);if(!(-_u<=s)){var h=l*l+c*c,p=f*f+v*v,g=(v*h-c*p)/s,d=(l*p-f*h)/s,v=d+a,y=da.pop()||new ke;y.arc=n,y.site=i,y.x=g+o,y.y=v+Math.sqrt(g*g+d*d),y.cy=v,n.circle=y;for(var m=null,M=pa._;M;)if(y.y<M.y||y.y===M.y&&y.x<=M.x){if(!M.L){m=M.P;break}M=M.L}else{if(!M.R){m=M;break}M=M.R}pa.insert(m,y),m||(ha=y)}}}}function Ee(n){var t=n.circle;t&&(t.P||(ha=t.N),pa.remove(t),da.push(t),Re(t),n.circle=null)}function Ae(n){for(var t,e=ca,r=At(n[0][0],n[0][1],n[1][0],n[1][1]),i=e.length;i--;)(function(n,t){var e=n.b;if(e)return!0;var r,i,u=n.a,o=t[0][0],a=t[1][0],l=t[0][1],c=t[1][1],f=n.l,s=n.r,h=f.x,p=f.y,g=s.x,d=s.y,v=(h+g)/2,y=(p+d)/2;if(d===p){if(v<o||a<=v)return;if(g<h){if(u){if(u.y>=c)return}else u={x:v,y:l};e={x:v,y:c}}else{if(u){if(u.y<l)return}else u={x:v,y:c};e={x:v,y:l}}}else if(i=y-(r=(h-g)/(d-p))*v,r<-1||1<r)if(g<h){if(u){if(u.y>=c)return}else u={x:(l-i)/r,y:l};e={x:(c-i)/r,y:c}}else{if(u){if(u.y<l)return}else u={x:(c-i)/r,y:c};e={x:(l-i)/r,y:l}}else if(p<d){if(u){if(u.x>=a)return}else u={x:o,y:r*o+i};e={x:a,y:r*a+i}}else{if(u){if(u.x<o)return}else u={x:a,y:r*a+i};e={x:o,y:r*o+i}}return n.a=u,n.b=e,!0})(t=e[i],n)&&r(t)&&!(iu(t.a.x-t.b.x)<bu&&iu(t.a.y-t.b.y)<bu)||(t.a=t.b=null,e.splice(i,1))}function Ce(n,t){this.l=n,this.r=t,this.a=this.b=null}function ze(n,t,e,r){var i=new Ce(n,t);return ca.push(i),e&&Le(i,n,t,e),r&&Le(i,t,n,r),fa[n.i].edges.push(new qe(i,n,t)),fa[t.i].edges.push(new qe(i,t,n)),i}function Le(n,t,e,r){n.a||n.b?n.l===e?n.b=r:n.a=r:(n.a=r,n.l=t,n.r=e)}function qe(n,t,e){var r=n.a,i=n.b;this.edge=n,this.site=t,this.angle=e?Math.atan2(e.y-t.y,e.x-t.x):n.l===t?Math.atan2(i.x-r.x,r.y-i.y):Math.atan2(r.x-i.x,i.y-r.y)}function Te(){this._=null}function Re(n){n.U=n.C=n.L=n.R=n.P=n.N=null}function De(n,t){var e=t,r=t.R,i=e.U;i?i.L===e?i.L=r:i.R=r:n._=r,r.U=i,e.U=r,e.R=r.L,e.R&&(e.R.U=e),r.L=e}function Pe(n,t){var e=t,r=t.L,i=e.U;i?i.L===e?i.L=r:i.R=r:n._=r,r.U=i,e.U=r,e.L=r.R,e.L&&(e.L.U=e),r.R=e}function Ue(n){for(;n.L;)n=n.L;return n}function je(n,t){var e,r,i,u=n.sort(Fe).pop();for(ca=[],fa=new Array(n.length),sa=new Te,pa=new Te;;)if(i=ha,u&&(!i||u.y<i.y||u.y===i.y&&u.x<i.x))u.x===e&&u.y===r||(fa[u.i]=new _e(u),xe(u),e=u.x,r=u.y),u=n.pop();else{if(!i)break;!function(n){var t=n.circle,e=t.x,r=t.cy,i={x:e,y:r},u=n.P,o=n.N,a=[n];Me(n);for(var l=u;l.circle&&iu(e-l.circle.x)<bu&&iu(r-l.circle.cy)<bu;)u=l.P,a.unshift(l),Me(l),l=u;a.unshift(l),Ee(l);for(var c=o;c.circle&&iu(e-c.circle.x)<bu&&iu(r-c.circle.cy)<bu;)o=c.N,a.push(c),Me(c),c=o;a.push(c),Ee(c);for(var f=a.length,s=1;s<f;++s)c=a[s],l=a[s-1],Le(c.edge,l.site,c.site,i);l=a[0],(c=a[f-1]).edge=ze(l.site,c.site,null,i),Ne(l),Ne(c)}(i.arc)}t&&(Ae(t),we(t));var o={cells:fa,edges:ca};return sa=pa=ca=fa=null,o}function Fe(n,t){return t.y-n.y||t.x-n.x}function He(n){return n.x}function Oe(n){return n.y}function Ie(){return{leaf:!0,nodes:[],point:null,x:null,y:null}}function Ye(n,t){n=Bi.rgb(n),t=Bi.rgb(t);var e=n.r,r=n.g,i=n.b,u=t.r-e,o=t.g-r,a=t.b-i;return function(n){return"#"+dn(Math.round(e+u*n))+dn(Math.round(r+o*n))+dn(Math.round(i+a*n))}}function Ze(n,t){var e,r={},i={};for(e in n)e in t?r[e]=$e(n[e],t[e]):i[e]=n[e];for(e in t)e in n||(i[e]=t[e]);return function(n){for(e in r)i[e]=r[e](n);return i}}function Ve(t,e){return t=+t,e=+e,function(n){return t*(1-n)+e*n}}function Xe(n,r){var t,e,i,u=ya.lastIndex=ma.lastIndex=0,o=-1,a=[],l=[];for(n+="",r+="";(t=ya.exec(n))&&(e=ma.exec(r));)(i=e.index)>u&&(i=r.slice(u,i),a[o]?a[o]+=i:a[++o]=i),(t=t[0])===(e=e[0])?a[o]?a[o]+=e:a[++o]=e:(a[++o]=null,l.push({i:o,x:Ve(t,e)})),u=ma.lastIndex;return u<r.length&&(i=r.slice(u),a[o]?a[o]+=i:a[++o]=i),a.length<2?l[0]?(r=l[0].x,function(n){return r(n)+""}):function(){return r}:(r=l.length,function(n){for(var t,e=0;e<r;++e)a[(t=l[e]).i]=t.x(n);return a.join("")})}function $e(n,t){for(var e,r=Bi.interpolators.length;0<=--r&&!(e=Bi.interpolators[r](n,t)););return e}function Be(n,t){for(var e=[],r=[],i=n.length,u=t.length,o=Math.min(n.length,t.length),a=0;a<o;++a)e.push($e(n[a],t[a]));for(;a<i;++a)r[a]=n[a];for(;a<u;++a)r[a]=t[a];return function(n){for(a=0;a<o;++a)r[a]=e[a](n);return r}}function We(t){return function(n){return 1-t(1-n)}}function Je(t){return function(n){return.5*(n<.5?t(2*n):2-t(2-2*n))}}function Ge(n){return n*n}function Ke(n){return n*n*n}function Qe(n){if(n<=0)return 0;if(1<=n)return 1;var t=n*n,e=t*n;return 4*(n<.5?e:3*(n-t)+e-.75)}function nr(n){return 1-Math.cos(n*Nu)}function tr(n){return Math.pow(2,10*(n-1))}function er(n){return 1-Math.sqrt(1-n*n)}function rr(n){return n<1/2.75?7.5625*n*n:n<2/2.75?7.5625*(n-=1.5/2.75)*n+.75:n<2.5/2.75?7.5625*(n-=2.25/2.75)*n+.9375:7.5625*(n-=2.625/2.75)*n+.984375}function ir(t,e){return e-=t,function(n){return Math.round(t+e*n)}}function ur(n){var t,e,r,i=[n.a,n.b],u=[n.c,n.d],o=ar(i),a=or(i,u),l=ar(((t=u)[0]+=(r=-a)*(e=i)[0],t[1]+=r*e[1],t))||0;i[0]*u[1]<u[0]*i[1]&&(i[0]*=-1,i[1]*=-1,o*=-1,a*=-1),this.rotate=(o?Math.atan2(i[1],i[0]):Math.atan2(-u[0],u[1]))*Au,this.translate=[n.e,n.f],this.scale=[o,l],this.skew=l?Math.atan2(a,l)*Au:0}function or(n,t){return n[0]*t[0]+n[1]*t[1]}function ar(n){var t=Math.sqrt(or(n,n));return t&&(n[0]/=t,n[1]/=t),t}function lr(n){return n.length?n.pop()+",":""}function cr(n,t){var e,r,i,u,o,a,l,c,f,s,h,p,g,d,v,y,m,M,x=[],b=[];return n=Bi.transform(n),t=Bi.transform(t),d=n.translate,v=t.translate,y=x,m=b,d[0]!==v[0]||d[1]!==v[1]?(M=y.push("translate(",null,",",null,")"),m.push({i:M-4,x:Ve(d[0],v[0])},{i:M-2,x:Ve(d[1],v[1])})):(v[0]||v[1])&&y.push("translate("+v+")"),s=n.rotate,h=t.rotate,p=x,g=b,s!==h?(180<s-h?h+=360:180<h-s&&(s+=360),g.push({i:p.push(lr(p)+"rotate(",null,")")-2,x:Ve(s,h)})):h&&p.push(lr(p)+"rotate("+h+")"),a=n.skew,l=t.skew,c=x,f=b,a!==l?f.push({i:c.push(lr(c)+"skewX(",null,")")-2,x:Ve(a,l)}):l&&c.push(lr(c)+"skewX("+l+")"),e=n.scale,r=t.scale,i=x,u=b,e[0]!==r[0]||e[1]!==r[1]?(o=i.push(lr(i)+"scale(",null,",",null,")"),u.push({i:o-4,x:Ve(e[0],r[0])},{i:o-2,x:Ve(e[1],r[1])})):1===r[0]&&1===r[1]||i.push(lr(i)+"scale("+r+")"),n=t=null,function(n){for(var t,e=-1,r=b.length;++e<r;)x[(t=b[e]).i]=t.x(n);return x.join("")}}function fr(t,e){return e=(e-=t=+t)||1/e,function(n){return(n-t)/e}}function sr(t,e){return e=(e-=t=+t)||1/e,function(n){return Math.max(0,Math.min(1,(n-t)/e))}}function hr(n){for(var t=n.source,e=n.target,r=function(n,t){if(n===t)return n;for(var e=pr(n),r=pr(t),i=e.pop(),u=r.pop(),o=null;i===u;)o=i,i=e.pop(),u=r.pop();return o}(t,e),i=[t];t!==r;)t=t.parent,i.push(t);for(var u=i.length;e!==r;)i.splice(u,0,e),e=e.parent;return i}function pr(n){for(var t=[],e=n.parent;null!=e;)t.push(n),e=(n=e).parent;return t.push(n),t}function gr(n){n.fixed|=2}function dr(n){n.fixed&=-7}function vr(n){n.fixed|=4,n.px=n.x,n.py=n.y}function yr(n){n.fixed&=-5}function mr(n,t){return Bi.rebind(n,t,"sort","children","value"),(n.nodes=n).links=Sr,n}function Mr(n,t){for(var e,r,i=[n];null!=(n=i.pop());)if(t(n),(r=n.children)&&(e=r.length))for(;0<=--e;)i.push(r[e])}function xr(n,t){for(var e=[n],r=[];null!=(n=e.pop());)if(r.push(n),(u=n.children)&&(i=u.length))for(var i,u,o=-1;++o<i;)e.push(u[o]);for(;null!=(n=r.pop());)t(n)}function br(n){return n.children}function _r(n){return n.value}function wr(n,t){return t.value-n.value}function Sr(n){return Bi.merge(n.map(function(t){return(t.children||[]).map(function(n){return{source:t,target:n}})}))}function kr(n){return n.x}function Nr(n){return n.y}function Er(n,t,e){n.y0=t,n.y=e}function Ar(n){return Bi.range(n.length)}function Cr(n){for(var t=-1,e=n[0].length,r=[];++t<e;)r[t]=0;return r}function zr(n){for(var t,e=1,r=0,i=n[0][1],u=n.length;e<u;++e)(t=n[e][1])>i&&(r=e,i=t);return r}function Lr(n){return n.reduce(qr,0)}function qr(n,t){return n+t[1]}function Tr(n,t){return Rr(n,Math.ceil(Math.log(t.length)/Math.LN2+1))}function Rr(n,t){for(var e=-1,r=+n[0],i=(n[1]-r)/t,u=[];++e<=t;)u[e]=i*e+r;return u}function Dr(n){return[Bi.min(n),Bi.max(n)]}function Pr(n,t){return n.value-t.value}function Ur(n,t){var e=n._pack_next;(n._pack_next=t)._pack_prev=n,(t._pack_next=e)._pack_prev=t}function jr(n,t){(n._pack_next=t)._pack_prev=n}function Fr(n,t){var e=t.x-n.x,r=t.y-n.y,i=n.r+t.r;return e*e+r*r<.999*i*i}function Hr(n){function t(n){l=Math.min(n.x-n.r,l),c=Math.max(n.x+n.r,c),f=Math.min(n.y-n.r,f),s=Math.max(n.y+n.r,s)}if((e=n.children)&&(a=e.length)){var e,r,i,u,o,a,l=1/0,c=-1/0,f=1/0,s=-1/0;if(e.forEach(Or),(r=e[0]).x=-r.r,r.y=0,t(r),1<a&&((i=e[1]).x=i.r,i.y=0,t(i),2<a))for(Yr(r,i,u=e[2]),t(u),Ur(r,u),Ur(r._pack_prev=u,i),i=r._pack_next,M=3;M<a;M++){Yr(r,i,u=e[M]);for(var h=0,p=1,g=1,d=i._pack_next;d!==i;d=d._pack_next,p++)if(Fr(d,u)){h=1;break}if(1==h)for(o=r._pack_prev;o!==d._pack_prev&&!Fr(o,u);o=o._pack_prev,g++);h?(p<g||p==g&&i.r<r.r?jr(r,i=d):jr(r=o,i),M--):(Ur(r,u),t(i=u))}for(var v=(l+c)/2,y=(f+s)/2,m=0,M=0;M<a;M++)(u=e[M]).x-=v,u.y-=y,m=Math.max(m,u.r+Math.sqrt(u.x*u.x+u.y*u.y));n.r=m,e.forEach(Ir)}}function Or(n){n._pack_next=n._pack_prev=n}function Ir(n){delete n._pack_next,delete n._pack_prev}function Yr(n,t,e){var r,i,u,o,a=n.r+e.r,l=t.x-n.x,c=t.y-n.y;a&&(l||c)?(r=t.r+e.r,u=.5+((a*=a)-(r*=r))/(2*(i=l*l+c*c)),o=Math.sqrt(Math.max(0,2*r*(a+i)-(a-=i)*a-r*r))/(2*i),e.x=n.x+u*l+o*c,e.y=n.y+u*c-o*l):(e.x=n.x+a,e.y=n.y)}function Zr(n,t){return n.parent==t.parent?1:2}function Vr(n){var t=n.children;return t.length?t[0]:n.t}function Xr(n){var t,e=n.children;return(t=e.length)?e[t-1]:n.t}function $r(n){return{x:n.x,y:n.y,dx:n.dx,dy:n.dy}}function Br(n,t){var e=n.x+t[3],r=n.y+t[0],i=n.dx-t[1]-t[3],u=n.dy-t[0]-t[2];return i<0&&(e+=i/2,i=0),u<0&&(r+=u/2,u=0),{x:e,y:r,dx:i,dy:u}}function Wr(n){var t=n[0],e=n[n.length-1];return t<e?[t,e]:[e,t]}function Jr(n){return n.rangeExtent?n.rangeExtent():Wr(n.range())}function Gr(n,t,e,r){var i=e(n[0],n[1]),u=r(t[0],t[1]);return function(n){return u(i(n))}}function Kr(n,t){var e,r=0,i=n.length-1,u=n[r],o=n[i];return o<u&&(e=r,r=i,i=e,e=u,u=o,o=e),n[r]=t.floor(u),n[i]=t.ceil(o),n}function Qr(t){return t?{floor:function(n){return Math.floor(n/t)*t},ceil:function(n){return Math.ceil(n/t)*t}}:Ca}function ni(e,n,t,r){var i=[],u=[],o=0,a=Math.min(e.length,n.length)-1;for(e[a]<e[0]&&(e=e.slice().reverse(),n=n.slice().reverse());++o<=a;)i.push(t(e[o-1],e[o])),u.push(r(n[o-1],n[o]));return function(n){var t=Bi.bisect(e,n,1,a)-1;return u[t](i[t](n))}}function ti(n,t){return Bi.rebind(n,t,"range","rangeRound","interpolate","clamp")}function ei(n,t){return Kr(n,Qr(ri(n,t)[2])),Kr(n,Qr(ri(n,t)[2])),n}function ri(n,t){null==t&&(t=10);var e=Wr(n),r=e[1]-e[0],i=Math.pow(10,Math.floor(Math.log(r/t)/Math.LN10)),u=t/r*i;return u<=.15?i*=10:u<=.35?i*=5:u<=.75&&(i*=2),e[0]=Math.ceil(e[0]/i)*i,e[1]=Math.floor(e[1]/i)*i+.5*i,e[2]=i,e}function ii(n,t){return Bi.range.apply(Bi,ri(n,t))}function ui(n,t,e){var r,i,u,o=ri(n,t);if(e){var a=Bu.exec(e);if(a.shift(),"s"===a[8]){var l=Bi.formatPrefix(Math.max(iu(o[0]),iu(o[1])));return a[7]||(a[7]="."+oi(l.scale(o[2]))),a[8]="f",e=Bi.format(a.join("")),function(n){return e(l.scale(n))+l.symbol}}a[7]||(a[7]="."+(r=a[8],u=oi((i=o)[2]),r in za?Math.abs(u-oi(Math.max(iu(i[0]),iu(i[1]))))+ +("e"!==r):u-2*("%"===r))),e=a.join("")}else e=",."+oi(o[2])+"f";return Bi.format(e)}function oi(n){return-Math.floor(Math.log(n)/Math.LN10+.01)}function ai(t){return function(n){return n<0?-Math.pow(-n,t):Math.pow(n,t)}}function li(){return 0}function ci(n){return n.innerRadius}function fi(n){return n.outerRadius}function si(n){return n.startAngle}function hi(n){return n.endAngle}function pi(n){return n&&n.padAngle}function gi(n,t,e,r){return 0<(n-e)*t-(t-r)*n?0:1}function di(n,t,e,r,i){var u=n[0]-t[0],o=n[1]-t[1],a=(i?r:-r)/Math.sqrt(u*u+o*o),l=a*o,c=-a*u,f=n[0]+l,s=n[1]+c,h=t[0]+l,p=t[1]+c,g=(f+h)/2,d=(s+p)/2,v=h-f,y=p-s,m=v*v+y*y,M=e-r,x=f*p-h*s,b=(y<0?-1:1)*Math.sqrt(Math.max(0,M*M*m-x*x)),_=(x*y-v*b)/m,w=(-x*v-y*b)/m,S=(x*y+v*b)/m,k=(-x*v+y*b)/m,N=_-g,E=w-d,A=S-g,C=k-d;return A*A+C*C<N*N+E*E&&(_=S,w=k),[[_-l,w-c],[_*e/M,w*e/M]]}function vi(c){function t(n){function t(){r.push("M",p(c(i),g))}for(var e,r=[],i=[],u=-1,o=n.length,a=bn(f),l=bn(s);++u<o;)h.call(this,e=n[u],u)?i.push([+a.call(this,e,u),+l.call(this,e,u)]):i.length&&(t(),i=[]);return i.length&&t(),r.length?r.join(""):null}var f=fe,s=se,h=Mt,p=yi,e=p.key,g=.7;return t.x=function(n){return arguments.length?(f=n,t):f},t.y=function(n){return arguments.length?(s=n,t):s},t.defined=function(n){return arguments.length?(h=n,t):h},t.interpolate=function(n){return arguments.length?(e="function"==typeof n?p=n:(p=ja.get(n)||yi).key,t):e},t.tension=function(n){return arguments.length?(g=n,t):g},t}function yi(n){return 1<n.length?n.join("L"):n+"Z"}function mi(n){return n.join("L")+"Z"}function Mi(n){for(var t=0,e=n.length,r=n[0],i=[r[0],",",r[1]];++t<e;)i.push("V",(r=n[t])[1],"H",r[0]);return i.join("")}function xi(n){for(var t=0,e=n.length,r=n[0],i=[r[0],",",r[1]];++t<e;)i.push("H",(r=n[t])[0],"V",r[1]);return i.join("")}function bi(n,t){if(t.length<1||n.length!=t.length&&n.length!=t.length+2)return yi(n);var e,r=n.length!=t.length,i="",u=n[0],o=n[1],a=t[0],l=a,c=1;if(r&&(i+="Q"+(o[0]-2*a[0]/3)+","+(o[1]-2*a[1]/3)+","+o[0]+","+o[1],u=n[1],c=2),1<t.length){l=t[1],o=n[c],c++,i+="C"+(u[0]+a[0])+","+(u[1]+a[1])+","+(o[0]-l[0])+","+(o[1]-l[1])+","+o[0]+","+o[1];for(var f=2;f<t.length;f++,c++)o=n[c],l=t[f],i+="S"+(o[0]-l[0])+","+(o[1]-l[1])+","+o[0]+","+o[1]}return r&&(e=n[c],i+="Q"+(o[0]+2*l[0]/3)+","+(o[1]+2*l[1]/3)+","+e[0]+","+e[1]),i}function _i(n,t){for(var e,r=[],i=(1-t)/2,u=n[0],o=n[1],a=1,l=n.length;++a<l;)e=u,u=o,o=n[a],r.push([i*(o[0]-e[0]),i*(o[1]-e[1])]);return r}function wi(n){if(n.length<3)return yi(n);var t=1,e=n.length,r=n[0],i=r[0],u=r[1],o=[i,i,i,(r=n[1])[0]],a=[u,u,u,r[1]],l=[i,",",u,"L",Si(Oa,o),",",Si(Oa,a)];for(n.push(n[e-1]);++t<=e;)r=n[t],o.shift(),o.push(r[0]),a.shift(),a.push(r[1]),ki(l,o,a);return n.pop(),l.push("L",r),l.join("")}function Si(n,t){return n[0]*t[0]+n[1]*t[1]+n[2]*t[2]+n[3]*t[3]}function ki(n,t,e){n.push("C",Si(Fa,t),",",Si(Fa,e),",",Si(Ha,t),",",Si(Ha,e),",",Si(Oa,t),",",Si(Oa,e))}function Ni(n,t){return(t[1]-n[1])/(t[0]-n[0])}function Ei(n){for(var t,e,r,i,u=[],o=function(n){for(var t=0,e=n.length-1,r=[],i=n[0],u=n[1],o=r[0]=Ni(i,u);++t<e;)r[t]=(o+(o=Ni(u,u=n[t+1])))/2;return r[t]=o,r}(n),a=-1,l=n.length-1;++a<l;)t=Ni(n[a],n[a+1]),iu(t)<bu?o[a]=o[a+1]=0:9<(i=(e=o[a]/t)*e+(r=o[a+1]/t)*r)&&(i=3*t/Math.sqrt(i),o[a]=i*e,o[a+1]=i*r);for(a=-1;++a<=l;)i=(n[Math.min(l,a+1)][0]-n[Math.max(0,a-1)][0])/(6*(1+o[a]*o[a])),u.push([i||0,o[a]*i||0]);return u}function Ai(n){for(var t,e,r,i=-1,u=n.length;++i<u;)e=(t=n[i])[0],r=t[1]-Nu,t[0]=e*Math.cos(r),t[1]=e*Math.sin(r);return n}function Ci(g){function t(n){function t(){u.push("M",x(g(a),w),_,b(g(o.reverse()),w),"Z")}for(var e,r,i,u=[],o=[],a=[],l=-1,c=n.length,f=bn(d),s=bn(y),h=d===v?function(){return r}:bn(v),p=y===m?function(){return i}:bn(m);++l<c;)M.call(this,e=n[l],l)?(o.push([r=+f.call(this,e,l),i=+s.call(this,e,l)]),a.push([+h.call(this,e,l),+p.call(this,e,l)])):o.length&&(t(),o=[],a=[]);return o.length&&t(),u.length?u.join(""):null}var d=fe,v=fe,y=0,m=se,M=Mt,x=yi,e=x.key,b=x,_="L",w=.7;return t.x=function(n){return arguments.length?(d=v=n,t):v},t.x0=function(n){return arguments.length?(d=n,t):d},t.x1=function(n){return arguments.length?(v=n,t):v},t.y=function(n){return arguments.length?(y=m=n,t):m},t.y0=function(n){return arguments.length?(y=n,t):y},t.y1=function(n){return arguments.length?(m=n,t):m},t.defined=function(n){return arguments.length?(M=n,t):M},t.interpolate=function(n){return arguments.length?(e="function"==typeof n?x=n:(x=ja.get(n)||yi).key,b=x.reverse||x,_=x.closed?"M":"L",t):e},t.tension=function(n){return arguments.length?(w=n,t):w},t}function zi(n){return n.radius}function Li(n){return[n.x,n.y]}function qi(){return 64}function Ti(){return"circle"}function Ri(n){var t=Math.sqrt(n/wu);return"M0,"+t+"A"+t+","+t+" 0 1,1 0,"+-t+"A"+t+","+t+" 0 1,1 0,"+t+"Z"}function Di(r){return function(){var n,t,e;(n=this[r])&&(e=n[t=n.active])&&(e.timer.c=null,e.timer.t=NaN,--n.count?delete n[t]:delete this[r],n.active+=.5,e.event&&e.event.interrupt.call(this,this.__data__,e.index))}}function Pi(n,t,e){return cu(n,Ba),n.namespace=t,n.id=e,n}function Ui(n,r,i,u){var o=n.id,a=n.namespace;return H(n,"function"==typeof i?function(n,t,e){n[a][o].tween.set(r,u(i.call(n,n.__data__,t,e)))}:(i=u(i),function(n){n[a][o].tween.set(r,i)}))}function ji(n){return null==n&&(n=""),function(){this.textContent=n}}function Fi(n){return null==n?"__transition__":"__transition_"+n+"__"}function Hi(u,o,i,a,n){function e(n){var t,e=g.active,r=g[e];for(var i in r&&(r.timer.c=null,r.timer.t=NaN,--g.count,delete g[e],r.event&&r.event.interrupt.call(u,u.__data__,r.index)),g){+i<a&&((t=g[i]).timer.c=null,t.timer.t=NaN,--g.count,delete g[i])}f.c=l,Sn(function(){return f.c&&l(n||1)&&(f.c=null,f.t=NaN),1},0,c),g.active=a,d.event&&d.event.start.call(u,u.__data__,o),p=[],d.tween.forEach(function(n,t){(t=t.call(u,u.__data__,o))&&p.push(t)}),h=d.ease,s=d.duration}function l(n){for(var t=n/s,e=h(t),r=p.length;0<r;)p[--r].call(u,e);return 1<=t?(d.event&&d.event.end.call(u,u.__data__,o),--g.count?delete g[a]:delete u[i],1):void 0}var c,f,s,h,p,g=u[i]||(u[i]={active:0,count:0}),d=g[a];d||(c=n.time,f=Sn(function(n){var t=d.delay;return f.t=t+c,t<=n?e(n-t):void(f.c=e)},0,c),d=g[a]={tween:new m,time:c,timer:f,delay:n.delay,duration:n.duration,ease:n.ease,index:o},n=null,++g.count)}function Oi(n,e,r){n.attr("transform",function(n){var t=e(n);return"translate("+(isFinite(t)?t:r(n))+",0)"})}function Ii(n,e,r){n.attr("transform",function(n){var t=e(n);return"translate(0,"+(isFinite(t)?t:r(n))+")"})}function Yi(n){return n.toISOString()}function Zi(t,i,n){function o(n){return t(n)}function a(n,t){var e=(n[1]-n[0])/t,r=Bi.bisect(rl,e);return r==rl.length?[i.year,ri(n.map(function(n){return n/31536e6}),t)[2]]:r?i[e/rl[r-1]<rl[r]/e?r-1:r]:[ol,ri(n,t)[2]]}return o.invert=function(n){return Vi(t.invert(n))},o.domain=function(n){return arguments.length?(t.domain(n),o):t.domain().map(Vi)},o.nice=function(t,e){function r(n){return!isNaN(n)&&!t.range(n,Vi(+n+1),e).length}var n=o.domain(),i=Wr(n),u=null==t?a(i,10):"number"==typeof t&&a(i,t);return u&&(t=u[0],e=u[1]),o.domain(Kr(n,1<e?{floor:function(n){for(;r(n=t.floor(n));)n=Vi(n-1);return n},ceil:function(n){for(;r(n=t.ceil(n));)n=Vi(+n+1);return n}}:t))},o.ticks=function(n,t){var e=Wr(o.domain()),r=null==n?a(e,10):"number"==typeof n?a(e,n):!n.range&&[{range:n},t];return r&&(n=r[0],t=r[1]),n.range(e[0],Vi(+e[1]+1),t<1?1:t)},o.tickFormat=function(){return n},o.copy=function(){return Zi(t.copy(),i,n)},ti(o,t)}function Vi(n){return new Date(n)}function Xi(n){return JSON.parse(n.responseText)}function $i(n){var t=Gi.createRange();return t.selectNode(Gi.body),t.createContextualFragment(n.responseText)}var Bi={version:"3.5.17"},Wi=[].slice,Ji=function(n){return Wi.call(n)},Gi=this.document;if(Gi)try{Ji(Gi.documentElement.childNodes)[0].nodeType}catch(n){Ji=function(n){for(var t=n.length,e=new Array(t);t--;)e[t]=n[t];return e}}if(Date.now||(Date.now=function(){return+new Date}),Gi)try{Gi.createElement("DIV").style.setProperty("opacity",0,"")}catch(n){var Ki=this.Element.prototype,Qi=Ki.setAttribute,nu=Ki.setAttributeNS,tu=this.CSSStyleDeclaration.prototype,eu=tu.setProperty;Ki.setAttribute=function(n,t){Qi.call(this,n,t+"")},Ki.setAttributeNS=function(n,t,e){nu.call(this,n,t,e+"")},tu.setProperty=function(n,t,e){eu.call(this,n,t+"",e)}}Bi.ascending=o,Bi.descending=function(n,t){return t<n?-1:n<t?1:n<=t?0:NaN},Bi.min=function(n,t){var e,r,i=-1,u=n.length;if(1===arguments.length){for(;++i<u;)if(null!=(r=n[i])&&r<=r){e=r;break}for(;++i<u;)null!=(r=n[i])&&r<e&&(e=r)}else{for(;++i<u;)if(null!=(r=t.call(n,n[i],i))&&r<=r){e=r;break}for(;++i<u;)null!=(r=t.call(n,n[i],i))&&r<e&&(e=r)}return e},Bi.max=function(n,t){var e,r,i=-1,u=n.length;if(1===arguments.length){for(;++i<u;)if(null!=(r=n[i])&&r<=r){e=r;break}for(;++i<u;)null!=(r=n[i])&&e<r&&(e=r)}else{for(;++i<u;)if(null!=(r=t.call(n,n[i],i))&&r<=r){e=r;break}for(;++i<u;)null!=(r=t.call(n,n[i],i))&&e<r&&(e=r)}return e},Bi.extent=function(n,t){var e,r,i,u=-1,o=n.length;if(1===arguments.length){for(;++u<o;)if(null!=(r=n[u])&&r<=r){e=i=r;break}for(;++u<o;)null!=(r=n[u])&&(r<e&&(e=r),i<r&&(i=r))}else{for(;++u<o;)if(null!=(r=t.call(n,n[u],u))&&r<=r){e=i=r;break}for(;++u<o;)null!=(r=t.call(n,n[u],u))&&(r<e&&(e=r),i<r&&(i=r))}return[e,i]},Bi.sum=function(n,t){var e,r=0,i=n.length,u=-1;if(1===arguments.length)for(;++u<i;)f(e=+n[u])&&(r+=e);else for(;++u<i;)f(e=+t.call(n,n[u],u))&&(r+=e);return r},Bi.mean=function(n,t){var e,r=0,i=n.length,u=-1,o=i;if(1===arguments.length)for(;++u<i;)f(e=c(n[u]))?r+=e:--o;else for(;++u<i;)f(e=c(t.call(n,n[u],u)))?r+=e:--o;return o?r/o:void 0},Bi.quantile=function(n,t){var e=(n.length-1)*t+1,r=Math.floor(e),i=+n[r-1],u=e-r;return u?i+u*(n[r]-i):i},Bi.median=function(n,t){var e,r=[],i=n.length,u=-1;if(1===arguments.length)for(;++u<i;)f(e=c(n[u]))&&r.push(e);else for(;++u<i;)f(e=c(t.call(n,n[u],u)))&&r.push(e);return r.length?Bi.quantile(r.sort(o),.5):void 0},Bi.variance=function(n,t){var e,r,i=n.length,u=0,o=0,a=-1,l=0;if(1===arguments.length)for(;++a<i;)f(e=c(n[a]))&&(o+=(r=e-u)*(e-(u+=r/++l)));else for(;++a<i;)f(e=c(t.call(n,n[a],a)))&&(o+=(r=e-u)*(e-(u+=r/++l)));return 1<l?o/(l-1):void 0},Bi.deviation=function(){var n=Bi.variance.apply(this,arguments);return n?Math.sqrt(n):n};var ru=n(o);Bi.bisectLeft=ru.left,Bi.bisect=Bi.bisectRight=ru.right,Bi.bisector=function(e){return n(1===e.length?function(n,t){return o(e(n),t)}:e)},Bi.shuffle=function(n,t,e){(u=arguments.length)<3&&(e=n.length,u<2&&(t=0));for(var r,i,u=e-t;u;)i=Math.random()*u--|0,r=n[u+t],n[u+t]=n[i+t],n[i+t]=r;return n},Bi.permute=function(n,t){for(var e=t.length,r=new Array(e);e--;)r[e]=n[t[e]];return r},Bi.pairs=function(n){for(var t=0,e=n.length-1,r=n[0],i=new Array(e<0?0:e);t<e;)i[t]=[r,r=n[++t]];return i},Bi.transpose=function(n){if(!(i=n.length))return[];for(var t=-1,e=Bi.min(n,l),r=new Array(e);++t<e;)for(var i,u=-1,o=r[t]=new Array(i);++u<i;)o[u]=n[u][t];return r},Bi.zip=function(){return Bi.transpose(arguments)},Bi.keys=function(n){var t=[];for(var e in n)t.push(e);return t},Bi.values=function(n){var t=[];for(var e in n)t.push(n[e]);return t},Bi.entries=function(n){var t=[];for(var e in n)t.push({key:e,value:n[e]});return t},Bi.merge=function(n){for(var t,e,r,i=n.length,u=-1,o=0;++u<i;)o+=n[u].length;for(e=new Array(o);0<=--i;)for(t=(r=n[i]).length;0<=--t;)e[--o]=r[t];return e};var iu=Math.abs;Bi.range=function(n,t,e){if(arguments.length<3&&(e=1,arguments.length<2&&(t=n,n=0)),(t-n)/e==1/0)throw new Error("infinite range");var r,i=[],u=function(n){for(var t=1;n*t%1;)t*=10;return t}(iu(e)),o=-1;if(n*=u,t*=u,(e*=u)<0)for(;(r=n+e*++o)>t;)i.push(r/u);else for(;(r=n+e*++o)<t;)i.push(r/u);return i},Bi.map=function(n,t){var e=new m;if(n instanceof m)n.forEach(function(n,t){e.set(n,t)});else if(Array.isArray(n)){var r,i=-1,u=n.length;if(1===arguments.length)for(;++i<u;)e.set(i,n[i]);else for(;++i<u;)e.set(t.call(n,r=n[i],i),r)}else for(var o in n)e.set(o,n[o]);return e};var uu="__proto__",ou="\0";t(m,{has:i,get:function(n){return this._[e(n)]},set:function(n,t){return this._[e(n)]=t},remove:u,keys:s,values:function(){var n=[];for(var t in this._)n.push(this._[t]);return n},entries:function(){var n=[];for(var t in this._)n.push({key:r(t),value:this._[t]});return n},size:h,empty:p,forEach:function(n){for(var t in this._)n.call(this,r(t),this._[t])}}),Bi.nest=function(){function s(e,n,r){if(r>=d.length)return p?p.call(g,n):h?n.sort(h):n;for(var t,i,u,o,a=-1,l=n.length,c=d[r++],f=new m;++a<l;)(o=f.get(t=c(i=n[a])))?o.push(i):f.set(t,[i]);return u=e?(i=e(),function(n,t){i.set(n,s(e,t,r))}):(i={},function(n,t){i[n]=s(e,t,r)}),f.forEach(u),i}var h,p,g={},d=[],t=[];return g.map=function(n,t){return s(t,n,0)},g.entries=function(n){return function e(n,r){if(r>=d.length)return n;var i=[],u=t[r++];return n.forEach(function(n,t){i.push({key:n,values:e(t,r)})}),u?i.sort(function(n,t){return u(n.key,t.key)}):i}(s(Bi.map,n,0),0)},g.key=function(n){return d.push(n),g},g.sortKeys=function(n){return t[d.length-1]=n,g},g.sortValues=function(n){return h=n,g},g.rollup=function(n){return p=n,g},g},Bi.set=function(n){var t=new g;if(n)for(var e=0,r=n.length;e<r;++e)t.add(n[e]);return t},t(g,{has:i,add:function(n){return this._[e(n+="")]=!0,n},remove:u,values:s,size:h,empty:p,forEach:function(n){for(var t in this._)n.call(this,r(t))}}),Bi.behavior={},Bi.rebind=function(n,t){for(var e,r=1,i=arguments.length;++r<i;)n[e=arguments[r]]=function(t,e,r){return function(){var n=r.apply(e,arguments);return n===e?t:n}}(n,t,t[e]);return n};var au=["webkit","ms","moz","Moz","o","O"];Bi.dispatch=function(){for(var n=new v,t=-1,e=arguments.length;++t<e;)n[arguments[t]]=M(n);return n},v.prototype.on=function(n,t){var e=n.indexOf("."),r="";if(0<=e&&(r=n.slice(e+1),n=n.slice(0,e)),n)return arguments.length<2?this[n].on(r):this[n].on(r,t);if(2===arguments.length){if(null==t)for(n in this)this.hasOwnProperty(n)&&this[n].on(r,null);return this}},Bi.event=null,Bi.requote=function(n){return n.replace(lu,"\\$&")};var lu=/[\\\^\$\*\+\?\|\[\]\(\)\.\{\}]/g,cu={}.__proto__?function(n,t){n.__proto__=t}:function(n,t){for(var e in t)n[e]=t[e]},fu=function(n,t){return t.querySelector(n)},su=function(n,t){return t.querySelectorAll(n)},hu=function(n,t){var e=n.matches||n[d(n,"matchesSelector")];return(hu=function(n,t){return e.call(n,t)})(n,t)};"function"==typeof Sizzle&&(fu=function(n,t){return Sizzle(n,t)[0]||null},hu=(su=Sizzle).matchesSelector),Bi.selection=function(){return Bi.select(Gi.documentElement)};var pu=Bi.selection.prototype=[];pu.select=function(n){var t,e,r,i,u=[];n=_(n);for(var o=-1,a=this.length;++o<a;){u.push(t=[]),t.parentNode=(r=this[o]).parentNode;for(var l=-1,c=r.length;++l<c;)(i=r[l])?(t.push(e=n.call(i,i.__data__,l,o)),e&&"__data__"in i&&(e.__data__=i.__data__)):t.push(null)}return b(u)},pu.selectAll=function(n){var t,e,r=[];n=w(n);for(var i=-1,u=this.length;++i<u;)for(var o=this[i],a=-1,l=o.length;++a<l;)(e=o[a])&&(r.push(t=Ji(n.call(e,e.__data__,a,i))),t.parentNode=e);return b(r)};var gu="http://www.w3.org/1999/xhtml",du={svg:"http://www.w3.org/2000/svg",xhtml:gu,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};Bi.ns={prefix:du,qualify:function(n){var t=n.indexOf(":"),e=n;return 0<=t&&"xmlns"!==(e=n.slice(0,t))&&(n=n.slice(t+1)),du.hasOwnProperty(e)?{space:du[e],local:n}:n}},pu.attr=function(n,t){if(arguments.length<2){if("string"==typeof n){var e=this.node();return(n=Bi.ns.qualify(n)).local?e.getAttributeNS(n.space,n.local):e.getAttribute(n)}for(t in n)this.each(S(t,n[t]));return this}return this.each(S(n,t))},pu.classed=function(n,t){if(arguments.length<2){if("string"==typeof n){var e=this.node(),r=(n=E(n)).length,i=-1;if(t=e.classList){for(;++i<r;)if(!t.contains(n[i]))return!1}else for(t=e.getAttribute("class");++i<r;)if(!N(n[i]).test(t))return!1;return!0}for(t in n)this.each(A(t,n[t]));return this}return this.each(A(n,t))},pu.style=function(n,t,e){var r=arguments.length;if(r<3){if("string"!=typeof n){for(e in r<2&&(t=""),n)this.each(z(e,n[e],t));return this}if(r<2){var i=this.node();return T(i).getComputedStyle(i,null).getPropertyValue(n)}e=""}return this.each(z(n,t,e))},pu.property=function(n,t){if(arguments.length<2){if("string"==typeof n)return this.node()[n];for(t in n)this.each(L(t,n[t]));return this}return this.each(L(n,t))},pu.text=function(t){return arguments.length?this.each("function"==typeof t?function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}:null==t?function(){this.textContent=""}:function(){this.textContent=t}):this.node().textContent},pu.html=function(t){return arguments.length?this.each("function"==typeof t?function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}:null==t?function(){this.innerHTML=""}:function(){this.innerHTML=t}):this.node().innerHTML},pu.append=function(n){return n=q(n),this.select(function(){return this.appendChild(n.apply(this,arguments))})},pu.insert=function(n,t){return n=q(n),t=_(t),this.select(function(){return this.insertBefore(n.apply(this,arguments),t.apply(this,arguments)||null)})},pu.remove=function(){return this.each(U)},pu.data=function(n,g){function t(n,t){var e,r,i=n.length,u=t.length,o=Math.min(i,u),a=new Array(u),l=new Array(u),c=new Array(i);if(g){for(var f,s=new m,h=new Array(i),p=-1;++p<i;)(e=n[p])&&(s.has(f=g.call(e,e.__data__,p))?c[p]=e:s.set(f,e),h[p]=f);for(p=-1;++p<u;)(e=s.get(f=g.call(t,r=t[p],p)))?!0!==e&&((a[p]=e).__data__=r):l[p]=j(r),s.set(f,!0);for(p=-1;++p<i;)p in h&&!0!==s.get(h[p])&&(c[p]=n[p])}else{for(p=-1;++p<o;)e=n[p],r=t[p],e?(e.__data__=r,a[p]=e):l[p]=j(r);for(;p<u;++p)l[p]=j(t[p]);for(;p<i;++p)c[p]=n[p]}l.update=a,l.parentNode=a.parentNode=c.parentNode=n.parentNode,d.push(l),v.push(a),y.push(c)}var e,r,i=-1,u=this.length;if(!arguments.length){for(n=new Array(u=(e=this[0]).length);++i<u;)(r=e[i])&&(n[i]=r.__data__);return n}var d=O([]),v=b([]),y=b([]);if("function"==typeof n)for(;++i<u;)t(e=this[i],n.call(e,e.parentNode.__data__,i));else for(;++i<u;)t(e=this[i],n);return v.enter=function(){return d},v.exit=function(){return y},v},pu.datum=function(n){return arguments.length?this.property("__data__",n):this.property("__data__")},pu.filter=function(n){var t,e,r,i=[];"function"!=typeof n&&(n=F(n));for(var u=0,o=this.length;u<o;u++){i.push(t=[]),t.parentNode=(e=this[u]).parentNode;for(var a=0,l=e.length;a<l;a++)(r=e[a])&&n.call(r,r.__data__,a,u)&&t.push(r)}return b(i)},pu.order=function(){for(var n=-1,t=this.length;++n<t;)for(var e,r=this[n],i=r.length-1,u=r[i];0<=--i;)(e=r[i])&&(u&&u!==e.nextSibling&&u.parentNode.insertBefore(e,u),u=e);return this},pu.sort=function(n){n=function(e){return arguments.length||(e=o),function(n,t){return n&&t?e(n.__data__,t.__data__):!n-!t}}.apply(this,arguments);for(var t=-1,e=this.length;++t<e;)this[t].sort(n);return this.order()},pu.each=function(r){return H(this,function(n,t,e){r.call(n,n.__data__,t,e)})},pu.call=function(n){var t=Ji(arguments);return n.apply(t[0]=this,t),this},pu.empty=function(){return!this.node()},pu.node=function(){for(var n=0,t=this.length;n<t;n++)for(var e=this[n],r=0,i=e.length;r<i;r++){var u=e[r];if(u)return u}return null},pu.size=function(){var n=0;return H(this,function(){++n}),n};var vu=[];Bi.selection.enter=O,(Bi.selection.enter.prototype=vu).append=pu.append,vu.empty=pu.empty,vu.node=pu.node,vu.call=pu.call,vu.size=pu.size,vu.select=function(n){for(var t,e,r,i,u,o=[],a=-1,l=this.length;++a<l;){r=(i=this[a]).update,o.push(t=[]),t.parentNode=i.parentNode;for(var c=-1,f=i.length;++c<f;)(u=i[c])?(t.push(r[c]=e=n.call(i.parentNode,u.__data__,c,a)),e.__data__=u.__data__):t.push(null)}return b(o)},vu.insert=function(n,t){return arguments.length<2&&(o=this,t=function(n,t,e){var r,i=o[e].update,u=i.length;for(e!=l&&(l=e,a=0),a<=t&&(a=t+1);!(r=i[a])&&++a<u;);return r}),pu.insert.call(this,n,t);var o,a,l},Bi.select=function(n){var t;return"string"==typeof n?(t=[fu(n,Gi)]).parentNode=Gi.documentElement:(t=[n]).parentNode=a(n),b([t])},Bi.selectAll=function(n){var t;return"string"==typeof n?(t=Ji(su(n,Gi))).parentNode=Gi.documentElement:(t=Ji(n)).parentNode=null,b([t])},pu.on=function(n,t,e){var r=arguments.length;if(r<3){if("string"!=typeof n){for(e in r<2&&(t=!1),n)this.each(I(e,n[e],t));return this}if(r<2)return(r=this.node()["__on"+n])&&r._;e=!1}return this.each(I(n,t,e))};var yu=Bi.map({mouseenter:"mouseover",mouseleave:"mouseout"});Gi&&yu.forEach(function(n){"on"+n in Gi&&yu.remove(n)});var mu,Mu=0;Bi.mouse=function(n){return X(n,x())};var xu=this.navigator&&/WebKit/.test(this.navigator.userAgent)?-1:0;Bi.touch=function(n,t,e){if(arguments.length<3&&(e=t,t=x().changedTouches),t)for(var r,i=0,u=t.length;i<u;++i)if((r=t[i]).identifier===e)return X(n,r)},Bi.behavior.drag=function(){function t(){this.on("mousedown.drag",e).on("touchstart.drag",r)}function n(f,s,h,p,g){return function(){var n=Bi.event.target.correspondingElement||Bi.event.target,r=this.parentNode,i=d.of(this,arguments),u=0,o=f(),t=".drag"+(null==o?"":"-"+o),e=Bi.select(h(n)).on(p+t,function(){var n,t,e=s(r,o);e&&(n=e[0]-l[0],t=e[1]-l[1],u|=n|t,i({type:"drag",x:(l=e)[0]+c[0],y:e[1]+c[1],dx:n,dy:t}))}).on(g+t,function(){s(r,o)&&(e.on(p+t,null).on(g+t,null),a(u),i({type:"dragend"}))}),a=V(n),l=s(r,o),c=v?[(c=v.apply(this,arguments)).x-l[0],c.y-l[1]]:[0,0];i({type:"dragstart"})}}var d=P(t,"drag","dragstart","dragend"),v=null,e=n(y,Bi.mouse,T,"mousemove","mouseup"),r=n($,Bi.touch,R,"touchmove","touchend");return t.origin=function(n){return arguments.length?(v=n,t):v},Bi.rebind(t,d,"on")},Bi.touches=function(e,n){return arguments.length<2&&(n=x().touches),n?Ji(n).map(function(n){var t=X(e,n);return t.identifier=n.identifier,t}):[]};var bu=1e-6,_u=bu*bu,wu=Math.PI,Su=2*wu,ku=Su-bu,Nu=wu/2,Eu=wu/180,Au=180/wu,Cu=Math.SQRT2;Bi.interpolateZoom=function(n,t){var o,e,r,a,l,i,c=n[0],f=n[1],s=n[2],u=t[0],h=t[1],p=t[2],g=u-c,d=h-f,v=g*g+d*d;return(i=v<_u?(l=Math.log(p/s)/Cu,function(n){return[c+n*g,f+n*d,s*Math.exp(Cu*n*l)]}):(o=Math.sqrt(v),e=(p*p-s*s+4*v)/(2*s*2*o),r=(p*p-s*s-4*v)/(2*p*2*o),a=Math.log(Math.sqrt(e*e+1)-e),l=(Math.log(Math.sqrt(r*r+1)-r)-a)/Cu,function(n){var t,e,r=n*l,i=K(a),u=s/(2*o)*(i*(e=Cu*r+a,((e=Math.exp(2*e))-1)/(e+1))-(t=a,((t=Math.exp(t))-1/t)/2));return[c+u*g,f+u*d,s*i/K(Cu*r+a)]})).duration=1e3*l,i},Bi.behavior.zoom=function(){function i(n){n.on(C,k).on(Lu+".zoom",e).on("dblclick.zoom",r).on(L,N)}function a(n){return[(n[0]-A.x)/A.k,(n[1]-A.y)/A.k]}function x(n){A.k=Math.max(v[0],Math.min(v[1],n))}function b(n,t){var e;t=[(e=t)[0]*A.k+A.x,e[1]*A.k+A.y],A.x+=n[0]-t[0],A.y+=n[1]-t[1]}function _(n,t,e,r){n.__chart__={x:A.x,y:A.y,k:A.k},x(Math.pow(2,r)),b(c=t,e),n=Bi.select(n),0<y&&(n=n.transition().duration(y)),n.call(i.event)}function t(){h&&h.domain(s.range().map(function(n){return(n-A.x)/A.k}).map(s.invert)),g&&g.domain(p.range().map(function(n){return(n-A.y)/A.k}).map(p.invert))}function l(n){m++||n({type:"zoomstart"})}function w(n){t(),n({type:"zoom",scale:A.k,translate:[A.x,A.y]})}function S(n){--m||(n({type:"zoomend"}),c=null)}function k(){var n=this,t=q.of(n,arguments),e=0,r=Bi.select(T(n)).on(M,function(){e=1,b(Bi.mouse(n),i),w(t)}).on(z,function(){r.on(M,null).on(z,null),u(e),S(t)}),i=a(Bi.mouse(n)),u=V(n);$a.call(n),l(t)}function N(){function f(){var n=Bi.touches(p);return c=A.k,n.forEach(function(n){n.identifier in d&&(d[n.identifier]=a(n))}),n}function n(){var n=Bi.event.target;Bi.select(n).on(y,s).on(m,h),M.push(n);for(var t=Bi.event.changedTouches,e=0,r=t.length;e<r;++e)d[t[e].identifier]=null;var i,u,o,a,l=f(),c=Date.now();1===l.length?(c-E<500&&(i=l[0],_(p,i,d[i.identifier],Math.floor(Math.log(A.k)/Math.LN2)+1),D()),E=c):1<l.length&&(i=l[0],u=l[1],o=i[0]-u[0],a=i[1]-u[1],v=o*o+a*a)}function s(){var n,t,e=Bi.touches(p);$a.call(p);for(var r,i,u,o,a=0,l=e.length;a<l;++a,t=null)if(n=e[a],t=d[n.identifier]){if(o)break;u=n,o=t}t&&(r=(r=n[0]-u[0])*r+(r=n[1]-u[1])*r,i=v&&Math.sqrt(r/v),u=[(u[0]+n[0])/2,(u[1]+n[1])/2],o=[(o[0]+t[0])/2,(o[1]+t[1])/2],x(i*c)),E=null,b(u,o),w(g)}function h(){if(Bi.event.touches.length){for(var n=Bi.event.changedTouches,t=0,e=n.length;t<e;++t)delete d[n[t].identifier];for(var r in d)return void f()}Bi.selectAll(M).on(i,null),u.on(C,k).on(L,N),o(),S(g)}var c,p=this,g=q.of(p,arguments),d={},v=0,i=".zoom-"+Bi.event.changedTouches[0].identifier,y="touchmove"+i,m="touchend"+i,M=[],u=Bi.select(p),o=V(p);n(),l(g),u.on(C,null).on(L,n)}function e(){var n=q.of(this,arguments);f?clearTimeout(f):($a.call(this),u=a(c=o||Bi.mouse(this)),l(n)),f=setTimeout(function(){f=null,S(n)},50),D(),x(Math.pow(2,.002*zu())*A.k),b(c,u),w(n)}function r(){var n=Bi.mouse(this),t=Math.log(A.k)/Math.LN2;_(this,n,a(n),Bi.event.shiftKey?Math.ceil(t)-1:Math.floor(t)+1)}var u,c,o,f,E,s,h,p,g,A={x:0,y:0,k:1},d=[960,500],v=qu,y=250,m=0,C="mousedown.zoom",M="mousemove.zoom",z="mouseup.zoom",L="touchstart.zoom",q=P(i,"zoomstart","zoom","zoomend");return Lu=Lu||("onwheel"in Gi?(zu=function(){return-Bi.event.deltaY*(Bi.event.deltaMode?120:1)},"wheel"):"onmousewheel"in Gi?(zu=function(){return Bi.event.wheelDelta},"mousewheel"):(zu=function(){return-Bi.event.detail},"MozMousePixelScroll")),i.event=function(n){n.each(function(){var a=q.of(this,arguments),t=A;Va?Bi.select(this).transition().each("start.zoom",function(){A=this.__chart__||{x:0,y:0,k:1},l(a)}).tween("zoom:zoom",function(){var r=d[0],n=d[1],i=c?c[0]:r/2,u=c?c[1]:n/2,o=Bi.interpolateZoom([(i-A.x)/A.k,(u-A.y)/A.k,r/A.k],[(i-t.x)/t.k,(u-t.y)/t.k,r/t.k]);return function(n){var t=o(n),e=r/t[2];this.__chart__=A={x:i-t[0]*e,y:u-t[1]*e,k:e},w(a)}}).each("interrupt.zoom",function(){S(a)}).each("end.zoom",function(){S(a)}):(this.__chart__=A,l(a),w(a),S(a))})},i.translate=function(n){return arguments.length?(A={x:+n[0],y:+n[1],k:A.k},t(),i):[A.x,A.y]},i.scale=function(n){return arguments.length?(A={x:A.x,y:A.y,k:null},x(+n),t(),i):A.k},i.scaleExtent=function(n){return arguments.length?(v=null==n?qu:[+n[0],+n[1]],i):v},i.center=function(n){return arguments.length?(o=n&&[+n[0],+n[1]],i):o},i.size=function(n){return arguments.length?(d=n&&[+n[0],+n[1]],i):d},i.duration=function(n){return arguments.length?(y=+n,i):y},i.x=function(n){return arguments.length?(s=(h=n).copy(),A={x:0,y:0,k:1},i):h},i.y=function(n){return arguments.length?(p=(g=n).copy(),A={x:0,y:0,k:1},i):g},Bi.rebind(i,q,"on")};var zu,Lu,qu=[0,1/0];(Bi.color=nn).prototype.toString=function(){return this.rgb()+""};var Tu=(Bi.hsl=tn).prototype=new nn;Tu.brighter=function(n){return n=Math.pow(.7,arguments.length?n:1),new tn(this.h,this.s,this.l/n)},Tu.darker=function(n){return n=Math.pow(.7,arguments.length?n:1),new tn(this.h,this.s,n*this.l)},Tu.rgb=function(){return en(this.h,this.s,this.l)};var Ru=(Bi.hcl=rn).prototype=new nn;Ru.brighter=function(n){return new rn(this.h,this.c,Math.min(100,this.l+Du*(arguments.length?n:1)))},Ru.darker=function(n){return new rn(this.h,this.c,Math.max(0,this.l-Du*(arguments.length?n:1)))},Ru.rgb=function(){return un(this.h,this.c,this.l).rgb()},Bi.lab=on;var Du=18,Pu=.95047,Uu=1,ju=1.08883,Fu=on.prototype=new nn;Fu.brighter=function(n){return new on(Math.min(100,this.l+Du*(arguments.length?n:1)),this.a,this.b)},Fu.darker=function(n){return new on(Math.max(0,this.l-Du*(arguments.length?n:1)),this.a,this.b)},Fu.rgb=function(){return an(this.l,this.a,this.b)};var Hu=(Bi.rgb=hn).prototype=new nn;Hu.brighter=function(n){n=Math.pow(.7,arguments.length?n:1);var t=this.r,e=this.g,r=this.b;return t||e||r?(t&&t<30&&(t=30),e&&e<30&&(e=30),r&&r<30&&(r=30),new hn(Math.min(255,t/n),Math.min(255,e/n),Math.min(255,r/n))):new hn(30,30,30)},Hu.darker=function(n){return new hn((n=Math.pow(.7,arguments.length?n:1))*this.r,n*this.g,n*this.b)},Hu.hsl=function(){return yn(this.r,this.g,this.b)},Hu.toString=function(){return"#"+dn(this.r)+dn(this.g)+dn(this.b)};var Ou=Bi.map({aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074});Ou.forEach(function(n,t){Ou.set(n,pn(t))}),Bi.functor=bn,Bi.xhr=_n(R),Bi.dsv=function(i,u){function o(n,t,e){arguments.length<3&&(e=t,t=null);var r=wn(n,u,null==t?a:l(t),e);return r.row=function(n){return arguments.length?r.response(null==(t=n)?a:l(n)):t},r}function a(n){return o.parse(n.responseText)}function l(t){return function(n){return o.parse(n.responseText,t)}}function t(n){return n.map(c).join(i)}function c(n){return e.test(n)?'"'+n.replace(/\"/g,'""')+'"':n}var e=new RegExp('["'+i+"\n]"),h=i.charCodeAt(0);return o.parse=function(n,r){var i;return o.parseRows(n,function(n,t){if(i)return i(n,t-1);var e=new Function("d","return {"+n.map(function(n,t){return JSON.stringify(n)+": d["+t+"]"}).join(",")+"}");i=r?function(n,t){return r(e(n),t)}:e})},o.parseRows=function(i,n){function t(){if(l<=c)return a;if(u)return u=!1,o;var n=c;if(34===i.charCodeAt(n)){for(var t=n;t++<l;)if(34===i.charCodeAt(t)){if(34!==i.charCodeAt(t+1))break;++t}return c=t+2,13===(e=i.charCodeAt(t+1))?(u=!0,10===i.charCodeAt(t+2)&&++c):10===e&&(u=!0),i.slice(n+1,t).replace(/""/g,'"')}for(;c<l;){var e,r=1;if(10===(e=i.charCodeAt(c++)))u=!0;else if(13===e)u=!0,10===i.charCodeAt(c)&&(++c,++r);else if(e!==h)continue;return i.slice(n,c-r)}return i.slice(n)}for(var e,u,o={},a={},r=[],l=i.length,c=0,f=0;(e=t())!==a;){for(var s=[];e!==o&&e!==a;)s.push(e),e=t();n&&null==(s=n(s,f++))||r.push(s)}return r},o.format=function(n){if(Array.isArray(n[0]))return o.formatRows(n);var e=new g,r=[];return n.forEach(function(n){for(var t in n)e.has(t)||r.push(e.add(t))}),[r.map(c).join(i)].concat(n.map(function(t){return r.map(function(n){return c(t[n])}).join(i)})).join("\n")},o.formatRows=function(n){return n.map(t).join("\n")},o},Bi.csv=Bi.dsv(",","text/csv"),Bi.tsv=Bi.dsv("\t","text/tab-separated-values");var Iu,Yu,Zu,Vu,Xu=this[d(this,"requestAnimationFrame")]||function(n){setTimeout(n,17)};Bi.timer=function(){Sn.apply(this,arguments)},Bi.timer.flush=function(){Nn(),En()},Bi.round=function(n,t){return t?Math.round(n*(t=Math.pow(10,t)))/t:Math.round(n)};var $u=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"].map(function(n,t){var e=Math.pow(10,3*iu(8-t));return{scale:8<t?function(n){return n/e}:function(n){return n*e},symbol:n}});Bi.formatPrefix=function(n,t){var e=0;return(n=+n)&&(n<0&&(n*=-1),t&&(n=Bi.round(n,An(n,t))),e=1+Math.floor(1e-12+Math.log(n)/Math.LN10),e=Math.max(-24,Math.min(24,3*Math.floor((e-1)/3)))),$u[8+e/3]};var Bu=/(?:([^{])?([<>=^]))?([+\- ])?([$#])?(0)?(\d+)?(,)?(\.-?\d+)?([a-z%])?/i,Wu=Bi.map({b:function(n){return n.toString(2)},c:function(n){return String.fromCharCode(n)},o:function(n){return n.toString(8)},x:function(n){return n.toString(16)},X:function(n){return n.toString(16).toUpperCase()},g:function(n,t){return n.toPrecision(t)},e:function(n,t){return n.toExponential(t)},f:function(n,t){return n.toFixed(t)},r:function(n,t){return(n=Bi.round(n,An(n,t))).toFixed(Math.max(0,Math.min(20,An(n*(1+1e-15),t))))}}),Ju=Bi.time={},Gu=Date;zn.prototype={getDate:function(){return this._.getUTCDate()},getDay:function(){return this._.getUTCDay()},getFullYear:function(){return this._.getUTCFullYear()},getHours:function(){return this._.getUTCHours()},getMilliseconds:function(){return this._.getUTCMilliseconds()},getMinutes:function(){return this._.getUTCMinutes()},getMonth:function(){return this._.getUTCMonth()},getSeconds:function(){return this._.getUTCSeconds()},getTime:function(){return this._.getTime()},getTimezoneOffset:function(){return 0},valueOf:function(){return this._.valueOf()},setDate:function(){Ku.setUTCDate.apply(this._,arguments)},setDay:function(){Ku.setUTCDay.apply(this._,arguments)},setFullYear:function(){Ku.setUTCFullYear.apply(this._,arguments)},setHours:function(){Ku.setUTCHours.apply(this._,arguments)},setMilliseconds:function(){Ku.setUTCMilliseconds.apply(this._,arguments)},setMinutes:function(){Ku.setUTCMinutes.apply(this._,arguments)},setMonth:function(){Ku.setUTCMonth.apply(this._,arguments)},setSeconds:function(){Ku.setUTCSeconds.apply(this._,arguments)},setTime:function(){Ku.setTime.apply(this._,arguments)}};var Ku=Date.prototype;Ju.year=Ln(function(n){return(n=Ju.day(n)).setMonth(0,1),n},function(n,t){n.setFullYear(n.getFullYear()+t)},function(n){return n.getFullYear()}),Ju.years=Ju.year.range,Ju.years.utc=Ju.year.utc.range,Ju.day=Ln(function(n){var t=new Gu(2e3,0);return t.setFullYear(n.getFullYear(),n.getMonth(),n.getDate()),t},function(n,t){n.setDate(n.getDate()+t)},function(n){return n.getDate()-1}),Ju.days=Ju.day.range,Ju.days.utc=Ju.day.utc.range,Ju.dayOfYear=function(n){var t=Ju.year(n);return Math.floor((n-t-6e4*(n.getTimezoneOffset()-t.getTimezoneOffset()))/864e5)},["sunday","monday","tuesday","wednesday","thursday","friday","saturday"].forEach(function(n,e){e=7-e;var t=Ju[n]=Ln(function(n){return(n=Ju.day(n)).setDate(n.getDate()-(n.getDay()+e)%7),n},function(n,t){n.setDate(n.getDate()+7*Math.floor(t))},function(n){var t=Ju.year(n).getDay();return Math.floor((Ju.dayOfYear(n)+(t+e)%7)/7)-(t!==e)});Ju[n+"s"]=t.range,Ju[n+"s"].utc=t.utc.range,Ju[n+"OfYear"]=function(n){var t=Ju.year(n).getDay();return Math.floor((Ju.dayOfYear(n)+(t+e)%7)/7)}}),Ju.week=Ju.sunday,Ju.weeks=Ju.sunday.range,Ju.weeks.utc=Ju.sunday.utc.range,Ju.weekOfYear=Ju.sundayOfYear;var Qu={"-":"",_:" ",0:"0"},no=/^\s*\d+/,to=/^%/;Bi.locale=function(n){return{numberFormat:(S=(t=n).decimal,a=t.thousands,l=t.grouping,r=t.currency,k=l&&a?function(n,t){for(var e=n.length,r=[],i=0,u=l[0],o=0;0<e&&0<u&&(t<o+u+1&&(u=Math.max(1,t-o)),r.push(n.substring(e-=u,e+u)),!((o+=u+1)>t));)u=l[i=(i+1)%l.length];return r.reverse().join(a)}:R,function(n){var t=Bu.exec(n),f=t[1]||" ",s=t[2]||">",h=t[3]||"-",e=t[4]||"",p=t[5],g=+t[6],d=t[7],v=t[8],y=t[9],m=1,M="",x="",b=!1,_=!0,v=v&&+v.substring(1);switch((p||"0"===f&&"="===s)&&(p=f="0",s="="),y){case"n":d=!0,y="g";break;case"%":m=100,x="%",y="f";break;case"p":m=100,x="%",y="r";break;case"b":case"o":case"x":case"X":"#"===e&&(M="0"+y.toLowerCase());case"c":_=!1;case"d":b=!0,v=0;break;case"s":m=-1,y="r"}"$"===e&&(M=r[0],x=r[1]),"r"!=y||v||(y="g"),null!=v&&("g"==y?v=Math.max(1,Math.min(21,v)):"e"!=y&&"f"!=y||(v=Math.max(0,Math.min(20,v)))),y=Wu.get(y)||Cn;var w=p&&d;return function(n){var t=x;if(b&&n%1)return"";var e,r=n<0||0===n&&1/n<0?(n=-n,"-"):"-"===h?"":h;m<0?(n=(e=Bi.formatPrefix(n,v)).scale(n),t=e.symbol+x):n*=m;var i,u,o,a=(n=y(n,v)).lastIndexOf(".");o=a<0?(u=_?n.lastIndexOf("e"):-1)<0?(i=n,""):(i=n.substring(0,u),n.substring(u)):(i=n.substring(0,a),S+n.substring(a+1)),!p&&d&&(i=k(i,1/0));var l=M.length+i.length+o.length+(w?0:r.length),c=l<g?new Array(l=g-l+1).join(f):"";return w&&(i=k(c+i,c.length?g-o.length:1/0)),r+=M,n=i+o,("<"===s?r+n+c:">"===s?c+r+n:"^"===s?c.substring(0,l>>=1)+r+n+c.substring(l):r+(w?n:c+n))+t}}),timeFormat:Tn(n)};var t,S,a,l,r,k};var eo=Bi.locale({decimal:".",thousands:",",grouping:[3],currency:["$",""],dateTime:"%a %b %e %X %Y",date:"%m/%d/%Y",time:"%H:%M:%S",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});Bi.format=eo.numberFormat,Bi.geo={},Qn.prototype={s:0,t:0,add:function(n){nt(n,this.t,ro),nt(ro.s,this.s,this),this.s?this.t+=ro.t:this.s=ro.t},reset:function(){this.s=this.t=0},valueOf:function(){return this.s}};var ro=new Qn;Bi.geo.stream=function(n,t){n&&io.hasOwnProperty(n.type)?io[n.type](n,t):tt(n,t)};var io={Feature:function(n,t){tt(n.geometry,t)},FeatureCollection:function(n,t){for(var e=n.features,r=-1,i=e.length;++r<i;)tt(e[r].geometry,t)}},uo={Sphere:function(n,t){t.sphere()},Point:function(n,t){n=n.coordinates,t.point(n[0],n[1],n[2])},MultiPoint:function(n,t){for(var e=n.coordinates,r=-1,i=e.length;++r<i;)n=e[r],t.point(n[0],n[1],n[2])},LineString:function(n,t){et(n.coordinates,t,0)},MultiLineString:function(n,t){for(var e=n.coordinates,r=-1,i=e.length;++r<i;)et(e[r],t,0)},Polygon:function(n,t){rt(n.coordinates,t)},MultiPolygon:function(n,t){for(var e=n.coordinates,r=-1,i=e.length;++r<i;)rt(e[r],t)},GeometryCollection:function(n,t){for(var e=n.geometries,r=-1,i=e.length;++r<i;)tt(e[r],t)}};Bi.geo.area=function(n){return oo=0,Bi.geo.stream(n,bo),oo};var oo,ao,lo,co,fo,so,ho,po,go,vo,yo,mo,Mo,xo=new Qn,bo={sphere:function(){oo+=4*wu},point:y,lineStart:y,lineEnd:y,polygonStart:function(){xo.reset(),bo.lineStart=it},polygonEnd:function(){var n=2*xo;oo+=n<0?4*wu+n:n,bo.lineStart=bo.lineEnd=bo.point=y}};function _o(n,t){yo.push(mo=[ao=n,co=n]),t<lo&&(lo=t),fo<t&&(fo=t)}function wo(n,t){var e,r,i,u,o,a,l,c=ut([n*Eu,t*Eu]);go?(e=at(go,c),ft(r=at([e[1],-e[0],0],e)),r=st(r),u=0<(i=n-so)?1:-1,o=r[0]*Au*u,(a=180<iu(i))^(u*so<o&&o<u*n)?(l=r[1]*Au,fo<l&&(fo=l)):a^(u*so<(o=(o+360)%360-180)&&o<u*n)?(l=-r[1]*Au)<lo&&(lo=l):(t<lo&&(lo=t),fo<t&&(fo=t)),a?n<so?Co(ao,n)>Co(ao,co)&&(co=n):Co(n,co)>Co(ao,co)&&(ao=n):ao<=co?(n<ao&&(ao=n),co<n&&(co=n)):so<n?Co(ao,n)>Co(ao,co)&&(co=n):Co(n,co)>Co(ao,co)&&(ao=n)):_o(n,t),go=c,so=n}function So(){Mo.point=wo}function ko(){mo[0]=ao,mo[1]=co,Mo.point=_o,go=null}function No(n,t){var e;go?vo+=180<iu(e=n-so)?e+(0<e?360:-360):e:(ho=n,po=t),bo.point(n,t),wo(n,t)}function Eo(){bo.lineStart()}function Ao(){No(ho,po),bo.lineEnd(),iu(vo)>bu&&(ao=-(co=180)),mo[0]=ao,mo[1]=co,go=null}function Co(n,t){return(t-=n)<0?t+360:t}function zo(n,t){return n[0]-t[0]}function Lo(n,t){return t[0]<=t[1]?t[0]<=n&&n<=t[1]:n<t[0]||t[1]<n}Bi.geo.bounds=(Mo={point:_o,lineStart:So,lineEnd:ko,polygonStart:function(){Mo.point=No,Mo.lineStart=Eo,Mo.lineEnd=Ao,vo=0,bo.polygonStart()},polygonEnd:function(){bo.polygonEnd(),Mo.point=_o,Mo.lineStart=So,Mo.lineEnd=ko,xo<0?(ao=-(co=180),lo=-(fo=90)):bu<vo?fo=90:vo<-bu&&(lo=-90),mo[0]=ao,mo[1]=co}},function(n){if(fo=co=-(ao=lo=1/0),yo=[],Bi.geo.stream(n,Mo),u=yo.length){yo.sort(zo);for(var t=1,e=[a=yo[0]];t<u;++t)Lo((i=yo[t])[0],a)||Lo(i[1],a)?(Co(a[0],i[1])>Co(a[0],a[1])&&(a[1]=i[1]),Co(i[0],a[1])>Co(a[0],a[1])&&(a[0]=i[0])):e.push(a=i);for(var r,i,u,o=-1/0,t=0,a=e[u=e.length-1];t<=u;a=i,++t)i=e[t],(r=Co(a[1],i[0]))>o&&(o=r,ao=i[0],co=a[1])}return yo=mo=null,ao===1/0||lo===1/0?[[NaN,NaN],[NaN,NaN]]:[[ao,lo],[co,fo]]}),Bi.geo.centroid=function(n){qo=To=Ro=Do=Po=Uo=jo=Fo=Ho=Oo=Io=0,Bi.geo.stream(n,Yo);var t=Ho,e=Oo,r=Io,i=t*t+e*e+r*r;return i<_u&&(t=Uo,e=jo,r=Fo,To<bu&&(t=Ro,e=Do,r=Po),(i=t*t+e*e+r*r)<_u)?[NaN,NaN]:[Math.atan2(e,t)*Au,G(r/Math.sqrt(i))*Au]};var qo,To,Ro,Do,Po,Uo,jo,Fo,Ho,Oo,Io,Yo={sphere:y,point:pt,lineStart:dt,lineEnd:vt,polygonStart:function(){Yo.lineStart=yt},polygonEnd:function(){Yo.lineStart=dt}},Zo=wt(Mt,function(s){var h,p=NaN,g=NaN,d=NaN;return{lineStart:function(){s.lineStart(),h=1},point:function(n,t){var e,r,i,u,o,a,l,c=0<n?wu:-wu,f=iu(n-p);iu(f-wu)<bu?(s.point(p,g=0<(g+t)/2?Nu:-Nu),s.point(d,g),s.lineEnd(),s.lineStart(),s.point(c,g),s.point(n,g),h=0):d!==c&&wu<=f&&(iu(p-d)<bu&&(p-=d*bu),iu(n-c)<bu&&(n-=c*bu),e=p,r=g,i=n,u=t,l=Math.sin(e-i),g=iu(l)>bu?Math.atan((Math.sin(r)*(a=Math.cos(u))*Math.sin(i)-Math.sin(u)*(o=Math.cos(r))*Math.sin(e))/(o*a*l)):(r+u)/2,s.point(d,g),s.lineEnd(),s.lineStart(),s.point(c,g),h=0),s.point(p=n,g=t),d=c},lineEnd:function(){s.lineEnd(),p=g=NaN},clean:function(){return 2-h}}},function(n,t,e,r){var i,u;null==n?(u=e*Nu,r.point(-wu,u),r.point(0,u),r.point(wu,u),r.point(wu,0),r.point(wu,-u),r.point(0,-u),r.point(-wu,-u),r.point(-wu,0),r.point(-wu,u)):iu(n[0]-t[0])>bu?(u=e*(i=n[0]<t[0]?wu:-wu)/2,r.point(-i,u),r.point(0,u),r.point(i,u)):r.point(t[0],t[1])},[-wu,-wu/2]),Vo=1e9;Bi.geo.clipExtent=function(){var t,e,r,i,u,o,a={stream:function(n){return u&&(u.valid=!1),(u=o(n)).valid=!0,u},extent:function(n){return arguments.length?(o=Ct(t=+n[0][0],e=+n[0][1],r=+n[1][0],i=+n[1][1]),u&&(u.valid=!1,u=null),a):[[t,e],[r,i]]}};return a.extent([[0,0],[960,500]])},(Bi.geo.conicEqualArea=function(){return zt(Lt)}).raw=Lt,Bi.geo.albers=function(){return Bi.geo.conicEqualArea().rotate([96,0]).center([-.6,38.7]).parallels([29.5,45.5]).scale(1070)},Bi.geo.albersUsa=function(){function i(n){var t=n[0],e=n[1];return r=null,u(t,e),r||(o(t,e),r)||a(t,e),r}var r,u,o,a,l=Bi.geo.albers(),c=Bi.geo.conicEqualArea().rotate([154,0]).center([-2,58.5]).parallels([55,65]),f=Bi.geo.conicEqualArea().rotate([157,0]).center([-3,19.9]).parallels([8,18]),s={point:function(n,t){r=[n,t]}};return i.invert=function(n){var t=l.scale(),e=l.translate(),r=(n[0]-e[0])/t,i=(n[1]-e[1])/t;return(.12<=i&&i<.234&&-.425<=r&&r<-.214?c:.166<=i&&i<.234&&-.214<=r&&r<-.115?f:l).invert(n)},i.stream=function(n){var e=l.stream(n),r=c.stream(n),i=f.stream(n);return{point:function(n,t){e.point(n,t),r.point(n,t),i.point(n,t)},sphere:function(){e.sphere(),r.sphere(),i.sphere()},lineStart:function(){e.lineStart(),r.lineStart(),i.lineStart()},lineEnd:function(){e.lineEnd(),r.lineEnd(),i.lineEnd()},polygonStart:function(){e.polygonStart(),r.polygonStart(),i.polygonStart()},polygonEnd:function(){e.polygonEnd(),r.polygonEnd(),i.polygonEnd()}}},i.precision=function(n){return arguments.length?(l.precision(n),c.precision(n),f.precision(n),i):l.precision()},i.scale=function(n){return arguments.length?(l.scale(n),c.scale(.35*n),f.scale(n),i.translate(l.translate())):l.scale()},i.translate=function(n){if(!arguments.length)return l.translate();var t=l.scale(),e=+n[0],r=+n[1];return u=l.translate(n).clipExtent([[e-.455*t,r-.238*t],[e+.455*t,r+.238*t]]).stream(s).point,o=c.translate([e-.307*t,r+.201*t]).clipExtent([[e-.425*t+bu,r+.12*t+bu],[e-.214*t-bu,r+.234*t-bu]]).stream(s).point,a=f.translate([e-.205*t,r+.212*t]).clipExtent([[e-.214*t+bu,r+.166*t+bu],[e-.115*t-bu,r+.234*t-bu]]).stream(s).point,i},i.scale(1070)};var Xo,$o,Bo,Wo,Jo,Go,Ko={point:y,lineStart:y,lineEnd:y,polygonStart:function(){$o=0,Ko.lineStart=qt},polygonEnd:function(){Ko.lineStart=Ko.lineEnd=Ko.point=y,Xo+=iu($o/2)}},Qo={point:function(n,t){n<Bo&&(Bo=n),Jo<n&&(Jo=n),t<Wo&&(Wo=t),Go<t&&(Go=t)},lineStart:y,lineEnd:y,polygonStart:y,polygonEnd:y},na={point:Dt,lineStart:Pt,lineEnd:Ut,polygonStart:function(){na.lineStart=jt},polygonEnd:function(){na.point=Dt,na.lineStart=Pt,na.lineEnd=Ut}};Bi.geo.path=function(){function t(n){return n&&("function"==typeof l&&o.pointRadius(+l.apply(this,arguments)),a&&a.valid||(a=u(o)),Bi.geo.stream(n,a)),o.result()}function r(){return a=null,t}var i,e,u,o,a,l=4.5;return t.area=function(n){return Xo=0,Bi.geo.stream(n,u(Ko)),Xo},t.centroid=function(n){return Ro=Do=Po=Uo=jo=Fo=Ho=Oo=Io=0,Bi.geo.stream(n,u(na)),Io?[Ho/Io,Oo/Io]:Fo?[Uo/Fo,jo/Fo]:Po?[Ro/Po,Do/Po]:[NaN,NaN]},t.bounds=function(n){return Jo=Go=-(Bo=Wo=1/0),Bi.geo.stream(n,u(Qo)),[[Bo,Wo],[Jo,Go]]},t.projection=function(n){return arguments.length?(u=(i=n)?n.stream||(e=n,t=Ht(function(n,t){return e([n*Au,t*Au])}),function(n){return Vt(t(n))}):R,r()):i;var e,t},t.context=function(n){return arguments.length?(o=null==(e=n)?new Tt:new Ft(n),"function"!=typeof l&&o.pointRadius(l),r()):e},t.pointRadius=function(n){return arguments.length?(l="function"==typeof n?n:(o.pointRadius(+n),+n),t):l},t.projection(Bi.geo.albersUsa()).context(null)},Bi.geo.transform=function(r){return{stream:function(n){var t=new Ot(n);for(var e in r)t[e]=r[e];return t}}},Ot.prototype={point:function(n,t){this.stream.point(n,t)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}},Bi.geo.projection=Yt,Bi.geo.projectionMutator=Zt,(Bi.geo.equirectangular=function(){return Yt(Xt)}).raw=Xt.invert=Xt,Bi.geo.rotation=function(t){function n(n){return(n=t(n[0]*Eu,n[1]*Eu))[0]*=Au,n[1]*=Au,n}return t=Bt(t[0]%360*Eu,t[1]*Eu,2<t.length?t[2]*Eu:0),n.invert=function(n){return(n=t.invert(n[0]*Eu,n[1]*Eu))[0]*=Au,n[1]*=Au,n},n},$t.invert=Xt,Bi.geo.circle=function(){function t(){var n="function"==typeof u?u.apply(this,arguments):u,e=Bt(-n[0]*Eu,-n[1]*Eu,0).invert,r=[];return i(null,null,1,{point:function(n,t){r.push(n=e(n,t)),n[0]*=Au,n[1]*=Au}}),{type:"Polygon",coordinates:[r]}}var e,i,u=[0,0],r=6;return t.origin=function(n){return arguments.length?(u=n,t):u},t.angle=function(n){return arguments.length?(i=Kt((e=+n)*Eu,r*Eu),t):e},t.precision=function(n){return arguments.length?(i=Kt(e*Eu,(r=+n)*Eu),t):r},t.angle(90)},Bi.geo.distance=function(n,t){var e,r=(t[0]-n[0])*Eu,i=n[1]*Eu,u=t[1]*Eu,o=Math.sin(r),a=Math.cos(r),l=Math.sin(i),c=Math.cos(i),f=Math.sin(u),s=Math.cos(u);return Math.atan2(Math.sqrt((e=s*o)*e+(e=c*f-l*s*a)*e),l*f+c*s*a)},Bi.geo.graticule=function(){function t(){return{type:"MultiLineString",coordinates:n()}}function n(){return Bi.range(Math.ceil(u/v)*v,i,v).map(h).concat(Bi.range(Math.ceil(c/y)*y,l,y).map(p)).concat(Bi.range(Math.ceil(r/g)*g,e,g).filter(function(n){return iu(n%v)>bu}).map(f)).concat(Bi.range(Math.ceil(a/d)*d,o,d).filter(function(n){return iu(n%y)>bu}).map(s))}var e,r,i,u,o,a,l,c,f,s,h,p,g=10,d=g,v=90,y=360,m=2.5;return t.lines=function(){return n().map(function(n){return{type:"LineString",coordinates:n}})},t.outline=function(){return{type:"Polygon",coordinates:[h(u).concat(p(l).slice(1),h(i).reverse().slice(1),p(c).reverse().slice(1))]}},t.extent=function(n){return arguments.length?t.majorExtent(n).minorExtent(n):t.minorExtent()},t.majorExtent=function(n){return arguments.length?(u=+n[0][0],i=+n[1][0],c=+n[0][1],l=+n[1][1],i<u&&(n=u,u=i,i=n),l<c&&(n=c,c=l,l=n),t.precision(m)):[[u,c],[i,l]]},t.minorExtent=function(n){return arguments.length?(r=+n[0][0],e=+n[1][0],a=+n[0][1],o=+n[1][1],e<r&&(n=r,r=e,e=n),o<a&&(n=a,a=o,o=n),t.precision(m)):[[r,a],[e,o]]},t.step=function(n){return arguments.length?t.majorStep(n).minorStep(n):t.minorStep()},t.majorStep=function(n){return arguments.length?(v=+n[0],y=+n[1],t):[v,y]},t.minorStep=function(n){return arguments.length?(g=+n[0],d=+n[1],t):[g,d]},t.precision=function(n){return arguments.length?(m=+n,f=ne(a,o,90),s=te(r,e,m),h=ne(c,l,90),p=te(u,i,m),t):m},t.majorExtent([[-180,-90+bu],[180,90-bu]]).minorExtent([[-180,-80-bu],[180,80+bu]])},Bi.geo.greatArc=function(){function t(){return{type:"LineString",coordinates:[e||i.apply(this,arguments),r||u.apply(this,arguments)]}}var e,r,i=ee,u=re;return t.distance=function(){return Bi.geo.distance(e||i.apply(this,arguments),r||u.apply(this,arguments))},t.source=function(n){return arguments.length?(e="function"==typeof(i=n)?null:n,t):i},t.target=function(n){return arguments.length?(r="function"==typeof(u=n)?null:n,t):u},t.precision=function(){return arguments.length?t:0},t},Bi.geo.interpolate=function(n,t){return e=n[0]*Eu,r=n[1]*Eu,i=t[0]*Eu,u=t[1]*Eu,o=Math.cos(r),a=Math.sin(r),l=Math.cos(u),c=Math.sin(u),f=o*Math.cos(e),s=o*Math.sin(e),h=l*Math.cos(i),p=l*Math.sin(i),g=2*Math.asin(Math.sqrt(Q(u-r)+o*l*Q(i-e))),d=1/Math.sin(g),(v=g?function(n){var t=Math.sin(n*=g)*d,e=Math.sin(g-n)*d,r=e*f+t*h,i=e*s+t*p,u=e*a+t*c;return[Math.atan2(i,r)*Au,Math.atan2(u,Math.sqrt(r*r+i*i))*Au]}:function(){return[e*Au,r*Au]}).distance=g,v;var e,r,i,u,o,a,l,c,f,s,h,p,g,d,v},Bi.geo.length=function(n){return ta=0,Bi.geo.stream(n,ea),ta};var ta,ea={sphere:y,point:y,lineStart:function(){function e(n,t){var e=Math.sin(t*=Eu),r=Math.cos(t),i=iu((n*=Eu)-o),u=Math.cos(i);ta+=Math.atan2(Math.sqrt((i=r*Math.sin(i))*i+(i=l*e-a*r*u)*i),a*e+l*r*u),o=n,a=e,l=r}var o,a,l;ea.point=function(n,t){o=n*Eu,a=Math.sin(t*=Eu),l=Math.cos(t),ea.point=e},ea.lineEnd=function(){ea.point=ea.lineEnd=y}},lineEnd:y,polygonStart:y,polygonEnd:y},ra=ie(function(n){return Math.sqrt(2/(1+n))},function(n){return 2*Math.asin(n/2)});(Bi.geo.azimuthalEqualArea=function(){return Yt(ra)}).raw=ra;var ia=ie(function(n){var t=Math.acos(n);return t&&t/Math.sin(t)},R);(Bi.geo.azimuthalEquidistant=function(){return Yt(ia)}).raw=ia,(Bi.geo.conicConformal=function(){return zt(ue)}).raw=ue,(Bi.geo.conicEquidistant=function(){return zt(oe)}).raw=oe;var ua=ie(function(n){return 1/n},Math.atan);(Bi.geo.gnomonic=function(){return Yt(ua)}).raw=ua,ae.invert=function(n,t){return[n,2*Math.atan(Math.exp(t))-Nu]},(Bi.geo.mercator=function(){return le(ae)}).raw=ae;var oa=ie(function(){return 1},Math.asin);(Bi.geo.orthographic=function(){return Yt(oa)}).raw=oa;var aa=ie(function(n){return 1/(1+n)},function(n){return 2*Math.atan(n)});(Bi.geo.stereographic=function(){return Yt(aa)}).raw=aa,ce.invert=function(n,t){return[-t,2*Math.atan(Math.exp(n))-Nu]},(Bi.geo.transverseMercator=function(){var n=le(ce),t=n.center,e=n.rotate;return n.center=function(n){return n?t([-n[1],n[0]]):[(n=t())[1],-n[0]]},n.rotate=function(n){return n?e([n[0],n[1],2<n.length?n[2]+90:90]):[(n=e())[0],n[1],n[2]-90]},e([0,0,90])}).raw=ce,Bi.geom={},Bi.geom.hull=function(n){function t(n){if(n.length<3)return[];for(var t=bn(h),e=bn(p),r=n.length,i=[],u=[],o=0;o<r;o++)i.push([+t.call(this,n[o],o),+e.call(this,n[o],o),o]);for(i.sort(pe),o=0;o<r;o++)u.push([i[o][0],-i[o][1]]);var a=he(i),l=he(u),c=l[0]===a[0],f=l[l.length-1]===a[a.length-1],s=[];for(o=a.length-1;0<=o;--o)s.push(n[i[a[o]][2]]);for(o=+c;o<l.length-f;++o)s.push(n[i[l[o]][2]]);return s}var h=fe,p=se;return arguments.length?t(n):(t.x=function(n){return arguments.length?(h=n,t):h},t.y=function(n){return arguments.length?(p=n,t):p},t)},Bi.geom.polygon=function(n){return cu(n,la),n};var la=Bi.geom.polygon.prototype=[];la.area=function(){for(var n,t=-1,e=this.length,r=this[e-1],i=0;++t<e;)n=r,r=this[t],i+=n[1]*r[0]-n[0]*r[1];return.5*i},la.centroid=function(n){var t,e,r=-1,i=this.length,u=0,o=0,a=this[i-1];for(arguments.length||(n=-1/(6*this.area()));++r<i;)t=a,a=this[r],e=t[0]*a[1]-a[0]*t[1],u+=(t[0]+a[0])*e,o+=(t[1]+a[1])*e;return[u*n,o*n]},la.clip=function(n){for(var t,e,r,i,u,o,a=ve(n),l=-1,c=this.length-ve(this),f=this[c-1];++l<c;){for(t=n.slice(),n.length=0,i=this[l],u=t[(r=t.length-a)-1],e=-1;++e<r;)ge(o=t[e],f,i)?(ge(u,f,i)||n.push(de(u,o,f,i)),n.push(o)):ge(u,f,i)&&n.push(de(u,o,f,i)),u=o;a&&n.push(n[0]),f=i}return n};var ca,fa,sa,ha,pa,ga=[],da=[];_e.prototype.prepare=function(){for(var n,t=this.edges,e=t.length;e--;)(n=t[e].edge).b&&n.a||t.splice(e,1);return t.sort(Se),t.length},qe.prototype={start:function(){return this.edge.l===this.site?this.edge.a:this.edge.b},end:function(){return this.edge.l===this.site?this.edge.b:this.edge.a}},Te.prototype={insert:function(n,t){var e,r,i;if(n){if(t.P=n,t.N=n.N,n.N&&(n.N.P=t),n.N=t,n.R){for(n=n.R;n.L;)n=n.L;n.L=t}else n.R=t;e=n}else e=this._?(n=Ue(this._),t.P=null,(t.N=n).P=n.L=t,n):(t.P=t.N=null,this._=t,null);for(t.L=t.R=null,t.U=e,t.C=!0,n=t;e&&e.C;)e===(r=e.U).L?(i=r.R)&&i.C?(e.C=i.C=!1,r.C=!0,n=r):(n===e.R&&(De(this,e),e=(n=e).U),e.C=!1,r.C=!0,Pe(this,r)):(i=r.L)&&i.C?(e.C=i.C=!1,r.C=!0,n=r):(n===e.L&&(Pe(this,e),e=(n=e).U),e.C=!1,r.C=!0,De(this,r)),e=n.U;this._.C=!1},remove:function(n){n.N&&(n.N.P=n.P),n.P&&(n.P.N=n.N),n.N=n.P=null;var t,e,r=n.U,i=n.L,u=n.R,o=i?u?Ue(u):i:u;if(r?r.L===n?r.L=o:r.R=o:this._=o,i&&u?(e=o.C,o.C=n.C,((o.L=i).U=o)!==u?(r=o.U,o.U=n.U,n=o.R,r.L=n,(o.R=u).U=o):(o.U=r,n=(r=o).R)):(e=n.C,n=o),n&&(n.U=r),!e){if(n&&n.C)return void(n.C=!1);do{if(n===this._)break;if(n===r.L){if((t=r.R).C&&(t.C=!1,r.C=!0,De(this,r),t=r.R),t.L&&t.L.C||t.R&&t.R.C){t.R&&t.R.C||(t.L.C=!1,t.C=!0,Pe(this,t),t=r.R),t.C=r.C,r.C=t.R.C=!1,De(this,r),n=this._;break}}else if((t=r.L).C&&(t.C=!1,r.C=!0,Pe(this,r),t=r.L),t.L&&t.L.C||t.R&&t.R.C){t.L&&t.L.C||(t.R.C=!1,t.C=!0,De(this,t),t=r.L),t.C=r.C,r.C=t.L.C=!1,Pe(this,r),n=this._;break}t.C=!0,r=(n=r).U}while(!n.C);n&&(n.C=!1)}}},Bi.geom.voronoi=function(n){function t(i){var u=new Array(i.length),o=f[0][0],a=f[0][1],l=f[1][0],c=f[1][1];return je(e(i),f).cells.forEach(function(n,t){var e=n.edges,r=n.site;(u[t]=e.length?e.map(function(n){var t=n.start();return[t.x,t.y]}):r.x>=o&&r.x<=l&&r.y>=a&&r.y<=c?[[o,c],[l,c],[l,a],[o,a]]:[]).point=i[t]}),u}function e(n){return n.map(function(n,t){return{x:Math.round(u(n,t)/bu)*bu,y:Math.round(o(n,t)/bu)*bu,i:t}})}var r=fe,i=se,u=r,o=i,f=va;return n?t(n):(t.links=function(t){return je(e(t)).edges.filter(function(n){return n.l&&n.r}).map(function(n){return{source:t[n.l.i],target:t[n.r.i]}})},t.triangles=function(h){var p=[];return je(e(h)).cells.forEach(function(n,t){for(var e,r,i,u,o=n.site,a=n.edges.sort(Se),l=-1,c=a.length,f=a[c-1].edge,s=f.l===o?f.r:f.l;++l<c;)e=s,s=(f=a[l].edge).l===o?f.r:f.l,t<e.i&&t<s.i&&(i=e,u=s,((r=o).x-u.x)*(i.y-r.y)-(r.x-i.x)*(u.y-r.y)<0)&&p.push([h[t],h[e.i],h[s.i]])}),p},t.x=function(n){return arguments.length?(u=bn(r=n),t):r},t.y=function(n){return arguments.length?(o=bn(i=n),t):i},t.clipExtent=function(n){return arguments.length?(f=null==n?va:n,t):f===va?null:f},t.size=function(n){return arguments.length?t.clipExtent(n&&[[0,0],n]):f===va?null:f&&f[1]},t)};var va=[[-1e6,-1e6],[1e6,1e6]];Bi.geom.delaunay=function(n){return Bi.geom.voronoi().triangles(n)},Bi.geom.quadtree=function(n,m,M,x,b){function t(n){function p(n,t,e,r,i,u,o,a){var l,c,f;isNaN(e)||isNaN(r)||(n.leaf?(l=n.x,c=n.y,null!=l?(iu(l-e)+iu(c-r)<.01||(f=n.point,n.x=n.y=n.point=null,s(n,f,l,c,i,u,o,a)),s(n,t,e,r,i,u,o,a)):(n.x=e,n.y=r,n.point=t)):s(n,t,e,r,i,u,o,a))}function s(n,t,e,r,i,u,o,a){var l=.5*(i+o),c=.5*(u+a),f=l<=e,s=c<=r,h=s<<1|f;n.leaf=!1,f?i=l:o=l,s?u=c:a=c,p(n=n.nodes[h]||(n.nodes[h]=Ie()),t,e,r,i,u,o,a)}var t,e,r,i,u,o,a,l,c,f=bn(w),h=bn(k);if(null!=m)o=m,a=M,l=x,c=b;else if(l=c=-(o=a=1/0),e=[],r=[],u=n.length,_)for(i=0;i<u;++i)(t=n[i]).x<o&&(o=t.x),t.y<a&&(a=t.y),t.x>l&&(l=t.x),t.y>c&&(c=t.y),e.push(t.x),r.push(t.y);else for(i=0;i<u;++i){var g=+f(t=n[i],i),d=+h(t,i);g<o&&(o=g),d<a&&(a=d),l<g&&(l=g),c<d&&(c=d),e.push(g),r.push(d)}var v=l-o,y=c-a;y<v?c=a+v:l=o+y;var S=Ie();if(S.add=function(n){p(S,n,+f(n,++i),+h(n,i),o,a,l,c)},S.visit=function(n){!function n(t,e,r,i,u,o){var a,l,c;t(e,r,i,u,o)||(a=.5*(r+u),l=.5*(i+o),(c=e.nodes)[0]&&n(t,c[0],r,i,a,l),c[1]&&n(t,c[1],a,i,u,l),c[2]&&n(t,c[2],r,l,a,o),c[3]&&n(t,c[3],a,l,u,o))}(n,S,o,a,l,c)},S.find=function(n){return t=S,v=n[0],y=n[1],w=1/0,function n(t,e,r,i,u){if(!(x<e||b<r||i<m||u<M)){var o,a,l,c,f;!(o=t.point)||(c=(a=v-t.x)*a+(l=y-t.y)*l)<w&&(f=Math.sqrt(w=c),m=v-f,M=y-f,x=v+f,b=y+f,_=o);for(var s=t.nodes,h=.5*(e+i),p=.5*(r+u),g=(p<=y)<<1|h<=v,d=g+4;g<d;++g)if(t=s[3&g])switch(3&g){case 0:n(t,e,r,h,p);break;case 1:n(t,h,r,i,p);break;case 2:n(t,e,p,h,u);break;case 3:n(t,h,p,i,u)}}}(t,m=o,M=a,x=l,b=c),_;var t,v,y,m,M,x,b,_,w},i=-1,null==m){for(;++i<u;)p(S,n[i],e[i],r[i],o,a,l,c);--i}else n.forEach(S.add);return e=r=n=t=null,S}var _,w=fe,k=se;return(_=arguments.length)?(w=He,k=Oe,3===_&&(b=M,x=m,M=m=0),t(n)):(t.x=function(n){return arguments.length?(w=n,t):w},t.y=function(n){return arguments.length?(k=n,t):k},t.extent=function(n){return arguments.length?(null==n?m=M=x=b=null:(m=+n[0][0],M=+n[0][1],x=+n[1][0],b=+n[1][1]),t):null==m?null:[[m,M],[x,b]]},t.size=function(n){return arguments.length?(null==n?m=M=x=b=null:(m=M=0,x=+n[0],b=+n[1]),t):null==m?null:[x-m,b-M]},t)},Bi.interpolateRgb=Ye,Bi.interpolateObject=Ze,Bi.interpolateNumber=Ve,Bi.interpolateString=Xe;var ya=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ma=new RegExp(ya.source,"g");Bi.interpolate=$e,Bi.interpolators=[function(n,t){var e=typeof t;return("string"==e?Ou.has(t.toLowerCase())||/^(#|rgb\(|hsl\()/i.test(t)?Ye:Xe:t instanceof nn?Ye:Array.isArray(t)?Be:"object"==e&&isNaN(t)?Ze:Ve)(n,t)}],Bi.interpolateArray=Be;function Ma(){return R}var xa=Bi.map({linear:Ma,poly:function(t){return function(n){return Math.pow(n,t)}},quad:function(){return Ge},cubic:function(){return Ke},sin:function(){return nr},exp:function(){return tr},circle:function(){return er},elastic:function(t,e){var r;return arguments.length<2&&(e=.45),r=arguments.length?e/Su*Math.asin(1/t):(t=1,e/4),function(n){return 1+t*Math.pow(2,-10*n)*Math.sin((n-r)*Su/e)}},back:function(t){return t=t||1.70158,function(n){return n*n*((t+1)*n-t)}},bounce:function(){return rr}}),ba=Bi.map({in:R,out:We,"in-out":Je,"out-in":function(n){return Je(We(n))}});Bi.ease=function(n){var t,e=n.indexOf("-"),r=0<=e?n.slice(0,e):n,i=0<=e?n.slice(e+1):"in",r=xa.get(r)||Ma,i=ba.get(i)||R;return t=i(r.apply(null,Wi.call(arguments,1))),function(n){return n<=0?0:1<=n?1:t(n)}},Bi.interpolateHcl=function(n,t){n=Bi.hcl(n),t=Bi.hcl(t);var e=n.h,r=n.c,i=n.l,u=t.h-e,o=t.c-r,a=t.l-i;return isNaN(o)&&(o=0,r=isNaN(r)?t.c:r),isNaN(u)?(u=0,e=isNaN(e)?t.h:e):180<u?u-=360:u<-180&&(u+=360),function(n){return un(e+u*n,r+o*n,i+a*n)+""}},Bi.interpolateHsl=function(n,t){n=Bi.hsl(n),t=Bi.hsl(t);var e=n.h,r=n.s,i=n.l,u=t.h-e,o=t.s-r,a=t.l-i;return isNaN(o)&&(o=0,r=isNaN(r)?t.s:r),isNaN(u)?(u=0,e=isNaN(e)?t.h:e):180<u?u-=360:u<-180&&(u+=360),function(n){return en(e+u*n,r+o*n,i+a*n)+""}},Bi.interpolateLab=function(n,t){n=Bi.lab(n),t=Bi.lab(t);var e=n.l,r=n.a,i=n.b,u=t.l-e,o=t.a-r,a=t.b-i;return function(n){return an(e+u*n,r+o*n,i+a*n)+""}},Bi.interpolateRound=ir,Bi.transform=function(n){var e=Gi.createElementNS(Bi.ns.prefix.svg,"g");return(Bi.transform=function(n){var t;return null!=n&&(e.setAttribute("transform",n),t=e.transform.baseVal.consolidate()),new ur(t?t.matrix:_a)})(n)},ur.prototype.toString=function(){return"translate("+this.translate+")rotate("+this.rotate+")skewX("+this.skew+")scale("+this.scale+")"};var _a={a:1,b:0,c:0,d:1,e:0,f:0};Bi.interpolateTransform=cr,Bi.layout={},Bi.layout.bundle=function(){return function(n){for(var t=[],e=-1,r=n.length;++e<r;)t.push(hr(n[e]));return t}},Bi.layout.chord=function(){function n(){var n,t,e,r,i,u={},o=[],a=Bi.range(x),l=[];for(y=[],m=[],n=0,r=-1;++r<x;){for(t=0,i=-1;++i<x;)t+=M[r][i];o.push(t),l.push(Bi.range(x)),n+=t}for(b&&a.sort(function(n,t){return b(o[n],o[t])}),_&&l.forEach(function(n,e){n.sort(function(n,t){return _(M[e][n],M[e][t])})}),n=(Su-S*x)/n,t=0,r=-1;++r<x;){for(e=t,i=-1;++i<x;){var c=a[r],f=l[c][i],s=M[c][f],h=t,p=t+=s*n;u[c+"-"+f]={index:c,subindex:f,startAngle:h,endAngle:p,value:s}}m[c]={index:c,startAngle:e,endAngle:t,value:o[c]},t+=S}for(r=-1;++r<x;)for(i=r-1;++i<x;){var g=u[r+"-"+i],d=u[i+"-"+r];(g.value||d.value)&&y.push(g.value<d.value?{source:d,target:g}:{source:g,target:d})}w&&v()}function v(){y.sort(function(n,t){return w((n.source.value+n.target.value)/2,(t.source.value+t.target.value)/2)})}var y,m,M,x,b,_,w,t={},S=0;return t.matrix=function(n){return arguments.length?(x=(M=n)&&M.length,y=m=null,t):M},t.padding=function(n){return arguments.length?(S=n,y=m=null,t):S},t.sortGroups=function(n){return arguments.length?(b=n,y=m=null,t):b},t.sortSubgroups=function(n){return arguments.length?(_=n,y=null,t):_},t.sortChords=function(n){return arguments.length?(w=n,y&&v(),t):w},t.chords=function(){return y||n(),y},t.groups=function(){return m||n(),m},t},Bi.layout.force=function(){function n(n){n.px=Bi.event.x,n.py=Bi.event.y,i.resume()}var s,t,h,p,g,d,i={},v=Bi.dispatch("start","tick","end"),y=[1,1],m=.9,u=wa,o=Sa,M=-30,x=ka,b=.1,_=.64,w=[],S=[];return i.tick=function(){if((h*=.99)<.005)return s=null,v.end({type:"end",alpha:h=0}),!0;for(var n,t,e,r,i,u,o,a,l=w.length,c=S.length,f=0;f<c;++f)e=(t=S[f]).source,(i=(o=(r=t.target).x-e.x)*o+(a=r.y-e.y)*a)&&(o*=i=h*g[f]*((i=Math.sqrt(i))-p[f])/i,a*=i,r.x-=o*(u=e.weight+r.weight?e.weight/(e.weight+r.weight):.5),r.y-=a*u,e.x+=o*(u=1-u),e.y+=a*u);if((u=h*b)&&(o=y[0]/2,a=y[1]/2,f=-1,u))for(;++f<l;)(t=w[f]).x+=(o-t.x)*u,t.y+=(a-t.y)*u;if(M)for(function n(t,e,r){var i,u=0,o=0;if(t.charge=0,!t.leaf)for(var a,l=t.nodes,c=l.length,f=-1;++f<c;)null!=(a=l[f])&&(n(a,e,r),t.charge+=a.charge,u+=a.charge*a.cx,o+=a.charge*a.cy);t.point&&(t.leaf||(t.point.x+=Math.random()-.5,t.point.y+=Math.random()-.5),i=e*r[t.point.index],t.charge+=t.pointCharge=i,u+=i*t.point.x,o+=i*t.point.y),t.cx=u/t.charge,t.cy=o/t.charge}(n=Bi.geom.quadtree(w),h,d),f=-1;++f<l;)(t=w[f]).fixed||n.visit(function(c){return function(n,t,e,r){if(n.point!==c){var i,u=n.cx-c.x,o=n.cy-c.y,a=r-t,l=u*u+o*o;if(a*a/_<l)return l<x&&(i=n.charge/l,c.px-=u*i,c.py-=o*i),!0;n.point&&l&&l<x&&(i=n.pointCharge/l,c.px-=u*i,c.py-=o*i)}return!n.charge}}(t));for(f=-1;++f<l;)(t=w[f]).fixed?(t.x=t.px,t.y=t.py):(t.x-=(t.px-(t.px=t.x))*m,t.y-=(t.py-(t.py=t.y))*m);v.tick({type:"tick",alpha:h})},i.nodes=function(n){return arguments.length?(w=n,i):w},i.links=function(n){return arguments.length?(S=n,i):S},i.size=function(n){return arguments.length?(y=n,i):y},i.linkDistance=function(n){return arguments.length?(u="function"==typeof n?n:+n,i):u},i.distance=i.linkDistance,i.linkStrength=function(n){return arguments.length?(o="function"==typeof n?n:+n,i):o},i.friction=function(n){return arguments.length?(m=+n,i):m},i.charge=function(n){return arguments.length?(M="function"==typeof n?n:+n,i):M},i.chargeDistance=function(n){return arguments.length?(x=n*n,i):Math.sqrt(x)},i.gravity=function(n){return arguments.length?(b=+n,i):b},i.theta=function(n){return arguments.length?(_=n*n,i):Math.sqrt(_)},i.alpha=function(n){return arguments.length?(n=+n,h?0<n?h=n:(s.c=null,s.t=NaN,s=null,v.end({type:"end",alpha:h=0})):0<n&&(v.start({type:"start",alpha:h=n}),s=Sn(i.tick)),i):h},i.start=function(){function n(n,t){if(!a){for(a=new Array(l),u=0;u<l;++u)a[u]=[];for(u=0;u<c;++u){var e=S[u];a[e.source.index].push(e.target),a[e.target.index].push(e.source)}}for(var r,i=a[f],u=-1,o=i.length;++u<o;)if(!isNaN(r=i[u][n]))return r;return Math.random()*t}for(var a,t,l=w.length,c=S.length,e=y[0],r=y[1],f=0;f<l;++f)(t=w[f]).index=f,t.weight=0;for(f=0;f<c;++f)"number"==typeof(t=S[f]).source&&(t.source=w[t.source]),"number"==typeof t.target&&(t.target=w[t.target]),++t.source.weight,++t.target.weight;for(f=0;f<l;++f)t=w[f],isNaN(t.x)&&(t.x=n("x",e)),isNaN(t.y)&&(t.y=n("y",r)),isNaN(t.px)&&(t.px=t.x),isNaN(t.py)&&(t.py=t.y);if(p=[],"function"==typeof u)for(f=0;f<c;++f)p[f]=+u.call(this,S[f],f);else for(f=0;f<c;++f)p[f]=u;if(g=[],"function"==typeof o)for(f=0;f<c;++f)g[f]=+o.call(this,S[f],f);else for(f=0;f<c;++f)g[f]=o;if(d=[],"function"==typeof M)for(f=0;f<l;++f)d[f]=+M.call(this,w[f],f);else for(f=0;f<l;++f)d[f]=M;return i.resume()},i.resume=function(){return i.alpha(.1)},i.stop=function(){return i.alpha(0)},i.drag=function(){return t=t||Bi.behavior.drag().origin(R).on("dragstart.force",gr).on("drag.force",n).on("dragend.force",dr),arguments.length?void this.on("mouseover.force",vr).on("mouseout.force",yr).call(t):t},Bi.rebind(i,v,"on")};var wa=20,Sa=1,ka=1/0;Bi.layout.hierarchy=function(){function a(n){var t,e,r,i,u=[n],o=[];for(n.depth=0;null!=(t=u.pop());)if(o.push(t),(r=c.call(a,t,t.depth))&&(e=r.length)){for(;0<=--e;)u.push(i=r[e]),i.parent=t,i.depth=t.depth+1;f&&(t.value=0),t.children=r}else f&&(t.value=+f.call(a,t,t.depth)||0),delete t.children;return xr(n,function(n){var t,e;l&&(t=n.children)&&t.sort(l),f&&(e=n.parent)&&(e.value+=n.value)}),o}var l=wr,c=br,f=_r;return a.sort=function(n){return arguments.length?(l=n,a):l},a.children=function(n){return arguments.length?(c=n,a):c},a.value=function(n){return arguments.length?(f=n,a):f},a.revalue=function(n){return f&&(Mr(n,function(n){n.children&&(n.value=0)}),xr(n,function(n){var t;n.children||(n.value=+f.call(a,n,n.depth)||0),(t=n.parent)&&(t.value+=n.value)})),n},a},Bi.layout.partition=function(){function t(n,t){var e=r.call(this,n,t);return function n(t,e,r,i){var u=t.children;if(t.x=e,t.y=t.depth*i,t.dx=r,t.dy=i,u&&(o=u.length)){var o,a,l,c=-1;for(r=t.value?r/t.value:0;++c<o;)n(a=u[c],e,l=a.value*r,i),e+=l}}(e[0],0,i[0],i[1]/function n(t){var e=t.children,r=0;if(e&&(i=e.length))for(var i,u=-1;++u<i;)r=Math.max(r,n(e[u]));return 1+r}(e[0])),e}var r=Bi.layout.hierarchy(),i=[1,1];return t.size=function(n){return arguments.length?(i=n,t):i},mr(t,r)},Bi.layout.pie=function(){function h(e){var t,n=e.length,r=e.map(function(n,t){return+p.call(h,n,t)}),i=+("function"==typeof d?d.apply(this,arguments):d),u=("function"==typeof v?v.apply(this,arguments):v)-i,o=Math.min(Math.abs(u)/n,+("function"==typeof y?y.apply(this,arguments):y)),a=o*(u<0?-1:1),l=Bi.sum(r),c=l?(u-n*a)/l:0,f=Bi.range(n),s=[];return null!=g&&f.sort(g===Na?function(n,t){return r[t]-r[n]}:function(n,t){return g(e[n],e[t])}),f.forEach(function(n){s[n]={data:e[n],value:t=r[n],startAngle:i,endAngle:i+=t*c+a,padAngle:o}}),s}var p=Number,g=Na,d=0,v=Su,y=0;return h.value=function(n){return arguments.length?(p=n,h):p},h.sort=function(n){return arguments.length?(g=n,h):g},h.startAngle=function(n){return arguments.length?(d=n,h):d},h.endAngle=function(n){return arguments.length?(v=n,h):v},h.padAngle=function(n){return arguments.length?(y=n,h):y},h};var Na={};Bi.layout.stack=function(){function s(n,t){if(!(e=n.length))return n;for(var e,r,i,u=(a=n.map(function(n,t){return h.call(s,n,t)})).map(function(n){return n.map(function(n,t){return[v.call(s,n,t),y.call(s,n,t)]})}),o=p.call(s,u,t),a=Bi.permute(a,o),u=Bi.permute(u,o),l=g.call(s,u,t),c=a[0].length,f=0;f<c;++f)for(d.call(s,a[0][f],i=l[f],u[0][f][1]),r=1;r<e;++r)d.call(s,a[r][f],i+=u[r-1][f][1],u[r][f][1]);return n}var h=R,p=Ar,g=Cr,d=Er,v=kr,y=Nr;return s.values=function(n){return arguments.length?(h=n,s):h},s.order=function(n){return arguments.length?(p="function"==typeof n?n:Ea.get(n)||Ar,s):p},s.offset=function(n){return arguments.length?(g="function"==typeof n?n:Aa.get(n)||Cr,s):g},s.x=function(n){return arguments.length?(v=n,s):v},s.y=function(n){return arguments.length?(y=n,s):y},s.out=function(n){return arguments.length?(d=n,s):d},s};var Ea=Bi.map({"inside-out":function(n){for(var t,e=n.length,r=n.map(zr),i=n.map(Lr),u=Bi.range(e).sort(function(n,t){return r[n]-r[t]}),o=0,a=0,l=[],c=[],f=0;f<e;++f)t=u[f],o<a?(o+=i[t],l.push(t)):(a+=i[t],c.push(t));return c.reverse().concat(l)},reverse:function(n){return Bi.range(n.length).reverse()},default:Ar}),Aa=Bi.map({silhouette:function(n){for(var t,e,r=n.length,i=n[0].length,u=[],o=0,a=[],l=0;l<i;++l){for(e=t=0;t<r;t++)e+=n[t][l][1];o<e&&(o=e),u.push(e)}for(l=0;l<i;++l)a[l]=(o-u[l])/2;return a},wiggle:function(n){var t,e,r,i,u,o,a,l,c,f=n.length,s=n[0],h=s.length,p=[];for(p[0]=l=c=0,e=1;e<h;++e){for(i=t=0;t<f;++t)i+=n[t][e][1];for(u=t=0,a=s[e][0]-s[e-1][0];t<f;++t){for(r=0,o=(n[t][e][1]-n[t][e-1][1])/(2*a);r<t;++r)o+=(n[r][e][1]-n[r][e-1][1])/a;u+=o*n[t][e][1]}p[e]=l-=i?u/i*a:0,l<c&&(c=l)}for(e=0;e<h;++e)p[e]-=c;return p},expand:function(n){for(var t,e,r=n.length,i=n[0].length,u=1/r,o=[],a=0;a<i;++a){for(e=t=0;t<r;t++)e+=n[t][a][1];if(e)for(t=0;t<r;t++)n[t][a][1]/=e;else for(t=0;t<r;t++)n[t][a][1]=u}for(a=0;a<i;++a)o[a]=0;return o},zero:Cr});Bi.layout.histogram=function(){function e(n,t){for(var e,r,i=[],u=n.map(h,this),o=p.call(this,u,t),a=g.call(this,o,u,t),t=-1,l=u.length,c=a.length-1,f=s?1:1/l;++t<c;)(e=i[t]=[]).dx=a[t+1]-(e.x=a[t]),e.y=0;if(0<c)for(t=-1;++t<l;)(r=u[t])>=o[0]&&r<=o[1]&&((e=i[Bi.bisect(a,r,1,c)-1]).y+=f,e.push(n[t]));return i}var s=!0,h=Number,p=Dr,g=Tr;return e.value=function(n){return arguments.length?(h=n,e):h},e.range=function(n){return arguments.length?(p=bn(n),e):p},e.bins=function(t){return arguments.length?(g="number"==typeof t?function(n){return Rr(n,t)}:bn(t),e):g},e.frequency=function(n){return arguments.length?(s=!!n,e):s},e},Bi.layout.pack=function(){function t(n,t){var e,r=c.call(this,n,t),i=r[0],u=s[0],o=s[1],a=null==l?Math.sqrt:"function"==typeof l?l:function(){return l};return i.x=i.y=0,xr(i,function(n){n.r=+a(n.value)}),xr(i,Hr),f&&(e=f*(l?1:Math.max(2*i.r/u,2*i.r/o))/2,xr(i,function(n){n.r+=e}),xr(i,Hr),xr(i,function(n){n.r-=e})),function n(t,e,r,i){var u=t.children;if(t.x=e+=i*t.x,t.y=r+=i*t.y,t.r*=i,u)for(var o=-1,a=u.length;++o<a;)n(u[o],e,r,i)}(i,u/2,o/2,l?1:1/Math.max(2*i.r/u,2*i.r/o)),r}var l,c=Bi.layout.hierarchy().sort(Pr),f=0,s=[1,1];return t.size=function(n){return arguments.length?(s=n,t):s},t.radius=function(n){return arguments.length?(l=null==n||"function"==typeof n?n:+n,t):l},t.padding=function(n){return arguments.length?(f=+n,t):f},mr(t,c)},Bi.layout.tree=function(){function t(n,t){var e,r,i,u,o,a,l=g.call(this,n,t),c=l[0],f=function(n){for(var t,e={A:null,children:[n]},r=[e];null!=(t=r.pop());)for(var i,u=t.children,o=0,a=u.length;o<a;++o)r.push((u[o]=i={_:u[o],parent:t,children:(i=u[o].children)&&i.slice()||[],A:null,a:null,z:0,m:0,c:0,s:0,t:null,i:o}).a=i);return e.children[0]}(c);return xr(f,s),f.parent.m=-f.z,Mr(f,h),y?Mr(c,p):(Mr(i=r=e=c,function(n){n.x<e.x&&(e=n),n.x>r.x&&(r=n),n.depth>i.depth&&(i=n)}),u=d(e,r)/2-e.x,o=v[0]/(r.x+d(r,e)/2+u),a=v[1]/(i.depth||1),Mr(c,function(n){n.x=(n.x+u)*o,n.y=n.depth*a})),l}function s(n){var t,e=n.children,r=n.parent.children,i=n.i?r[n.i-1]:null;e.length?(function(n){for(var t,e=0,r=0,i=n.children,u=i.length;0<=--u;)(t=i[u]).z+=e,t.m+=e,e+=t.s+(r+=t.c)}(n),t=(e[0].z+e[e.length-1].z)/2,i?(n.z=i.z+d(n._,i._),n.m=n.z-t):n.z=t):i&&(n.z=i.z+d(n._,i._)),n.parent.A=function(n,t,e){if(t){for(var r,i=n,u=n,o=t,a=i.parent.children[0],l=i.m,c=u.m,f=o.m,s=a.m;o=Xr(o),i=Vr(i),o&&i;)a=Vr(a),(u=Xr(u)).a=n,0<(r=o.z+f-i.z-l+d(o._,i._))&&(function(n,t,e){var r=e/(t.i-n.i);t.c-=r,t.s+=e,n.c+=r,t.z+=e,t.m+=e}(function(n,t,e){return n.a.parent===t.parent?n.a:e}(o,n,e),n,r),l+=r,c+=r),f+=o.m,l+=i.m,s+=a.m,c+=u.m;o&&!Xr(u)&&(u.t=o,u.m+=f-c),i&&!Vr(a)&&(a.t=i,a.m+=l-s,e=n)}return e}(n,i,n.parent.A||r[0])}function h(n){n._.x=n.z+n.parent.m,n.m+=n.parent.m}function p(n){n.x*=v[0],n.y=n.depth*v[1]}var g=Bi.layout.hierarchy().sort(null).value(null),d=Zr,v=[1,1],y=null;return t.separation=function(n){return arguments.length?(d=n,t):d},t.size=function(n){return arguments.length?(y=null==(v=n)?p:null,t):y?null:v},t.nodeSize=function(n){return arguments.length?(y=null==(v=n)?null:p,t):y?v:null},mr(t,g)},Bi.layout.cluster=function(){function t(n,t){var i,e=f.call(this,n,t),r=e[0],u=0;xr(r,function(n){var t,e,r=n.children;r&&r.length?(n.x=(e=r).reduce(function(n,t){return n+t.x},0)/e.length,n.y=(t=r,1+Bi.max(t,function(n){return n.y}))):(n.x=i?u+=s(n,i):0,n.y=0,i=n)});var o=function n(t){var e=t.children;return e&&e.length?n(e[0]):t}(r),a=function n(t){var e,r=t.children;return r&&(e=r.length)?n(r[e-1]):t}(r),l=o.x-s(o,a)/2,c=a.x+s(a,o)/2;return xr(r,p?function(n){n.x=(n.x-r.x)*h[0],n.y=(r.y-n.y)*h[1]}:function(n){n.x=(n.x-l)/(c-l)*h[0],n.y=(1-(r.y?n.y/r.y:1))*h[1]}),e}var f=Bi.layout.hierarchy().sort(null).value(null),s=Zr,h=[1,1],p=!1;return t.separation=function(n){return arguments.length?(s=n,t):s},t.size=function(n){return arguments.length?(p=null==(h=n),t):p?null:h},t.nodeSize=function(n){return arguments.length?(p=null!=(h=n),t):p?h:null},mr(t,f)},Bi.layout.treemap=function(){function f(n,t){for(var e,r,i=-1,u=n.length;++i<u;)r=(e=n[i]).value*(t<0?0:t),e.area=isNaN(r)||r<=0?0:r}function s(n){var t=n.children;if(t&&t.length){var e,r,i,u=g(n),o=[],a=t.slice(),l=1/0,c="slice"===d?u.dx:"dice"===d?u.dy:"slice-dice"===d?1&n.depth?u.dy:u.dx:Math.min(u.dx,u.dy);for(f(a,u.dx*u.dy/n.value),o.area=0;0<(i=a.length);)o.push(e=a[i-1]),o.area+=e.area,l="squarify"!==d||(r=function(n,t){for(var e,r=n.area,i=0,u=1/0,o=-1,a=n.length;++o<a;)(e=n[o].area)&&(e<u&&(u=e),i<e&&(i=e));return t*=t,(r*=r)?Math.max(t*i*v/r,r/(t*u*v)):1/0}(o,c))<=l?(a.pop(),r):(o.area-=o.pop().area,h(o,c,u,!1),c=Math.min(u.dx,u.dy),1/(o.length=o.area=0));o.length&&(h(o,c,u,!0),o.length=o.area=0),t.forEach(s)}}function o(n){var t=n.children;if(t&&t.length){var e,r=g(n),i=t.slice(),u=[];for(f(i,r.dx*r.dy/n.value),u.area=0;e=i.pop();)u.push(e),u.area+=e.area,null!=e.z&&(h(u,e.z?r.dx:r.dy,r,!i.length),u.length=u.area=0);t.forEach(o)}}function h(n,t,e,r){var i,u=-1,o=n.length,a=e.x,l=e.y,c=t?p(n.area/t):0;if(t==e.dx){for((r||c>e.dy)&&(c=e.dy);++u<o;)(i=n[u]).x=a,i.y=l,i.dy=c,a+=i.dx=Math.min(e.x+e.dx-a,c?p(i.area/c):0);i.z=!0,i.dx+=e.x+e.dx-a,e.y+=c,e.dy-=c}else{for((r||c>e.dx)&&(c=e.dx);++u<o;)(i=n[u]).x=a,i.y=l,i.dx=c,l+=i.dy=Math.min(e.y+e.dy-l,c?p(i.area/c):0);i.z=!1,i.dy+=e.y+e.dy-l,e.x+=c,e.dx-=c}}function r(n){var t=i||u(n),e=t[0];return e.x=e.y=0,e.value?(e.dx=a[0],e.dy=a[1]):e.dx=e.dy=0,i&&u.revalue(e),f([e],e.dx*e.dy/e.value),(i?o:s)(e),c&&(i=t),t}var i,u=Bi.layout.hierarchy(),p=Math.round,a=[1,1],l=null,g=$r,c=!1,d="squarify",v=.5*(1+Math.sqrt(5));return r.size=function(n){return arguments.length?(a=n,r):a},r.padding=function(e){function n(n){return Br(n,e)}return arguments.length?(g=null==(l=e)?$r:"function"==(t=typeof e)?function(n){var t=e.call(r,n,n.depth);return null==t?$r(n):Br(n,"number"==typeof t?[t,t,t,t]:t)}:("number"==t&&(e=[e,e,e,e]),n),r):l;var t},r.round=function(n){return arguments.length?(p=n?Math.round:Number,r):p!=Number},r.sticky=function(n){return arguments.length?(c=n,i=null,r):c},r.ratio=function(n){return arguments.length?(v=n,r):v},r.mode=function(n){return arguments.length?(d=n+"",r):d},mr(r,u)},Bi.random={normal:function(r,i){var n=arguments.length;return n<2&&(i=1),n<1&&(r=0),function(){for(var n,t,e;!(e=(n=2*Math.random()-1)*n+(t=2*Math.random()-1)*t)||1<e;);return r+i*n*Math.sqrt(-2*Math.log(e)/e)}},logNormal:function(){var n=Bi.random.normal.apply(Bi,arguments);return function(){return Math.exp(n())}},bates:function(n){var t=Bi.random.irwinHall(n);return function(){return t()/n}},irwinHall:function(e){return function(){for(var n=0,t=0;t<e;t++)n+=Math.random();return n}}},Bi.scale={};var Ca={floor:R,ceil:R};Bi.scale.linear=function(){return function n(e,r,i,u){function t(){var n=2<Math.min(e.length,r.length)?ni:Gr,t=u?sr:fr;return a=n(e,r,t,i),l=n(r,e,t,$e),o}function o(n){return a(n)}var a,l;return o.invert=function(n){return l(n)},o.domain=function(n){return arguments.length?(e=n.map(Number),t()):e},o.range=function(n){return arguments.length?(r=n,t()):r},o.rangeRound=function(n){return o.range(n).interpolate(ir)},o.clamp=function(n){return arguments.length?(u=n,t()):u},o.interpolate=function(n){return arguments.length?(i=n,t()):i},o.ticks=function(n){return ii(e,n)},o.tickFormat=function(n,t){return ui(e,n,t)},o.nice=function(n){return ei(e,n),t()},o.copy=function(){return n(e,r,i,u)},t()}([0,1],[0,1],$e,!1)};var za={s:1,g:1,p:1,r:1,e:1};Bi.scale.log=function(){return function n(t,l,c,f){function s(n){return(c?Math.log(n<0?0:n):-Math.log(0<n?0:-n))/Math.log(l)}function h(n){return c?Math.pow(l,n):-Math.pow(l,-n)}function i(n){return t(s(n))}return i.invert=function(n){return h(t.invert(n))},i.domain=function(n){return arguments.length?(c=0<=n[0],t.domain((f=n.map(Number)).map(s)),i):f},i.base=function(n){return arguments.length?(l=+n,t.domain(f.map(s)),i):l},i.nice=function(){var n=Kr(f.map(s),c?Math:qa);return t.domain(n),f=n.map(h),i},i.ticks=function(){var n=Wr(f),t=[],e=n[0],r=n[1],i=Math.floor(s(e)),u=Math.ceil(s(r)),o=l%1?2:l;if(isFinite(u-i)){if(c){for(;i<u;i++)for(var a=1;a<o;a++)t.push(h(i)*a);t.push(h(i))}else for(t.push(h(i));i++<u;)for(a=o-1;0<a;a--)t.push(h(i)*a);for(i=0;t[i]<e;i++);for(u=t.length;t[u-1]>r;u--);t=t.slice(i,u)}return t},i.tickFormat=function(n,e){if(!arguments.length)return La;arguments.length<2?e=La:"function"!=typeof e&&(e=Bi.format(e));var r=Math.max(1,l*n/i.ticks().length);return function(n){var t=n/h(Math.round(s(n)));return t*l<l-.5&&(t*=l),t<=r?e(n):""}},i.copy=function(){return n(t.copy(),l,c,f)},ti(i,t)}(Bi.scale.linear().domain([0,1]),10,!0,[1,10])};var La=Bi.format(".0e"),qa={floor:function(n){return-Math.ceil(-n)},ceil:function(n){return-Math.floor(-n)}};Bi.scale.pow=function(){return function n(t,e,r){function i(n){return t(u(n))}var u=ai(e),o=ai(1/e);return i.invert=function(n){return o(t.invert(n))},i.domain=function(n){return arguments.length?(t.domain((r=n.map(Number)).map(u)),i):r},i.ticks=function(n){return ii(r,n)},i.tickFormat=function(n,t){return ui(r,n,t)},i.nice=function(n){return i.domain(ei(r,n))},i.exponent=function(n){return arguments.length?(u=ai(e=n),o=ai(1/e),t.domain(r.map(u)),i):e},i.copy=function(){return n(t.copy(),e,r)},ti(i,t)}(Bi.scale.linear(),1,[0,1])},Bi.scale.sqrt=function(){return Bi.scale.pow().exponent(.5)},Bi.scale.ordinal=function(){return function n(a,l){function c(n){return s[((i.get(n)||("range"===l.t?i.set(n,a.push(n)):NaN))-1)%s.length]}function f(t,e){return Bi.range(a.length).map(function(n){return t+e*n})}var i,s,h;return c.domain=function(n){if(!arguments.length)return a;a=[],i=new m;for(var t,e=-1,r=n.length;++e<r;)i.has(t=n[e])||i.set(t,a.push(t));return c[l.t].apply(c,l.a)},c.range=function(n){return arguments.length?(s=n,h=0,l={t:"range",a:arguments},c):s},c.rangePoints=function(n,t){arguments.length<2&&(t=0);var e=n[0],r=n[1],i=a.length<2?(e=(e+r)/2,0):(r-e)/(a.length-1+t);return s=f(e+i*t/2,i),h=0,l={t:"rangePoints",a:arguments},c},c.rangeRoundPoints=function(n,t){arguments.length<2&&(t=0);var e=n[0],r=n[1],i=a.length<2?(e=r=Math.round((e+r)/2),0):(r-e)/(a.length-1+t)|0;return s=f(e+Math.round(i*t/2+(r-e-(a.length-1+t)*i)/2),i),h=0,l={t:"rangeRoundPoints",a:arguments},c},c.rangeBands=function(n,t,e){arguments.length<2&&(t=0),arguments.length<3&&(e=t);var r=n[1]<n[0],i=n[+r],u=(n[1-r]-i)/(a.length-t+2*e);return s=f(i+u*e,u),r&&s.reverse(),h=u*(1-t),l={t:"rangeBands",a:arguments},c},c.rangeRoundBands=function(n,t,e){arguments.length<2&&(t=0),arguments.length<3&&(e=t);var r=n[1]<n[0],i=n[+r],u=n[1-r],o=Math.floor((u-i)/(a.length-t+2*e));return s=f(i+Math.round((u-i-(a.length-t)*o)/2),o),r&&s.reverse(),h=Math.round(o*(1-t)),l={t:"rangeRoundBands",a:arguments},c},c.rangeBand=function(){return h},c.rangeExtent=function(){return Wr(l.a[0])},c.copy=function(){return n(a,l)},c.domain(a)}([],{t:"range",a:[[]]})},Bi.scale.category10=function(){return Bi.scale.ordinal().range(Ta)},Bi.scale.category20=function(){return Bi.scale.ordinal().range(Ra)},Bi.scale.category20b=function(){return Bi.scale.ordinal().range(Da)},Bi.scale.category20c=function(){return Bi.scale.ordinal().range(Pa)};var Ta=[2062260,16744206,2924588,14034728,9725885,9197131,14907330,8355711,12369186,1556175].map(gn),Ra=[2062260,11454440,16744206,16759672,2924588,10018698,14034728,16750742,9725885,12955861,9197131,12885140,14907330,16234194,8355711,13092807,12369186,14408589,1556175,10410725].map(gn),Da=[3750777,5395619,7040719,10264286,6519097,9216594,11915115,13556636,9202993,12426809,15186514,15190932,8666169,11356490,14049643,15177372,8077683,10834324,13528509,14589654].map(gn),Pa=[3244733,7057110,10406625,13032431,15095053,16616764,16625259,16634018,3253076,7652470,10607003,13101504,7695281,10394312,12369372,14342891,6513507,9868950,12434877,14277081].map(gn);Bi.scale.quantile=function(){return function n(e,r){function t(){var n=0,t=r.length;for(u=[];++n<t;)u[n-1]=Bi.quantile(e,n/t);return i}function i(n){return isNaN(n=+n)?void 0:r[Bi.bisect(u,n)]}var u;return i.domain=function(n){return arguments.length?(e=n.map(c).filter(f).sort(o),t()):e},i.range=function(n){return arguments.length?(r=n,t()):r},i.quantiles=function(){return u},i.invertExtent=function(n){return(n=r.indexOf(n))<0?[NaN,NaN]:[0<n?u[n-1]:e[0],n<u.length?u[n]:e[e.length-1]]},i.copy=function(){return n(e,r)},t()}([],[])},Bi.scale.quantize=function(){return function n(t,e,r){function i(n){return r[Math.max(0,Math.min(a,Math.floor(o*(n-t))))]}function u(){return o=r.length/(e-t),a=r.length-1,i}var o,a;return i.domain=function(n){return arguments.length?(t=+n[0],e=+n[n.length-1],u()):[t,e]},i.range=function(n){return arguments.length?(r=n,u()):r},i.invertExtent=function(n){return[n=(n=r.indexOf(n))<0?NaN:n/o+t,n+1/o]},i.copy=function(){return n(t,e,r)},u()}(0,1,[0,1])},Bi.scale.threshold=function(){return function n(t,e){function r(n){return n<=n?e[Bi.bisect(t,n)]:void 0}return r.domain=function(n){return arguments.length?(t=n,r):t},r.range=function(n){return arguments.length?(e=n,r):e},r.invertExtent=function(n){return n=e.indexOf(n),[t[n-1],t[n]]},r.copy=function(){return n(t,e)},r}([.5],[0,1])},Bi.scale.identity=function(){return function n(e){function t(n){return+n}return(t.invert=t).domain=t.range=function(n){return arguments.length?(e=n.map(t),t):e},t.ticks=function(n){return ii(e,n)},t.tickFormat=function(n,t){return ui(e,n,t)},t.copy=function(){return n(e)},t}([0,1])},Bi.svg={},Bi.svg.arc=function(){function t(){var n=Math.max(0,+F.apply(this,arguments)),t=Math.max(0,+H.apply(this,arguments)),e=Y.apply(this,arguments)-Nu,r=Z.apply(this,arguments)-Nu,i=Math.abs(r-e),u=r<e?0:1;if(t<n&&(o=t,t=n,n=o),ku<=i)return j(t,u)+(n?j(n,1-u):"")+"Z";var o,a,l,c,f,s,h,p,g,d,v,y,m,M,x,b,_,w,S,k,N,E,A,C,z,L,q,T,R,D=0,P=0,U=[];return(c=(+V.apply(this,arguments)||0)/2)&&(l=I===Ua?Math.sqrt(n*n+t*t):+I.apply(this,arguments),u||(P*=-1),t&&(P=G(l/t*Math.sin(c))),n&&(D=G(l/n*Math.sin(c)))),t?(g=t*Math.cos(e+P),d=t*Math.sin(e+P),v=t*Math.cos(r-P),f=t*Math.sin(r-P),h=Math.abs(r-e-2*P)<=wu?0:1,P&&gi(g,d,v,f)===u^h&&(p=(e+r)/2,g=t*Math.cos(p),d=t*Math.sin(p),v=f=null)):g=d=0,n?(M=n*Math.cos(r-D),x=n*Math.sin(r-D),b=n*Math.cos(e+D),s=n*Math.sin(e+D),y=Math.abs(e-r+2*D)<=wu?0:1,D&&gi(M,x,b,s)===1-u^y&&(m=(e+r)/2,M=n*Math.cos(m),x=n*Math.sin(m),b=s=null)):M=x=0,bu<i&&.001<(o=Math.min(Math.abs(t-n)/2,+O.apply(this,arguments)))?(a=n<t^u?0:1,C=z=o,i<wu&&(w=g-(_=null==b?[M,x]:null==v?[g,d]:de([g,d],[b,s],[v,f],[M,x]))[0],S=d-_[1],k=v-_[0],N=f-_[1],E=1/Math.sin(Math.acos((w*k+S*N)/(Math.sqrt(w*w+S*S)*Math.sqrt(k*k+N*N)))/2),A=Math.sqrt(_[0]*_[0]+_[1]*_[1]),C=Math.min(o,(n-A)/(E-1)),z=Math.min(o,(t-A)/(1+E))),null!=v?(L=di(null==b?[M,x]:[b,s],[g,d],t,z,u),q=di([v,f],[M,x],t,z,u),o===z?U.push("M",L[0],"A",z,",",z," 0 0,",a," ",L[1],"A",t,",",t," 0 ",1-u^gi(L[1][0],L[1][1],q[1][0],q[1][1]),",",u," ",q[1],"A",z,",",z," 0 0,",a," ",q[0]):U.push("M",L[0],"A",z,",",z," 0 1,",a," ",q[0])):U.push("M",g,",",d),null!=b?(T=di([g,d],[b,s],n,-C,u),R=di([M,x],null==v?[g,d]:[v,f],n,-C,u),o===C?U.push("L",R[0],"A",C,",",C," 0 0,",a," ",R[1],"A",n,",",n," 0 ",u^gi(R[1][0],R[1][1],T[1][0],T[1][1]),",",1-u," ",T[1],"A",C,",",C," 0 0,",a," ",T[0]):U.push("L",R[0],"A",C,",",C," 0 0,",a," ",T[0])):U.push("L",M,",",x)):(U.push("M",g,",",d),null!=v&&U.push("A",t,",",t," 0 ",h,",",u," ",v,",",f),U.push("L",M,",",x),null!=b&&U.push("A",n,",",n," 0 ",y,",",1-u," ",b,",",s)),U.push("Z"),U.join("")}function j(n,t){return"M0,"+n+"A"+n+","+n+" 0 1,"+t+" 0,"+-n+"A"+n+","+n+" 0 1,"+t+" 0,"+n}var F=ci,H=fi,O=li,I=Ua,Y=si,Z=hi,V=pi;return t.innerRadius=function(n){return arguments.length?(F=bn(n),t):F},t.outerRadius=function(n){return arguments.length?(H=bn(n),t):H},t.cornerRadius=function(n){return arguments.length?(O=bn(n),t):O},t.padRadius=function(n){return arguments.length?(I=n==Ua?Ua:bn(n),t):I},t.startAngle=function(n){return arguments.length?(Y=bn(n),t):Y},t.endAngle=function(n){return arguments.length?(Z=bn(n),t):Z},t.padAngle=function(n){return arguments.length?(V=bn(n),t):V},t.centroid=function(){var n=(+F.apply(this,arguments)+ +H.apply(this,arguments))/2,t=(+Y.apply(this,arguments)+ +Z.apply(this,arguments))/2-Nu;return[Math.cos(t)*n,Math.sin(t)*n]},t};var Ua="auto";Bi.svg.line=function(){return vi(R)};var ja=Bi.map({linear:yi,"linear-closed":mi,step:function(n){for(var t=0,e=n.length,r=n[0],i=[r[0],",",r[1]];++t<e;)i.push("H",(r[0]+(r=n[t])[0])/2,"V",r[1]);return 1<e&&i.push("H",r[0]),i.join("")},"step-before":Mi,"step-after":xi,basis:wi,"basis-open":function(n){if(n.length<4)return yi(n);for(var t,e=[],r=-1,i=n.length,u=[0],o=[0];++r<3;)t=n[r],u.push(t[0]),o.push(t[1]);for(e.push(Si(Oa,u)+","+Si(Oa,o)),--r;++r<i;)t=n[r],u.shift(),u.push(t[0]),o.shift(),o.push(t[1]),ki(e,u,o);return e.join("")},"basis-closed":function(n){for(var t,e,r=-1,i=n.length,u=i+4,o=[],a=[];++r<4;)e=n[r%i],o.push(e[0]),a.push(e[1]);for(t=[Si(Oa,o),",",Si(Oa,a)],--r;++r<u;)e=n[r%i],o.shift(),o.push(e[0]),a.shift(),a.push(e[1]),ki(t,o,a);return t.join("")},bundle:function(n,t){var e=n.length-1;if(e)for(var r,i,u=n[0][0],o=n[0][1],a=n[e][0]-u,l=n[e][1]-o,c=-1;++c<=e;)i=c/e,(r=n[c])[0]=t*r[0]+(1-t)*(u+i*a),r[1]=t*r[1]+(1-t)*(o+i*l);return wi(n)},cardinal:function(n,t){return n.length<3?yi(n):n[0]+bi(n,_i(n,t))},"cardinal-open":function(n,t){return n.length<4?yi(n):n[1]+bi(n.slice(1,-1),_i(n,t))},"cardinal-closed":function(n,t){return n.length<3?mi(n):n[0]+bi((n.push(n[0]),n),_i([n[n.length-2]].concat(n,[n[1]]),t))},monotone:function(n){return n.length<3?yi(n):n[0]+bi(n,Ei(n))}});ja.forEach(function(n,t){t.key=n,t.closed=/-closed$/.test(n)});var Fa=[0,2/3,1/3,0],Ha=[0,1/3,2/3,0],Oa=[0,1/6,2/3,1/6];Bi.svg.line.radial=function(){var n=vi(Ai);return n.radius=n.x,delete n.x,n.angle=n.y,delete n.y,n},(Mi.reverse=xi).reverse=Mi,Bi.svg.area=function(){return Ci(R)},Bi.svg.area.radial=function(){var n=Ci(Ai);return n.radius=n.x,delete n.x,n.innerRadius=n.x0,delete n.x0,n.outerRadius=n.x1,delete n.x1,n.angle=n.y,delete n.y,n.startAngle=n.y0,delete n.y0,n.endAngle=n.y1,delete n.y1,n},Bi.svg.chord=function(){function t(n,t){var e,r,i=o(this,c,n,t),u=o(this,f,n,t);return"M"+i.p0+a(i.r,i.p1,i.a1-i.a0)+(r=u,(e=i).a0==r.a0&&e.a1==r.a1?l(i.r,i.p1,i.r,i.p0):l(i.r,i.p1,u.r,u.p0)+a(u.r,u.p1,u.a1-u.a0)+l(u.r,u.p1,i.r,i.p0))+"Z"}function o(n,t,e,r){var i=t.call(n,e,r),u=s.call(n,i,r),o=h.call(n,i,r)-Nu,a=p.call(n,i,r)-Nu;return{r:u,a0:o,a1:a,p0:[u*Math.cos(o),u*Math.sin(o)],p1:[u*Math.cos(a),u*Math.sin(a)]}}function a(n,t,e){return"A"+n+","+n+" 0 "+ +(wu<e)+",1 "+t}function l(n,t,e,r){return"Q 0,0 "+r}var c=ee,f=re,s=zi,h=si,p=hi;return t.radius=function(n){return arguments.length?(s=bn(n),t):s},t.source=function(n){return arguments.length?(c=bn(n),t):c},t.target=function(n){return arguments.length?(f=bn(n),t):f},t.startAngle=function(n){return arguments.length?(h=bn(n),t):h},t.endAngle=function(n){return arguments.length?(p=bn(n),t):p},t},Bi.svg.diagonal=function(){function t(n,t){var e=o.call(this,n,t),r=a.call(this,n,t),i=(e.y+r.y)/2,u=[e,{x:e.x,y:i},{x:r.x,y:i},r];return"M"+(u=u.map(l))[0]+"C"+u[1]+" "+u[2]+" "+u[3]}var o=ee,a=re,l=Li;return t.source=function(n){return arguments.length?(o=bn(n),t):o},t.target=function(n){return arguments.length?(a=bn(n),t):a},t.projection=function(n){return arguments.length?(l=n,t):l},t},Bi.svg.diagonal.radial=function(){var n=Bi.svg.diagonal(),t=Li,e=n.projection;return n.projection=function(n){return arguments.length?e((r=t=n,function(){var n=r.apply(this,arguments),t=n[0],e=n[1]-Nu;return[t*Math.cos(e),t*Math.sin(e)]})):t;var r},n},Bi.svg.symbol=function(){function t(n,t){return(Ia.get(e.call(this,n,t))||Ri)(r.call(this,n,t))}var e=Ti,r=qi;return t.type=function(n){return arguments.length?(e=bn(n),t):e},t.size=function(n){return arguments.length?(r=bn(n),t):r},t};var Ia=Bi.map({circle:Ri,cross:function(n){var t=Math.sqrt(n/5)/2;return"M"+-3*t+","+-t+"H"+-t+"V"+-3*t+"H"+t+"V"+-t+"H"+3*t+"V"+t+"H"+t+"V"+3*t+"H"+-t+"V"+t+"H"+-3*t+"Z"},diamond:function(n){var t=Math.sqrt(n/(2*Za)),e=t*Za;return"M0,"+-t+"L"+e+",0 0,"+t+" "+-e+",0Z"},square:function(n){var t=Math.sqrt(n)/2;return"M"+-t+","+-t+"L"+t+","+-t+" "+t+","+t+" "+-t+","+t+"Z"},"triangle-down":function(n){var t=Math.sqrt(n/Ya),e=t*Ya/2;return"M0,"+e+"L"+t+","+-e+" "+-t+","+-e+"Z"},"triangle-up":function(n){var t=Math.sqrt(n/Ya),e=t*Ya/2;return"M0,"+-e+"L"+t+","+e+" "+-t+","+e+"Z"}});Bi.svg.symbolTypes=Ia.keys();var Ya=Math.sqrt(3),Za=Math.tan(30*Eu);pu.transition=function(n){for(var t,e,r=Va||++Wa,i=Fi(n),u=[],o=Xa||{time:Date.now(),ease:Qe,delay:0,duration:250},a=-1,l=this.length;++a<l;){u.push(t=[]);for(var c=this[a],f=-1,s=c.length;++f<s;)(e=c[f])&&Hi(e,f,i,r,o),t.push(e)}return Pi(u,i,r)},pu.interrupt=function(n){return this.each(null==n?$a:Di(Fi(n)))};var Va,Xa,$a=Di(Fi()),Ba=[],Wa=0;Ba.call=pu.call,Ba.empty=pu.empty,Ba.node=pu.node,Ba.size=pu.size,Bi.transition=function(n,t){return n&&n.transition?Va?n.transition(t):n:Bi.selection().transition(n)},(Bi.transition.prototype=Ba).select=function(n){var t,e,r,i=this.id,u=this.namespace,o=[];n=_(n);for(var a=-1,l=this.length;++a<l;){o.push(t=[]);for(var c=this[a],f=-1,s=c.length;++f<s;)(r=c[f])&&(e=n.call(r,r.__data__,f,a))?("__data__"in r&&(e.__data__=r.__data__),Hi(e,f,u,i,r[u][i]),t.push(e)):t.push(null)}return Pi(o,u,i)},Ba.selectAll=function(n){var t,e,r,i,u,o=this.id,a=this.namespace,l=[];n=w(n);for(var c=-1,f=this.length;++c<f;)for(var s=this[c],h=-1,p=s.length;++h<p;)if(r=s[h]){u=r[a][o],e=n.call(r,r.__data__,h,c),l.push(t=[]);for(var g=-1,d=e.length;++g<d;)(i=e[g])&&Hi(i,g,a,o,u),t.push(i)}return Pi(l,a,o)},Ba.filter=function(n){var t,e,r=[];"function"!=typeof n&&(n=F(n));for(var i=0,u=this.length;i<u;i++){r.push(t=[]);for(var o,a=0,l=(o=this[i]).length;a<l;a++)(e=o[a])&&n.call(e,e.__data__,a,i)&&t.push(e)}return Pi(r,this.namespace,this.id)},Ba.tween=function(t,e){var r=this.id,i=this.namespace;return arguments.length<2?this.node()[i][r].tween.get(t):H(this,null==e?function(n){n[i][r].tween.remove(t)}:function(n){n[i][r].tween.set(t,e)})},Ba.attr=function(n,t){function r(){this.removeAttribute(o)}function i(){this.removeAttributeNS(o.space,o.local)}if(arguments.length<2){for(t in n)this.attr(t,n[t]);return this}var u="transform"==n?cr:$e,o=Bi.ns.qualify(n);return Ui(this,"attr."+n,t,o.local?function(e){return null==e?i:(e+="",function(){var t,n=this.getAttributeNS(o.space,o.local);return n!==e&&(t=u(n,e),function(n){this.setAttributeNS(o.space,o.local,t(n))})})}:function(e){return null==e?r:(e+="",function(){var t,n=this.getAttribute(o);return n!==e&&(t=u(n,e),function(n){this.setAttribute(o,t(n))})})})},Ba.attrTween=function(n,r){var i=Bi.ns.qualify(n);return this.tween("attr."+n,i.local?function(n,t){var e=r.call(this,n,t,this.getAttributeNS(i.space,i.local));return e&&function(n){this.setAttributeNS(i.space,i.local,e(n))}}:function(n,t){var e=r.call(this,n,t,this.getAttribute(i));return e&&function(n){this.setAttribute(i,e(n))}})},Ba.style=function(r,n,i){function t(){this.style.removeProperty(r)}var e=arguments.length;if(e<3){if("string"!=typeof r){for(i in e<2&&(n=""),r)this.style(i,r[i],n);return this}i=""}return Ui(this,"style."+r,n,function(e){return null==e?t:(e+="",function(){var t,n=T(this).getComputedStyle(this,null).getPropertyValue(r);return n!==e&&(t=$e(n,e),function(n){this.style.setProperty(r,t(n),i)})})})},Ba.styleTween=function(r,i,u){return arguments.length<3&&(u=""),this.tween("style."+r,function(n,t){var e=i.call(this,n,t,T(this).getComputedStyle(this,null).getPropertyValue(r));return e&&function(n){this.style.setProperty(r,e(n),u)}})},Ba.text=function(n){return Ui(this,"text",n,ji)},Ba.remove=function(){var t=this.namespace;return this.each("end.transition",function(){var n;this[t].count<2&&(n=this.parentNode)&&n.removeChild(this)})},Ba.ease=function(t){var e=this.id,r=this.namespace;return arguments.length<1?this.node()[r][e].ease:("function"!=typeof t&&(t=Bi.ease.apply(Bi,arguments)),H(this,function(n){n[r][e].ease=t}))},Ba.delay=function(r){var i=this.id,u=this.namespace;return arguments.length<1?this.node()[u][i].delay:H(this,"function"==typeof r?function(n,t,e){n[u][i].delay=+r.call(n,n.__data__,t,e)}:(r=+r,function(n){n[u][i].delay=r}))},Ba.duration=function(r){var i=this.id,u=this.namespace;return arguments.length<1?this.node()[u][i].duration:H(this,"function"==typeof r?function(n,t,e){n[u][i].duration=Math.max(1,r.call(n,n.__data__,t,e))}:(r=Math.max(1,r),function(n){n[u][i].duration=r}))},Ba.each=function(r,e){var i=this.id,u=this.namespace;if(arguments.length<2){var n=Xa,t=Va;try{Va=i,H(this,function(n,t,e){Xa=n[u][i],r.call(n,n.__data__,t,e)})}finally{Xa=n,Va=t}}else H(this,function(n){var t=n[u][i];(t.event||(t.event=Bi.dispatch("start","end","interrupt"))).on(r,e)});return this},Ba.transition=function(){for(var n,t,e,r=this.id,i=++Wa,u=this.namespace,o=[],a=0,l=this.length;a<l;a++){o.push(n=[]);for(var c,f=0,s=(c=this[a]).length;f<s;f++)(t=c[f])&&Hi(t,f,u,i,{time:(e=t[u][r]).time,ease:e.ease,delay:e.delay+e.duration,duration:e.duration}),n.push(t)}return Pi(o,u,i)},Bi.svg.axis=function(){function e(n){n.each(function(){var n,t=Bi.select(this),e=this.__chart__||E,r=this.__chart__=E.copy(),i=null==T?r.ticks?r.ticks.apply(r,q):r.domain():T,u=null==N?r.tickFormat?r.tickFormat.apply(r,q):R:N,o=t.selectAll(".tick").data(i,r),a=o.enter().insert("g",".domain").attr("class","tick").style("opacity",bu),l=Bi.transition(o.exit()).style("opacity",bu).remove(),c=Bi.transition(o.order()).style("opacity",1),f=Math.max(C,0)+L,s=Jr(r),h=t.selectAll(".domain").data([0]),p=(h.enter().append("path").attr("class","domain"),Bi.transition(h));a.append("line"),a.append("text");var g,d,v,y,m,M,x=a.select("line"),b=c.select("line"),_=o.select("text").text(u),w=a.select("text"),S=c.select("text"),k="top"===A||"left"===A?-1:1;"bottom"===A||"top"===A?(n=Oi,g="x",v="y",d="x2",y="y2",_.attr("dy",k<0?"0em":".71em").style("text-anchor","middle"),p.attr("d","M"+s[0]+","+k*z+"V0H"+s[1]+"V"+k*z)):(n=Ii,g="y",v="x",d="y2",y="x2",_.attr("dy",".32em").style("text-anchor",k<0?"end":"start"),p.attr("d","M"+k*z+","+s[0]+"H0V"+s[1]+"H"+k*z)),x.attr(y,k*C),w.attr(v,k*f),b.attr(d,0).attr(y,k*C),S.attr(g,0).attr(v,k*f),r.rangeBand?(M=(m=r).rangeBand()/2,e=r=function(n){return m(n)+M}):e.rangeBand?e=r:l.call(n,r,e),a.call(n,e,r),c.call(n,r,r)})}var N,E=Bi.scale.linear(),A=Ja,C=6,z=6,L=3,q=[10],T=null;return e.scale=function(n){return arguments.length?(E=n,e):E},e.orient=function(n){return arguments.length?(A=n in Ga?n+"":Ja,e):A},e.ticks=function(){return arguments.length?(q=Ji(arguments),e):q},e.tickValues=function(n){return arguments.length?(T=n,e):T},e.tickFormat=function(n){return arguments.length?(N=n,e):N},e.tickSize=function(n){var t=arguments.length;return t?(C=+n,z=+arguments[t-1],e):C},e.innerTickSize=function(n){return arguments.length?(C=+n,e):C},e.outerTickSize=function(n){return arguments.length?(z=+n,e):z},e.tickPadding=function(n){return arguments.length?(L=+n,e):L},e.tickSubdivide=function(){return arguments.length&&e},e};var Ja="bottom",Ga={top:1,right:1,bottom:1,left:1};Bi.svg.brush=function(){function m(n){n.each(function(){var n=Bi.select(this).style("pointer-events","all").style("-webkit-tap-highlight-color","rgba(0,0,0,0)").on("mousedown.brush",o).on("touchstart.brush",o),t=n.selectAll(".background").data([0]);t.enter().append("rect").attr("class","background").style("visibility","hidden").style("cursor","crosshair"),n.selectAll(".extent").data([0]).enter().append("rect").attr("class","extent").style("cursor","move");var e=n.selectAll(".resize").data(a,R);e.exit().remove(),e.enter().append("g").attr("class",function(n){return"resize "+n}).style("cursor",function(n){return Ka[n]}).append("rect").attr("x",function(n){return/[ew]$/.test(n)?-3:null}).attr("y",function(n){return/^[ns]/.test(n)?-3:null}).attr("width",6).attr("height",6).style("visibility","hidden"),e.style("display",m.empty()?"none":null);var r,i=Bi.transition(n),u=Bi.transition(t);k&&(r=Jr(k),u.attr("x",r[0]).attr("width",r[1]-r[0]),x(i)),N&&(r=Jr(N),u.attr("y",r[0]).attr("height",r[1]-r[0]),b(i)),M(i)})}function M(n){n.selectAll(".resize").attr("transform",function(n){return"translate("+E[+/e$/.test(n)]+","+A[+/^s/.test(n)]+")"})}function x(n){n.select(".extent").attr("x",E[0]),n.selectAll(".extent,.n>rect,.s>rect").attr("width",E[1]-E[0])}function b(n){n.select(".extent").attr("y",A[0]),n.selectAll(".extent,.e>rect,.w>rect").attr("height",A[1]-A[0])}function o(){function n(){var n=Bi.mouse(o),t=!1;u&&(n[0]+=u[0],n[1]+=u[1]),g||(Bi.event.altKey?(s=s||[(E[0]+E[1])/2,(A[0]+A[1])/2],v[0]=E[+(n[0]<s[0])],v[1]=A[+(n[1]<s[1])]):s=null),h&&e(n,k,0)&&(x(c),t=!0),p&&e(n,N,1)&&(b(c),t=!0),t&&(M(c),l({type:"brush",mode:g?"move":"resize"}))}function e(n,t,e){var r,i,u=Jr(t),o=u[0],a=u[1],l=v[e],c=e?A:E,f=c[1]-c[0];return g&&(o-=l,a-=f+l),r=(e?z:C)?Math.max(o,Math.min(a,n[e])):n[e],g?i=(r+=l)+f:(s&&(l=Math.max(o,Math.min(a,2*s[e]-r))),l<r?(i=r,r=l):i=l),(c[0]!=r||c[1]!=i)&&(e?w=null:_=null,c[0]=r,c[1]=i,1)}function t(){n(),c.style("pointer-events","all").selectAll(".resize").style("display",m.empty()?"none":null),Bi.select("body").style("cursor",null),y.on("mousemove.brush",null).on("mouseup.brush",null).on("touchmove.brush",null).on("touchend.brush",null).on("keydown.brush",null).on("keyup.brush",null),d(),l({type:"brushend"})}var s,r,i,u,o=this,a=Bi.select(Bi.event.target),l=S.of(o,arguments),c=Bi.select(o),f=a.datum(),h=!/^(n|s)$/.test(f)&&k,p=!/^(e|w)$/.test(f)&&N,g=a.classed("extent"),d=V(o),v=Bi.mouse(o),y=Bi.select(T(o)).on("keydown.brush",function(){32==Bi.event.keyCode&&(g||(s=null,v[0]-=E[1],v[1]-=A[1],g=2),D())}).on("keyup.brush",function(){32==Bi.event.keyCode&&2==g&&(v[0]+=E[1],v[1]+=A[1],g=0,D())});Bi.event.changedTouches?y.on("touchmove.brush",n).on("touchend.brush",t):y.on("mousemove.brush",n).on("mouseup.brush",t),c.interrupt().selectAll("*").interrupt(),g?(v[0]=E[0]-v[0],v[1]=A[0]-v[1]):f?(r=+/w$/.test(f),i=+/^n/.test(f),u=[E[1-r]-v[0],A[1-i]-v[1]],v[0]=E[r],v[1]=A[i]):Bi.event.altKey&&(s=v.slice()),c.style("pointer-events","none").selectAll(".resize").style("display",null),Bi.select("body").style("cursor",a.style("cursor")),l({type:"brushstart"}),n()}var _,w,S=P(m,"brushstart","brush","brushend"),k=null,N=null,E=[0,0],A=[0,0],C=!0,z=!0,a=Qa[0];return m.event=function(n){n.each(function(){var r=S.of(this,arguments),i={x:E,y:A,i:_,j:w},n=this.__chart__||i;this.__chart__=i,Va?Bi.select(this).transition().each("start.brush",function(){_=n.i,w=n.j,E=n.x,A=n.y,r({type:"brushstart"})}).tween("brush:brush",function(){var t=Be(E,i.x),e=Be(A,i.y);return _=w=null,function(n){E=i.x=t(n),A=i.y=e(n),r({type:"brush",mode:"resize"})}}).each("end.brush",function(){_=i.i,w=i.j,r({type:"brush",mode:"resize"}),r({type:"brushend"})}):(r({type:"brushstart"}),r({type:"brush",mode:"resize"}),r({type:"brushend"}))})},m.x=function(n){return arguments.length?(a=Qa[!(k=n)<<1|!N],m):k},m.y=function(n){return arguments.length?(a=Qa[!k<<1|!(N=n)],m):N},m.clamp=function(n){return arguments.length?(k&&N?(C=!!n[0],z=!!n[1]):k?C=!!n:N&&(z=!!n),m):k&&N?[C,z]:k?C:N?z:null},m.extent=function(n){var t,e,r,i,u;return arguments.length?(k&&(t=n[0],e=n[1],N&&(t=t[0],e=e[0]),_=[t,e],k.invert&&(t=k(t),e=k(e)),e<t&&(u=t,t=e,e=u),t==E[0]&&e==E[1]||(E=[t,e])),N&&(r=n[0],i=n[1],k&&(r=r[1],i=i[1]),w=[r,i],N.invert&&(r=N(r),i=N(i)),i<r&&(u=r,r=i,i=u),r==A[0]&&i==A[1]||(A=[r,i])),m):(k&&(_?(t=_[0],e=_[1]):(t=E[0],e=E[1],k.invert&&(t=k.invert(t),e=k.invert(e)),e<t&&(u=t,t=e,e=u))),N&&(w?(r=w[0],i=w[1]):(r=A[0],i=A[1],N.invert&&(r=N.invert(r),i=N.invert(i)),i<r&&(u=r,r=i,i=u))),k&&N?[[t,r],[e,i]]:k?[t,e]:N&&[r,i])},m.clear=function(){return m.empty()||(E=[0,0],A=[0,0],_=w=null),m},m.empty=function(){return!!k&&E[0]==E[1]||!!N&&A[0]==A[1]},Bi.rebind(m,S,"on")};var Ka={n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"},Qa=[["n","e","s","w","nw","ne","se","sw"],["e","w"],["n","s"],[]],nl=Ju.format=eo.timeFormat,tl=nl.utc,el=tl("%Y-%m-%dT%H:%M:%S.%LZ");nl.iso=Date.prototype.toISOString&&+new Date("2000-01-01T00:00:00.000Z")?Yi:el,Yi.parse=function(n){var t=new Date(n);return isNaN(t)?null:t},Yi.toString=el.toString,Ju.second=Ln(function(n){return new Gu(1e3*Math.floor(n/1e3))},function(n,t){n.setTime(n.getTime()+1e3*Math.floor(t))},function(n){return n.getSeconds()}),Ju.seconds=Ju.second.range,Ju.seconds.utc=Ju.second.utc.range,Ju.minute=Ln(function(n){return new Gu(6e4*Math.floor(n/6e4))},function(n,t){n.setTime(n.getTime()+6e4*Math.floor(t))},function(n){return n.getMinutes()}),Ju.minutes=Ju.minute.range,Ju.minutes.utc=Ju.minute.utc.range,Ju.hour=Ln(function(n){var t=n.getTimezoneOffset()/60;return new Gu(36e5*(Math.floor(n/36e5-t)+t))},function(n,t){n.setTime(n.getTime()+36e5*Math.floor(t))},function(n){return n.getHours()}),Ju.hours=Ju.hour.range,Ju.hours.utc=Ju.hour.utc.range,Ju.month=Ln(function(n){return(n=Ju.day(n)).setDate(1),n},function(n,t){n.setMonth(n.getMonth()+t)},function(n){return n.getMonth()}),Ju.months=Ju.month.range,Ju.months.utc=Ju.month.utc.range;var rl=[1e3,5e3,15e3,3e4,6e4,3e5,9e5,18e5,36e5,108e5,216e5,432e5,864e5,1728e5,6048e5,2592e6,7776e6,31536e6],il=[[Ju.second,1],[Ju.second,5],[Ju.second,15],[Ju.second,30],[Ju.minute,1],[Ju.minute,5],[Ju.minute,15],[Ju.minute,30],[Ju.hour,1],[Ju.hour,3],[Ju.hour,6],[Ju.hour,12],[Ju.day,1],[Ju.day,2],[Ju.week,1],[Ju.month,1],[Ju.month,3],[Ju.year,1]],ul=nl.multi([[".%L",function(n){return n.getMilliseconds()}],[":%S",function(n){return n.getSeconds()}],["%I:%M",function(n){return n.getMinutes()}],["%I %p",function(n){return n.getHours()}],["%a %d",function(n){return n.getDay()&&1!=n.getDate()}],["%b %d",function(n){return 1!=n.getDate()}],["%B",function(n){return n.getMonth()}],["%Y",Mt]]),ol={range:function(n,t,e){return Bi.range(Math.ceil(n/e)*e,+t,e).map(Vi)},floor:R,ceil:R};il.year=Ju.year,Ju.scale=function(){return Zi(Bi.scale.linear(),il,ul)};var al=il.map(function(n){return[n[0].utc,n[1]]}),ll=tl.multi([[".%L",function(n){return n.getUTCMilliseconds()}],[":%S",function(n){return n.getUTCSeconds()}],["%I:%M",function(n){return n.getUTCMinutes()}],["%I %p",function(n){return n.getUTCHours()}],["%a %d",function(n){return n.getUTCDay()&&1!=n.getUTCDate()}],["%b %d",function(n){return 1!=n.getUTCDate()}],["%B",function(n){return n.getUTCMonth()}],["%Y",Mt]]);al.year=Ju.year.utc,Ju.scale.utc=function(){return Zi(Bi.scale.linear(),al,ll)},Bi.text=_n(function(n){return n.responseText}),Bi.json=function(n,t){return wn(n,"application/json",Xi,t)},Bi.html=function(n,t){return wn(n,"text/html",$i,t)},Bi.xml=_n(function(n){return n.responseXML}),"function"==typeof define&&define.amd?(this.d3=Bi,define(Bi)):"object"==typeof module&&module.exports?module.exports=Bi:this.d3=Bi}();