!function(e){var o,M,k,V,R,E,t=(o=e.document,M=Array.prototype.slice,k=e.requestAnimationFrame||e.mozRequestAnimationFrame||e.webkitRequestAnimationFrame||e.msRequestAnimationFrame||function(e){return setTimeout(e,1e3/60)},R=V=50,E={dialRadius:40,dialStartAngle:135,dialEndAngle:45,value:0,max:100,min:0,valueDialClass:"value",valueClass:"value-text",dialClass:"dial",gaugeClass:"gauge",showValue:!0,gaugeColor:null,label:function(e){return Math.round(e)}},function(e,a){var i,o,t,n,r=e,d=(a=function(e){var n=e;return M.call(arguments,1).forEach(function(e){for(var t in e)e.hasOwnProperty(t)&&(n[t]=e[t])}),n}({},E,a)).max,f=a.min,c=S(a.value,f,d),l=a.dialRadius,s=a.showValue,u=a.dialStartAngle,m=a.dialEndAngle,v=a.valueDialClass,h=a.valueClass,g=(a.valueLabelClass,a.dialClass),p=a.gaugeClass,w=a.color,A=a.label,x=a.viewBox;function b(e,t,n,a){var i,o,r,l,s=(o=t,{end:j(r=V,l=R,i=e,n),start:j(r,l,i,o)}),u=s.start,d=s.end,f=void 0===a?1:a;return["M",u.x,u.y,"A",e,e,0,f,1,d.x,d.y].join(" ")}function C(e){var t=F(100*(e-f)/(d-f),360-Math.abs(u-m)),n=t<=180?0:1;s&&(i.textContent=A.call(a,e)),o.setAttribute("d",b(l,u,t+u,n))}function y(e,t){var n=w(e),a="stroke "+1e3*t+"ms ease";o.style=["stroke: "+n,"-webkit-transition: "+a,"-moz-transition: "+a,"transition: "+a].join(";")}return u<m&&(console.log("WARN! startAngle < endAngle, Swapping"),n=u,u=m,m=n),t={setMaxValue:function(e){d=e},setValue:function(e){c=S(e,f,d),w&&y(c,0),C(c)},setValueAnimated:function(e,t){var n,a,i,o,r,l,s,u=c;u!==(c=S(e,f,d))&&(w&&y(c,t),a=1,i=60*(n={start:u||0,end:c,duration:t||1,step:function(e,t){C(e)}}).duration,r=n.end-(o=n.start||0),l=n.step,s=n.easing||function(e){return(e/=.5)<1?.5*Math.pow(e,3):.5*(Math.pow(e-2,3)+2)},k(function e(){var t=a/i,n=r*s(t)+o;l(n,a),a+=1,t<1&&k(e)}))},getValue:function(){return c}},function(e){i=q("text",{x:50,y:50,fill:"#999",class:h,"font-size":"100%","font-family":"sans-serif","font-weight":"normal","text-anchor":"middle","alignment-baseline":"middle","dominant-baseline":"central"}),o=q("path",{class:v,fill:"none",stroke:"#666","stroke-width":2.5,d:b(l,u,u)});var t=F(100,360-Math.abs(u-m)),n=q("svg",{viewBox:x||"0 0 100 100",class:p},[q("path",{class:g,fill:"none",stroke:"#eee","stroke-width":2,d:b(l,u,m,t<=180?0:1)}),i,o]);e.appendChild(n)}(r),t.setValue(c),t});function q(e,t,n){var a=o.createElementNS("http://www.w3.org/2000/svg",e);for(var i in t)a.setAttribute(i,t[i]);return n&&n.forEach(function(e){a.appendChild(e)}),a}function F(e,t){return e*t/100}function S(e,t,n){var a=Number(e);return n<a?n:a<t?t:a}function j(e,t,n,a){var i=a*Math.PI/180;return{x:Math.round(1e3*(e+n*Math.cos(i)))/1e3,y:Math.round(1e3*(t+n*Math.sin(i)))/1e3}}"function"==typeof define&&define.amd?define(function(){return t}):"object"==typeof module&&module.exports?module.exports=t:e.Gauge=t}("undefined"==typeof window?this:window);