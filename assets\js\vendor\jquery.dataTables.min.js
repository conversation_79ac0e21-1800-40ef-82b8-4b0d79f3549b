!(function (n) {
  "function" == typeof define && define.amd
    ? define(["jquery"], function (t) {
        return n(t, window, document);
      })
    : "object" == typeof exports
    ? (module.exports = function (t, e) {
        return (
          (t = t || window),
          (e =
            e ||
            ("undefined" != typeof window
              ? require("jquery")
              : require("jquery")(t))),
          n(e, t, t.document)
        );
      })
    : n(jQuery, window, document);
})(function (P, m, v, j) {
  function o(e) {
    var n,
      a,
      r = {};
    P.each(e, function (t) {
      (n = t.match(/^([^A-Z]+?)([A-Z])/)) &&
        -1 !== "a aa ai ao as b fn i m o s ".indexOf(n[1] + " ") &&
        ((a = t.replace(n[0], n[2].toLowerCase())),
        (r[a] = t),
        "o" === n[1] && o(e[t]));
    }),
      (e._hungarianMap = r);
  }
  function D(e, n, a) {
    var r;
    e._hungarianMap || o(e),
      P.each(n, function (t) {
        (r = e._hungarianMap[t]) === j ||
          (!a && n[r] !== j) ||
          ("o" === r.charAt(0)
            ? (n[r] || (n[r] = {}), P.extend(!0, n[r], n[t]), D(e[r], n[r], a))
            : (n[r] = n[t]));
      });
  }
  function y(t) {
    var e,
      n = Qt.defaults.oLanguage,
      a = n.sDecimal;
    a && Bt(a),
      t &&
        ((e = t.sZeroRecords),
        !t.sEmptyTable &&
          e &&
          "No data available in table" === n.sEmptyTable &&
          Pt(t, t, "sZeroRecords", "sEmptyTable"),
        !t.sLoadingRecords &&
          e &&
          "Loading..." === n.sLoadingRecords &&
          Pt(t, t, "sZeroRecords", "sLoadingRecords"),
        t.sInfoThousands && (t.sThousands = t.sInfoThousands),
        (t = t.sDecimal) && a !== t && Bt(t));
  }
  function _(t) {
    if (
      (se(t, "ordering", "bSort"),
      se(t, "orderMulti", "bSortMulti"),
      se(t, "orderClasses", "bSortClasses"),
      se(t, "orderCellsTop", "bSortCellsTop"),
      se(t, "order", "aaSorting"),
      se(t, "orderFixed", "aaSortingFixed"),
      se(t, "paging", "bPaginate"),
      se(t, "pagingType", "sPaginationType"),
      se(t, "pageLength", "iDisplayLength"),
      se(t, "searching", "bFilter"),
      "boolean" == typeof t.sScrollX && (t.sScrollX = t.sScrollX ? "100%" : ""),
      "boolean" == typeof t.scrollX && (t.scrollX = t.scrollX ? "100%" : ""),
      (t = t.aoSearchCols))
    )
      for (var e = 0, n = t.length; e < n; e++)
        t[e] && D(Qt.models.oSearch, t[e]);
  }
  function T(t) {
    se(t, "orderable", "bSortable"),
      se(t, "orderData", "aDataSort"),
      se(t, "orderSequence", "asSorting"),
      se(t, "orderDataType", "sortDataType");
    var e = t.aDataSort;
    "number" != typeof e || P.isArray(e) || (t.aDataSort = [e]);
  }
  function C(t) {
    var e, n, a, r;
    Qt.__browser ||
      ((e = {}),
      (Qt.__browser = e),
      (r = (a = (n = P("<div/>")
        .css({
          position: "fixed",
          top: 0,
          left: -1 * P(m).scrollLeft(),
          height: 1,
          width: 1,
          overflow: "hidden",
        })
        .append(
          P("<div/>")
            .css({
              position: "absolute",
              top: 1,
              left: 1,
              width: 100,
              overflow: "scroll",
            })
            .append(P("<div/>").css({ width: "100%", height: 10 }))
        )
        .appendTo("body")).children()).children()),
      (e.barWidth = a[0].offsetWidth - a[0].clientWidth),
      (e.bScrollOversize =
        100 === r[0].offsetWidth && 100 !== a[0].clientWidth),
      (e.bScrollbarLeft = 1 !== Math.round(r.offset().left)),
      (e.bBounding = !!n[0].getBoundingClientRect().width),
      n.remove()),
      P.extend(t.oBrowser, Qt.__browser),
      (t.oScroll.iBarWidth = Qt.__browser.barWidth);
  }
  function n(t, e, n, a, r, o) {
    var i,
      l = !1;
    for (n !== j && ((i = n), (l = !0)); a !== r; )
      t.hasOwnProperty(a) &&
        ((i = l ? e(i, t[a], a, t) : t[a]), (l = !0), (a += o));
    return i;
  }
  function w(t, e) {
    var n = Qt.defaults.column,
      a = t.aoColumns.length,
      n = P.extend({}, Qt.models.oColumn, n, {
        nTh: e || v.createElement("th"),
        sTitle: n.sTitle ? n.sTitle : e ? e.innerHTML : "",
        aDataSort: n.aDataSort ? n.aDataSort : [a],
        mData: n.mData ? n.mData : a,
        idx: a,
      });
    t.aoColumns.push(n),
      ((n = t.aoPreSearchCols)[a] = P.extend({}, Qt.models.oSearch, n[a])),
      x(t, a, P(e).data());
  }
  function x(t, e, n) {
    var a,
      e = t.aoColumns[e],
      r = t.oClasses,
      o = P(e.nTh);
    e.sWidthOrig ||
      ((e.sWidthOrig = o.attr("width") || null),
      (a = (o.attr("style") || "").match(/width:\s*(\d+[pxem%]+)/)) &&
        (e.sWidthOrig = a[1])),
      n !== j &&
        null !== n &&
        (T(n),
        D(Qt.defaults.column, n, !0),
        n.mDataProp === j || n.mData || (n.mData = n.mDataProp),
        n.sType && (e._sManualType = n.sType),
        n.className && !n.sClass && (n.sClass = n.className),
        n.sClass && o.addClass(n.sClass),
        P.extend(e, n),
        Pt(e, n, "sWidth", "sWidthOrig"),
        n.iDataSort !== j && (e.aDataSort = [n.iDataSort]),
        Pt(e, n, "aDataSort"));
    var i = e.mData,
      l = O(i),
      s = e.mRender ? O(e.mRender) : null,
      n = function (t) {
        return "string" == typeof t && -1 !== t.indexOf("@");
      };
    (e._bAttrSrc =
      P.isPlainObject(i) && (n(i.sort) || n(i.type) || n(i.filter))),
      (e._setter = null),
      (e.fnGetData = function (t, e, n) {
        var a = l(t, e, j, n);
        return s && e ? s(a, e, t, n) : a;
      }),
      (e.fnSetData = function (t, e, n) {
        return p(i)(t, e, n);
      }),
      "number" != typeof i && (t._rowReadObject = !0),
      t.oFeatures.bSort || ((e.bSortable = !1), o.addClass(r.sSortableNone)),
      (t = -1 !== P.inArray("asc", e.asSorting)),
      (n = -1 !== P.inArray("desc", e.asSorting)),
      e.bSortable && (t || n)
        ? t && !n
          ? ((e.sSortingClass = r.sSortableAsc),
            (e.sSortingClassJUI = r.sSortJUIAscAllowed))
          : !t && n
          ? ((e.sSortingClass = r.sSortableDesc),
            (e.sSortingClassJUI = r.sSortJUIDescAllowed))
          : ((e.sSortingClass = r.sSortable), (e.sSortingClassJUI = r.sSortJUI))
        : ((e.sSortingClass = r.sSortableNone), (e.sSortingClassJUI = ""));
  }
  function N(t) {
    if (!1 !== t.oFeatures.bAutoWidth) {
      var e = t.aoColumns;
      bt(t);
      for (var n = 0, a = e.length; n < a; n++)
        e[n].nTh.style.width = e[n].sWidth;
    }
    ("" === (e = t.oScroll).sY && "" === e.sX) || pt(t),
      Ot(t, null, "column-sizing", [t]);
  }
  function H(t, e) {
    var n = I(t, "bVisible");
    return "number" == typeof n[e] ? n[e] : null;
  }
  function u(t, e) {
    var n = I(t, "bVisible");
    return -1 !== (n = P.inArray(e, n)) ? n : null;
  }
  function S(t) {
    var n = 0;
    return (
      P.each(t.aoColumns, function (t, e) {
        e.bVisible && "none" !== P(e.nTh).css("display") && n++;
      }),
      n
    );
  }
  function I(t, n) {
    var a = [];
    return (
      P.map(t.aoColumns, function (t, e) {
        t[n] && a.push(e);
      }),
      a
    );
  }
  function i(t) {
    for (
      var e,
        n,
        a,
        r,
        o,
        i,
        l,
        s = t.aoColumns,
        u = t.aoData,
        c = Qt.ext.type.detect,
        f = 0,
        d = s.length;
      f < d;
      f++
    )
      if (((l = []), !(o = s[f]).sType && o._sManualType))
        o.sType = o._sManualType;
      else if (!o.sType) {
        for (e = 0, n = c.length; e < n; e++) {
          for (
            a = 0, r = u.length;
            a < r &&
            (l[a] === j && (l[a] = R(t, a, f, "type")),
            (i = c[e](l[a], t)) || e === c.length - 1) &&
            "html" !== i;
            a++
          );
          if (i) {
            o.sType = i;
            break;
          }
        }
        o.sType || (o.sType = "string");
      }
  }
  function A(t, e, n, a) {
    var r,
      o,
      i,
      l,
      s,
      u,
      c = t.aoColumns;
    if (e)
      for (r = e.length - 1; 0 <= r; r--) {
        var f = (u = e[r]).targets !== j ? u.targets : u.aTargets;
        for (P.isArray(f) || (f = [f]), o = 0, i = f.length; o < i; o++)
          if ("number" == typeof f[o] && 0 <= f[o]) {
            for (; c.length <= f[o]; ) w(t);
            a(f[o], u);
          } else if ("number" == typeof f[o] && f[o] < 0) a(c.length + f[o], u);
          else if ("string" == typeof f[o])
            for (l = 0, s = c.length; l < s; l++)
              ("_all" != f[o] && !P(c[l].nTh).hasClass(f[o])) || a(l, u);
      }
    if (n) for (r = 0, t = n.length; r < t; r++) a(r, n[r]);
  }
  function F(t, e, n, a) {
    var r = t.aoData.length,
      o = P.extend(!0, {}, Qt.models.oRow, { src: n ? "dom" : "data", idx: r });
    (o._aData = e), t.aoData.push(o);
    for (var i = t.aoColumns, l = 0, s = i.length; l < s; l++)
      i[l].sType = null;
    return (
      t.aiDisplayMaster.push(r),
      (e = t.rowIdFn(e)) !== j && (t.aIds[e] = o),
      (!n && t.oFeatures.bDeferRender) || b(t, r, n, a),
      r
    );
  }
  function L(n, t) {
    var a;
    return (
      t instanceof P || (t = P(t)),
      t.map(function (t, e) {
        return (a = s(n, e)), F(n, a.data, e, a.cells);
      })
    );
  }
  function R(t, e, n, a) {
    var r = t.iDraw,
      o = t.aoColumns[n],
      i = t.aoData[e]._aData,
      l = o.sDefaultContent,
      s = o.fnGetData(i, a, { settings: t, row: e, col: n });
    if (s === j)
      return (
        t.iDrawError != r &&
          null === l &&
          (Rt(
            t,
            0,
            "Requested unknown parameter " +
              ("function" == typeof o.mData
                ? "{function}"
                : "'" + o.mData + "'") +
              " for row " +
              e +
              ", column " +
              n,
            4
          ),
          (t.iDrawError = r)),
        l
      );
    if ((s !== i && null !== s) || null === l || a === j) {
      if ("function" == typeof s) return s.call(i);
    } else s = l;
    return null === s && "display" == a ? "" : s;
  }
  function a(t, e, n, a) {
    t.aoColumns[n].fnSetData(t.aoData[e]._aData, a, {
      settings: t,
      row: e,
      col: n,
    });
  }
  function c(t) {
    return P.map(t.match(/(\\.|[^\.])+/g) || [""], function (t) {
      return t.replace(/\\\./g, ".");
    });
  }
  function O(r) {
    if (P.isPlainObject(r)) {
      var o = {};
      return (
        P.each(r, function (t, e) {
          e && (o[t] = O(e));
        }),
        function (t, e, n, a) {
          var r = o[e] || o._;
          return r !== j ? r(t, e, n, a) : t;
        }
      );
    }
    if (null === r)
      return function (t) {
        return t;
      };
    if ("function" == typeof r)
      return function (t, e, n, a) {
        return r(t, e, n, a);
      };
    if (
      "string" != typeof r ||
      (-1 === r.indexOf(".") && -1 === r.indexOf("[") && -1 === r.indexOf("("))
    )
      return function (t) {
        return t[r];
      };
    var l = function (t, e, n) {
      var a, r;
      if ("" !== n)
        for (var o = 0, i = (r = c(n)).length; o < i; o++) {
          if (((n = r[o].match(ue)), (a = r[o].match(ce)), n)) {
            if (
              ((r[o] = r[o].replace(ue, "")),
              "" !== r[o] && (t = t[r[o]]),
              (a = []),
              r.splice(0, o + 1),
              (r = r.join(".")),
              P.isArray(t))
            )
              for (o = 0, i = t.length; o < i; o++) a.push(l(t[o], e, r));
            t = "" === (t = n[0].substring(1, n[0].length - 1)) ? a : a.join(t);
            break;
          }
          if (a) (r[o] = r[o].replace(ce, "")), (t = t[r[o]]());
          else {
            if (null === t || t[r[o]] === j) return j;
            t = t[r[o]];
          }
        }
      return t;
    };
    return function (t, e) {
      return l(t, e, r);
    };
  }
  function p(a) {
    if (P.isPlainObject(a)) return p(a._);
    if (null === a) return function () {};
    if ("function" == typeof a)
      return function (t, e, n) {
        a(t, "set", e, n);
      };
    if (
      "string" != typeof a ||
      (-1 === a.indexOf(".") && -1 === a.indexOf("[") && -1 === a.indexOf("("))
    )
      return function (t, e) {
        t[a] = e;
      };
    var s = function (t, e, n) {
      for (
        var a, r, o = (n = c(n))[n.length - 1], i = 0, l = n.length - 1;
        i < l;
        i++
      ) {
        if (((a = n[i].match(ue)), (r = n[i].match(ce)), a)) {
          if (
            ((n[i] = n[i].replace(ue, "")),
            (t[n[i]] = []),
            (o = n.slice()).splice(0, i + 1),
            (a = o.join(".")),
            P.isArray(e))
          )
            for (r = 0, l = e.length; r < l; r++)
              s((o = {}), e[r], a), t[n[i]].push(o);
          else t[n[i]] = e;
          return;
        }
        r && ((n[i] = n[i].replace(ce, "")), (t = t[n[i]](e))),
          (null !== t[n[i]] && t[n[i]] !== j) || (t[n[i]] = {}),
          (t = t[n[i]]);
      }
      o.match(ce) ? t[o.replace(ce, "")](e) : (t[o.replace(ue, "")] = e);
    };
    return function (t, e) {
      return s(t, e, a);
    };
  }
  function g(t) {
    return ie(t.aoData, "_aData");
  }
  function l(t) {
    (t.aoData.length = 0),
      (t.aiDisplayMaster.length = 0),
      (t.aiDisplay.length = 0),
      (t.aIds = {});
  }
  function f(t, e, n) {
    for (var a = -1, r = 0, o = t.length; r < o; r++)
      t[r] == e ? (a = r) : t[r] > e && t[r]--;
    -1 != a && n === j && t.splice(a, 1);
  }
  function r(n, a, t, e) {
    var r,
      o = n.aoData[a],
      i = function (t, e) {
        for (; t.childNodes.length; ) t.removeChild(t.firstChild);
        t.innerHTML = R(n, a, e, "display");
      };
    if ("dom" !== t && ((t && "auto" !== t) || "dom" !== o.src)) {
      var l = o.anCells;
      if (l)
        if (e !== j) i(l[e], e);
        else for (t = 0, r = l.length; t < r; t++) i(l[t], t);
    } else o._aData = s(n, o, e, e === j ? j : o._aData).data;
    if (
      ((o._aSortData = null),
      (o._aFilterData = null),
      (i = n.aoColumns),
      e !== j)
    )
      i[e].sType = null;
    else {
      for (t = 0, r = i.length; t < r; t++) i[t].sType = null;
      h(n, o);
    }
  }
  function s(t, e, n, a) {
    function r(t, e) {
      var n;
      "string" != typeof t ||
        (-1 !== (n = t.indexOf("@")) &&
          ((n = t.substring(n + 1)), p(t)(a, e.getAttribute(n))));
    }
    function o(t) {
      (n !== j && n !== f) ||
        ((l = d[f]),
        (s = P.trim(t.innerHTML)),
        l && l._bAttrSrc
          ? (p(l.mData._)(a, s),
            r(l.mData.sort, t),
            r(l.mData.type, t),
            r(l.mData.filter, t))
          : h
          ? (l._setter || (l._setter = p(l.mData)), l._setter(a, s))
          : (a[f] = s)),
        f++;
    }
    var i,
      l,
      s,
      u = [],
      c = e.firstChild,
      f = 0,
      d = t.aoColumns,
      h = t._rowReadObject,
      a = a !== j ? a : h ? {} : [];
    if (c)
      for (; c; )
        ("TD" != (i = c.nodeName.toUpperCase()) && "TH" != i) ||
          (o(c), u.push(c)),
          (c = c.nextSibling);
    else for (c = 0, i = (u = e.anCells).length; c < i; c++) o(u[c]);
    return (
      (e = e.firstChild ? e : e.nTr) &&
        (e = e.getAttribute("id")) &&
        p(t.rowId)(a, e),
      { data: a, cells: u }
    );
  }
  function b(t, e, n, a) {
    var r,
      o,
      i,
      l,
      s,
      u,
      c = t.aoData[e],
      f = c._aData,
      d = [];
    if (null === c.nTr) {
      for (
        r = n || v.createElement("tr"),
          c.nTr = r,
          c.anCells = d,
          r._DT_RowIndex = e,
          h(t, c),
          l = 0,
          s = t.aoColumns.length;
        l < s;
        l++
      )
        (i = t.aoColumns[l]),
          ((o = (u = !n) ? v.createElement(i.sCellType) : a[l])._DT_CellIndex =
            { row: e, column: l }),
          d.push(o),
          (!u &&
            ((n && !i.mRender && i.mData === l) ||
              (P.isPlainObject(i.mData) && i.mData._ === l + ".display"))) ||
            (o.innerHTML = R(t, e, l, "display")),
          i.sClass && (o.className += " " + i.sClass),
          i.bVisible && !n
            ? r.appendChild(o)
            : !i.bVisible && n && o.parentNode.removeChild(o),
          i.fnCreatedCell &&
            i.fnCreatedCell.call(t.oInstance, o, R(t, e, l), f, e, l);
      Ot(t, "aoRowCreatedCallback", null, [r, f, e, d]);
    }
    c.nTr.setAttribute("role", "row");
  }
  function h(t, e) {
    var n,
      a = e.nTr,
      r = e._aData;
    a &&
      ((n = t.rowIdFn(r)) && (a.id = n),
      r.DT_RowClass &&
        ((n = r.DT_RowClass.split(" ")),
        (e.__rowc = e.__rowc ? le(e.__rowc.concat(n)) : n),
        P(a).removeClass(e.__rowc.join(" ")).addClass(r.DT_RowClass)),
      r.DT_RowAttr && P(a).attr(r.DT_RowAttr),
      r.DT_RowData && P(a).data(r.DT_RowData));
  }
  function d(t) {
    var e,
      n,
      a,
      r,
      o,
      i = t.nTHead,
      l = t.nTFoot,
      s = 0 === P("th, td", i).length,
      u = t.oClasses,
      c = t.aoColumns;
    for (s && (r = P("<tr/>").appendTo(i)), e = 0, n = c.length; e < n; e++)
      (o = c[e]),
        (a = P(o.nTh).addClass(o.sClass)),
        s && a.appendTo(r),
        t.oFeatures.bSort &&
          (a.addClass(o.sSortingClass),
          !1 !== o.bSortable &&
            (a.attr("tabindex", t.iTabIndex).attr("aria-controls", t.sTableId),
            wt(t, o.nTh, e))),
        o.sTitle != a[0].innerHTML && a.html(o.sTitle),
        Mt(t, "header")(t, a, o, u);
    if (
      (s && B(t.aoHeader, i),
      P(i).find(">tr").attr("role", "row"),
      P(i).find(">tr>th, >tr>td").addClass(u.sHeaderTH),
      P(l).find(">tr>th, >tr>td").addClass(u.sFooterTH),
      null !== l)
    )
      for (e = 0, n = (t = t.aoFooter[0]).length; e < n; e++)
        ((o = c[e]).nTf = t[e].cell), o.sClass && P(o.nTf).addClass(o.sClass);
  }
  function k(t, e, n) {
    var a,
      r,
      o,
      i,
      l = [],
      s = [],
      u = t.aoColumns.length;
    if (e) {
      for (n === j && (n = !1), a = 0, r = e.length; a < r; a++) {
        for (l[a] = e[a].slice(), l[a].nTr = e[a].nTr, o = u - 1; 0 <= o; o--)
          t.aoColumns[o].bVisible || n || l[a].splice(o, 1);
        s.push([]);
      }
      for (a = 0, r = l.length; a < r; a++) {
        if ((t = l[a].nTr)) for (; (o = t.firstChild); ) t.removeChild(o);
        for (o = 0, e = l[a].length; o < e; o++)
          if (((i = u = 1), s[a][o] === j)) {
            for (
              t.appendChild(l[a][o].cell), s[a][o] = 1;
              l[a + u] !== j && l[a][o].cell == l[a + u][o].cell;

            )
              (s[a + u][o] = 1), u++;
            for (; l[a][o + i] !== j && l[a][o].cell == l[a][o + i].cell; ) {
              for (n = 0; n < u; n++) s[a + n][o + i] = 1;
              i++;
            }
            P(l[a][o].cell).attr("rowspan", u).attr("colspan", i);
          }
      }
    }
  }
  function M(t) {
    var e = Ot(t, "aoPreDrawCallback", "preDraw", [t]);
    if (-1 !== P.inArray(!1, e)) dt(t, !1);
    else {
      var e = [],
        n = 0,
        a = t.asStripeClasses,
        r = a.length,
        o = t.oLanguage,
        i = t.iInitDisplayStart,
        l = "ssp" == Wt(t),
        s = t.aiDisplay;
      (t.bDrawing = !0),
        i !== j &&
          -1 !== i &&
          ((t._iDisplayStart = !l && i >= t.fnRecordsDisplay() ? 0 : i),
          (t.iInitDisplayStart = -1));
      var i = t._iDisplayStart,
        u = t.fnDisplayEnd();
      if (t.bDeferLoading) (t.bDeferLoading = !1), t.iDraw++, dt(t, !1);
      else if (l) {
        if (!t.bDestroying && !X(t)) return;
      } else t.iDraw++;
      if (0 !== s.length)
        for (o = l ? t.aoData.length : u, l = l ? 0 : i; l < o; l++) {
          var c = s[l],
            f = t.aoData[c];
          null === f.nTr && b(t, c);
          var d,
            h = f.nTr;
          0 !== r &&
            ((d = a[n % r]),
            f._sRowStripe != d &&
              (P(h).removeClass(f._sRowStripe).addClass(d),
              (f._sRowStripe = d))),
            Ot(t, "aoRowCallback", null, [h, f._aData, n, l, c]),
            e.push(h),
            n++;
        }
      else
        (n = o.sZeroRecords),
          1 == t.iDraw && "ajax" == Wt(t)
            ? (n = o.sLoadingRecords)
            : o.sEmptyTable && 0 === t.fnRecordsTotal() && (n = o.sEmptyTable),
          (e[0] = P("<tr/>", { class: r ? a[0] : "" }).append(
            P("<td />", {
              valign: "top",
              colSpan: S(t),
              class: t.oClasses.sRowEmpty,
            }).html(n)
          )[0]);
      Ot(t, "aoHeaderCallback", "header", [
        P(t.nTHead).children("tr")[0],
        g(t),
        i,
        u,
        s,
      ]),
        Ot(t, "aoFooterCallback", "footer", [
          P(t.nTFoot).children("tr")[0],
          g(t),
          i,
          u,
          s,
        ]),
        (a = P(t.nTBody)).children().detach(),
        a.append(P(e)),
        Ot(t, "aoDrawCallback", "draw", [t]),
        (t.bSorted = !1),
        (t.bFiltered = !1),
        (t.bDrawing = !1);
    }
  }
  function W(t, e) {
    var n = t.oFeatures,
      a = n.bFilter;
    n.bSort && _t(t),
      a ? $(t, t.oPreviousSearch) : (t.aiDisplay = t.aiDisplayMaster.slice()),
      !0 !== e && (t._iDisplayStart = 0),
      (t._drawHold = e),
      M(t),
      (t._drawHold = !1);
  }
  function E(t) {
    var e = t.oClasses,
      n = P(t.nTable),
      n = P("<div/>").insertBefore(n),
      a = t.oFeatures,
      r = P("<div/>", {
        id: t.sTableId + "_wrapper",
        class: e.sWrapper + (t.nTFoot ? "" : " " + e.sNoFooter),
      });
    (t.nHolding = n[0]),
      (t.nTableWrapper = r[0]),
      (t.nTableReinsertBefore = t.nTable.nextSibling);
    for (var o, i, l, s, u, c, f = t.sDom.split(""), d = 0; d < f.length; d++) {
      if (((o = null), "<" == (i = f[d]))) {
        if (((l = P("<div/>")[0]), "'" == (s = f[d + 1]) || '"' == s)) {
          for (u = "", c = 2; f[d + c] != s; ) (u += f[d + c]), c++;
          "H" == u ? (u = e.sJUIHeader) : "F" == u && (u = e.sJUIFooter),
            -1 != u.indexOf(".")
              ? ((s = u.split(".")),
                (l.id = s[0].substr(1, s[0].length - 1)),
                (l.className = s[1]))
              : "#" == u.charAt(0)
              ? (l.id = u.substr(1, u.length - 1))
              : (l.className = u),
            (d += c);
        }
        r.append(l), (r = P(l));
      } else if (">" == i) r = r.parent();
      else if ("l" == i && a.bPaginate && a.bLengthChange) o = st(t);
      else if ("f" == i && a.bFilter) o = G(t);
      else if ("r" == i && a.bProcessing) o = ft(t);
      else if ("t" == i) o = ht(t);
      else if ("i" == i && a.bInfo) o = nt(t);
      else if ("p" == i && a.bPaginate) o = ut(t);
      else if (0 !== Qt.ext.feature.length)
        for (c = 0, s = (l = Qt.ext.feature).length; c < s; c++)
          if (i == l[c].cFeature) {
            o = l[c].fnInit(t);
            break;
          }
      o && ((l = t.aanFeatures)[i] || (l[i] = []), l[i].push(o), r.append(o));
    }
    n.replaceWith(r), (t.nHolding = null);
  }
  function B(t, e) {
    var n,
      a,
      r,
      o,
      i,
      l,
      s,
      u,
      c,
      f,
      d = P(e).children("tr");
    for (t.splice(0, t.length), r = 0, l = d.length; r < l; r++) t.push([]);
    for (r = 0, l = d.length; r < l; r++)
      for (a = (n = d[r]).firstChild; a; ) {
        if (
          "TD" == a.nodeName.toUpperCase() ||
          "TH" == a.nodeName.toUpperCase()
        ) {
          for (
            u = (u = +a.getAttribute("colspan")) && 0 !== u && 1 !== u ? u : 1,
              c =
                (c = +a.getAttribute("rowspan")) && 0 !== c && 1 !== c ? c : 1,
              o = 0,
              i = t[r];
            i[o];

          )
            o++;
          for (s = o, f = 1 === u, i = 0; i < u; i++)
            for (o = 0; o < c; o++)
              (t[r + o][s + i] = { cell: a, unique: f }), (t[r + o].nTr = n);
        }
        a = a.nextSibling;
      }
  }
  function U(t, e, n) {
    var a = [];
    n || ((n = t.aoHeader), e && B((n = []), e));
    for (var e = 0, r = n.length; e < r; e++)
      for (var o = 0, i = n[e].length; o < i; o++)
        !n[e][o].unique || (a[o] && t.bSortCellsTop) || (a[o] = n[e][o].cell);
    return a;
  }
  function V(a, t, e) {
    var r, o;
    Ot(a, "aoServerParams", "serverParams", [t]),
      t &&
        P.isArray(t) &&
        ((r = {}),
        (o = /(.*?)\[\]$/),
        P.each(t, function (t, e) {
          var n = e.name.match(o);
          n
            ? ((n = n[0]), r[n] || (r[n] = []), r[n].push(e.value))
            : (r[e.name] = e.value);
        }),
        (t = r));
    function n(t) {
      Ot(a, null, "xhr", [a, t, a.jqXHR]), e(t);
    }
    var i,
      l,
      s = a.ajax,
      u = a.oInstance;
    P.isPlainObject(s) &&
      s.data &&
      ((l = "function" == typeof (i = s.data) ? i(t, a) : i),
      (t = "function" == typeof i && l ? l : P.extend(!0, t, l)),
      delete s.data),
      (l = {
        data: t,
        success: function (t) {
          var e = t.error || t.sError;
          e && Rt(a, 0, e), (a.json = t), n(t);
        },
        dataType: "json",
        cache: !1,
        type: a.sServerMethod,
        error: function (t, e) {
          var n = Ot(a, null, "xhr", [a, null, a.jqXHR]);
          -1 === P.inArray(!0, n) &&
            ("parsererror" == e
              ? Rt(a, 0, "Invalid JSON response", 1)
              : 4 === t.readyState && Rt(a, 0, "Ajax error", 7)),
            dt(a, !1);
        },
      }),
      (a.oAjaxData = t),
      Ot(a, null, "preXhr", [a, t]),
      a.fnServerData
        ? a.fnServerData.call(
            u,
            a.sAjaxSource,
            P.map(t, function (t, e) {
              return { name: e, value: t };
            }),
            n,
            a
          )
        : a.sAjaxSource || "string" == typeof s
        ? (a.jqXHR = P.ajax(P.extend(l, { url: s || a.sAjaxSource })))
        : "function" == typeof s
        ? (a.jqXHR = s.call(u, t, n, a))
        : ((a.jqXHR = P.ajax(P.extend(l, s))), (s.data = i));
  }
  function X(e) {
    return (
      !e.bAjaxDataGet ||
      (e.iDraw++,
      dt(e, !0),
      V(e, t(e), function (t) {
        J(e, t);
      }),
      !1)
    );
  }
  function t(t) {
    function n(t, e) {
      u.push({ name: t, value: e });
    }
    var e,
      a,
      r = t.aoColumns,
      o = r.length,
      i = t.oFeatures,
      l = t.oPreviousSearch,
      s = t.aoPreSearchCols,
      u = [],
      c = yt(t),
      f = t._iDisplayStart,
      d = !1 !== i.bPaginate ? t._iDisplayLength : -1;
    n("sEcho", t.iDraw),
      n("iColumns", o),
      n("sColumns", ie(r, "sName").join(",")),
      n("iDisplayStart", f),
      n("iDisplayLength", d);
    var h = {
      draw: t.iDraw,
      columns: [],
      order: [],
      start: f,
      length: d,
      search: { value: l.sSearch, regex: l.bRegex },
    };
    for (f = 0; f < o; f++)
      (e = r[f]),
        (a = s[f]),
        (d = "function" == typeof e.mData ? "function" : e.mData),
        h.columns.push({
          data: d,
          name: e.sName,
          searchable: e.bSearchable,
          orderable: e.bSortable,
          search: { value: a.sSearch, regex: a.bRegex },
        }),
        n("mDataProp_" + f, d),
        i.bFilter &&
          (n("sSearch_" + f, a.sSearch),
          n("bRegex_" + f, a.bRegex),
          n("bSearchable_" + f, e.bSearchable)),
        i.bSort && n("bSortable_" + f, e.bSortable);
    return (
      i.bFilter && (n("sSearch", l.sSearch), n("bRegex", l.bRegex)),
      i.bSort &&
        (P.each(c, function (t, e) {
          h.order.push({ column: e.col, dir: e.dir }),
            n("iSortCol_" + t, e.col),
            n("sSortDir_" + t, e.dir);
        }),
        n("iSortingCols", c.length)),
      null === (r = Qt.ext.legacy.ajax) ? (t.sAjaxSource ? u : h) : r ? u : h
    );
  }
  function J(t, e) {
    var n = q(t, e),
      a = e.sEcho !== j ? e.sEcho : e.draw,
      r = e.iTotalRecords !== j ? e.iTotalRecords : e.recordsTotal,
      o =
        e.iTotalDisplayRecords !== j
          ? e.iTotalDisplayRecords
          : e.recordsFiltered;
    if (a !== j) {
      if (+a < t.iDraw) return;
      t.iDraw = +a;
    }
    for (
      l(t),
        t._iRecordsTotal = parseInt(r, 10),
        t._iRecordsDisplay = parseInt(o, 10),
        a = 0,
        r = n.length;
      a < r;
      a++
    )
      F(t, n[a]);
    (t.aiDisplay = t.aiDisplayMaster.slice()),
      (t.bAjaxDataGet = !1),
      M(t),
      t._bInitComplete || it(t, e),
      (t.bAjaxDataGet = !0),
      dt(t, !1);
  }
  function q(t, e) {
    var n =
      P.isPlainObject(t.ajax) && t.ajax.dataSrc !== j
        ? t.ajax.dataSrc
        : t.sAjaxDataProp;
    return "data" === n ? e.aaData || e[n] : "" !== n ? O(n)(e) : e;
  }
  function G(n) {
    function t() {
      var t = this.value ? this.value : "";
      t != o.sSearch &&
        ($(n, {
          sSearch: t,
          bRegex: o.bRegex,
          bSmart: o.bSmart,
          bCaseInsensitive: o.bCaseInsensitive,
        }),
        (n._iDisplayStart = 0),
        M(n));
    }
    var e = n.oClasses,
      a = n.sTableId,
      r = n.oLanguage,
      o = n.oPreviousSearch,
      i = n.aanFeatures,
      l = '<input type="search" class="' + e.sFilterInput + '"/>',
      s = (s = r.sSearch).match(/_INPUT_/) ? s.replace("_INPUT_", l) : s + l,
      e = P("<div/>", {
        id: i.f ? null : a + "_filter",
        class: e.sFilter,
      }).append(P("<label/>").append(s)),
      i = null !== n.searchDelay ? n.searchDelay : "ssp" === Wt(n) ? 400 : 0,
      u = P("input", e)
        .val(o.sSearch)
        .attr("placeholder", r.sSearchPlaceholder)
        .on("keyup.DT search.DT input.DT paste.DT cut.DT", i ? ge(t, i) : t)
        .on("mouseup", function () {
          setTimeout(function () {
            t.call(u[0]);
          }, 10);
        })
        .on("keypress.DT", function (t) {
          if (13 == t.keyCode) return !1;
        })
        .attr("aria-controls", a);
    return (
      P(n.nTable).on("search.dt.DT", function (t, e) {
        if (n === e)
          try {
            u[0] !== v.activeElement && u.val(o.sSearch);
          } catch (t) {}
      }),
      e[0]
    );
  }
  function $(t, e, n) {
    function a(t) {
      (r.sSearch = t.sSearch),
        (r.bRegex = t.bRegex),
        (r.bSmart = t.bSmart),
        (r.bCaseInsensitive = t.bCaseInsensitive);
    }
    var r = t.oPreviousSearch,
      o = t.aoPreSearchCols;
    if ((i(t), "ssp" != Wt(t))) {
      for (
        Z(
          t,
          e.sSearch,
          n,
          e.bEscapeRegex !== j ? !e.bEscapeRegex : e.bRegex,
          e.bSmart,
          e.bCaseInsensitive
        ),
          a(e),
          e = 0;
        e < o.length;
        e++
      )
        Y(
          t,
          o[e].sSearch,
          e,
          o[e].bEscapeRegex !== j ? !o[e].bEscapeRegex : o[e].bRegex,
          o[e].bSmart,
          o[e].bCaseInsensitive
        );
      z(t);
    } else a(e);
    (t.bFiltered = !0), Ot(t, null, "search", [t]);
  }
  function z(t) {
    for (
      var e, n, a = Qt.ext.search, r = t.aiDisplay, o = 0, i = a.length;
      o < i;
      o++
    ) {
      for (var l = [], s = 0, u = r.length; s < u; s++)
        (n = r[s]),
          (e = t.aoData[n]),
          a[o](t, e._aFilterData, n, e._aData, s) && l.push(n);
      (r.length = 0), P.merge(r, l);
    }
  }
  function Y(t, e, n, a, r, o) {
    if ("" !== e) {
      for (
        var i = [], l = t.aiDisplay, a = K(e, a, r, o), r = 0;
        r < l.length;
        r++
      )
        (e = t.aoData[l[r]]._aFilterData[n]), a.test(e) && i.push(l[r]);
      t.aiDisplay = i;
    }
  }
  function Z(t, e, n, a, r, o) {
    var i,
      r = K(e, a, r, o),
      l = t.oPreviousSearch.sSearch,
      s = t.aiDisplayMaster,
      o = [];
    if ((0 !== Qt.ext.search.length && (n = !0), (i = Q(t)), e.length <= 0))
      t.aiDisplay = s.slice();
    else {
      for (
        (i ||
          n ||
          a ||
          l.length > e.length ||
          0 !== e.indexOf(l) ||
          t.bSorted) &&
          (t.aiDisplay = s.slice()),
          e = t.aiDisplay,
          n = 0;
        n < e.length;
        n++
      )
        r.test(t.aoData[e[n]]._sFilterRow) && o.push(e[n]);
      t.aiDisplay = o;
    }
  }
  function K(t, e, n, a) {
    return (
      (t = e ? t : fe(t)),
      n &&
        (t =
          "^(?=.*?" +
          P.map(t.match(/"[^"]+"|[^ ]+/g) || [""], function (t) {
            var e;
            return (
              '"' === t.charAt(0) && (t = (e = t.match(/^"(.*)"$/)) ? e[1] : t),
              t.replace('"', "")
            );
          }).join(")(?=.*?") +
          ").*$"),
      RegExp(t, a ? "i" : "")
    );
  }
  function Q(t) {
    for (
      var e,
        n,
        a,
        r,
        o,
        i = t.aoColumns,
        l = Qt.ext.type.search,
        s = !1,
        u = 0,
        c = t.aoData.length;
      u < c;
      u++
    )
      if (!(o = t.aoData[u])._aFilterData) {
        for (a = [], e = 0, n = i.length; e < n; e++)
          (s = i[e]).bSearchable
            ? ((r = R(t, u, e, "filter")),
              l[s.sType] && (r = l[s.sType](r)),
              null === r && (r = ""),
              "string" != typeof r && r.toString && (r = r.toString()))
            : (r = ""),
            r.indexOf &&
              -1 !== r.indexOf("&") &&
              ((de.innerHTML = r), (r = he ? de.textContent : de.innerText)),
            r.replace && (r = r.replace(/[\r\n\u2028]/g, "")),
            a.push(r);
        (o._aFilterData = a), (o._sFilterRow = a.join("  ")), (s = !0);
      }
    return s;
  }
  function tt(t) {
    return {
      search: t.sSearch,
      smart: t.bSmart,
      regex: t.bRegex,
      caseInsensitive: t.bCaseInsensitive,
    };
  }
  function et(t) {
    return {
      sSearch: t.search,
      bSmart: t.smart,
      bRegex: t.regex,
      bCaseInsensitive: t.caseInsensitive,
    };
  }
  function nt(t) {
    var e = t.sTableId,
      n = t.aanFeatures.i,
      a = P("<div/>", { class: t.oClasses.sInfo, id: n ? null : e + "_info" });
    return (
      n ||
        (t.aoDrawCallback.push({ fn: at, sName: "information" }),
        a.attr("role", "status").attr("aria-live", "polite"),
        P(t.nTable).attr("aria-describedby", e + "_info")),
      a[0]
    );
  }
  function at(t) {
    var e,
      n,
      a,
      r,
      o,
      i,
      l = t.aanFeatures.i;
    0 !== l.length &&
      ((e = t.oLanguage),
      (n = t._iDisplayStart + 1),
      (a = t.fnDisplayEnd()),
      (r = t.fnRecordsTotal()),
      (i = (o = t.fnRecordsDisplay()) ? e.sInfo : e.sInfoEmpty),
      o !== r && (i += " " + e.sInfoFiltered),
      (i = rt(t, (i += e.sInfoPostFix))),
      null !== (e = e.fnInfoCallback) &&
        (i = e.call(t.oInstance, t, n, a, r, o, i)),
      P(l).html(i));
  }
  function rt(t, e) {
    var n = t.fnFormatNumber,
      a = t._iDisplayStart + 1,
      r = t._iDisplayLength,
      o = t.fnRecordsDisplay(),
      i = -1 === r;
    return e
      .replace(/_START_/g, n.call(t, a))
      .replace(/_END_/g, n.call(t, t.fnDisplayEnd()))
      .replace(/_MAX_/g, n.call(t, t.fnRecordsTotal()))
      .replace(/_TOTAL_/g, n.call(t, o))
      .replace(/_PAGE_/g, n.call(t, i ? 1 : Math.ceil(a / r)))
      .replace(/_PAGES_/g, n.call(t, i ? 1 : Math.ceil(o / r)));
  }
  function ot(n) {
    var a,
      t,
      r = n.iInitDisplayStart,
      e = n.aoColumns,
      o = n.oFeatures,
      i = n.bDeferLoading;
    if (n.bInitialised) {
      for (
        E(n),
          d(n),
          k(n, n.aoHeader),
          k(n, n.aoFooter),
          dt(n, !0),
          o.bAutoWidth && bt(n),
          a = 0,
          o = e.length;
        a < o;
        a++
      )
        (t = e[a]).sWidth && (t.nTh.style.width = Dt(t.sWidth));
      Ot(n, null, "preInit", [n]),
        W(n),
        ("ssp" == (e = Wt(n)) && !i) ||
          ("ajax" == e
            ? V(n, [], function (t) {
                var e = q(n, t);
                for (a = 0; a < e.length; a++) F(n, e[a]);
                (n.iInitDisplayStart = r), W(n), dt(n, !1), it(n, t);
              })
            : (dt(n, !1), it(n)));
    } else
      setTimeout(function () {
        ot(n);
      }, 200);
  }
  function it(t, e) {
    (t._bInitComplete = !0),
      (e || t.oInit.aaData) && N(t),
      Ot(t, null, "plugin-init", [t, e]),
      Ot(t, "aoInitComplete", "init", [t, e]);
  }
  function lt(t, e) {
    var n = parseInt(e, 10);
    (t._iDisplayLength = n), kt(t), Ot(t, null, "length", [t, n]);
  }
  function st(a) {
    for (
      var t = a.oClasses,
        e = a.sTableId,
        n = a.aLengthMenu,
        r = (o = P.isArray(n[0])) ? n[0] : n,
        n = o ? n[1] : n,
        o = P("<select/>", {
          name: e + "_length",
          "aria-controls": e,
          class: t.sLengthSelect,
        }),
        i = 0,
        l = r.length;
      i < l;
      i++
    )
      o[0][i] = new Option(
        "number" == typeof n[i] ? a.fnFormatNumber(n[i]) : n[i],
        r[i]
      );
    var s = P("<div><label/></div>").addClass(t.sLength);
    return (
      a.aanFeatures.l || (s[0].id = e + "_length"),
      s
        .children()
        .append(a.oLanguage.sLengthMenu.replace("_MENU_", o[0].outerHTML)),
      P("select", s)
        .val(a._iDisplayLength)
        .on("change.DT", function () {
          lt(a, P(this).val()), M(a);
        }),
      P(a.nTable).on("length.dt.DT", function (t, e, n) {
        a === e && P("select", s).val(n);
      }),
      s[0]
    );
  }
  function ut(t) {
    function i(t) {
      M(t);
    }
    var e = t.sPaginationType,
      l = Qt.ext.pager[e],
      s = "function" == typeof l,
      e = P("<div/>").addClass(t.oClasses.sPaging + e)[0],
      u = t.aanFeatures;
    return (
      s || l.fnInit(t, e, i),
      u.p ||
        ((e.id = t.sTableId + "_paginate"),
        t.aoDrawCallback.push({
          fn: function (t) {
            if (s)
              for (
                var e = t._iDisplayStart,
                  n = t._iDisplayLength,
                  a = t.fnRecordsDisplay(),
                  e = (r = -1 === n) ? 0 : Math.ceil(e / n),
                  n = r ? 1 : Math.ceil(a / n),
                  a = l(e, n),
                  r = 0,
                  o = u.p.length;
                r < o;
                r++
              )
                Mt(t, "pageButton")(t, u.p[r], r, a, e, n);
            else l.fnUpdate(t, i);
          },
          sName: "pagination",
        })),
      e
    );
  }
  function ct(t, e, n) {
    var a = t._iDisplayStart,
      r = t._iDisplayLength,
      o = t.fnRecordsDisplay();
    return (
      0 === o || -1 === r
        ? (a = 0)
        : "number" == typeof e
        ? o < (a = e * r) && (a = 0)
        : "first" == e
        ? (a = 0)
        : "previous" == e
        ? (a = 0 <= r ? a - r : 0) < 0 && (a = 0)
        : "next" == e
        ? a + r < o && (a += r)
        : "last" == e
        ? (a = Math.floor((o - 1) / r) * r)
        : Rt(t, 0, "Unknown paging action: " + e, 5),
      (e = t._iDisplayStart !== a),
      (t._iDisplayStart = a),
      e && (Ot(t, null, "page", [t]), n && M(t)),
      e
    );
  }
  function ft(t) {
    return P("<div/>", {
      id: t.aanFeatures.r ? null : t.sTableId + "_processing",
      class: t.oClasses.sProcessing,
    })
      .html(t.oLanguage.sProcessing)
      .insertBefore(t.nTable)[0];
  }
  function dt(t, e) {
    t.oFeatures.bProcessing &&
      P(t.aanFeatures.r).css("display", e ? "block" : "none"),
      Ot(t, null, "processing", [t, e]);
  }
  function ht(t) {
    var e = P(t.nTable);
    e.attr("role", "grid");
    var n = t.oScroll;
    if ("" === n.sX && "" === n.sY) return t.nTable;
    var a = n.sX,
      r = n.sY,
      o = t.oClasses,
      i = e.children("caption"),
      l = i.length ? i[0]._captionSide : null,
      s = P(e[0].cloneNode(!1)),
      u = P(e[0].cloneNode(!1)),
      c = e.children("tfoot");
    c.length || (c = null),
      (s = P("<div/>", { class: o.sScrollWrapper })
        .append(
          P("<div/>", { class: o.sScrollHead })
            .css({
              overflow: "hidden",
              position: "relative",
              border: 0,
              width: a ? (a ? Dt(a) : null) : "100%",
            })
            .append(
              P("<div/>", { class: o.sScrollHeadInner })
                .css({
                  "box-sizing": "content-box",
                  width: n.sXInner || "100%",
                })
                .append(
                  s
                    .removeAttr("id")
                    .css("margin-left", 0)
                    .append("top" === l ? i : null)
                    .append(e.children("thead"))
                )
            )
        )
        .append(
          P("<div/>", { class: o.sScrollBody })
            .css({
              position: "relative",
              overflow: "auto",
              width: a ? Dt(a) : null,
            })
            .append(e)
        )),
      c &&
        s.append(
          P("<div/>", { class: o.sScrollFoot })
            .css({
              overflow: "hidden",
              border: 0,
              width: a ? (a ? Dt(a) : null) : "100%",
            })
            .append(
              P("<div/>", { class: o.sScrollFootInner }).append(
                u
                  .removeAttr("id")
                  .css("margin-left", 0)
                  .append("bottom" === l ? i : null)
                  .append(e.children("tfoot"))
              )
            )
        );
    var f = (e = s.children())[0],
      o = e[1],
      d = c ? e[2] : null;
    return (
      a &&
        P(o).on("scroll.DT", function () {
          var t = this.scrollLeft;
          (f.scrollLeft = t), c && (d.scrollLeft = t);
        }),
      P(o).css("max-height", r),
      n.bCollapse || P(o).css("height", r),
      (t.nScrollHead = f),
      (t.nScrollBody = o),
      (t.nScrollFoot = d),
      t.aoDrawCallback.push({ fn: pt, sName: "scrolling" }),
      s[0]
    );
  }
  function pt(n) {
    function t(t) {
      ((t = t.style).paddingTop = "0"),
        (t.paddingBottom = "0"),
        (t.borderTopWidth = "0"),
        (t.borderBottomWidth = "0"),
        (t.height = 0);
    }
    var e,
      a,
      r,
      o,
      i = (u = n.oScroll).sX,
      l = u.sXInner,
      s = u.sY,
      u = u.iBarWidth,
      c = P(n.nScrollHead),
      f = c[0].style,
      d = (p = c.children("div"))[0].style,
      h = p.children("table"),
      p = n.nScrollBody,
      g = P(p),
      b = p.style,
      m = P(n.nScrollFoot).children("div"),
      S = m.children("table"),
      v = P(n.nTHead),
      D = P(n.nTable),
      y = D[0],
      _ = y.style,
      T = n.nTFoot ? P(n.nTFoot) : null,
      C = n.oBrowser,
      w = C.bScrollOversize,
      x = ie(n.aoColumns, "nTh"),
      I = [],
      A = [],
      F = [],
      L = [],
      R = p.scrollHeight > p.clientHeight;
    n.scrollBarVis !== R && n.scrollBarVis !== j
      ? ((n.scrollBarVis = R), N(n))
      : ((n.scrollBarVis = R),
        D.children("thead, tfoot").remove(),
        T &&
          ((a = T.clone().prependTo(D)),
          (e = T.find("tr")),
          (a = a.find("tr"))),
        (r = v.clone().prependTo(D)),
        (v = v.find("tr")),
        (R = r.find("tr")),
        r.find("th, td").removeAttr("tabindex"),
        i || ((b.width = "100%"), (c[0].style.width = "100%")),
        P.each(U(n, r), function (t, e) {
          (o = H(n, t)), (e.style.width = n.aoColumns[o].sWidth);
        }),
        T &&
          gt(function (t) {
            t.style.width = "";
          }, a),
        (c = D.outerWidth()),
        "" === i
          ? ((_.width = "100%"),
            w &&
              (D.find("tbody").height() > p.offsetHeight ||
                "scroll" == g.css("overflow-y")) &&
              (_.width = Dt(D.outerWidth() - u)),
            (c = D.outerWidth()))
          : "" !== l && ((_.width = Dt(l)), (c = D.outerWidth())),
        gt(t, R),
        gt(function (t) {
          F.push(t.innerHTML), I.push(Dt(P(t).css("width")));
        }, R),
        gt(function (t, e) {
          -1 !== P.inArray(t, x) && (t.style.width = I[e]);
        }, v),
        P(R).height(0),
        T &&
          (gt(t, a),
          gt(function (t) {
            L.push(t.innerHTML), A.push(Dt(P(t).css("width")));
          }, a),
          gt(function (t, e) {
            t.style.width = A[e];
          }, e),
          P(a).height(0)),
        gt(function (t, e) {
          (t.innerHTML = '<div class="dataTables_sizing">' + F[e] + "</div>"),
            (t.childNodes[0].style.height = "0"),
            (t.childNodes[0].style.overflow = "hidden"),
            (t.style.width = I[e]);
        }, R),
        T &&
          gt(function (t, e) {
            (t.innerHTML = '<div class="dataTables_sizing">' + L[e] + "</div>"),
              (t.childNodes[0].style.height = "0"),
              (t.childNodes[0].style.overflow = "hidden"),
              (t.style.width = A[e]);
          }, a),
        D.outerWidth() < c
          ? ((e =
              p.scrollHeight > p.offsetHeight || "scroll" == g.css("overflow-y")
                ? c + u
                : c),
            w &&
              (p.scrollHeight > p.offsetHeight ||
                "scroll" == g.css("overflow-y")) &&
              (_.width = Dt(e - u)),
            ("" !== i && "" === l) ||
              Rt(n, 1, "Possible column misalignment", 6))
          : (e = "100%"),
        (b.width = Dt(e)),
        (f.width = Dt(e)),
        T && (n.nScrollFoot.style.width = Dt(e)),
        !s && w && (b.height = Dt(y.offsetHeight + u)),
        (i = D.outerWidth()),
        (h[0].style.width = Dt(i)),
        (d.width = Dt(i)),
        (l = D.height() > p.clientHeight || "scroll" == g.css("overflow-y")),
        (d[(s = "padding" + (C.bScrollbarLeft ? "Left" : "Right"))] = l
          ? u + "px"
          : "0px"),
        T &&
          ((S[0].style.width = Dt(i)),
          (m[0].style.width = Dt(i)),
          (m[0].style[s] = l ? u + "px" : "0px")),
        D.children("colgroup").insertBefore(D.children("thead")),
        g.trigger("scroll"),
        (!n.bSorted && !n.bFiltered) || n._drawHold || (p.scrollTop = 0));
  }
  function gt(t, e, n) {
    for (var a, r, o = 0, i = 0, l = e.length; i < l; ) {
      for (a = e[i].firstChild, r = n ? n[i].firstChild : null; a; )
        1 === a.nodeType && (n ? t(a, r, o) : t(a, o), o++),
          (a = a.nextSibling),
          (r = n ? r.nextSibling : null);
      i++;
    }
  }
  function bt(t) {
    var e,
      n,
      a = t.nTable,
      r = t.aoColumns,
      o = (g = t.oScroll).sY,
      i = g.sX,
      l = g.sXInner,
      s = r.length,
      u = I(t, "bVisible"),
      c = P("th", t.nTHead),
      f = a.getAttribute("width"),
      d = a.parentNode,
      h = !1,
      p = t.oBrowser,
      g = p.bScrollOversize;
    for (
      (e = a.style.width) && -1 !== e.indexOf("%") && (f = e), e = 0;
      e < u.length;
      e++
    )
      null !== (n = r[u[e]]).sWidth &&
        ((n.sWidth = mt(n.sWidthOrig, d)), (h = !0));
    if (g || (!h && !i && !o && s == S(t) && s == c.length))
      for (e = 0; e < s; e++)
        null !== (u = H(t, e)) && (r[u].sWidth = Dt(c.eq(e).width()));
    else {
      (s = P(a).clone().css("visibility", "hidden").removeAttr("id"))
        .find("tbody tr")
        .remove();
      var b = P("<tr/>").appendTo(s.find("tbody"));
      for (
        s.find("thead, tfoot").remove(),
          s.append(P(t.nTHead).clone()).append(P(t.nTFoot).clone()),
          s.find("tfoot th, tfoot td").css("width", ""),
          c = U(t, s.find("thead")[0]),
          e = 0;
        e < u.length;
        e++
      )
        (n = r[u[e]]),
          (c[e].style.width =
            null !== n.sWidthOrig && "" !== n.sWidthOrig
              ? Dt(n.sWidthOrig)
              : ""),
          n.sWidthOrig &&
            i &&
            P(c[e]).append(
              P("<div/>").css({
                width: n.sWidthOrig,
                margin: 0,
                padding: 0,
                border: 0,
                height: 1,
              })
            );
      if (t.aoData.length)
        for (e = 0; e < u.length; e++)
          (n = r[(h = u[e])]),
            P(St(t, h)).clone(!1).append(n.sContentPadding).appendTo(b);
      for (
        P("[name]", s).removeAttr("name"),
          n = P("<div/>")
            .css(
              i || o
                ? {
                    position: "absolute",
                    top: 0,
                    left: 0,
                    height: 1,
                    right: 0,
                    overflow: "hidden",
                  }
                : {}
            )
            .append(s)
            .appendTo(d),
          i && l
            ? s.width(l)
            : i
            ? (s.css("width", "auto"),
              s.removeAttr("width"),
              s.width() < d.clientWidth && f && s.width(d.clientWidth))
            : o
            ? s.width(d.clientWidth)
            : f && s.width(f),
          e = o = 0;
        e < u.length;
        e++
      )
        (l = (d = P(c[e])).outerWidth() - d.width()),
          (o += d =
            p.bBounding
              ? Math.ceil(c[e].getBoundingClientRect().width)
              : d.outerWidth()),
          (r[u[e]].sWidth = Dt(d - l));
      (a.style.width = Dt(o)), n.remove();
    }
    f && (a.style.width = Dt(f)),
      (!f && !i) ||
        t._reszEvt ||
        ((a = function () {
          P(m).on(
            "resize.DT-" + t.sInstance,
            ge(function () {
              N(t);
            })
          );
        }),
        g ? setTimeout(a, 1e3) : a(),
        (t._reszEvt = !0));
  }
  function mt(t, e) {
    if (!t) return 0;
    var n = P("<div/>")
        .css("width", Dt(t))
        .appendTo(e || v.body),
      a = n[0].offsetWidth;
    return n.remove(), a;
  }
  function St(t, e) {
    var n = vt(t, e);
    if (n < 0) return null;
    var a = t.aoData[n];
    return a.nTr ? a.anCells[e] : P("<td/>").html(R(t, n, e, "display"))[0];
  }
  function vt(t, e) {
    for (var n, a = -1, r = -1, o = 0, i = t.aoData.length; o < i; o++)
      (n = (n = (n = R(t, o, e, "display") + "").replace(pe, "")).replace(
        /&nbsp;/g,
        " "
      )).length > a && ((a = n.length), (r = o));
    return r;
  }
  function Dt(t) {
    return null === t
      ? "0px"
      : "number" == typeof t
      ? t < 0
        ? "0px"
        : t + "px"
      : t.match(/\d$/)
      ? t + "px"
      : t;
  }
  function yt(t) {
    var e,
      n,
      a,
      r = [],
      o = t.aoColumns,
      i = t.aaSortingFixed,
      l = P.isPlainObject(i),
      s = [],
      u = function (t) {
        t.length && !P.isArray(t[0]) ? s.push(t) : P.merge(s, t);
      };
    for (
      P.isArray(i) && u(i),
        l && i.pre && u(i.pre),
        u(t.aaSorting),
        l && i.post && u(i.post),
        t = 0;
      t < s.length;
      t++
    )
      for (i = 0, l = (u = o[(a = s[t][0])].aDataSort).length; i < l; i++)
        (n = o[(e = u[i])].sType || "string"),
          s[t]._idx === j && (s[t]._idx = P.inArray(s[t][1], o[e].asSorting)),
          r.push({
            src: a,
            col: e,
            dir: s[t][1],
            index: s[t]._idx,
            type: n,
            formatter: Qt.ext.type.order[n + "-pre"],
          });
    return r;
  }
  function _t(t) {
    var e,
      n,
      a,
      u,
      c = [],
      f = Qt.ext.type.order,
      d = t.aoData,
      r = 0,
      o = t.aiDisplayMaster;
    for (i(t), e = 0, n = (u = yt(t)).length; e < n; e++)
      (a = u[e]).formatter && r++, It(t, a.col);
    if ("ssp" != Wt(t) && 0 !== u.length) {
      for (e = 0, n = o.length; e < n; e++) c[o[e]] = e;
      r === u.length
        ? o.sort(function (t, e) {
            for (
              var n,
                a,
                r,
                o = u.length,
                i = d[t]._aSortData,
                l = d[e]._aSortData,
                s = 0;
              s < o;
              s++
            )
              if (
                0 !==
                (n =
                  (n = i[(r = u[s]).col]) < (a = l[r.col]) ? -1 : a < n ? 1 : 0)
              )
                return "asc" === r.dir ? n : -n;
            return (n = c[t]) < (a = c[e]) ? -1 : a < n ? 1 : 0;
          })
        : o.sort(function (t, e) {
            for (
              var n,
                a,
                r,
                o = u.length,
                i = d[t]._aSortData,
                l = d[e]._aSortData,
                s = 0;
              s < o;
              s++
            )
              if (
                ((n = i[(r = u[s]).col]),
                (a = l[r.col]),
                0 !==
                  (n = (r = f[r.type + "-" + r.dir] || f["string-" + r.dir])(
                    n,
                    a
                  )))
              )
                return n;
            return (n = c[t]) < (a = c[e]) ? -1 : a < n ? 1 : 0;
          });
    }
    t.bSorted = !0;
  }
  function Tt(t) {
    for (
      var e,
        n = t.aoColumns,
        a = yt(t),
        t = t.oLanguage.oAria,
        r = 0,
        o = n.length;
      r < o;
      r++
    ) {
      var i = (e = n[r]).asSorting,
        l = e.sTitle.replace(/<.*?>/g, ""),
        s = e.nTh;
      s.removeAttribute("aria-sort"),
        e.bSortable &&
          (l +=
            "asc" ===
            (e =
              0 < a.length && a[0].col == r
                ? (s.setAttribute(
                    "aria-sort",
                    "asc" == a[0].dir ? "ascending" : "descending"
                  ),
                  i[a[0].index + 1] || i[0])
                : i[0])
              ? t.sSortAscending
              : t.sSortDescending),
        s.setAttribute("aria-label", l);
    }
  }
  function Ct(t, e, n, a) {
    function r(t, e) {
      var n = t._idx;
      return (
        n === j && (n = P.inArray(t[1], i)),
        n + 1 < i.length ? n + 1 : e ? null : 0
      );
    }
    var o = t.aaSorting,
      i = t.aoColumns[e].asSorting;
    "number" == typeof o[0] && (o = t.aaSorting = [o]),
      n && t.oFeatures.bSortMulti
        ? -1 !== (n = P.inArray(e, ie(o, "0")))
          ? (null === (e = r(o[n], !0)) && 1 === o.length && (e = 0),
            null === e ? o.splice(n, 1) : ((o[n][1] = i[e]), (o[n]._idx = e)))
          : (o.push([e, i[0], 0]), (o[o.length - 1]._idx = 0))
        : o.length && o[0][0] == e
        ? ((e = r(o[0])), (o.length = 1), (o[0][1] = i[e]), (o[0]._idx = e))
        : ((o.length = 0), o.push([e, i[0]]), (o[0]._idx = 0)),
      W(t),
      "function" == typeof a && a(t);
  }
  function wt(e, t, n, a) {
    var r = e.aoColumns[n];
    Nt(t, {}, function (t) {
      !1 !== r.bSortable &&
        (e.oFeatures.bProcessing
          ? (dt(e, !0),
            setTimeout(function () {
              Ct(e, n, t.shiftKey, a), "ssp" !== Wt(e) && dt(e, !1);
            }, 0))
          : Ct(e, n, t.shiftKey, a));
    });
  }
  function xt(t) {
    var e,
      n,
      a = t.aLastSort,
      r = t.oClasses.sSortColumn,
      o = yt(t),
      i = t.oFeatures;
    if (i.bSort && i.bSortClasses) {
      for (i = 0, e = a.length; i < e; i++)
        (n = a[i].src),
          P(ie(t.aoData, "anCells", n)).removeClass(r + (i < 2 ? i + 1 : 3));
      for (i = 0, e = o.length; i < e; i++)
        (n = o[i].src),
          P(ie(t.aoData, "anCells", n)).addClass(r + (i < 2 ? i + 1 : 3));
    }
    t.aLastSort = o;
  }
  function It(t, e) {
    var n,
      a = t.aoColumns[e],
      r = Qt.ext.order[a.sSortDataType];
    r && (n = r.call(t.oInstance, t, e, u(t, e)));
    for (
      var o,
        i = Qt.ext.type.order[a.sType + "-pre"],
        l = 0,
        s = t.aoData.length;
      l < s;
      l++
    )
      (a = t.aoData[l])._aSortData || (a._aSortData = []),
        (a._aSortData[e] && !r) ||
          ((o = r ? n[l] : R(t, l, e, "sort")),
          (a._aSortData[e] = i ? i(o) : o));
  }
  function At(n) {
    var t;
    n.oFeatures.bStateSave &&
      !n.bDestroying &&
      ((t = {
        time: +new Date(),
        start: n._iDisplayStart,
        length: n._iDisplayLength,
        order: P.extend(!0, [], n.aaSorting),
        search: tt(n.oPreviousSearch),
        columns: P.map(n.aoColumns, function (t, e) {
          return { visible: t.bVisible, search: tt(n.aoPreSearchCols[e]) };
        }),
      }),
      Ot(n, "aoStateSaveParams", "stateSaveParams", [n, t]),
      (n.oSavedState = t),
      n.fnStateSaveCallback.call(n.oInstance, n, t));
  }
  function Ft(n, t, a) {
    var r,
      o,
      e,
      i = n.aoColumns,
      t = function (t) {
        if (t && t.time) {
          var e = Ot(n, "aoStateLoadParams", "stateLoadParams", [n, t]);
          if (
            -1 === P.inArray(!1, e) &&
            !(
              (0 < (e = n.iStateDuration) && t.time < new Date() - 1e3 * e) ||
              (t.columns && i.length !== t.columns.length)
            )
          ) {
            if (
              ((n.oLoadedState = P.extend(!0, {}, t)),
              t.start !== j &&
                ((n._iDisplayStart = t.start), (n.iInitDisplayStart = t.start)),
              t.length !== j && (n._iDisplayLength = t.length),
              t.order !== j &&
                ((n.aaSorting = []),
                P.each(t.order, function (t, e) {
                  n.aaSorting.push(e[0] >= i.length ? [0, e[1]] : e);
                })),
              t.search !== j && P.extend(n.oPreviousSearch, et(t.search)),
              t.columns)
            )
              for (r = 0, o = t.columns.length; r < o; r++)
                (e = t.columns[r]).visible !== j && (i[r].bVisible = e.visible),
                  e.search !== j &&
                    P.extend(n.aoPreSearchCols[r], et(e.search));
            Ot(n, "aoStateLoaded", "stateLoaded", [n, t]);
          }
        }
        a();
      };
    n.oFeatures.bStateSave
      ? (e = n.fnStateLoadCallback.call(n.oInstance, n, t)) !== j && t(e)
      : a();
  }
  function Lt(t) {
    var e = Qt.settings;
    return -1 !== (t = P.inArray(t, ie(e, "nTable"))) ? e[t] : null;
  }
  function Rt(t, e, n, a) {
    if (
      ((n =
        "DataTables warning: " +
        (t ? "table id=" + t.sTableId + " - " : "") +
        n),
      a &&
        (n +=
          ". For more information about this error, please see http://datatables.net/tn/" +
          a),
      e)
    )
      m.console && console.log && console.log(n);
    else if (
      ((e = (e = Qt.ext).sErrMode || e.errMode),
      t && Ot(t, null, "error", [t, a, n]),
      "alert" == e)
    )
      alert(n);
    else {
      if ("throw" == e) throw Error(n);
      "function" == typeof e && e(t, a, n);
    }
  }
  function Pt(n, a, t, e) {
    P.isArray(t)
      ? P.each(t, function (t, e) {
          P.isArray(e) ? Pt(n, a, e[0], e[1]) : Pt(n, a, e);
        })
      : (e === j && (e = t), a[t] !== j && (n[e] = a[t]));
  }
  function jt(t, e, n) {
    var a, r;
    for (r in e)
      e.hasOwnProperty(r) &&
        ((a = e[r]),
        P.isPlainObject(a)
          ? (P.isPlainObject(t[r]) || (t[r] = {}), P.extend(!0, t[r], a))
          : (t[r] =
              n && "data" !== r && "aaData" !== r && P.isArray(a)
                ? a.slice()
                : a));
    return t;
  }
  function Nt(e, t, n) {
    P(e)
      .on("click.DT", t, function (t) {
        P(e).trigger("blur"), n(t);
      })
      .on("keypress.DT", t, function (t) {
        13 === t.which && (t.preventDefault(), n(t));
      })
      .on("selectstart.DT", function () {
        return !1;
      });
  }
  function Ht(t, e, n, a) {
    n && t[e].push({ fn: n, sName: a });
  }
  function Ot(e, t, n, a) {
    var r = [];
    return (
      t &&
        (r = P.map(e[t].slice().reverse(), function (t) {
          return t.fn.apply(e.oInstance, a);
        })),
      null !== n &&
        ((t = P.Event(n + ".dt")), P(e.nTable).trigger(t, a), r.push(t.result)),
      r
    );
  }
  function kt(t) {
    var e = t._iDisplayStart,
      n = t.fnDisplayEnd(),
      a = t._iDisplayLength;
    n <= e && (e = n - a),
      (e -= e % a),
      (-1 === a || e < 0) && (e = 0),
      (t._iDisplayStart = e);
  }
  function Mt(t, e) {
    var n = t.renderer,
      a = Qt.ext.renderer[e];
    return P.isPlainObject(n) && n[e]
      ? a[n[e]] || a._
      : ("string" == typeof n && a[n]) || a._;
  }
  function Wt(t) {
    return t.oFeatures.bServerSide
      ? "ssp"
      : t.ajax || t.sAjaxSource
      ? "ajax"
      : "dom";
  }
  function Et(t, e) {
    var n = [],
      n = Fe.numbers_length,
      a = Math.floor(n / 2);
    return (
      e <= n
        ? (n = $t(0, e))
        : t <= a
        ? ((n = $t(0, n - 2)).push("ellipsis"), n.push(e - 1))
        : (e - 1 - a <= t
            ? (n = $t(e - (n - 2), e))
            : ((n = $t(t - a + 2, t + a - 1)).push("ellipsis"), n.push(e - 1)),
          n.splice(0, 0, "ellipsis"),
          n.splice(0, 0, 0)),
      (n.DT_el = "span"),
      n
    );
  }
  function Bt(n) {
    P.each(
      {
        num: function (t) {
          return Le(t, n);
        },
        "num-fmt": function (t) {
          return Le(t, n, oe);
        },
        "html-num": function (t) {
          return Le(t, n, ne);
        },
        "html-num-fmt": function (t) {
          return Le(t, n, ne, oe);
        },
      },
      function (t, e) {
        (Yt.type.order[t + n + "-pre"] = e),
          t.match(/^html\-/) && (Yt.type.search[t + n] = Yt.type.search.html);
      }
    );
  }
  function e(e) {
    return function () {
      var t = [Lt(this[Qt.ext.iApiIndex])].concat(
        Array.prototype.slice.call(arguments)
      );
      return Qt.ext.internal[e].apply(this, t);
    };
  }
  function Ut(t) {
    return !t || !0 === t || "-" === t;
  }
  function Vt(t) {
    var e = parseInt(t, 10);
    return !isNaN(e) && isFinite(t) ? e : null;
  }
  function Xt(t, e) {
    return (
      te[e] || (te[e] = RegExp(fe(e), "g")),
      "string" == typeof t && "." !== e
        ? t.replace(/\./g, "").replace(te[e], ".")
        : t
    );
  }
  function Jt(t, e, n) {
    var a = "string" == typeof t;
    return (
      !!Ut(t) ||
      (e && a && (t = Xt(t, e)),
      n && a && (t = t.replace(oe, "")),
      !isNaN(parseFloat(t)) && isFinite(t))
    );
  }
  function qt(t, e, n) {
    return (
      !!Ut(t) ||
      ((Ut(t) || "string" == typeof t) && !!Jt(t.replace(ne, ""), e, n)) ||
      null
    );
  }
  function Gt(t, e, n, a) {
    var r = [],
      o = 0,
      i = e.length;
    if (a !== j) for (; o < i; o++) t[e[o]][n] && r.push(t[e[o]][n][a]);
    else for (; o < i; o++) r.push(t[e[o]][n]);
    return r;
  }
  function $t(t, e) {
    var n,
      a = [];
    e === j ? ((e = 0), (n = t)) : ((n = e), (e = t));
    for (var r = e; r < n; r++) a.push(r);
    return a;
  }
  function zt(t) {
    for (var e = [], n = 0, a = t.length; n < a; n++) t[n] && e.push(t[n]);
    return e;
  }
  var Yt,
    Zt,
    Kt,
    Qt = function (b) {
      (this.$ = function (t, e) {
        return this.api(!0).$(t, e);
      }),
        (this._ = function (t, e) {
          return this.api(!0).rows(t, e).data();
        }),
        (this.api = function (t) {
          return new Se(t ? Lt(this[Yt.iApiIndex]) : this);
        }),
        (this.fnAddData = function (t, e) {
          var n = this.api(!0),
            a =
              P.isArray(t) && (P.isArray(t[0]) || P.isPlainObject(t[0]))
                ? n.rows.add(t)
                : n.row.add(t);
          return (e !== j && !e) || n.draw(), a.flatten().toArray();
        }),
        (this.fnAdjustColumnSizing = function (t) {
          var e = this.api(!0).columns.adjust(),
            n = e.settings()[0],
            a = n.oScroll;
          t === j || t ? e.draw(!1) : ("" === a.sX && "" === a.sY) || pt(n);
        }),
        (this.fnClearTable = function (t) {
          var e = this.api(!0).clear();
          (t !== j && !t) || e.draw();
        }),
        (this.fnClose = function (t) {
          this.api(!0).row(t).child.hide();
        }),
        (this.fnDeleteRow = function (t, e, n) {
          var a = this.api(!0),
            r = (t = a.rows(t)).settings()[0],
            o = r.aoData[t[0][0]];
          return (
            t.remove(), e && e.call(this, r, o), (n !== j && !n) || a.draw(), o
          );
        }),
        (this.fnDestroy = function (t) {
          this.api(!0).destroy(t);
        }),
        (this.fnDraw = function (t) {
          this.api(!0).draw(t);
        }),
        (this.fnFilter = function (t, e, n, a, r, o) {
          (r = this.api(!0)),
            null === e || e === j
              ? r.search(t, n, a, o)
              : r.column(e).search(t, n, a, o),
            r.draw();
        }),
        (this.fnGetData = function (t, e) {
          var n = this.api(!0);
          if (t === j) return n.data().toArray();
          var a = t.nodeName ? t.nodeName.toLowerCase() : "";
          return e !== j || "td" == a || "th" == a
            ? n.cell(t, e).data()
            : n.row(t).data() || null;
        }),
        (this.fnGetNodes = function (t) {
          var e = this.api(!0);
          return t !== j
            ? e.row(t).node()
            : e.rows().nodes().flatten().toArray();
        }),
        (this.fnGetPosition = function (t) {
          var e = this.api(!0),
            n = t.nodeName.toUpperCase();
          return "TR" == n
            ? e.row(t).index()
            : "TD" == n || "TH" == n
            ? [(t = e.cell(t).index()).row, t.columnVisible, t.column]
            : null;
        }),
        (this.fnIsOpen = function (t) {
          return this.api(!0).row(t).child.isShown();
        }),
        (this.fnOpen = function (t, e, n) {
          return this.api(!0).row(t).child(e, n).show().child()[0];
        }),
        (this.fnPageChange = function (t, e) {
          var n = this.api(!0).page(t);
          (e !== j && !e) || n.draw(!1);
        }),
        (this.fnSetColumnVis = function (t, e, n) {
          (t = this.api(!0).column(t).visible(e)),
            (n !== j && !n) || t.columns.adjust().draw();
        }),
        (this.fnSettings = function () {
          return Lt(this[Yt.iApiIndex]);
        }),
        (this.fnSort = function (t) {
          this.api(!0).order(t).draw();
        }),
        (this.fnSortListener = function (t, e, n) {
          this.api(!0).order.listener(t, e, n);
        }),
        (this.fnUpdate = function (t, e, n, a, r) {
          var o = this.api(!0);
          return (
            n === j || null === n ? o.row(e).data(t) : o.cell(e, n).data(t),
            (r !== j && !r) || o.columns.adjust(),
            (a !== j && !a) || o.draw(),
            0
          );
        }),
        (this.fnVersionCheck = Yt.fnVersionCheck);
      var m = this,
        S = b === j,
        v = this.length;
      for (var t in (S && (b = {}),
      (this.oApi = this.internal = Yt.internal),
      Qt.ext.internal))
        t && (this[t] = e(t));
      return (
        this.each(function () {
          var t = {},
            n = 1 < v ? jt(t, b, !0) : b,
            a = 0,
            t = this.getAttribute("id"),
            r = !1,
            e = Qt.defaults,
            o = P(this);
          if ("table" != this.nodeName.toLowerCase())
            Rt(
              null,
              0,
              "Non-table node initialisation (" + this.nodeName + ")",
              2
            );
          else {
            _(e),
              T(e.column),
              D(e, e, !0),
              D(e.column, e.column, !0),
              D(e, P.extend(n, o.data()), !0);
            for (var i = Qt.settings, a = 0, l = i.length; a < l; a++) {
              var s = i[a];
              if (
                s.nTable == this ||
                (s.nTHead && s.nTHead.parentNode == this) ||
                (s.nTFoot && s.nTFoot.parentNode == this)
              ) {
                var u = n.bRetrieve !== j ? n.bRetrieve : e.bRetrieve;
                if (S || u) return s.oInstance;
                if (n.bDestroy !== j ? n.bDestroy : e.bDestroy) {
                  s.oInstance.fnDestroy();
                  break;
                }
                return void Rt(s, 0, "Cannot reinitialise DataTable", 3);
              }
              if (s.sTableId == this.id) {
                i.splice(a, 1);
                break;
              }
            }
            (null !== t && "" !== t) ||
              (this.id = t = "DataTables_Table_" + Qt.ext._unique++);
            var c = P.extend(!0, {}, Qt.models.oSettings, {
              sDestroyWidth: o[0].style.width,
              sInstance: t,
              sTableId: t,
            });
            (c.nTable = this),
              (c.oApi = m.internal),
              (c.oInit = n),
              i.push(c),
              (c.oInstance = 1 === m.length ? m : o.dataTable()),
              _(n),
              y(n.oLanguage),
              n.aLengthMenu &&
                !n.iDisplayLength &&
                (n.iDisplayLength = P.isArray(n.aLengthMenu[0])
                  ? n.aLengthMenu[0][0]
                  : n.aLengthMenu[0]),
              (n = jt(P.extend(!0, {}, e), n)),
              Pt(
                c.oFeatures,
                n,
                "bPaginate bLengthChange bFilter bSort bSortMulti bInfo bProcessing bAutoWidth bSortClasses bServerSide bDeferRender".split(
                  " "
                )
              ),
              Pt(c, n, [
                "asStripeClasses",
                "ajax",
                "fnServerData",
                "fnFormatNumber",
                "sServerMethod",
                "aaSorting",
                "aaSortingFixed",
                "aLengthMenu",
                "sPaginationType",
                "sAjaxSource",
                "sAjaxDataProp",
                "iStateDuration",
                "sDom",
                "bSortCellsTop",
                "iTabIndex",
                "fnStateLoadCallback",
                "fnStateSaveCallback",
                "renderer",
                "searchDelay",
                "rowId",
                ["iCookieDuration", "iStateDuration"],
                ["oSearch", "oPreviousSearch"],
                ["aoSearchCols", "aoPreSearchCols"],
                ["iDisplayLength", "_iDisplayLength"],
              ]),
              Pt(c.oScroll, n, [
                ["sScrollX", "sX"],
                ["sScrollXInner", "sXInner"],
                ["sScrollY", "sY"],
                ["bScrollCollapse", "bCollapse"],
              ]),
              Pt(c.oLanguage, n, "fnInfoCallback"),
              Ht(c, "aoDrawCallback", n.fnDrawCallback, "user"),
              Ht(c, "aoServerParams", n.fnServerParams, "user"),
              Ht(c, "aoStateSaveParams", n.fnStateSaveParams, "user"),
              Ht(c, "aoStateLoadParams", n.fnStateLoadParams, "user"),
              Ht(c, "aoStateLoaded", n.fnStateLoaded, "user"),
              Ht(c, "aoRowCallback", n.fnRowCallback, "user"),
              Ht(c, "aoRowCreatedCallback", n.fnCreatedRow, "user"),
              Ht(c, "aoHeaderCallback", n.fnHeaderCallback, "user"),
              Ht(c, "aoFooterCallback", n.fnFooterCallback, "user"),
              Ht(c, "aoInitComplete", n.fnInitComplete, "user"),
              Ht(c, "aoPreDrawCallback", n.fnPreDrawCallback, "user"),
              (c.rowIdFn = O(n.rowId)),
              C(c);
            var f = c.oClasses;
            P.extend(f, Qt.ext.classes, n.oClasses),
              o.addClass(f.sTable),
              c.iInitDisplayStart === j &&
                ((c.iInitDisplayStart = n.iDisplayStart),
                (c._iDisplayStart = n.iDisplayStart)),
              null !== n.iDeferLoading &&
                ((c.bDeferLoading = !0),
                (t = P.isArray(n.iDeferLoading)),
                (c._iRecordsDisplay = t ? n.iDeferLoading[0] : n.iDeferLoading),
                (c._iRecordsTotal = t ? n.iDeferLoading[1] : n.iDeferLoading));
            var d = c.oLanguage;
            P.extend(!0, d, n.oLanguage),
              d.sUrl &&
                (P.ajax({
                  dataType: "json",
                  url: d.sUrl,
                  success: function (t) {
                    y(t), D(e.oLanguage, t), P.extend(!0, d, t), ot(c);
                  },
                  error: function () {
                    ot(c);
                  },
                }),
                (r = !0)),
              null === n.asStripeClasses &&
                (c.asStripeClasses = [f.sStripeOdd, f.sStripeEven]);
            var h,
              t = c.asStripeClasses,
              p = o.children("tbody").find("tr").eq(0);
            if (
              (-1 !==
                P.inArray(
                  !0,
                  P.map(t, function (t) {
                    return p.hasClass(t);
                  })
                ) &&
                (P("tbody tr", this).removeClass(t.join(" ")),
                (c.asDestroyStripes = t.slice())),
              (t = []),
              0 !== (i = this.getElementsByTagName("thead")).length &&
                (B(c.aoHeader, i[0]), (t = U(c))),
              null === n.aoColumns)
            )
              for (i = [], a = 0, l = t.length; a < l; a++) i.push(null);
            else i = n.aoColumns;
            for (a = 0, l = i.length; a < l; a++) w(c, t ? t[a] : null);
            A(c, n.aoColumnDefs, i, function (t, e) {
              x(c, t, e);
            }),
              p.length &&
                ((h = function (t, e) {
                  return null !== t.getAttribute("data-" + e) ? e : null;
                }),
                P(p[0])
                  .children("th, td")
                  .each(function (t, e) {
                    var n,
                      a,
                      r = c.aoColumns[t];
                    r.mData === t &&
                      ((n = h(e, "sort") || h(e, "order")),
                      (a = h(e, "filter") || h(e, "search")),
                      (null === n && null === a) ||
                        ((r.mData = {
                          _: t + ".display",
                          sort: null !== n ? t + ".@data-" + n : j,
                          type: null !== n ? t + ".@data-" + n : j,
                          filter: null !== a ? t + ".@data-" + a : j,
                        }),
                        x(c, t)));
                  }));
            var g = c.oFeatures,
              t = function () {
                if (n.aaSorting === j) {
                  var t = c.aaSorting;
                  for (a = 0, l = t.length; a < l; a++)
                    t[a][1] = c.aoColumns[a].asSorting[0];
                }
                xt(c),
                  g.bSort &&
                    Ht(c, "aoDrawCallback", function () {
                      var t, n;
                      c.bSorted &&
                        ((t = yt(c)),
                        (n = {}),
                        P.each(t, function (t, e) {
                          n[e.src] = e.dir;
                        }),
                        Ot(c, null, "order", [c, t, n]),
                        Tt(c));
                    }),
                  Ht(
                    c,
                    "aoDrawCallback",
                    function () {
                      (c.bSorted || "ssp" === Wt(c) || g.bDeferRender) && xt(c);
                    },
                    "sc"
                  );
                var t = o.children("caption").each(function () {
                    this._captionSide = P(this).css("caption-side");
                  }),
                  e = o.children("thead");
                if (
                  (0 === e.length && (e = P("<thead/>").appendTo(o)),
                  (c.nTHead = e[0]),
                  0 === (e = o.children("tbody")).length &&
                    (e = P("<tbody/>").appendTo(o)),
                  (c.nTBody = e[0]),
                  0 === (e = o.children("tfoot")).length &&
                    0 < t.length &&
                    ("" !== c.oScroll.sX || "" !== c.oScroll.sY) &&
                    (e = P("<tfoot/>").appendTo(o)),
                  0 === e.length || 0 === e.children().length
                    ? o.addClass(f.sNoFooter)
                    : 0 < e.length &&
                      ((c.nTFoot = e[0]), B(c.aoFooter, c.nTFoot)),
                  n.aaData)
                )
                  for (a = 0; a < n.aaData.length; a++) F(c, n.aaData[a]);
                else
                  (!c.bDeferLoading && "dom" != Wt(c)) ||
                    L(c, P(c.nTBody).children("tr"));
                (c.aiDisplay = c.aiDisplayMaster.slice()),
                  !(c.bInitialised = !0) === r && ot(c);
              };
            n.bStateSave
              ? ((g.bStateSave = !0),
                Ht(c, "aoDrawCallback", At, "state_save"),
                Ft(c, n, t))
              : t();
          }
        }),
        (m = null),
        this
      );
    },
    te = {},
    ee = /[\r\n\u2028]/g,
    ne = /<.*?>/g,
    ae =
      /^\d{2,4}[\.\/\-]\d{1,2}[\.\/\-]\d{1,2}([T ]{1}\d{1,2}[:\.]\d{2}([\.:]\d{2})?)?$/,
    re = RegExp(
      "(\\/|\\.|\\*|\\+|\\?|\\||\\(|\\)|\\[|\\]|\\{|\\}|\\\\|\\$|\\^|\\-)",
      "g"
    ),
    oe = /[',$£€¥%\u2009\u202F\u20BD\u20a9\u20BArfkɃΞ]/gi,
    ie = function (t, e, n) {
      var a = [],
        r = 0,
        o = t.length;
      if (n !== j) for (; r < o; r++) t[r] && t[r][e] && a.push(t[r][e][n]);
      else for (; r < o; r++) t[r] && a.push(t[r][e]);
      return a;
    },
    le = function (t) {
      var e;
      t: {
        if (!(t.length < 2))
          for (
            var n = (e = t.slice().sort())[0], a = 1, r = e.length;
            a < r;
            a++
          ) {
            if (e[a] === n) {
              e = !1;
              break t;
            }
            n = e[a];
          }
        e = !0;
      }
      if (e) return t.slice();
      e = [];
      var o,
        r = t.length,
        i = 0,
        a = 0;
      t: for (; a < r; a++) {
        for (n = t[a], o = 0; o < i; o++) if (e[o] === n) continue t;
        e.push(n), i++;
      }
      return e;
    };
  Qt.util = {
    throttle: function (a, t) {
      var r,
        o,
        i = t !== j ? t : 200;
      return function () {
        var t = this,
          e = +new Date(),
          n = arguments;
        r && e < r + i
          ? (clearTimeout(o),
            (o = setTimeout(function () {
              (r = j), a.apply(t, n);
            }, i)))
          : ((r = e), a.apply(t, n));
      };
    },
    escapeRegex: function (t) {
      return t.replace(re, "\\$1");
    },
  };
  var se = function (t, e, n) {
      t[e] !== j && (t[n] = t[e]);
    },
    ue = /\[.*?\]$/,
    ce = /\(\)$/,
    fe = Qt.util.escapeRegex,
    de = P("<div>")[0],
    he = de.textContent !== j,
    pe = /<.*?>/g,
    ge = Qt.util.throttle,
    be = [],
    me = Array.prototype,
    Se = function (t, e) {
      if (!(this instanceof Se)) return new Se(t, e);
      function n(t) {
        var e, n, a, r, o;
        (e = t),
          (r = Qt.settings),
          (o = P.map(r, function (t) {
            return t.nTable;
          })),
          (t = e
            ? e.nTable && e.oApi
              ? [e]
              : e.nodeName && "table" === e.nodeName.toLowerCase()
              ? -1 !== (n = P.inArray(e, o))
                ? [r[n]]
                : null
              : e && "function" == typeof e.settings
              ? e.settings().toArray()
              : ("string" == typeof e ? (a = P(e)) : e instanceof P && (a = e),
                a
                  ? a
                      .map(function () {
                        return -1 !== (n = P.inArray(this, o)) ? r[n] : null;
                      })
                      .toArray()
                  : void 0)
            : []) && i.push.apply(i, t);
      }
      var i = [];
      if (P.isArray(t)) for (var a = 0, r = t.length; a < r; a++) n(t[a]);
      else n(t);
      (this.context = le(i)),
        e && P.merge(this, e),
        (this.selector = { rows: null, cols: null, opts: null }),
        Se.extend(this, this, be);
    };
  (Qt.Api = Se),
    P.extend(Se.prototype, {
      any: function () {
        return 0 !== this.count();
      },
      concat: me.concat,
      context: [],
      count: function () {
        return this.flatten().length;
      },
      each: function (t) {
        for (var e = 0, n = this.length; e < n; e++)
          t.call(this, this[e], e, this);
        return this;
      },
      eq: function (t) {
        var e = this.context;
        return e.length > t ? new Se(e[t], this[t]) : null;
      },
      filter: function (t) {
        var e = [];
        if (me.filter) e = me.filter.call(this, t, this);
        else
          for (var n = 0, a = this.length; n < a; n++)
            t.call(this, this[n], n, this) && e.push(this[n]);
        return new Se(this.context, e);
      },
      flatten: function () {
        var t = [];
        return new Se(this.context, t.concat.apply(t, this.toArray()));
      },
      join: me.join,
      indexOf:
        me.indexOf ||
        function (t, e) {
          for (var n = e || 0, a = this.length; n < a; n++)
            if (this[n] === t) return n;
          return -1;
        },
      iterator: function (t, e, n, a) {
        var r,
          o,
          i,
          l,
          s,
          u,
          c,
          f = [],
          d = this.context,
          h = this.selector;
        for (
          "string" == typeof t && ((a = n), (n = e), (e = t), (t = !1)),
            o = 0,
            i = d.length;
          o < i;
          o++
        ) {
          var p = new Se(d[o]);
          if ("table" === e) (r = n.call(p, d[o], o)) !== j && f.push(r);
          else if ("columns" === e || "rows" === e)
            (r = n.call(p, d[o], this[o], o)) !== j && f.push(r);
          else if (
            "column" === e ||
            "column-rows" === e ||
            "row" === e ||
            "cell" === e
          )
            for (
              c = this[o],
                "column-rows" === e && (u = Ce(d[o], h.opts)),
                l = 0,
                s = c.length;
              l < s;
              l++
            )
              (r = c[l]),
                (r =
                  "cell" === e
                    ? n.call(p, d[o], r.row, r.column, o, l)
                    : n.call(p, d[o], r, o, l, u)) !== j && f.push(r);
        }
        return f.length || a
          ? (((e = (t = new Se(d, t ? f.concat.apply([], f) : f))
              .selector).rows = h.rows),
            (e.cols = h.cols),
            (e.opts = h.opts),
            t)
          : this;
      },
      lastIndexOf:
        me.lastIndexOf ||
        function (t, e) {
          return this.indexOf.apply(this.toArray.reverse(), arguments);
        },
      length: 0,
      map: function (t) {
        var e = [];
        if (me.map) e = me.map.call(this, t, this);
        else
          for (var n = 0, a = this.length; n < a; n++)
            e.push(t.call(this, this[n], n));
        return new Se(this.context, e);
      },
      pluck: function (e) {
        return this.map(function (t) {
          return t[e];
        });
      },
      pop: me.pop,
      push: me.push,
      reduce:
        me.reduce ||
        function (t, e) {
          return n(this, t, e, 0, this.length, 1);
        },
      reduceRight:
        me.reduceRight ||
        function (t, e) {
          return n(this, t, e, this.length - 1, -1, -1);
        },
      reverse: me.reverse,
      selector: null,
      shift: me.shift,
      slice: function () {
        return new Se(this.context, this);
      },
      sort: me.sort,
      splice: me.splice,
      toArray: function () {
        return me.slice.call(this);
      },
      to$: function () {
        return P(this);
      },
      toJQuery: function () {
        return P(this);
      },
      unique: function () {
        return new Se(this.context, le(this));
      },
      unshift: me.unshift,
    }),
    (Se.extend = function (t, e, n) {
      if (n.length && e && (e instanceof Se || e.__dt_wrapper))
        for (var a, r = 0, o = n.length; r < o; r++)
          (e[(a = n[r]).name] =
            "function" === a.type
              ? (function (e, n, a) {
                  return function () {
                    var t = n.apply(e, arguments);
                    return Se.extend(t, t, a.methodExt), t;
                  };
                })(t, a.val, a)
              : "object" === a.type
              ? {}
              : a.val),
            (e[a.name].__dt_wrapper = !0),
            Se.extend(t, e[a.name], a.propExt);
    }),
    (Se.register = Zt =
      function (t, e) {
        if (P.isArray(t))
          for (var n = 0, a = t.length; n < a; n++) Se.register(t[n], e);
        else
          for (
            var r, o = t.split("."), i = be, n = 0, a = o.length;
            n < a;
            n++
          ) {
            var l,
              s = (r = -1 !== o[n].indexOf("()"))
                ? o[n].replace("()", "")
                : o[n];
            t: {
              l = 0;
              for (var u = i.length; l < u; l++)
                if (i[l].name === s) {
                  l = i[l];
                  break t;
                }
              l = null;
            }
            l ||
              ((l = {
                name: s,
                val: {},
                methodExt: [],
                propExt: [],
                type: "object",
              }),
              i.push(l)),
              n === a - 1
                ? ((l.val = e),
                  (l.type =
                    "function" == typeof e
                      ? "function"
                      : P.isPlainObject(e)
                      ? "object"
                      : "other"))
                : (i = r ? l.methodExt : l.propExt);
          }
      }),
    (Se.registerPlural = Kt =
      function (t, e, n) {
        Se.register(t, n),
          Se.register(e, function () {
            var t = n.apply(this, arguments);
            return t === this
              ? this
              : t instanceof Se
              ? t.length
                ? P.isArray(t[0])
                  ? new Se(t.context, t[0])
                  : t[0]
                : j
              : t;
          });
      });
  var ve = function (t, e) {
    if (P.isArray(t))
      return P.map(t, function (t) {
        return ve(t, e);
      });
    if ("number" == typeof t) return [e[t]];
    var n = P.map(e, function (t) {
      return t.nTable;
    });
    return P(n)
      .filter(t)
      .map(function () {
        var t = P.inArray(this, n);
        return e[t];
      })
      .toArray();
  };
  Zt("tables()", function (t) {
    return t !== j && null !== t ? new Se(ve(t, this.context)) : this;
  }),
    Zt("table()", function (t) {
      var e = (t = this.tables(t)).context;
      return e.length ? new Se(e[0]) : t;
    }),
    Kt("tables().nodes()", "table().node()", function () {
      return this.iterator(
        "table",
        function (t) {
          return t.nTable;
        },
        1
      );
    }),
    Kt("tables().body()", "table().body()", function () {
      return this.iterator(
        "table",
        function (t) {
          return t.nTBody;
        },
        1
      );
    }),
    Kt("tables().header()", "table().header()", function () {
      return this.iterator(
        "table",
        function (t) {
          return t.nTHead;
        },
        1
      );
    }),
    Kt("tables().footer()", "table().footer()", function () {
      return this.iterator(
        "table",
        function (t) {
          return t.nTFoot;
        },
        1
      );
    }),
    Kt("tables().containers()", "table().container()", function () {
      return this.iterator(
        "table",
        function (t) {
          return t.nTableWrapper;
        },
        1
      );
    }),
    Zt("draw()", function (e) {
      return this.iterator("table", function (t) {
        "page" === e
          ? M(t)
          : ("string" == typeof e && (e = "full-hold" !== e), W(t, !1 === e));
      });
    }),
    Zt("page()", function (e) {
      return e === j
        ? this.page.info().page
        : this.iterator("table", function (t) {
            ct(t, e);
          });
    }),
    Zt("page.info()", function () {
      if (0 === this.context.length) return j;
      var t = this.context[0],
        e = t._iDisplayStart,
        n = t.oFeatures.bPaginate ? t._iDisplayLength : -1,
        a = t.fnRecordsDisplay(),
        r = -1 === n;
      return {
        page: r ? 0 : Math.floor(e / n),
        pages: r ? 1 : Math.ceil(a / n),
        start: e,
        end: t.fnDisplayEnd(),
        length: n,
        recordsTotal: t.fnRecordsTotal(),
        recordsDisplay: a,
        serverSide: "ssp" === Wt(t),
      };
    }),
    Zt("page.len()", function (e) {
      return e === j
        ? 0 !== this.context.length
          ? this.context[0]._iDisplayLength
          : j
        : this.iterator("table", function (t) {
            lt(t, e);
          });
    });
  function De(a, r, t) {
    var e, n;
    t &&
      (e = new Se(a)).one("draw", function () {
        t(e.ajax.json());
      }),
      "ssp" == Wt(a)
        ? W(a, r)
        : (dt(a, !0),
          (n = a.jqXHR) && 4 !== n.readyState && n.abort(),
          V(a, [], function (t) {
            l(a);
            for (var e = 0, n = (t = q(a, t)).length; e < n; e++) F(a, t[e]);
            W(a, r), dt(a, !1);
          }));
  }
  Zt("ajax.json()", function () {
    var t = this.context;
    if (0 < t.length) return t[0].json;
  }),
    Zt("ajax.params()", function () {
      var t = this.context;
      if (0 < t.length) return t[0].oAjaxData;
    }),
    Zt("ajax.reload()", function (e, n) {
      return this.iterator("table", function (t) {
        De(t, !1 === n, e);
      });
    }),
    Zt("ajax.url()", function (e) {
      var t = this.context;
      return e === j
        ? 0 === t.length
          ? j
          : (t = t[0]).ajax
          ? P.isPlainObject(t.ajax)
            ? t.ajax.url
            : t.ajax
          : t.sAjaxSource
        : this.iterator("table", function (t) {
            P.isPlainObject(t.ajax) ? (t.ajax.url = e) : (t.ajax = e);
          });
    }),
    Zt("ajax.url().load()", function (e, n) {
      return this.iterator("table", function (t) {
        De(t, !1 === n, e);
      });
    });
  function ye(t, e, n, a, r) {
    var o,
      i,
      l,
      s,
      u,
      c = [],
      f = typeof e;
    for (
      (e && "string" !== f && "function" !== f && e.length !== j) || (e = [e]),
        f = 0,
        l = e.length;
      f < l;
      f++
    )
      for (
        s = 0,
          u = (i =
            e[f] && e[f].split && !e[f].match(/[\[\(:]/)
              ? e[f].split(",")
              : [e[f]]).length;
        s < u;
        s++
      )
        (o = n("string" == typeof i[s] ? P.trim(i[s]) : i[s])) &&
          o.length &&
          (c = c.concat(o));
    if ((t = Yt.selector[t]).length)
      for (f = 0, l = t.length; f < l; f++) c = t[f](a, r, c);
    return le(c);
  }
  function _e(t) {
    return (
      (t = t || {}).filter && t.search === j && (t.search = t.filter),
      P.extend({ search: "none", order: "current", page: "all" }, t)
    );
  }
  function Te(t) {
    for (var e = 0, n = t.length; e < n; e++)
      if (0 < t[e].length)
        return (
          (t[0] = t[e]),
          (t[0].length = 1),
          (t.length = 1),
          (t.context = [t.context[e]]),
          t
        );
    return (t.length = 0), t;
  }
  var Ce = function (t, e) {
    var n = [],
      a = t.aiDisplay,
      r = t.aiDisplayMaster,
      o = e.search;
    if (((l = e.order), (s = e.page), "ssp" == Wt(t)))
      return "removed" === o ? [] : $t(0, r.length);
    if ("current" == s)
      for (l = t._iDisplayStart, s = t.fnDisplayEnd(); l < s; l++) n.push(a[l]);
    else if ("current" == l || "applied" == l) {
      if ("none" == o) n = r.slice();
      else if ("applied" == o) n = a.slice();
      else if ("removed" == o) {
        for (var i = {}, l = 0, s = a.length; l < s; l++) i[a[l]] = null;
        n = P.map(r, function (t) {
          return i.hasOwnProperty(t) ? null : t;
        });
      }
    } else if ("index" == l || "original" == l)
      for (l = 0, s = t.aoData.length; l < s; l++)
        "none" == o
          ? n.push(l)
          : ((-1 === (r = P.inArray(l, a)) && "removed" == o) ||
              (0 <= r && "applied" == o)) &&
            n.push(l);
    return n;
  };
  Zt("rows()", function (t, e) {
    t === j ? (t = "") : P.isPlainObject(t) && ((e = t), (t = ""));
    var e = _e(e),
      n = this.iterator(
        "table",
        function (r) {
          var o,
            i = e;
          return ye(
            "row",
            t,
            function (n) {
              var t = Vt(n),
                a = r.aoData;
              if (null !== t && !i) return [t];
              if (((o = o || Ce(r, i)), null !== t && -1 !== P.inArray(t, o)))
                return [t];
              if (null === n || n === j || "" === n) return o;
              if ("function" == typeof n)
                return P.map(o, function (t) {
                  var e = a[t];
                  return n(t, e._aData, e.nTr) ? t : null;
                });
              if (n.nodeName) {
                var t = n._DT_RowIndex,
                  e = n._DT_CellIndex;
                return t !== j
                  ? a[t] && a[t].nTr === n
                    ? [t]
                    : []
                  : e
                  ? a[e.row] && a[e.row].nTr === n.parentNode
                    ? [e.row]
                    : []
                  : (t = P(n).closest("*[data-dt-row]")).length
                  ? [t.data("dt-row")]
                  : [];
              }
              return "string" == typeof n &&
                "#" === n.charAt(0) &&
                (t = r.aIds[n.replace(/^#/, "")]) !== j
                ? [t.idx]
                : ((t = zt(Gt(r.aoData, o, "nTr"))),
                  P(t)
                    .filter(n)
                    .map(function () {
                      return this._DT_RowIndex;
                    })
                    .toArray());
            },
            r,
            i
          );
        },
        1
      );
    return (n.selector.rows = t), (n.selector.opts = e), n;
  }),
    Zt("rows().nodes()", function () {
      return this.iterator(
        "row",
        function (t, e) {
          return t.aoData[e].nTr || j;
        },
        1
      );
    }),
    Zt("rows().data()", function () {
      return this.iterator(
        !0,
        "rows",
        function (t, e) {
          return Gt(t.aoData, e, "_aData");
        },
        1
      );
    }),
    Kt("rows().cache()", "row().cache()", function (a) {
      return this.iterator(
        "row",
        function (t, e) {
          var n = t.aoData[e];
          return "search" === a ? n._aFilterData : n._aSortData;
        },
        1
      );
    }),
    Kt("rows().invalidate()", "row().invalidate()", function (n) {
      return this.iterator("row", function (t, e) {
        r(t, e, n);
      });
    }),
    Kt("rows().indexes()", "row().index()", function () {
      return this.iterator(
        "row",
        function (t, e) {
          return e;
        },
        1
      );
    }),
    Kt("rows().ids()", "row().id()", function (t) {
      for (var e = [], n = this.context, a = 0, r = n.length; a < r; a++)
        for (var o = 0, i = this[a].length; o < i; o++) {
          var l = n[a].rowIdFn(n[a].aoData[this[a][o]]._aData);
          e.push((!0 === t ? "#" : "") + l);
        }
      return new Se(n, e);
    }),
    Kt("rows().remove()", "row().remove()", function () {
      var c = this;
      return (
        this.iterator("row", function (t, e, n) {
          var a,
            r,
            o,
            i,
            l,
            s = t.aoData,
            u = s[e];
          for (s.splice(e, 1), a = 0, r = s.length; a < r; a++)
            if (
              ((l = (o = s[a]).anCells),
              null !== o.nTr && (o.nTr._DT_RowIndex = a),
              null !== l)
            )
              for (o = 0, i = l.length; o < i; o++) l[o]._DT_CellIndex.row = a;
          f(t.aiDisplayMaster, e),
            f(t.aiDisplay, e),
            f(c[n], e, !1),
            0 < t._iRecordsDisplay && t._iRecordsDisplay--,
            kt(t),
            (e = t.rowIdFn(u._aData)) !== j && delete t.aIds[e];
        }),
        this.iterator("table", function (t) {
          for (var e = 0, n = t.aoData.length; e < n; e++) t.aoData[e].idx = e;
        }),
        this
      );
    }),
    Zt("rows.add()", function (o) {
      var t = this.iterator(
          "table",
          function (t) {
            for (var e, n = [], a = 0, r = o.length; a < r; a++)
              (e = o[a]).nodeName && "TR" === e.nodeName.toUpperCase()
                ? n.push(L(t, e)[0])
                : n.push(F(t, e));
            return n;
          },
          1
        ),
        e = this.rows(-1);
      return e.pop(), P.merge(e, t), e;
    }),
    Zt("row()", function (t, e) {
      return Te(this.rows(t, e));
    }),
    Zt("row().data()", function (t) {
      var e = this.context;
      if (t === j)
        return e.length && this.length ? e[0].aoData[this[0]]._aData : j;
      var n = e[0].aoData[this[0]];
      return (
        (n._aData = t),
        P.isArray(t) && n.nTr && n.nTr.id && p(e[0].rowId)(t, n.nTr.id),
        r(e[0], this[0], "data"),
        this
      );
    }),
    Zt("row().node()", function () {
      var t = this.context;
      return (t.length && this.length && t[0].aoData[this[0]].nTr) || null;
    }),
    Zt("row.add()", function (e) {
      e instanceof P && e.length && (e = e[0]);
      var t = this.iterator("table", function (t) {
        return e.nodeName && "TR" === e.nodeName.toUpperCase()
          ? L(t, e)[0]
          : F(t, e);
      });
      return this.row(t[0]);
    });
  function we(t, e) {
    var n = t.context;
    n.length &&
      (n = n[0].aoData[e !== j ? e : t[0]]) &&
      n._details &&
      (n._details.remove(), (n._detailsShow = j), (n._details = j));
  }
  function xe(t, e) {
    var n,
      i,
      r,
      l,
      a = t.context;
    a.length &&
      t.length &&
      (n = a[0].aoData[t[0]])._details &&
      ((n._detailsShow = e)
        ? n._details.insertAfter(n.nTr)
        : n._details.detach(),
      (i = a[0]),
      (r = new Se(i)),
      (l = i.aoData),
      r.off(
        "draw.dt.DT_details column-visibility.dt.DT_details destroy.dt.DT_details"
      ),
      0 < ie(l, "_details").length &&
        (r.on("draw.dt.DT_details", function (t, e) {
          i === e &&
            r
              .rows({ page: "current" })
              .eq(0)
              .each(function (t) {
                (t = l[t])._detailsShow && t._details.insertAfter(t.nTr);
              });
        }),
        r.on("column-visibility.dt.DT_details", function (t, e) {
          if (i === e)
            for (var n, a = S(e), r = 0, o = l.length; r < o; r++)
              (n = l[r])._details &&
                n._details.children("td[colspan]").attr("colspan", a);
        }),
        r.on("destroy.dt.DT_details", function (t, e) {
          if (i === e)
            for (var n = 0, a = l.length; n < a; n++) l[n]._details && we(r, n);
        })));
  }
  Zt("row().child()", function (t, e) {
    var r,
      o,
      i,
      n = this.context;
    return t === j
      ? n.length && this.length
        ? n[0].aoData[this[0]]._details
        : j
      : (!0 === t
          ? this.child.show()
          : !1 === t
          ? we(this)
          : n.length &&
            this.length &&
            ((r = n[0]),
            (n = n[0].aoData[this[0]]),
            (o = []),
            (i = function (t, e) {
              if (P.isArray(t) || t instanceof P)
                for (var n = 0, a = t.length; n < a; n++) i(t[n], e);
              else
                t.nodeName && "tr" === t.nodeName.toLowerCase()
                  ? o.push(t)
                  : ((n = P("<tr><td/></tr>").addClass(e)),
                    (P("td", n).addClass(e).html(t)[0].colSpan = S(r)),
                    o.push(n[0]));
            })(t, e),
            n._details && n._details.detach(),
            (n._details = P(o)),
            n._detailsShow && n._details.insertAfter(n.nTr)),
        this);
  }),
    Zt(["row().child.show()", "row().child().show()"], function () {
      return xe(this, !0), this;
    }),
    Zt(["row().child.hide()", "row().child().hide()"], function () {
      return xe(this, !1), this;
    }),
    Zt(["row().child.remove()", "row().child().remove()"], function () {
      return we(this), this;
    }),
    Zt("row().child.isShown()", function () {
      var t = this.context;
      return (
        (t.length && this.length && t[0].aoData[this[0]]._detailsShow) || !1
      );
    });
  function Ie(t, e, n, a, r) {
    for (var n = [], a = 0, o = r.length; a < o; a++) n.push(R(t, r[a], e));
    return n;
  }
  var Ae = /^([^:]+):(name|visIdx|visible)$/;
  Zt("columns()", function (e, n) {
    e === j ? (e = "") : P.isPlainObject(e) && ((n = e), (e = ""));
    var n = _e(n),
      t = this.iterator(
        "table",
        function (o) {
          var t = e,
            i = n,
            l = o.aoColumns,
            s = ie(l, "sName"),
            u = ie(l, "nTh");
          return ye(
            "column",
            t,
            function (n) {
              var t = Vt(n);
              if ("" === n) return $t(l.length);
              if (null !== t) return [0 <= t ? t : l.length + t];
              if ("function" == typeof n) {
                var a = Ce(o, i);
                return P.map(l, function (t, e) {
                  return n(e, Ie(o, e, 0, 0, a), u[e]) ? e : null;
                });
              }
              var r = "string" == typeof n ? n.match(Ae) : "";
              if (r)
                switch (r[2]) {
                  case "visIdx":
                  case "visible":
                    if ((t = parseInt(r[1], 10)) < 0) {
                      var e = P.map(l, function (t, e) {
                        return t.bVisible ? e : null;
                      });
                      return [e[e.length + t]];
                    }
                    return [H(o, t)];
                  case "name":
                    return P.map(s, function (t, e) {
                      return t === r[1] ? e : null;
                    });
                  default:
                    return [];
                }
              return n.nodeName && n._DT_CellIndex
                ? [n._DT_CellIndex.column]
                : (t = P(u)
                    .filter(n)
                    .map(function () {
                      return P.inArray(this, u);
                    })
                    .toArray()).length || !n.nodeName
                ? t
                : (t = P(n).closest("*[data-dt-column]")).length
                ? [t.data("dt-column")]
                : [];
            },
            o,
            i
          );
        },
        1
      );
    return (t.selector.cols = e), (t.selector.opts = n), t;
  }),
    Kt("columns().header()", "column().header()", function () {
      return this.iterator(
        "column",
        function (t, e) {
          return t.aoColumns[e].nTh;
        },
        1
      );
    }),
    Kt("columns().footer()", "column().footer()", function () {
      return this.iterator(
        "column",
        function (t, e) {
          return t.aoColumns[e].nTf;
        },
        1
      );
    }),
    Kt("columns().data()", "column().data()", function () {
      return this.iterator("column-rows", Ie, 1);
    }),
    Kt("columns().dataSrc()", "column().dataSrc()", function () {
      return this.iterator(
        "column",
        function (t, e) {
          return t.aoColumns[e].mData;
        },
        1
      );
    }),
    Kt("columns().cache()", "column().cache()", function (o) {
      return this.iterator(
        "column-rows",
        function (t, e, n, a, r) {
          return Gt(
            t.aoData,
            r,
            "search" === o ? "_aFilterData" : "_aSortData",
            e
          );
        },
        1
      );
    }),
    Kt("columns().nodes()", "column().nodes()", function () {
      return this.iterator(
        "column-rows",
        function (t, e, n, a, r) {
          return Gt(t.aoData, r, "anCells", e);
        },
        1
      );
    }),
    Kt("columns().visible()", "column().visible()", function (u, n) {
      var e = this,
        t = this.iterator("column", function (t, e) {
          if (u === j) return t.aoColumns[e].bVisible;
          var n,
            a = t.aoColumns,
            r = a[e],
            o = t.aoData;
          if (u !== j && r.bVisible !== u) {
            if (u)
              for (
                var i = P.inArray(!0, ie(a, "bVisible"), e + 1),
                  l = 0,
                  s = o.length;
                l < s;
                l++
              )
                (n = o[l].nTr),
                  (a = o[l].anCells),
                  n && n.insertBefore(a[e], a[i] || null);
            else P(ie(t.aoData, "anCells", e)).detach();
            r.bVisible = u;
          }
        });
      return (
        u !== j &&
          this.iterator("table", function (t) {
            k(t, t.aoHeader),
              k(t, t.aoFooter),
              t.aiDisplay.length ||
                P(t.nTBody).find("td[colspan]").attr("colspan", S(t)),
              At(t),
              e.iterator("column", function (t, e) {
                Ot(t, null, "column-visibility", [t, e, u, n]);
              }),
              (n !== j && !n) || e.columns.adjust();
          }),
        t
      );
    }),
    Kt("columns().indexes()", "column().index()", function (n) {
      return this.iterator(
        "column",
        function (t, e) {
          return "visible" === n ? u(t, e) : e;
        },
        1
      );
    }),
    Zt("columns.adjust()", function () {
      return this.iterator(
        "table",
        function (t) {
          N(t);
        },
        1
      );
    }),
    Zt("column.index()", function (t, e) {
      if (0 !== this.context.length) {
        var n = this.context[0];
        if ("fromVisible" === t || "toData" === t) return H(n, e);
        if ("fromData" === t || "toVisible" === t) return u(n, e);
      }
    }),
    Zt("column()", function (t, e) {
      return Te(this.columns(t, e));
    }),
    Zt("cells()", function (g, t, b) {
      if (
        (P.isPlainObject(g) &&
          (g.row === j ? ((b = g), (g = null)) : ((b = t), (t = null))),
        P.isPlainObject(t) && ((b = t), (t = null)),
        null === t || t === j)
      )
        return this.iterator("table", function (n) {
          var a,
            r,
            o,
            i,
            l,
            s,
            u,
            t = g,
            e = _e(b),
            c = n.aoData,
            f = Ce(n, e),
            d = zt(Gt(c, f, "anCells")),
            h = P([].concat.apply([], d)),
            p = n.aoColumns.length;
          return ye(
            "cell",
            t,
            function (t) {
              var e = "function" == typeof t;
              if (null === t || t === j || e) {
                for (r = [], o = 0, i = f.length; o < i; o++)
                  for (a = f[o], l = 0; l < p; l++)
                    (s = { row: a, column: l }),
                      e
                        ? ((u = c[a]),
                          t(s, R(n, a, l), u.anCells ? u.anCells[l] : null) &&
                            r.push(s))
                        : r.push(s);
                return r;
              }
              return P.isPlainObject(t)
                ? t.column !== j && t.row !== j && -1 !== P.inArray(t.row, f)
                  ? [t]
                  : []
                : (e = h
                    .filter(t)
                    .map(function (t, e) {
                      return {
                        row: e._DT_CellIndex.row,
                        column: e._DT_CellIndex.column,
                      };
                    })
                    .toArray()).length || !t.nodeName
                ? e
                : (u = P(t).closest("*[data-dt-row]")).length
                ? [{ row: u.data("dt-row"), column: u.data("dt-column") }]
                : [];
            },
            n,
            e
          );
        });
      var a,
        r,
        o,
        i,
        e = b ? { page: b.page, order: b.order, search: b.search } : {},
        l = this.columns(t, e),
        s = this.rows(g, e),
        e = this.iterator(
          "table",
          function (t, e) {
            var n = [];
            for (a = 0, r = s[e].length; a < r; a++)
              for (o = 0, i = l[e].length; o < i; o++)
                n.push({ row: s[e][a], column: l[e][o] });
            return n;
          },
          1
        ),
        e = b && b.selected ? this.cells(e, b) : e;
      return P.extend(e.selector, { cols: t, rows: g, opts: b }), e;
    }),
    Kt("cells().nodes()", "cell().node()", function () {
      return this.iterator(
        "cell",
        function (t, e, n) {
          return (t = t.aoData[e]) && t.anCells ? t.anCells[n] : j;
        },
        1
      );
    }),
    Zt("cells().data()", function () {
      return this.iterator(
        "cell",
        function (t, e, n) {
          return R(t, e, n);
        },
        1
      );
    }),
    Kt("cells().cache()", "cell().cache()", function (a) {
      return (
        (a = "search" === a ? "_aFilterData" : "_aSortData"),
        this.iterator(
          "cell",
          function (t, e, n) {
            return t.aoData[e][a][n];
          },
          1
        )
      );
    }),
    Kt("cells().render()", "cell().render()", function (a) {
      return this.iterator(
        "cell",
        function (t, e, n) {
          return R(t, e, n, a);
        },
        1
      );
    }),
    Kt("cells().indexes()", "cell().index()", function () {
      return this.iterator(
        "cell",
        function (t, e, n) {
          return { row: e, column: n, columnVisible: u(t, n) };
        },
        1
      );
    }),
    Kt("cells().invalidate()", "cell().invalidate()", function (a) {
      return this.iterator("cell", function (t, e, n) {
        r(t, e, a, n);
      });
    }),
    Zt("cell()", function (t, e, n) {
      return Te(this.cells(t, e, n));
    }),
    Zt("cell().data()", function (t) {
      var e = this.context,
        n = this[0];
      return t === j
        ? e.length && n.length
          ? R(e[0], n[0].row, n[0].column)
          : j
        : (a(e[0], n[0].row, n[0].column, t),
          r(e[0], n[0].row, "data", n[0].column),
          this);
    }),
    Zt("order()", function (e, t) {
      var n = this.context;
      return e === j
        ? 0 !== n.length
          ? n[0].aaSorting
          : j
        : ("number" == typeof e
            ? (e = [[e, t]])
            : e.length &&
              !P.isArray(e[0]) &&
              (e = Array.prototype.slice.call(arguments)),
          this.iterator("table", function (t) {
            t.aaSorting = e.slice();
          }));
    }),
    Zt("order.listener()", function (e, n, a) {
      return this.iterator("table", function (t) {
        wt(t, e, n, a);
      });
    }),
    Zt("order.fixed()", function (e) {
      if (e)
        return this.iterator("table", function (t) {
          t.aaSortingFixed = P.extend(!0, {}, e);
        });
      var t = (t = this.context).length ? t[0].aaSortingFixed : j;
      return P.isArray(t) ? { pre: t } : t;
    }),
    Zt(["columns().order()", "column().order()"], function (a) {
      var r = this;
      return this.iterator("table", function (t, e) {
        var n = [];
        P.each(r[e], function (t, e) {
          n.push([e, a]);
        }),
          (t.aaSorting = n);
      });
    }),
    Zt("search()", function (e, n, a, r) {
      var t = this.context;
      return e === j
        ? 0 !== t.length
          ? t[0].oPreviousSearch.sSearch
          : j
        : this.iterator("table", function (t) {
            t.oFeatures.bFilter &&
              $(
                t,
                P.extend({}, t.oPreviousSearch, {
                  sSearch: e + "",
                  bRegex: null !== n && n,
                  bSmart: null === a || a,
                  bCaseInsensitive: null === r || r,
                }),
                1
              );
          });
    }),
    Kt("columns().search()", "column().search()", function (a, r, o, i) {
      return this.iterator("column", function (t, e) {
        var n = t.aoPreSearchCols;
        if (a === j) return n[e].sSearch;
        t.oFeatures.bFilter &&
          (P.extend(n[e], {
            sSearch: a + "",
            bRegex: null !== r && r,
            bSmart: null === o || o,
            bCaseInsensitive: null === i || i,
          }),
          $(t, t.oPreviousSearch, 1));
      });
    }),
    Zt("state()", function () {
      return this.context.length ? this.context[0].oSavedState : null;
    }),
    Zt("state.clear()", function () {
      return this.iterator("table", function (t) {
        t.fnStateSaveCallback.call(t.oInstance, t, {});
      });
    }),
    Zt("state.loaded()", function () {
      return this.context.length ? this.context[0].oLoadedState : null;
    }),
    Zt("state.save()", function () {
      return this.iterator("table", function (t) {
        At(t);
      });
    }),
    (Qt.versionCheck = Qt.fnVersionCheck =
      function (t) {
        for (
          var e,
            n,
            a = Qt.version.split("."),
            r = 0,
            o = (t = t.split(".")).length;
          r < o;
          r++
        )
          if ((e = parseInt(a[r], 10) || 0) !== (n = parseInt(t[r], 10) || 0))
            return n < e;
        return !0;
      }),
    (Qt.isDataTable = Qt.fnIsDataTable =
      function (t) {
        var r = P(t).get(0),
          o = !1;
        return (
          t instanceof Qt.Api ||
          (P.each(Qt.settings, function (t, e) {
            var n = e.nScrollHead ? P("table", e.nScrollHead)[0] : null,
              a = e.nScrollFoot ? P("table", e.nScrollFoot)[0] : null;
            (e.nTable !== r && n !== r && a !== r) || (o = !0);
          }),
          o)
        );
      }),
    (Qt.tables = Qt.fnTables =
      function (e) {
        var t = !1;
        P.isPlainObject(e) && ((t = e.api), (e = e.visible));
        var n = P.map(Qt.settings, function (t) {
          if (!e || (e && P(t.nTable).is(":visible"))) return t.nTable;
        });
        return t ? new Se(n) : n;
      }),
    (Qt.camelToHungarian = D),
    Zt("$()", function (t, e) {
      var n = this.rows(e).nodes(),
        n = P(n);
      return P([].concat(n.filter(t).toArray(), n.find(t).toArray()));
    }),
    P.each(["on", "one", "off"], function (t, n) {
      Zt(n + "()", function () {
        var t = Array.prototype.slice.call(arguments);
        t[0] = P.map(t[0].split(/\s/), function (t) {
          return t.match(/\.dt\b/) ? t : t + ".dt";
        }).join(" ");
        var e = P(this.tables().nodes());
        return e[n].apply(e, t), this;
      });
    }),
    Zt("clear()", function () {
      return this.iterator("table", function (t) {
        l(t);
      });
    }),
    Zt("settings()", function () {
      return new Se(this.context, this.context);
    }),
    Zt("init()", function () {
      var t = this.context;
      return t.length ? t[0].oInit : null;
    }),
    Zt("data()", function () {
      return this.iterator("table", function (t) {
        return ie(t.aoData, "_aData");
      }).flatten();
    }),
    Zt("destroy()", function (f) {
      return (
        (f = f || !1),
        this.iterator("table", function (e) {
          var n,
            t = e.nTableWrapper.parentNode,
            a = e.oClasses,
            r = e.nTable,
            o = e.nTBody,
            i = e.nTHead,
            l = e.nTFoot,
            s = P(r),
            o = P(o),
            u = P(e.nTableWrapper),
            c = P.map(e.aoData, function (t) {
              return t.nTr;
            });
          (e.bDestroying = !0),
            Ot(e, "aoDestroyCallback", "destroy", [e]),
            f || new Se(e).columns().visible(!0),
            u.off(".DT").find(":not(tbody *)").off(".DT"),
            P(m).off(".DT-" + e.sInstance),
            r != i.parentNode && (s.children("thead").detach(), s.append(i)),
            l &&
              r != l.parentNode &&
              (s.children("tfoot").detach(), s.append(l)),
            (e.aaSorting = []),
            (e.aaSortingFixed = []),
            xt(e),
            P(c).removeClass(e.asStripeClasses.join(" ")),
            P("th, td", i).removeClass(
              a.sSortable +
                " " +
                a.sSortableAsc +
                " " +
                a.sSortableDesc +
                " " +
                a.sSortableNone
            ),
            o.children().detach(),
            o.append(c),
            s[(i = f ? "remove" : "detach")](),
            u[i](),
            !f &&
              t &&
              (t.insertBefore(r, e.nTableReinsertBefore),
              s.css("width", e.sDestroyWidth).removeClass(a.sTable),
              (n = e.asDestroyStripes.length) &&
                o.children().each(function (t) {
                  P(this).addClass(e.asDestroyStripes[t % n]);
                })),
            -1 !== (t = P.inArray(e, Qt.settings)) && Qt.settings.splice(t, 1);
        })
      );
    }),
    P.each(["column", "row", "cell"], function (t, s) {
      Zt(s + "s().every()", function (o) {
        var i = this.selector.opts,
          l = this;
        return this.iterator(s, function (t, e, n, a, r) {
          o.call(
            l[s](e, "cell" === s ? n : i, "cell" === s ? i : j),
            e,
            n,
            a,
            r
          );
        });
      });
    }),
    Zt("i18n()", function (t, e, n) {
      var a = this.context[0];
      return (
        (t = O(t)(a.oLanguage)) === j && (t = e),
        n !== j && P.isPlainObject(t) && (t = t[n] !== j ? t[n] : t._),
        t.replace("%d", n)
      );
    }),
    (Qt.version = "1.10.21"),
    (Qt.settings = []),
    (Qt.models = {}),
    (Qt.models.oSearch = {
      bCaseInsensitive: !0,
      sSearch: "",
      bRegex: !1,
      bSmart: !0,
    }),
    (Qt.models.oRow = {
      nTr: null,
      anCells: null,
      _aData: [],
      _aSortData: null,
      _aFilterData: null,
      _sFilterRow: null,
      _sRowStripe: "",
      src: null,
      idx: -1,
    }),
    (Qt.models.oColumn = {
      idx: null,
      aDataSort: null,
      asSorting: null,
      bSearchable: null,
      bSortable: null,
      bVisible: null,
      _sManualType: null,
      _bAttrSrc: !1,
      fnCreatedCell: null,
      fnGetData: null,
      fnSetData: null,
      mData: null,
      mRender: null,
      nTh: null,
      nTf: null,
      sClass: null,
      sContentPadding: null,
      sDefaultContent: null,
      sName: null,
      sSortDataType: "std",
      sSortingClass: null,
      sSortingClassJUI: null,
      sTitle: null,
      sType: null,
      sWidth: null,
      sWidthOrig: null,
    }),
    (Qt.defaults = {
      aaData: null,
      aaSorting: [[0, "asc"]],
      aaSortingFixed: [],
      ajax: null,
      aLengthMenu: [10, 25, 50, 100],
      aoColumns: null,
      aoColumnDefs: null,
      aoSearchCols: [],
      asStripeClasses: null,
      bAutoWidth: !0,
      bDeferRender: !1,
      bDestroy: !1,
      bFilter: !0,
      bInfo: !0,
      bLengthChange: !0,
      bPaginate: !0,
      bProcessing: !1,
      bRetrieve: !1,
      bScrollCollapse: !1,
      bServerSide: !1,
      bSort: !0,
      bSortMulti: !0,
      bSortCellsTop: !1,
      bSortClasses: !0,
      bStateSave: !1,
      fnCreatedRow: null,
      fnDrawCallback: null,
      fnFooterCallback: null,
      fnFormatNumber: function (t) {
        return t
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, this.oLanguage.sThousands);
      },
      fnHeaderCallback: null,
      fnInfoCallback: null,
      fnInitComplete: null,
      fnPreDrawCallback: null,
      fnRowCallback: null,
      fnServerData: null,
      fnServerParams: null,
      fnStateLoadCallback: function (t) {
        try {
          return JSON.parse(
            (-1 === t.iStateDuration ? sessionStorage : localStorage).getItem(
              "DataTables_" + t.sInstance + "_" + location.pathname
            )
          );
        } catch (t) {
          return {};
        }
      },
      fnStateLoadParams: null,
      fnStateLoaded: null,
      fnStateSaveCallback: function (t, e) {
        try {
          (-1 === t.iStateDuration ? sessionStorage : localStorage).setItem(
            "DataTables_" + t.sInstance + "_" + location.pathname,
            JSON.stringify(e)
          );
        } catch (t) {}
      },
      fnStateSaveParams: null,
      iStateDuration: 7200,
      iDeferLoading: null,
      iDisplayLength: 10,
      iDisplayStart: 0,
      iTabIndex: 0,
      oClasses: {},
      oLanguage: {
        oAria: {
          sSortAscending: ": activate to sort column ascending",
          sSortDescending: ": activate to sort column descending",
        },
        oPaginate: {
          sFirst: "First",
          sLast: "Last",
          sNext: "Next",
          sPrevious: "Previous",
        },
        sEmptyTable: "No data available in table",
        sInfo: "Showing _START_ to _END_ of _TOTAL_ entries",
        sInfoEmpty: "Showing 0 to 0 of 0 entries",
        sInfoFiltered: "(filtered from _MAX_ total entries)",
        sInfoPostFix: "",
        sDecimal: "",
        sThousands: ",",
        sLengthMenu: "Show _MENU_ entries",
        sLoadingRecords: "Loading...",
        sProcessing: "Processing...",
        sSearch: "Search:",
        sSearchPlaceholder: "",
        sUrl: "",
        sZeroRecords: "No matching records found",
      },
      oSearch: P.extend({}, Qt.models.oSearch),
      sAjaxDataProp: "data",
      sAjaxSource: null,
      sDom: "lfrtip",
      searchDelay: null,
      sPaginationType: "simple_numbers",
      sScrollX: "",
      sScrollXInner: "",
      sScrollY: "",
      sServerMethod: "GET",
      renderer: null,
      rowId: "DT_RowId",
    }),
    o(Qt.defaults),
    (Qt.defaults.column = {
      aDataSort: null,
      iDataSort: -1,
      asSorting: ["asc", "desc"],
      bSearchable: !0,
      bSortable: !0,
      bVisible: !0,
      fnCreatedCell: null,
      mData: null,
      mRender: null,
      sCellType: "td",
      sClass: "",
      sContentPadding: "",
      sDefaultContent: null,
      sName: "",
      sSortDataType: "std",
      sTitle: null,
      sType: null,
      sWidth: null,
    }),
    o(Qt.defaults.column),
    (Qt.models.oSettings = {
      oFeatures: {
        bAutoWidth: null,
        bDeferRender: null,
        bFilter: null,
        bInfo: null,
        bLengthChange: null,
        bPaginate: null,
        bProcessing: null,
        bServerSide: null,
        bSort: null,
        bSortMulti: null,
        bSortClasses: null,
        bStateSave: null,
      },
      oScroll: {
        bCollapse: null,
        iBarWidth: 0,
        sX: null,
        sXInner: null,
        sY: null,
      },
      oLanguage: { fnInfoCallback: null },
      oBrowser: {
        bScrollOversize: !1,
        bScrollbarLeft: !1,
        bBounding: !1,
        barWidth: 0,
      },
      ajax: null,
      aanFeatures: [],
      aoData: [],
      aiDisplay: [],
      aiDisplayMaster: [],
      aIds: {},
      aoColumns: [],
      aoHeader: [],
      aoFooter: [],
      oPreviousSearch: {},
      aoPreSearchCols: [],
      aaSorting: null,
      aaSortingFixed: [],
      asStripeClasses: null,
      asDestroyStripes: [],
      sDestroyWidth: 0,
      aoRowCallback: [],
      aoHeaderCallback: [],
      aoFooterCallback: [],
      aoDrawCallback: [],
      aoRowCreatedCallback: [],
      aoPreDrawCallback: [],
      aoInitComplete: [],
      aoStateSaveParams: [],
      aoStateLoadParams: [],
      aoStateLoaded: [],
      sTableId: "",
      nTable: null,
      nTHead: null,
      nTFoot: null,
      nTBody: null,
      nTableWrapper: null,
      bDeferLoading: !1,
      bInitialised: !1,
      aoOpenRows: [],
      sDom: null,
      searchDelay: null,
      sPaginationType: "two_button",
      iStateDuration: 0,
      aoStateSave: [],
      aoStateLoad: [],
      oSavedState: null,
      oLoadedState: null,
      sAjaxSource: null,
      sAjaxDataProp: null,
      bAjaxDataGet: !0,
      jqXHR: null,
      json: j,
      oAjaxData: j,
      fnServerData: null,
      aoServerParams: [],
      sServerMethod: null,
      fnFormatNumber: null,
      aLengthMenu: null,
      iDraw: 0,
      bDrawing: !1,
      iDrawError: -1,
      _iDisplayLength: 10,
      _iDisplayStart: 0,
      _iRecordsTotal: 0,
      _iRecordsDisplay: 0,
      oClasses: {},
      bFiltered: !1,
      bSorted: !1,
      bSortCellsTop: null,
      oInit: null,
      aoDestroyCallback: [],
      fnRecordsTotal: function () {
        return "ssp" == Wt(this)
          ? +this._iRecordsTotal
          : this.aiDisplayMaster.length;
      },
      fnRecordsDisplay: function () {
        return "ssp" == Wt(this)
          ? +this._iRecordsDisplay
          : this.aiDisplay.length;
      },
      fnDisplayEnd: function () {
        var t = this._iDisplayLength,
          e = this._iDisplayStart,
          n = e + t,
          a = this.aiDisplay.length,
          r = this.oFeatures,
          o = r.bPaginate;
        return r.bServerSide
          ? !1 === o || -1 === t
            ? e + a
            : Math.min(e + t, this._iRecordsDisplay)
          : !o || a < n || -1 === t
          ? a
          : n;
      },
      oInstance: null,
      sInstance: null,
      iTabIndex: 0,
      nScrollHead: null,
      nScrollFoot: null,
      aLastSort: [],
      oPlugins: {},
      rowIdFn: null,
      rowId: null,
    }),
    (Qt.ext = Yt =
      {
        buttons: {},
        classes: {},
        builder: "-source-",
        errMode: "alert",
        feature: [],
        search: [],
        selector: { cell: [], column: [], row: [] },
        internal: {},
        legacy: { ajax: null },
        pager: {},
        renderer: { pageButton: {}, header: {} },
        order: {},
        type: { detect: [], search: {}, order: {} },
        _unique: 0,
        fnVersionCheck: Qt.fnVersionCheck,
        iApiIndex: 0,
        oJUIClasses: {},
        sVersion: Qt.version,
      }),
    P.extend(Yt, {
      afnFiltering: Yt.search,
      aTypes: Yt.type.detect,
      ofnSearch: Yt.type.search,
      oSort: Yt.type.order,
      afnSortData: Yt.order,
      aoFeatures: Yt.feature,
      oApi: Yt.internal,
      oStdClasses: Yt.classes,
      oPagination: Yt.pager,
    }),
    P.extend(Qt.ext.classes, {
      sTable: "dataTable",
      sNoFooter: "no-footer",
      sPageButton: "paginate_button",
      sPageButtonActive: "current",
      sPageButtonDisabled: "disabled",
      sStripeOdd: "odd",
      sStripeEven: "even",
      sRowEmpty: "dataTables_empty",
      sWrapper: "dataTables_wrapper",
      sFilter: "dataTables_filter",
      sInfo: "dataTables_info",
      sPaging: "dataTables_paginate paging_",
      sLength: "dataTables_length",
      sProcessing: "dataTables_processing",
      sSortAsc: "sorting_asc",
      sSortDesc: "sorting_desc",
      sSortable: "sorting",
      sSortableAsc: "sorting_asc_disabled",
      sSortableDesc: "sorting_desc_disabled",
      sSortableNone: "sorting_disabled",
      sSortColumn: "sorting_",
      sFilterInput: "",
      sLengthSelect: "",
      sScrollWrapper: "dataTables_scroll",
      sScrollHead: "dataTables_scrollHead",
      sScrollHeadInner: "dataTables_scrollHeadInner",
      sScrollBody: "dataTables_scrollBody",
      sScrollFoot: "dataTables_scrollFoot",
      sScrollFootInner: "dataTables_scrollFootInner",
      sHeaderTH: "",
      sFooterTH: "",
      sSortJUIAsc: "",
      sSortJUIDesc: "",
      sSortJUI: "",
      sSortJUIAscAllowed: "",
      sSortJUIDescAllowed: "",
      sSortJUIWrapper: "",
      sSortIcon: "",
      sJUIHeader: "",
      sJUIFooter: "",
    });
  var Fe = Qt.ext.pager;
  P.extend(Fe, {
    simple: function () {
      return ["previous", "next"];
    },
    full: function () {
      return ["first", "previous", "next", "last"];
    },
    numbers: function (t, e) {
      return [Et(t, e)];
    },
    simple_numbers: function (t, e) {
      return ["previous", Et(t, e), "next"];
    },
    full_numbers: function (t, e) {
      return ["first", "previous", Et(t, e), "next", "last"];
    },
    first_last_numbers: function (t, e) {
      return ["first", Et(t, e), "last"];
    },
    _numbers: Et,
    numbers_length: 7,
  }),
    P.extend(!0, Qt.ext.renderer, {
      pageButton: {
        _: function (s, t, u, e, c, f) {
          var d,
            h,
            n,
            p = s.oClasses,
            g = s.oLanguage.oPaginate,
            b = s.oLanguage.oAria.paginate || {},
            m = 0,
            S = function (t, e) {
              function n(t) {
                ct(s, t.data.action, !0);
              }
              for (
                var a, r, o = p.sPageButtonDisabled, i = 0, l = e.length;
                i < l;
                i++
              )
                if (((r = e[i]), P.isArray(r)))
                  (a = P("<" + (r.DT_el || "div") + "/>").appendTo(t)), S(a, r);
                else {
                  switch (((d = null), (h = r), (a = s.iTabIndex), r)) {
                    case "ellipsis":
                      t.append('<span class="ellipsis">&#x2026;</span>');
                      break;
                    case "first":
                      (d = g.sFirst), 0 === c && ((a = -1), (h = h + " " + o));
                      break;
                    case "previous":
                      (d = g.sPrevious),
                        0 === c && ((a = -1), (h = h + " " + o));
                      break;
                    case "next":
                      (d = g.sNext),
                        (0 !== f && c !== f - 1) ||
                          ((a = -1), (h = h + " " + o));
                      break;
                    case "last":
                      (d = g.sLast),
                        c === f - 1 && ((a = -1), (h = h + " " + o));
                      break;
                    default:
                      (d = r + 1), (h = c === r ? p.sPageButtonActive : "");
                  }
                  null !== d &&
                    (Nt(
                      (a = P("<a>", {
                        class: p.sPageButton + " " + h,
                        "aria-controls": s.sTableId,
                        "aria-label": b[r],
                        "data-dt-idx": m,
                        tabindex: a,
                        id:
                          0 === u && "string" == typeof r
                            ? s.sTableId + "_" + r
                            : null,
                      })
                        .html(d)
                        .appendTo(t)),
                      { action: r },
                      n
                    ),
                    m++);
                }
            };
          try {
            n = P(t).find(v.activeElement).data("dt-idx");
          } catch (t) {}
          S(P(t).empty(), e),
            n !== j &&
              P(t)
                .find("[data-dt-idx=" + n + "]")
                .trigger("focus");
        },
      },
    }),
    P.extend(Qt.ext.type.detect, [
      function (t, e) {
        var n = e.oLanguage.sDecimal;
        return Jt(t, n) ? "num" + n : null;
      },
      function (t) {
        if (t && !(t instanceof Date) && !ae.test(t)) return null;
        var e = Date.parse(t);
        return (null !== e && !isNaN(e)) || Ut(t) ? "date" : null;
      },
      function (t, e) {
        var n = e.oLanguage.sDecimal;
        return Jt(t, n, !0) ? "num-fmt" + n : null;
      },
      function (t, e) {
        var n = e.oLanguage.sDecimal;
        return qt(t, n) ? "html-num" + n : null;
      },
      function (t, e) {
        var n = e.oLanguage.sDecimal;
        return qt(t, n, !0) ? "html-num-fmt" + n : null;
      },
      function (t) {
        return Ut(t) || ("string" == typeof t && -1 !== t.indexOf("<"))
          ? "html"
          : null;
      },
    ]),
    P.extend(Qt.ext.type.search, {
      html: function (t) {
        return Ut(t)
          ? t
          : "string" == typeof t
          ? t.replace(ee, " ").replace(ne, "")
          : "";
      },
      string: function (t) {
        return !Ut(t) && "string" == typeof t ? t.replace(ee, " ") : t;
      },
    });
  var Le = function (t, e, n, a) {
    return 0 === t || (t && "-" !== t)
      ? (e && (t = Xt(t, e)),
        t.replace && (n && (t = t.replace(n, "")), a && (t = t.replace(a, ""))),
        +t)
      : -1 / 0;
  };
  P.extend(Yt.type.order, {
    "date-pre": function (t) {
      return (t = Date.parse(t)), isNaN(t) ? -1 / 0 : t;
    },
    "html-pre": function (t) {
      return Ut(t)
        ? ""
        : t.replace
        ? t.replace(/<.*?>/g, "").toLowerCase()
        : t + "";
    },
    "string-pre": function (t) {
      return Ut(t)
        ? ""
        : "string" == typeof t
        ? t.toLowerCase()
        : t.toString
        ? t.toString()
        : "";
    },
    "string-asc": function (t, e) {
      return t < e ? -1 : e < t ? 1 : 0;
    },
    "string-desc": function (t, e) {
      return t < e ? 1 : e < t ? -1 : 0;
    },
  }),
    Bt(""),
    P.extend(!0, Qt.ext.renderer, {
      header: {
        _: function (r, o, i, l) {
          P(r.nTable).on("order.dt.DT", function (t, e, n, a) {
            r === e &&
              ((t = i.idx),
              o
                .removeClass(
                  i.sSortingClass + " " + l.sSortAsc + " " + l.sSortDesc
                )
                .addClass(
                  "asc" == a[t]
                    ? l.sSortAsc
                    : "desc" == a[t]
                    ? l.sSortDesc
                    : i.sSortingClass
                ));
          });
        },
        jqueryui: function (r, o, i, l) {
          P("<div/>")
            .addClass(l.sSortJUIWrapper)
            .append(o.contents())
            .append(
              P("<span/>").addClass(l.sSortIcon + " " + i.sSortingClassJUI)
            )
            .appendTo(o),
            P(r.nTable).on("order.dt.DT", function (t, e, n, a) {
              r === e &&
                ((t = i.idx),
                o
                  .removeClass(l.sSortAsc + " " + l.sSortDesc)
                  .addClass(
                    "asc" == a[t]
                      ? l.sSortAsc
                      : "desc" == a[t]
                      ? l.sSortDesc
                      : i.sSortingClass
                  ),
                o
                  .find("span." + l.sSortIcon)
                  .removeClass(
                    l.sSortJUIAsc +
                      " " +
                      l.sSortJUIDesc +
                      " " +
                      l.sSortJUI +
                      " " +
                      l.sSortJUIAscAllowed +
                      " " +
                      l.sSortJUIDescAllowed
                  )
                  .addClass(
                    "asc" == a[t]
                      ? l.sSortJUIAsc
                      : "desc" == a[t]
                      ? l.sSortJUIDesc
                      : i.sSortingClassJUI
                  ));
            });
        },
      },
    });
  function Re(t) {
    return "string" == typeof t
      ? t
          .replace(/&/g, "&amp;")
          .replace(/</g, "&lt;")
          .replace(/>/g, "&gt;")
          .replace(/"/g, "&quot;")
      : t;
  }
  return (
    (Qt.render = {
      number: function (a, r, o, i, l) {
        return {
          display: function (t) {
            if ("number" != typeof t && "string" != typeof t) return t;
            var e = t < 0 ? "-" : "",
              n = parseFloat(t);
            return isNaN(n)
              ? Re(t)
              : ((n = n.toFixed(o)),
                (t = Math.abs(n)),
                (n = parseInt(t, 10)),
                (t = o ? r + (t - n).toFixed(o).substring(2) : ""),
                e +
                  (i || "") +
                  n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, a) +
                  t +
                  (l || ""));
          },
        };
      },
      text: function () {
        return { display: Re, filter: Re };
      },
    }),
    P.extend(Qt.ext.internal, {
      _fnExternApiFunc: e,
      _fnBuildAjax: V,
      _fnAjaxUpdate: X,
      _fnAjaxParameters: t,
      _fnAjaxUpdateDraw: J,
      _fnAjaxDataSrc: q,
      _fnAddColumn: w,
      _fnColumnOptions: x,
      _fnAdjustColumnSizing: N,
      _fnVisibleToColumnIndex: H,
      _fnColumnIndexToVisible: u,
      _fnVisbleColumns: S,
      _fnGetColumns: I,
      _fnColumnTypes: i,
      _fnApplyColumnDefs: A,
      _fnHungarianMap: o,
      _fnCamelToHungarian: D,
      _fnLanguageCompat: y,
      _fnBrowserDetect: C,
      _fnAddData: F,
      _fnAddTr: L,
      _fnNodeToDataIndex: function (t, e) {
        return e._DT_RowIndex !== j ? e._DT_RowIndex : null;
      },
      _fnNodeToColumnIndex: function (t, e, n) {
        return P.inArray(n, t.aoData[e].anCells);
      },
      _fnGetCellData: R,
      _fnSetCellData: a,
      _fnSplitObjNotation: c,
      _fnGetObjectDataFn: O,
      _fnSetObjectDataFn: p,
      _fnGetDataMaster: g,
      _fnClearTable: l,
      _fnDeleteIndex: f,
      _fnInvalidate: r,
      _fnGetRowElements: s,
      _fnCreateTr: b,
      _fnBuildHead: d,
      _fnDrawHead: k,
      _fnDraw: M,
      _fnReDraw: W,
      _fnAddOptionsHtml: E,
      _fnDetectHeader: B,
      _fnGetUniqueThs: U,
      _fnFeatureHtmlFilter: G,
      _fnFilterComplete: $,
      _fnFilterCustom: z,
      _fnFilterColumn: Y,
      _fnFilter: Z,
      _fnFilterCreateSearch: K,
      _fnEscapeRegex: fe,
      _fnFilterData: Q,
      _fnFeatureHtmlInfo: nt,
      _fnUpdateInfo: at,
      _fnInfoMacros: rt,
      _fnInitialise: ot,
      _fnInitComplete: it,
      _fnLengthChange: lt,
      _fnFeatureHtmlLength: st,
      _fnFeatureHtmlPaginate: ut,
      _fnPageChange: ct,
      _fnFeatureHtmlProcessing: ft,
      _fnProcessingDisplay: dt,
      _fnFeatureHtmlTable: ht,
      _fnScrollDraw: pt,
      _fnApplyToChildren: gt,
      _fnCalculateColumnWidths: bt,
      _fnThrottle: ge,
      _fnConvertToWidth: mt,
      _fnGetWidestNode: St,
      _fnGetMaxLenString: vt,
      _fnStringToCss: Dt,
      _fnSortFlatten: yt,
      _fnSort: _t,
      _fnSortAria: Tt,
      _fnSortListener: Ct,
      _fnSortAttachListener: wt,
      _fnSortingClasses: xt,
      _fnSortData: It,
      _fnSaveState: At,
      _fnLoadState: Ft,
      _fnSettingsFromNode: Lt,
      _fnLog: Rt,
      _fnMap: Pt,
      _fnBindAction: Nt,
      _fnCallbackReg: Ht,
      _fnCallbackFire: Ot,
      _fnLengthOverflow: kt,
      _fnRenderer: Mt,
      _fnDataSource: Wt,
      _fnRowAttributes: h,
      _fnExtend: jt,
      _fnCalculateEnd: function () {},
    }),
    (((P.fn.dataTable = Qt).$ = P).fn.dataTableSettings = Qt.settings),
    (P.fn.dataTableExt = Qt.ext),
    (P.fn.DataTable = function (t) {
      return P(this).dataTable(t).api();
    }),
    P.each(Qt, function (t, e) {
      P.fn.DataTable[t] = e;
    }),
    P.fn.dataTable
  );
});
