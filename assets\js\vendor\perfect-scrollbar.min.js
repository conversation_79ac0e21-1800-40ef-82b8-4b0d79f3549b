!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).PerfectScrollbar=e()}(this,function(){"use strict";var v=Math.abs,m=Math.floor;function Y(t){return getComputedStyle(t)}function d(t,e){for(var i in e){var l=e[i];"number"==typeof l&&(l+="px"),t.style[i]=l}return t}function p(t){var e=document.createElement("div");return e.className=t,e}function s(t,e){if(!l)throw new Error("No element matching method supported");return l.call(t,e)}function n(t){t.remove?t.remove():t.parentNode&&t.parentNode.removeChild(t)}function o(t,e){return Array.prototype.filter.call(t.children,function(t){return s(t,e)})}function X(t,e){var i=t.element.classList,l=W.state.scrolling(e);i.contains(l)?clearTimeout(r[e]):i.add(l)}function w(t,e){r[e]=setTimeout(function(){return t.isAlive&&t.element.classList.remove(W.state.scrolling(e))},t.settings.scrollingThreshold)}function f(t){if("function"==typeof window.CustomEvent)return new CustomEvent(t);var e=document.createEvent("CustomEvent");return e.initCustomEvent(t,!1,!1,void 0),e}function t(t,e,i,l,r){var n;if(void 0===l&&(l=!0),void 0===r&&(r=!1),"top"===e)n=["contentHeight","containerHeight","scrollTop","y","up","down"];else{if("left"!==e)throw new Error("A proper axis should be provided");n=["contentWidth","containerWidth","scrollLeft","x","left","right"]}!function(t,e,i,l,r){var n=i[0],o=i[1],s=i[2],a=i[3],c=i[4],h=i[5];void 0===l&&(l=!0),void 0===r&&(r=!1);var u=t.element;t.reach[a]=null,u[s]<1&&(t.reach[a]="start"),u[s]>t[n]-t[o]-1&&(t.reach[a]="end"),e&&(u.dispatchEvent(f("ps-scroll-"+a)),e<0?u.dispatchEvent(f("ps-scroll-"+c)):0<e&&u.dispatchEvent(f("ps-scroll-"+h)),l&&function(t,e){X(t,e),w(t,e)}(t,a)),t.reach[a]&&(e||r)&&u.dispatchEvent(f("ps-"+a+"-reach-"+t.reach[a]))}(t,i,n,l,r)}function b(t){return parseInt(t,10)||0}function y(t){var e=Math.ceil,i=t.element,l=m(i.scrollTop),r=i.getBoundingClientRect();t.containerWidth=e(r.width),t.containerHeight=e(r.height),t.contentWidth=i.scrollWidth,t.contentHeight=i.scrollHeight,i.contains(t.scrollbarXRail)||(o(i,W.element.rail("x")).forEach(n),i.appendChild(t.scrollbarXRail)),i.contains(t.scrollbarYRail)||(o(i,W.element.rail("y")).forEach(n),i.appendChild(t.scrollbarYRail)),!t.settings.suppressScrollX&&t.containerWidth+t.settings.scrollXMarginOffset<t.contentWidth?(t.scrollbarXActive=!0,t.railXWidth=t.containerWidth-t.railXMarginWidth,t.railXRatio=t.containerWidth/t.railXWidth,t.scrollbarXWidth=a(t,b(t.railXWidth*t.containerWidth/t.contentWidth)),t.scrollbarXLeft=b((t.negativeScrollAdjustment+i.scrollLeft)*(t.railXWidth-t.scrollbarXWidth)/(t.contentWidth-t.containerWidth))):t.scrollbarXActive=!1,!t.settings.suppressScrollY&&t.containerHeight+t.settings.scrollYMarginOffset<t.contentHeight?(t.scrollbarYActive=!0,t.railYHeight=t.containerHeight-t.railYMarginHeight,t.railYRatio=t.containerHeight/t.railYHeight,t.scrollbarYHeight=a(t,b(t.railYHeight*t.containerHeight/t.contentHeight)),t.scrollbarYTop=b(l*(t.railYHeight-t.scrollbarYHeight)/(t.contentHeight-t.containerHeight))):t.scrollbarYActive=!1,t.scrollbarXLeft>=t.railXWidth-t.scrollbarXWidth&&(t.scrollbarXLeft=t.railXWidth-t.scrollbarXWidth),t.scrollbarYTop>=t.railYHeight-t.scrollbarYHeight&&(t.scrollbarYTop=t.railYHeight-t.scrollbarYHeight),function(t,e){var i={width:e.railXWidth},l=m(t.scrollTop);i.left=e.isRtl?e.negativeScrollAdjustment+t.scrollLeft+e.containerWidth-e.contentWidth:t.scrollLeft,e.isScrollbarXUsingBottom?i.bottom=e.scrollbarXBottom-l:i.top=e.scrollbarXTop+l,d(e.scrollbarXRail,i);var r={top:l,height:e.railYHeight};e.isScrollbarYUsingRight?e.isRtl?r.right=e.contentWidth-(e.negativeScrollAdjustment+t.scrollLeft)-e.scrollbarYRight-e.scrollbarYOuterWidth-9:r.right=e.scrollbarYRight-t.scrollLeft:e.isRtl?r.left=e.negativeScrollAdjustment+t.scrollLeft+2*e.containerWidth-e.contentWidth-e.scrollbarYLeft-e.scrollbarYOuterWidth:r.left=e.scrollbarYLeft+t.scrollLeft,d(e.scrollbarYRail,r),d(e.scrollbarX,{left:e.scrollbarXLeft,width:e.scrollbarXWidth-e.railBorderXWidth}),d(e.scrollbarY,{top:e.scrollbarYTop,height:e.scrollbarYHeight-e.railBorderYWidth})}(i,t),t.scrollbarXActive?i.classList.add(W.state.active("x")):(i.classList.remove(W.state.active("x")),t.scrollbarXWidth=0,t.scrollbarXLeft=0,i.scrollLeft=!0===t.isRtl?t.contentWidth:0),t.scrollbarYActive?i.classList.add(W.state.active("y")):(i.classList.remove(W.state.active("y")),t.scrollbarYHeight=0,t.scrollbarYTop=0,i.scrollTop=0)}function a(t,e){var i=Math.min,l=Math.max;return t.settings.minScrollbarLength&&(e=l(e,t.settings.minScrollbarLength)),t.settings.maxScrollbarLength&&(e=i(e,t.settings.maxScrollbarLength)),e}function e(i,t){function l(t){t.touches&&t.touches[0]&&(t[s]=t.touches[0].pageY),f[u]=b+v*(t[s]-g),X(i,d),y(i),t.stopPropagation(),t.preventDefault()}function r(){w(i,d),i[p].classList.remove(W.state.clicking),i.event.unbind(i.ownerDocument,"mousemove",l)}function e(t,e){b=f[u],e&&t.touches&&(t[s]=t.touches[0].pageY),g=t[s],v=(i[o]-i[n])/(i[a]-i[h]),e?i.event.bind(i.ownerDocument,"touchmove",l):(i.event.bind(i.ownerDocument,"mousemove",l),i.event.once(i.ownerDocument,"mouseup",r),t.preventDefault()),i[p].classList.add(W.state.clicking),t.stopPropagation()}var n=t[0],o=t[1],s=t[2],a=t[3],c=t[4],h=t[5],u=t[6],d=t[7],p=t[8],f=i.element,b=null,g=null,v=null;i.event.bind(i[c],"mousedown",function(t){e(t)}),i.event.bind(i[c],"touchstart",function(t){e(t,!0)})}function i(t){this.element=t,this.handlers={}}var l="undefined"!=typeof Element&&(Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector),W={main:"ps",rtl:"ps__rtl",element:{thumb:function(t){return"ps__thumb-"+t},rail:function(t){return"ps__rail-"+t},consuming:"ps__child--consume"},state:{focus:"ps--focus",clicking:"ps--clicking",active:function(t){return"ps--active-"+t},scrolling:function(t){return"ps--scrolling-"+t}}},r={x:null,y:null},c={isEmpty:{configurable:!0}};i.prototype.bind=function(t,e){void 0===this.handlers[t]&&(this.handlers[t]=[]),this.handlers[t].push(e),this.element.addEventListener(t,e,!1)},i.prototype.unbind=function(e,i){var l=this;this.handlers[e]=this.handlers[e].filter(function(t){return!(!i||t===i)||(l.element.removeEventListener(e,t,!1),!1)})},i.prototype.unbindAll=function(){for(var t in this.handlers)this.unbind(t)},c.isEmpty.get=function(){var e=this;return Object.keys(this.handlers).every(function(t){return 0===e.handlers[t].length})},Object.defineProperties(i.prototype,c);function g(){this.eventElements=[]}g.prototype.eventElement=function(e){var t=this.eventElements.filter(function(t){return t.element===e})[0];return t||(t=new i(e),this.eventElements.push(t)),t},g.prototype.bind=function(t,e,i){this.eventElement(t).bind(e,i)},g.prototype.unbind=function(t,e,i){var l=this.eventElement(t);l.unbind(e,i),l.isEmpty&&this.eventElements.splice(this.eventElements.indexOf(l),1)},g.prototype.unbindAll=function(){this.eventElements.forEach(function(t){return t.unbindAll()}),this.eventElements=[]},g.prototype.once=function(t,e,i){var l=this.eventElement(t),r=function(t){l.unbind(e,r),i(t)};l.bind(e,r)};function h(t,e){var i,l,r=this;if(void 0===e&&(e={}),"string"==typeof t&&(t=document.querySelector(t)),!t||!t.nodeName)throw new Error("no element is specified to initialize PerfectScrollbar");for(var n in(this.element=t).classList.add(W.main),this.settings={handlers:["click-rail","drag-thumb","keyboard","wheel","touch"],maxScrollbarLength:null,minScrollbarLength:null,scrollingThreshold:1e3,scrollXMarginOffset:0,scrollYMarginOffset:0,suppressScrollX:!1,suppressScrollY:!1,swipeEasing:!0,useBothWheelAxes:!1,wheelPropagation:!0,wheelSpeed:1},e)this.settings[n]=e[n];function o(){return t.classList.add(W.state.focus)}function s(){return t.classList.remove(W.state.focus)}this.containerWidth=null,this.containerHeight=null,this.contentWidth=null,this.contentHeight=null,this.isRtl="rtl"===Y(t).direction,!0===this.isRtl&&t.classList.add(W.rtl),this.isNegativeScroll=(l=t.scrollLeft,t.scrollLeft=-1,i=t.scrollLeft<0,t.scrollLeft=l,i),this.negativeScrollAdjustment=this.isNegativeScroll?t.scrollWidth-t.clientWidth:0,this.event=new g,this.ownerDocument=t.ownerDocument||document,this.scrollbarXRail=p(W.element.rail("x")),t.appendChild(this.scrollbarXRail),this.scrollbarX=p(W.element.thumb("x")),this.scrollbarXRail.appendChild(this.scrollbarX),this.scrollbarX.setAttribute("tabindex",0),this.event.bind(this.scrollbarX,"focus",o),this.event.bind(this.scrollbarX,"blur",s),this.scrollbarXActive=null,this.scrollbarXWidth=null,this.scrollbarXLeft=null;var a=Y(this.scrollbarXRail);this.scrollbarXBottom=parseInt(a.bottom,10),isNaN(this.scrollbarXBottom)?(this.isScrollbarXUsingBottom=!1,this.scrollbarXTop=b(a.top)):this.isScrollbarXUsingBottom=!0,this.railBorderXWidth=b(a.borderLeftWidth)+b(a.borderRightWidth),d(this.scrollbarXRail,{display:"block"}),this.railXMarginWidth=b(a.marginLeft)+b(a.marginRight),d(this.scrollbarXRail,{display:""}),this.railXWidth=null,this.railXRatio=null,this.scrollbarYRail=p(W.element.rail("y")),t.appendChild(this.scrollbarYRail),this.scrollbarY=p(W.element.thumb("y")),this.scrollbarYRail.appendChild(this.scrollbarY),this.scrollbarY.setAttribute("tabindex",0),this.event.bind(this.scrollbarY,"focus",o),this.event.bind(this.scrollbarY,"blur",s),this.scrollbarYActive=null,this.scrollbarYHeight=null,this.scrollbarYTop=null;var c,h,u=Y(this.scrollbarYRail);this.scrollbarYRight=parseInt(u.right,10),isNaN(this.scrollbarYRight)?(this.isScrollbarYUsingRight=!1,this.scrollbarYLeft=b(u.left)):this.isScrollbarYUsingRight=!0,this.scrollbarYOuterWidth=this.isRtl?(c=this.scrollbarY,b((h=Y(c)).width)+b(h.paddingLeft)+b(h.paddingRight)+b(h.borderLeftWidth)+b(h.borderRightWidth)):null,this.railBorderYWidth=b(u.borderTopWidth)+b(u.borderBottomWidth),d(this.scrollbarYRail,{display:"block"}),this.railYMarginHeight=b(u.marginTop)+b(u.marginBottom),d(this.scrollbarYRail,{display:""}),this.railYHeight=null,this.railYRatio=null,this.reach={x:t.scrollLeft<=0?"start":t.scrollLeft>=this.contentWidth-this.containerWidth?"end":null,y:t.scrollTop<=0?"start":t.scrollTop>=this.contentHeight-this.containerHeight?"end":null},this.isAlive=!0,this.settings.handlers.forEach(function(t){return R[t](r)}),this.lastScrollTop=m(t.scrollTop),this.lastScrollLeft=t.scrollLeft,this.event.bind(this.element,"scroll",function(t){return r.onScroll(t)}),y(this)}var L={isWebKit:"undefined"!=typeof document&&"WebkitAppearance"in document.documentElement.style,supportsTouch:"undefined"!=typeof window&&("ontouchstart"in window||"maxTouchPoints"in window.navigator&&0<window.navigator.maxTouchPoints||window.DocumentTouch&&document instanceof window.DocumentTouch),supportsIePointer:"undefined"!=typeof navigator&&navigator.msMaxTouchPoints,isChrome:"undefined"!=typeof navigator&&/Chrome/i.test(navigator&&navigator.userAgent)},R={"click-rail":function(i){i.element,i.event.bind(i.scrollbarY,"mousedown",function(t){return t.stopPropagation()}),i.event.bind(i.scrollbarYRail,"mousedown",function(t){var e=t.pageY-window.pageYOffset-i.scrollbarYRail.getBoundingClientRect().top>i.scrollbarYTop?1:-1;i.element.scrollTop+=e*i.containerHeight,y(i),t.stopPropagation()}),i.event.bind(i.scrollbarX,"mousedown",function(t){return t.stopPropagation()}),i.event.bind(i.scrollbarXRail,"mousedown",function(t){var e=t.pageX-window.pageXOffset-i.scrollbarXRail.getBoundingClientRect().left>i.scrollbarXLeft?1:-1;i.element.scrollLeft+=e*i.containerWidth,y(i),t.stopPropagation()})},"drag-thumb":function(t){e(t,["containerWidth","contentWidth","pageX","railXWidth","scrollbarX","scrollbarXWidth","scrollLeft","x","scrollbarXRail"]),e(t,["containerHeight","contentHeight","pageY","railYHeight","scrollbarY","scrollbarYHeight","scrollTop","y","scrollbarYRail"])},keyboard:function(n){var o=n.element;n.event.bind(n.ownerDocument,"keydown",function(t){if(!(t.isDefaultPrevented&&t.isDefaultPrevented()||t.defaultPrevented)&&(s(o,":hover")||s(n.scrollbarX,":focus")||s(n.scrollbarY,":focus"))){var e=document.activeElement?document.activeElement:n.ownerDocument.activeElement;if(e){if("IFRAME"===e.tagName)e=e.contentDocument.activeElement;else for(;e.shadowRoot;)e=e.shadowRoot.activeElement;if(s(r=e,"input,[contenteditable]")||s(r,"select,[contenteditable]")||s(r,"textarea,[contenteditable]")||s(r,"button,[contenteditable]"))return}var i=0,l=0;switch(t.which){case 37:i=t.metaKey?-n.contentWidth:t.altKey?-n.containerWidth:-30;break;case 38:l=t.metaKey?n.contentHeight:t.altKey?n.containerHeight:30;break;case 39:i=t.metaKey?n.contentWidth:t.altKey?n.containerWidth:30;break;case 40:l=t.metaKey?-n.contentHeight:t.altKey?-n.containerHeight:-30;break;case 32:l=t.shiftKey?n.containerHeight:-n.containerHeight;break;case 33:l=n.containerHeight;break;case 34:l=-n.containerHeight;break;case 36:l=n.contentHeight;break;case 35:l=-n.contentHeight;break;default:return}n.settings.suppressScrollX&&0!==i||n.settings.suppressScrollY&&0!==l||(o.scrollTop-=l,o.scrollLeft+=i,y(n),function(t,e){var i=m(o.scrollTop);if(0===t){if(!n.scrollbarYActive)return;if(0===i&&0<e||i>=n.contentHeight-n.containerHeight&&e<0)return!n.settings.wheelPropagation}var l=o.scrollLeft;if(0===e){if(!n.scrollbarXActive)return;if(0===l&&t<0||l>=n.contentWidth-n.containerWidth&&0<t)return!n.settings.wheelPropagation}return 1}(i,l)&&t.preventDefault())}var r})},wheel:function(b){function t(t){var e,i,l,r,n,o,s,a,c,h,u,d=(i=(e=t).deltaX,l=-1*e.deltaY,void 0!==i&&void 0!==l||(i=-1*e.wheelDeltaX/6,l=e.wheelDeltaY/6),e.deltaMode&&1===e.deltaMode&&(i*=10,l*=10),i!=i&&l!=l&&(i=0,l=e.wheelDelta),e.shiftKey?[-l,-i]:[i,l]),p=d[0],f=d[1];!function(t,e,i){if(!L.isWebKit&&g.querySelector("select:focus"))return 1;if(g.contains(t))for(var l=t;l&&l!==g;){if(l.classList.contains(W.element.consuming))return 1;var r=Y(l);if(i&&r.overflowY.match(/(scroll|auto)/)){var n=l.scrollHeight-l.clientHeight;if(0<n&&(0<l.scrollTop&&i<0||l.scrollTop<n&&0<i))return 1}if(e&&r.overflowX.match(/(scroll|auto)/)){var o=l.scrollWidth-l.clientWidth;if(0<o&&(0<l.scrollLeft&&e<0||l.scrollLeft<o&&0<e))return 1}l=l.parentNode}}(t.target,p,f)&&(r=!1,b.settings.useBothWheelAxes?b.scrollbarYActive&&!b.scrollbarXActive?(f?g.scrollTop-=f*b.settings.wheelSpeed:g.scrollTop+=p*b.settings.wheelSpeed,r=!0):b.scrollbarXActive&&!b.scrollbarYActive&&(p?g.scrollLeft+=p*b.settings.wheelSpeed:g.scrollLeft-=f*b.settings.wheelSpeed,r=!0):(g.scrollTop-=f*b.settings.wheelSpeed,g.scrollLeft+=p*b.settings.wheelSpeed),y(b),(r=r||(n=p,o=f,s=m(g.scrollTop),a=0===g.scrollTop,c=s+g.offsetHeight===g.scrollHeight,h=0===g.scrollLeft,u=g.scrollLeft+g.offsetWidth===g.scrollWidth,!(v(o)>v(n)?a||c:h||u)||!b.settings.wheelPropagation))&&!t.ctrlKey&&(t.stopPropagation(),t.preventDefault()))}var g=b.element;void 0===window.onwheel?void 0!==window.onmousewheel&&b.event.bind(g,"mousewheel",t):b.event.bind(g,"wheel",t)},touch:function(s){function a(t,e){u.scrollTop-=e,u.scrollLeft-=t,y(s)}function c(t){return t.targetTouches?t.targetTouches[0]:t}function h(t){return(!t.pointerType||"pen"!==t.pointerType||0!==t.buttons)&&(t.targetTouches&&1===t.targetTouches.length||t.pointerType&&"mouse"!==t.pointerType&&t.pointerType!==t.MSPOINTER_TYPE_MOUSE)}function t(t){var e;h(t)&&(e=c(t),d.pageX=e.pageX,d.pageY=e.pageY,p=(new Date).getTime(),null!==l&&clearInterval(l))}function e(t){if(h(t)){var e=c(t),i={pageX:e.pageX,pageY:e.pageY},l=i.pageX-d.pageX,r=i.pageY-d.pageY;if(function(t,e,i){if(u.contains(t))for(var l=t;l&&l!==u;){if(l.classList.contains(W.element.consuming))return 1;var r=Y(l);if(i&&r.overflowY.match(/(scroll|auto)/)){var n=l.scrollHeight-l.clientHeight;if(0<n&&(0<l.scrollTop&&i<0||l.scrollTop<n&&0<i))return 1}if(e&&r.overflowX.match(/(scroll|auto)/)){var o=l.scrollWidth-l.clientWidth;if(0<o&&(0<l.scrollLeft&&e<0||l.scrollLeft<o&&0<e))return 1}l=l.parentNode}}(t.target,l,r))return;a(l,r),d=i;var n=(new Date).getTime(),o=n-p;0<o&&(f.x=l/o,f.y=r/o,p=n),function(t,e){var i=m(u.scrollTop),l=u.scrollLeft,r=v(t),n=v(e);if(r<n){if(e<0&&i===s.contentHeight-s.containerHeight||0<e&&0===i)return 0===window.scrollY&&0<e&&L.isChrome}else if(n<r&&(t<0&&l===s.contentWidth-s.containerWidth||0<t&&0===l))return 1;return 1}(l,r)&&t.preventDefault()}}function i(){s.settings.swipeEasing&&(clearInterval(l),l=setInterval(function(){return s.isInitialized||!f.x&&!f.y||v(f.x)<.01&&v(f.y)<.01?void clearInterval(l):(a(30*f.x,30*f.y),f.x*=.8,void(f.y*=.8))},10))}var u,d,p,f,l;(L.supportsTouch||L.supportsIePointer)&&(u=s.element,d={},p=0,f={},l=null,L.supportsTouch?(s.event.bind(u,"touchstart",t),s.event.bind(u,"touchmove",e),s.event.bind(u,"touchend",i)):L.supportsIePointer&&(window.PointerEvent?(s.event.bind(u,"pointerdown",t),s.event.bind(u,"pointermove",e),s.event.bind(u,"pointerup",i)):window.MSPointerEvent&&(s.event.bind(u,"MSPointerDown",t),s.event.bind(u,"MSPointerMove",e),s.event.bind(u,"MSPointerUp",i))))}};return h.prototype.update=function(){this.isAlive&&(this.negativeScrollAdjustment=this.isNegativeScroll?this.element.scrollWidth-this.element.clientWidth:0,d(this.scrollbarXRail,{display:"block"}),d(this.scrollbarYRail,{display:"block"}),this.railXMarginWidth=b(Y(this.scrollbarXRail).marginLeft)+b(Y(this.scrollbarXRail).marginRight),this.railYMarginHeight=b(Y(this.scrollbarYRail).marginTop)+b(Y(this.scrollbarYRail).marginBottom),d(this.scrollbarXRail,{display:"none"}),d(this.scrollbarYRail,{display:"none"}),y(this),t(this,"top",0,!1,!0),t(this,"left",0,!1,!0),d(this.scrollbarXRail,{display:""}),d(this.scrollbarYRail,{display:""}))},h.prototype.onScroll=function(){this.isAlive&&(y(this),t(this,"top",this.element.scrollTop-this.lastScrollTop),t(this,"left",this.element.scrollLeft-this.lastScrollLeft),this.lastScrollTop=m(this.element.scrollTop),this.lastScrollLeft=this.element.scrollLeft)},h.prototype.destroy=function(){this.isAlive&&(this.event.unbindAll(),n(this.scrollbarX),n(this.scrollbarY),n(this.scrollbarXRail),n(this.scrollbarYRail),this.removePsClasses(),this.element=null,this.scrollbarX=null,this.scrollbarY=null,this.scrollbarXRail=null,this.scrollbarYRail=null,this.isAlive=!1)},h.prototype.removePsClasses=function(){this.element.className=this.element.className.split(" ").filter(function(t){return!t.match(/^ps([-_].+|)$/)}).join(" ")},h});