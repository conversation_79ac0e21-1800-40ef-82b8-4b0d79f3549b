!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Quill=e():t.Quill=e()}("undefined"!=typeof self?self:this,function(){return r={},o.m=n=[function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=n(17),r=n(18),i=n(19),l=n(48),a=n(49),s=n(50),u=n(51),c=n(52),f=n(11),h=n(29),p=n(30),d=n(28),y=n(1),v={Scope:y.Scope,create:y.create,find:y.find,query:y.query,register:y.register,Container:o.default,Format:r.default,Leaf:i.default,Embed:u.default,Scroll:l.default,Block:s.default,Inline:a.default,Text:c.default,Attributor:{Attribute:f.default,Class:h.default,Style:p.default,Store:d.default}};e.default=v},function(t,o,e){"use strict";function r(t,e){var n;if(void 0===e&&(e=u.ANY),"string"==typeof t)n=d[t]||f[t];else if(t instanceof Text||t.nodeType===Node.TEXT_NODE)n=d.text;else if("number"==typeof t)t&u.LEVEL&u.BLOCK?n=d.block:t&u.LEVEL&u.INLINE&&(n=d.inline);else if(t instanceof HTMLElement){var o=(t.getAttribute("class")||"").split(/\s+/);for(var r in o)if(n=h[o[r]])break;n=n||p[t.tagName]}return null!=n&&e&u.LEVEL&n.scope&&e&u.TYPE&n.scope?n:null}var i,n=this&&this.__extends||(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},function(t,e){function n(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(o,"__esModule",{value:!0});var l,a=(l=Error,n(s,l),s);function s(t){var e=this;return t="[Parchment] "+t,(e=l.call(this,t)||this).message=t,e.name=e.constructor.name,e}o.ParchmentError=a;var u,c,f={},h={},p={},d={};o.DATA_KEY="__blot",(c=u=o.Scope||(o.Scope={}))[c.TYPE=3]="TYPE",c[c.LEVEL=12]="LEVEL",c[c.ATTRIBUTE=13]="ATTRIBUTE",c[c.BLOT=14]="BLOT",c[c.INLINE=7]="INLINE",c[c.BLOCK=11]="BLOCK",c[c.BLOCK_BLOT=10]="BLOCK_BLOT",c[c.INLINE_BLOT=6]="INLINE_BLOT",c[c.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",c[c.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",c[c.ANY=15]="ANY",o.create=function(t,e){var n=r(t);if(null==n)throw new a("Unable to create "+t+" blot");return new n(t instanceof Node||t.nodeType===Node.TEXT_NODE?t:n.create(e),e)},o.find=function t(e,n){return void 0===n&&(n=!1),null==e?null:null!=e[o.DATA_KEY]?e[o.DATA_KEY].blot:n?t(e.parentNode,n):null},o.query=r,o.register=function e(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(1<t.length)return t.map(function(t){return e(t)});var o=t[0];if("string"!=typeof o.blotName&&"string"!=typeof o.attrName)throw new a("Invalid definition");if("abstract"===o.blotName)throw new a("Cannot register abstract class");return"string"==typeof(d[o.blotName||o.attrName]=o).keyName?f[o.keyName]=o:(null!=o.className&&(h[o.className]=o),null!=o.tagName&&(Array.isArray(o.tagName)?o.tagName=o.tagName.map(function(t){return t.toUpperCase()}):o.tagName=o.tagName.toUpperCase(),(Array.isArray(o.tagName)?o.tagName:[o.tagName]).forEach(function(t){null!=p[t]&&null!=o.className||(p[t]=o)}))),o}},function(t,e){"use strict";function p(t){return"function"==typeof Array.isArray?Array.isArray(t):"[object Array]"===i.call(t)}function d(t){if(t&&"[object Object]"===i.call(t)){var e,n=r.call(t,"constructor"),o=t.constructor&&t.constructor.prototype&&r.call(t.constructor.prototype,"isPrototypeOf");if(!t.constructor||n||o){for(e in t);return void 0===e||r.call(t,e)}}}function y(t,e){n&&"__proto__"===e.name?n(t,e.name,{enumerable:!0,configurable:!0,value:e.newValue,writable:!0}):t[e.name]=e.newValue}function v(t,e){if("__proto__"===e){if(!r.call(t,e))return;if(o)return o(t,e).value}return t[e]}var r=Object.prototype.hasOwnProperty,i=Object.prototype.toString,n=Object.defineProperty,o=Object.getOwnPropertyDescriptor;t.exports=function t(e,n){var o,r,i,l,a,s,u=e,c=1,f=arguments.length,h=!1;for("boolean"==typeof u&&(h=u,u=n||{},c=2),(null==u||"object"!=typeof u&&"function"!=typeof u)&&(u={});c<f;++c)if(null!=(o=arguments[c]))for(r in o)i=v(u,r),u!==(l=v(o,r))&&(h&&l&&(d(l)||(a=p(l)))?(s=a?(a=!1,i&&p(i)?i:[]):i&&d(i)?i:{},y(u,{name:r,newValue:t(h,s,l)})):void 0!==l&&y(u,{name:r,newValue:l}));return u}},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function l(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}function a(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return null==t?e:("function"==typeof t.formats&&(e=(0,c.default)(e,t.formats())),null==t.parent||"scroll"==t.parent.blotName||t.parent.statics.scope!==t.statics.scope?e:a(t.parent,e))}Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.BlockEmbed=e.bubbleFormats=void 0;function s(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:s(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var u=function(t,e,n){return e&&g(t.prototype,e),n&&g(t,n),t},c=o(n(2)),f=o(n(4)),h=o(n(0)),p=o(n(14)),d=o(n(5)),y=o(n(8)),v=(l(b,h.default.Embed),u(b,[{key:"attach",value:function(){s(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"attach",this).call(this),this.attributes=new h.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return(new f.default).insert(this.value(),(0,c.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(t,e){var n=h.default.query(t,h.default.Scope.BLOCK_ATTRIBUTE);null!=n&&this.attributes.attribute(n,e)}},{key:"formatAt",value:function(t,e,n,o){this.format(n,o)}},{key:"insertAt",value:function(t,e,n){var o;"string"==typeof e&&e.endsWith("\n")?(o=h.default.create(m.blotName),this.parent.insertBefore(o,0===t?this:this.next),o.insertAt(0,e.slice(0,-1))):s(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"insertAt",this).call(this,t,e,n)}}]),b);function b(){return r(this,b),i(this,(b.__proto__||Object.getPrototypeOf(b)).apply(this,arguments))}function g(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}v.scope=h.default.Scope.BLOCK_BLOT;var m=(l(_,h.default.Block),u(_,[{key:"delta",value:function(){return null==this.cache.delta&&(this.cache.delta=this.descendants(h.default.Leaf).reduce(function(t,e){return 0===e.length()?t:t.insert(e.value(),a(e))},new f.default).insert("\n",a(this))),this.cache.delta}},{key:"deleteAt",value:function(t,e){s(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"deleteAt",this).call(this,t,e),this.cache={}}},{key:"formatAt",value:function(t,e,n,o){e<=0||(h.default.query(n,h.default.Scope.BLOCK)?t+e===this.length()&&this.format(n,o):s(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"formatAt",this).call(this,t,Math.min(e,this.length()-t-1),n,o),this.cache={})}},{key:"insertAt",value:function(t,e,n){if(null!=n)return s(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"insertAt",this).call(this,t,e,n);var o,r,i;0!==e.length&&(0<(r=(o=e.split("\n")).shift()).length&&(t<this.length()-1||null==this.children.tail?s(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"insertAt",this).call(this,Math.min(t,this.length()-1),r):this.children.tail.insertAt(this.children.tail.length(),r),this.cache={}),i=this,o.reduce(function(t,e){return(i=i.split(t,!0)).insertAt(0,e),e.length},t+r.length))}},{key:"insertBefore",value:function(t,e){var n=this.children.head;s(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"insertBefore",this).call(this,t,e),n instanceof p.default&&n.remove(),this.cache={}}},{key:"length",value:function(){return null==this.cache.length&&(this.cache.length=s(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"length",this).call(this)+1),this.cache.length}},{key:"moveChildren",value:function(t,e){s(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"moveChildren",this).call(this,t,e),this.cache={}}},{key:"optimize",value:function(t){s(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"optimize",this).call(this,t),this.cache={}}},{key:"path",value:function(t){return s(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"path",this).call(this,t,!0)}},{key:"removeChild",value:function(t){s(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"removeChild",this).call(this,t),this.cache={}}},{key:"split",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]&&arguments[1];if(e&&(0===t||t>=this.length()-1)){var n=this.clone();return 0===t?(this.parent.insertBefore(n,this),this):(this.parent.insertBefore(n,this.next),n)}var o=s(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"split",this).call(this,t,e);return this.cache={},o}}]),_);function _(t){r(this,_);var e=i(this,(_.__proto__||Object.getPrototypeOf(_)).call(this,t));return e.cache={},e}m.blotName="block",m.tagName="P",m.defaultChild="break",m.allowedChildren=[d.default,h.default.Embed,y.default],e.bubbleFormats=a,e.BlockEmbed=v,e.default=m},function(t,e,n){function p(t){Array.isArray(t)?this.ops=t:null!=t&&Array.isArray(t.ops)?this.ops=t.ops:this.ops=[]}var s=n(54),d=n(12),o=n(2),y=n(20),r=String.fromCharCode(0);p.prototype.insert=function(t,e){var n={};return 0===t.length?this:(n.insert=t,null!=e&&"object"==typeof e&&0<Object.keys(e).length&&(n.attributes=e),this.push(n))},p.prototype.delete=function(t){return t<=0?this:this.push({delete:t})},p.prototype.retain=function(t,e){if(t<=0)return this;var n={retain:t};return null!=e&&"object"==typeof e&&0<Object.keys(e).length&&(n.attributes=e),this.push(n)},p.prototype.push=function(t){var e=this.ops.length,n=this.ops[e-1];if(t=o(!0,{},t),"object"==typeof n){if("number"==typeof t.delete&&"number"==typeof n.delete)return this.ops[e-1]={delete:n.delete+t.delete},this;if("number"==typeof n.delete&&null!=t.insert&&(--e,"object"!=typeof(n=this.ops[e-1])))return this.ops.unshift(t),this;if(d(t.attributes,n.attributes)){if("string"==typeof t.insert&&"string"==typeof n.insert)return this.ops[e-1]={insert:n.insert+t.insert},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this;if("number"==typeof t.retain&&"number"==typeof n.retain)return this.ops[e-1]={retain:n.retain+t.retain},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this}}return e===this.ops.length?this.ops.push(t):this.ops.splice(e,0,t),this},p.prototype.chop=function(){var t=this.ops[this.ops.length-1];return t&&t.retain&&!t.attributes&&this.ops.pop(),this},p.prototype.filter=function(t){return this.ops.filter(t)},p.prototype.forEach=function(t){this.ops.forEach(t)},p.prototype.map=function(t){return this.ops.map(t)},p.prototype.partition=function(e){var n=[],o=[];return this.forEach(function(t){(e(t)?n:o).push(t)}),[n,o]},p.prototype.reduce=function(t,e){return this.ops.reduce(t,e)},p.prototype.changeLength=function(){return this.reduce(function(t,e){return e.insert?t+y.length(e):e.delete?t-e.delete:t},0)},p.prototype.length=function(){return this.reduce(function(t,e){return t+y.length(e)},0)},p.prototype.slice=function(t,e){t=t||0,"number"!=typeof e&&(e=1/0);for(var n,o=[],r=y.iterator(this.ops),i=0;i<e&&r.hasNext();){i<t?n=r.next(t-i):(n=r.next(e-i),o.push(n)),i+=y.length(n)}return new p(o)},p.prototype.compose=function(t){var e=y.iterator(this.ops),n=y.iterator(t.ops),o=[],r=n.peek();if(null!=r&&"number"==typeof r.retain&&null==r.attributes){for(var i=r.retain;"insert"===e.peekType()&&e.peekLength()<=i;)i-=e.peekLength(),o.push(e.next());0<r.retain-i&&n.next(r.retain-i)}for(var l=new p(o);e.hasNext()||n.hasNext();)if("insert"===n.peekType())l.push(n.next());else if("delete"===e.peekType())l.push(e.next());else{var a=Math.min(e.peekLength(),n.peekLength()),s=e.next(a),u=n.next(a);if("number"==typeof u.retain){var c={};"number"==typeof s.retain?c.retain=a:c.insert=s.insert;var f=y.attributes.compose(s.attributes,u.attributes,"number"==typeof s.retain);if(f&&(c.attributes=f),l.push(c),!n.hasNext()&&d(l.ops[l.ops.length-1],c)){var h=new p(e.rest());return l.concat(h).chop()}}else"number"==typeof u.delete&&"number"==typeof s.retain&&l.push(u)}return l.chop()},p.prototype.concat=function(t){var e=new p(this.ops.slice());return 0<t.ops.length&&(e.push(t.ops[0]),e.ops=e.ops.concat(t.ops.slice(1))),e},p.prototype.diff=function(n,t){if(this.ops===n.ops)return new p;var e=[this,n].map(function(e){return e.map(function(t){if(null!=t.insert)return"string"==typeof t.insert?t.insert:r;throw new Error("diff() called "+(e===n?"on":"with")+" non-document")}).join("")}),i=new p,o=s(e[0],e[1],t),l=y.iterator(this.ops),a=y.iterator(n.ops);return o.forEach(function(t){for(var e=t[1].length;0<e;){var n=0;switch(t[0]){case s.INSERT:n=Math.min(a.peekLength(),e),i.push(a.next(n));break;case s.DELETE:n=Math.min(e,l.peekLength()),l.next(n),i.delete(n);break;case s.EQUAL:n=Math.min(l.peekLength(),a.peekLength(),e);var o=l.next(n),r=a.next(n);d(o.insert,r.insert)?i.retain(n,y.attributes.diff(o.attributes,r.attributes)):i.push(r).delete(n)}e-=n}}),i.chop()},p.prototype.eachLine=function(t,e){e=e||"\n";for(var n=y.iterator(this.ops),o=new p,r=0;n.hasNext();){if("insert"!==n.peekType())return;var i=n.peek(),l=y.length(i)-n.peekLength(),a="string"==typeof i.insert?i.insert.indexOf(e,l)-l:-1;if(a<0)o.push(n.next());else if(0<a)o.push(n.next(a));else{if(!1===t(o,n.next(1).attributes||{},r))return;r+=1,o=new p}}0<o.length()&&t(o,{},r)},p.prototype.transform=function(t,e){if(e=!!e,"number"==typeof t)return this.transformPosition(t,e);for(var n=y.iterator(this.ops),o=y.iterator(t.ops),r=new p;n.hasNext()||o.hasNext();)if("insert"!==n.peekType()||!e&&"insert"===o.peekType())if("insert"===o.peekType())r.push(o.next());else{var i=Math.min(n.peekLength(),o.peekLength()),l=n.next(i),a=o.next(i);if(l.delete)continue;a.delete?r.push(a):r.retain(i,y.attributes.transform(l.attributes,a.attributes,e))}else r.retain(y.length(n.next()));return r.chop()},p.prototype.transformPosition=function(t,e){e=!!e;for(var n=y.iterator(this.ops),o=0;n.hasNext()&&o<=t;){var r=n.peekLength(),i=n.peekType();n.next(),"delete"!==i?("insert"===i&&(o<t||!e)&&(t+=r),o+=r):t-=Math.min(r,t-o)}return t},t.exports=p},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}Object.defineProperty(e,"__esModule",{value:!0});function l(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:l(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var r=function(t,e,n){return e&&c(t.prototype,e),n&&c(t,n),t},i=o(n(8)),a=o(n(0)),s=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(u,a.default.Inline),r(u,[{key:"formatAt",value:function(t,e,n,o){var r;u.compare(this.statics.blotName,n)<0&&a.default.query(n,a.default.Scope.BLOT)?(r=this.isolate(t,e),o&&r.wrap(n,o)):l(u.prototype.__proto__||Object.getPrototypeOf(u.prototype),"formatAt",this).call(this,t,e,n,o)}},{key:"optimize",value:function(t){var e;l(u.prototype.__proto__||Object.getPrototypeOf(u.prototype),"optimize",this).call(this,t),this.parent instanceof u&&0<u.compare(this.statics.blotName,this.parent.statics.blotName)&&(e=this.parent.isolate(this.offset(),this.length()),this.moveChildren(e),e.wrap(this))}}],[{key:"compare",value:function(t,e){var n=u.order.indexOf(t),o=u.order.indexOf(e);return 0<=n||0<=o?n-o:t===e?0:t<e?-1:1}}]),u);function u(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}function c(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}s.allowedChildren=[s,a.default.Embed,i.default],s.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],e.default=s},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function i(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function l(t,n){if((n=(0,_.default)(!0,{container:t,modules:{clipboard:!0,keyboard:!0,history:!0}},n)).theme&&n.theme!==k.DEFAULTS.theme){if(n.theme=k.import("themes/"+n.theme),null==n.theme)throw new Error("Invalid theme "+n.theme+". Did you register it?")}else n.theme=w.default;var e=(0,_.default)(!0,{},n.theme.DEFAULTS);[e,n].forEach(function(e){e.modules=e.modules||{},Object.keys(e.modules).forEach(function(t){!0===e.modules[t]&&(e.modules[t]={})})});var o=Object.keys(e.modules).concat(Object.keys(n.modules)).reduce(function(t,e){var n=k.import("modules/"+e);return null==n?x.error("Cannot load "+e+" module. Are you sure you registered it?"):t[e]=n.DEFAULTS||{},t},{});return null!=n.modules&&n.modules.toolbar&&n.modules.toolbar.constructor!==Object&&(n.modules.toolbar={container:n.modules.toolbar}),n=(0,_.default)(!0,{},k.DEFAULTS,{modules:o},e,n),["bounds","container","scrollingContainer"].forEach(function(t){"string"==typeof n[t]&&(n[t]=document.querySelector(n[t]))}),n.modules=Object.keys(n.modules).reduce(function(t,e){return n.modules[e]&&(t[e]=n.modules[e]),t},{}),n}function u(t,e,n,o){if(this.options.strict&&!this.isEnabled()&&e===y.default.sources.USER)return new p.default;var r,i,l,a=null==n?null:this.getSelection(),s=this.editor.delta,u=t();return null!=a&&(!0===n&&(n=a.index),null==o?a=f(a,u,e):0!==o&&(a=f(a,n,o,e)),this.setSelection(a,y.default.sources.SILENT)),0<u.length()&&(i=[y.default.events.TEXT_CHANGE,u,s,e],(r=this.emitter).emit.apply(r,[y.default.events.EDITOR_CHANGE].concat(i)),e!==y.default.sources.SILENT&&(l=this.emitter).emit.apply(l,i)),u}function c(t,e,n,o,r){var i={};return"number"==typeof t.index&&"number"==typeof t.length?t=(e=("number"!=typeof e&&(r=o,o=n,n=e),t.length),t.index):"number"!=typeof e&&(r=o,o=n,n=e,e=0),"object"===(void 0===n?"undefined":a(n))?(i=n,r=o):"string"==typeof n&&(null!=o?i[n]=o:r=n),[t,e,i,r=r||y.default.sources.API]}function f(t,e,n,o){if(null==t)return null;var r,i,l,a,s=void 0,u=void 0;return u=e instanceof p.default?(r=[t.index,t.index+t.length].map(function(t){return e.transformPosition(t,o!==y.default.sources.USER)}),s=(i=h(r,2))[0],i[1]):(l=[t.index,t.index+t.length].map(function(t){return t<e||t===e&&o===y.default.sources.USER?t:0<=n?t+n:Math.max(e,t+n)}),s=(a=h(l,2))[0],a[1]),new g.Range(s,u-s)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.overload=e.expandConfig=void 0;var a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},h=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],o=!0,r=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(o=(l=a.next()).done)&&(n.push(l.value),!e||n.length!==e);o=!0);}catch(t){r=!0,i=t}finally{try{!o&&a.return&&a.return()}finally{if(r)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},r=function(t,e,n){return e&&s(t.prototype,e),n&&s(t,n),t};function s(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}n(53);var p=o(n(4)),d=o(n(57)),y=o(n(9)),v=o(n(7)),b=o(n(0)),g=n(22),m=o(g),_=o(n(2)),O=o(n(10)),w=o(n(32)),x=(0,O.default)("quill"),k=(r(E,null,[{key:"debug",value:function(t){!0===t&&(t="log"),O.default.level(t)}},{key:"find",value:function(t){return t.__quill||b.default.find(t)}},{key:"import",value:function(t){return null==this.imports[t]&&x.error("Cannot import "+t+". Are you sure it was registered?"),this.imports[t]}},{key:"register",value:function(e,n){var t,o=this,r=2<arguments.length&&void 0!==arguments[2]&&arguments[2];"string"!=typeof e?"string"==typeof(t=e.attrName||e.blotName)?this.register("formats/"+t,e,n):Object.keys(e).forEach(function(t){o.register(t,e[t],n)}):(null==this.imports[e]||r||x.warn("Overwriting "+e+" with",n),this.imports[e]=n,(e.startsWith("blots/")||e.startsWith("formats/"))&&"abstract"!==n.blotName?b.default.register(n):e.startsWith("modules")&&"function"==typeof n.register&&n.register())}}]),r(E,[{key:"addContainer",value:function(t){var e,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;return"string"==typeof t&&(e=t,(t=document.createElement("div")).classList.add(e)),this.container.insertBefore(t,n),t}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(t,e,n){var o=this,r=c(t,e,n),i=h(r,4);return t=i[0],e=i[1],n=i[3],u.call(this,function(){return o.editor.deleteText(t,e)},n,t,-1*e)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var t=!(0<arguments.length&&void 0!==arguments[0])||arguments[0];this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}},{key:"focus",value:function(){var t=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=t,this.scrollIntoView()}},{key:"format",value:function(n,o){var r=this,t=2<arguments.length&&void 0!==arguments[2]?arguments[2]:y.default.sources.API;return u.call(this,function(){var t=r.getSelection(!0),e=new p.default;if(null==t)return e;if(b.default.query(n,b.default.Scope.BLOCK))e=r.editor.formatLine(t.index,t.length,i({},n,o));else{if(0===t.length)return r.selection.format(n,o),e;e=r.editor.formatText(t.index,t.length,i({},n,o))}return r.setSelection(t,y.default.sources.SILENT),e},t)}},{key:"formatLine",value:function(t,e,n,o,r){var i,l=this,a=c(t,e,n,o,r),s=h(a,4);return t=s[0],e=s[1],i=s[2],r=s[3],u.call(this,function(){return l.editor.formatLine(t,e,i)},r,t,0)}},{key:"formatText",value:function(t,e,n,o,r){var i,l=this,a=c(t,e,n,o,r),s=h(a,4);return t=s[0],e=s[1],i=s[2],r=s[3],u.call(this,function(){return l.editor.formatText(t,e,i)},r,t,0)}},{key:"getBounds",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,n="number"==typeof t?this.selection.getBounds(t,e):this.selection.getBounds(t.index,t.length),o=this.container.getBoundingClientRect();return{bottom:n.bottom-o.top,height:n.height,left:n.left-o.left,right:n.right-o.left,top:n.top-o.top,width:n.width}}},{key:"getContents",value:function(){var t=c(n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,o=1<arguments.length&&void 0!==arguments[1]?arguments[1]:this.getLength()-n),e=h(t,2),n=e[0],o=e[1];return this.editor.getContents(n,o)}},{key:"getFormat",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0),e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;return"number"==typeof t?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}},{key:"getIndex",value:function(t){return t.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(t){return this.scroll.leaf(t)}},{key:"getLine",value:function(t){return this.scroll.line(t)}},{key:"getLines",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!=typeof t?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}},{key:"getModule",value:function(t){return this.theme.modules[t]}},{key:"getSelection",value:function(){return 0<arguments.length&&void 0!==arguments[0]&&arguments[0]&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var t=c(n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,o=1<arguments.length&&void 0!==arguments[1]?arguments[1]:this.getLength()-n),e=h(t,2),n=e[0],o=e[1];return this.editor.getText(n,o)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(t,e,n){var o=this,r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:E.sources.API;return u.call(this,function(){return o.editor.insertEmbed(t,e,n)},r,t)}},{key:"insertText",value:function(t,e,n,o,r){var i,l=this,a=c(t,0,n,o,r),s=h(a,4);return t=s[0],i=s[2],r=s[3],u.call(this,function(){return l.editor.insertText(t,e,i)},r,t,e.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(t,e,n){this.clipboard.dangerouslyPasteHTML(t,e,n)}},{key:"removeFormat",value:function(t,e,n){var o=this,r=c(t,e,n),i=h(r,4);return t=i[0],e=i[1],n=i[3],u.call(this,function(){return o.editor.removeFormat(t,e)},n,t)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(r){var i=this,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:y.default.sources.API;return u.call(this,function(){r=new p.default(r);var t=i.getLength(),e=i.editor.deleteText(0,t),n=i.editor.applyDelta(r),o=n.ops[n.ops.length-1];return null!=o&&"string"==typeof o.insert&&"\n"===o.insert[o.insert.length-1]&&(i.editor.deleteText(i.getLength()-1,1),n.delete(1)),e.compose(n)},t)}},{key:"setSelection",value:function(t,e,n){var o,r;null==t?this.selection.setRange(null,e||E.sources.API):(o=c(t,e,n),t=(r=h(o,4))[0],e=r[1],n=r[3],this.selection.setRange(new g.Range(t,e),n),n!==y.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer))}},{key:"setText",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:y.default.sources.API,n=(new p.default).insert(t);return this.setContents(n,e)}},{key:"update",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:y.default.sources.USER,e=this.scroll.update(t);return this.selection.update(t),e}},{key:"updateContents",value:function(t){var e=this,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:y.default.sources.API;return u.call(this,function(){return t=new p.default(t),e.editor.applyDelta(t,n)},n,!0)}}]),E);function E(t){var r=this,e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,E),this.options=l(t,e),this.container=this.options.container,null==this.container)return x.error("Invalid Quill container",t);this.options.debug&&E.debug(this.options.debug);var n=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",(this.container.__quill=this).root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new y.default,this.scroll=b.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new d.default(this.scroll),this.selection=new m.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(y.default.events.EDITOR_CHANGE,function(t){t===y.default.events.TEXT_CHANGE&&r.root.classList.toggle("ql-blank",r.editor.isBlank())}),this.emitter.on(y.default.events.SCROLL_UPDATE,function(t,e){var n=r.selection.lastRange,o=n&&0===n.length?n.index:void 0;u.call(r,function(){return r.editor.update(null,e,o)},t)});var o=this.clipboard.convert("<div class='ql-editor' style=\"white-space: normal;\">"+n+"<p><br></p></div>");this.setContents(o),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}k.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},k.events=y.default.events,k.sources=y.default.sources,k.version="1.3.7",k.imports={delta:p.default,parchment:b.default,"core/module":v.default,"core/theme":w.default},e.expandConfig=l,e.overload=c,e.default=k},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});function o(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")})(this,o),this.quill=t,this.options=e}o.DEFAULTS={},e.default=o},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=n(0),r=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(i,(o&&o.__esModule?o:{default:o}).default.Text),i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}e.default=r},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}Object.defineProperty(e,"__esModule",{value:!0});var r=function(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),t},i=o(n(58)),l=(0,o(n(10)).default)("quill:events");function a(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}["selectionchange","mousedown","mouseup","click"].forEach(function(t){document.addEventListener(t,function(){for(var t=arguments.length,n=Array(t),e=0;e<t;e++)n[e]=arguments[e];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(t){var e;t.__quill&&t.__quill.emitter&&(e=t.__quill.emitter).handleDOM.apply(e,n)})})});var s=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(u,i.default),r(u,[{key:"emit",value:function(){l.log.apply(l,arguments),function t(e,n,o){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,n);if(void 0===r){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,o)}if("value"in r)return r.value;var l=r.get;return void 0!==l?l.call(o):void 0}(u.prototype.__proto__||Object.getPrototypeOf(u.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(o){for(var t=arguments.length,r=Array(1<t?t-1:0),e=1;e<t;e++)r[e-1]=arguments[e];(this.listeners[o.type]||[]).forEach(function(t){var e=t.node,n=t.handler;o.target!==e&&!e.contains(o.target)||n.apply(void 0,[o].concat(r))})}},{key:"listenDOM",value:function(t,e,n){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push({node:e,handler:n})}}]),u);function u(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u);var t=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(u.__proto__||Object.getPrototypeOf(u)).call(this));return t.listeners={},t.on("error",l.error),t}s.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},s.sources={API:"api",SILENT:"silent",USER:"user"},e.default=s},function(t,e,n){"use strict";function o(t){if(i.indexOf(t)<=i.indexOf(l)){for(var e,n=arguments.length,o=Array(1<n?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];(e=console)[t].apply(e,o)}}function r(n){return i.reduce(function(t,e){return t[e]=o.bind(console,e,n),t},{})}Object.defineProperty(e,"__esModule",{value:!0});var i=["error","warn","log","info"],l="warn";o.level=r.level=function(t){l=t},e.default=r},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(1),o=(i.keys=function(t){return[].map.call(t.attributes,function(t){return t.name})},i.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(t.setAttribute(this.keyName,e),!0)},i.prototype.canAdd=function(t,e){return null!=r.query(t,r.Scope.BLOT&(this.scope|r.Scope.TYPE))&&(null==this.whitelist||("string"==typeof e?-1<this.whitelist.indexOf(e.replace(/["']/g,"")):-1<this.whitelist.indexOf(e)))},i.prototype.remove=function(t){t.removeAttribute(this.keyName)},i.prototype.value=function(t){var e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""},i);function i(t,e,n){void 0===n&&(n={}),this.attrName=t,this.keyName=e;var o=r.Scope.TYPE&r.Scope.ATTRIBUTE;null!=n.scope?this.scope=n.scope&r.Scope.LEVEL|o:this.scope=r.Scope.ATTRIBUTE,null!=n.whitelist&&(this.whitelist=n.whitelist)}e.default=o},function(t,e,n){function a(t){return null==t}function s(t){return t&&"object"==typeof t&&"number"==typeof t.length&&"function"==typeof t.copy&&"function"==typeof t.slice&&!(0<t.length&&"number"!=typeof t[0])}var u=Array.prototype.slice,c=n(55),f=n(56),h=t.exports=function(t,e,n){return n=n||{},t===e||(t instanceof Date&&e instanceof Date?t.getTime()===e.getTime():!t||!e||"object"!=typeof t&&"object"!=typeof e?n.strict?t===e:t==e:function(t,e,n){var o,r;if(a(t)||a(e))return!1;if(t.prototype!==e.prototype)return!1;if(f(t))return!!f(e)&&(t=u.call(t),e=u.call(e),h(t,e,n));if(s(t)){if(!s(e))return!1;if(t.length!==e.length)return!1;for(o=0;o<t.length;o++)if(t[o]!==e[o])return!1;return!0}try{var i=c(t),l=c(e)}catch(t){return!1}if(i.length!=l.length)return!1;for(i.sort(),l.sort(),o=i.length-1;0<=o;o--)if(i[o]!=l[o])return!1;for(o=i.length-1;0<=o;o--)if(r=i[o],!h(t[r],e[r],n))return!1;return typeof t==typeof e}(t,e,n))}},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function l(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.Code=void 0;function a(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:a(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var s=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],o=!0,r=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(o=(l=a.next()).done)&&(n.push(l.value),!e||n.length!==e);o=!0);}catch(t){r=!0,i=t}finally{try{!o&&a.return&&a.return()}finally{if(r)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},u=function(t,e,n){return e&&b(t.prototype,e),n&&b(t,n),t},c=o(n(4)),f=o(n(0)),h=o(n(3)),p=o(n(5)),d=o(n(8)),y=(l(v,p.default),v);function v(){return r(this,v),i(this,(v.__proto__||Object.getPrototypeOf(v)).apply(this,arguments))}function b(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}y.blotName="code",y.tagName="CODE";var g=(l(m,h.default),u(m,[{key:"delta",value:function(){var n=this,t=this.domNode.textContent;return t.endsWith("\n")&&(t=t.slice(0,-1)),t.split("\n").reduce(function(t,e){return t.insert(e).insert("\n",n.formats())},new c.default)}},{key:"format",value:function(t,e){var n,o;t===this.statics.blotName&&e||(n=this.descendant(d.default,this.length()-1),null!=(o=s(n,1)[0])&&o.deleteAt(o.length()-1,1),a(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"format",this).call(this,t,e))}},{key:"formatAt",value:function(t,e,n,o){var r,i,l,a,s;0===e||null==f.default.query(n,f.default.Scope.BLOCK)||n===this.statics.blotName&&o===this.statics.formats(this.domNode)||(r=this.newlineIndex(t))<0||t+e<=r||(l=r-(i=this.newlineIndex(t,!0)+1)+1,s=(a=this.isolate(i,l)).next,a.format(n,o),s instanceof m&&s.formatAt(0,t-i+e-l,n,o))}},{key:"insertAt",value:function(t,e,n){var o,r,i,l;null==n&&(o=this.descendant(d.default,t),i=(r=s(o,2))[0],l=r[1],i.insertAt(l,e))}},{key:"length",value:function(){var t=this.domNode.textContent.length;return this.domNode.textContent.endsWith("\n")?t:t+1}},{key:"newlineIndex",value:function(t){if(1<arguments.length&&void 0!==arguments[1]&&arguments[1])return this.domNode.textContent.slice(0,t).lastIndexOf("\n");var e=this.domNode.textContent.slice(t).indexOf("\n");return-1<e?t+e:-1}},{key:"optimize",value:function(t){this.domNode.textContent.endsWith("\n")||this.appendChild(f.default.create("text","\n")),a(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"optimize",this).call(this,t);var e=this.next;null!=e&&e.prev===this&&e.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===e.statics.formats(e.domNode)&&(e.optimize(t),e.moveChildren(this),e.remove())}},{key:"replace",value:function(t){a(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"replace",this).call(this,t),[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(t){var e=f.default.find(t);null==e?t.parentNode.removeChild(t):e instanceof f.default.Embed?e.remove():e.unwrap()})}}],[{key:"create",value:function(t){var e=a(m.__proto__||Object.getPrototypeOf(m),"create",this).call(this,t);return e.setAttribute("spellcheck",!1),e}},{key:"formats",value:function(){return!0}}]),m);function m(){return r(this,m),i(this,(m.__proto__||Object.getPrototypeOf(m)).apply(this,arguments))}g.blotName="code-block",g.tagName="PRE",g.TAB="  ",e.Code=y,e.default=g},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=function(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),t},r=n(0),i=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(l,(r&&r.__esModule?r:{default:r}).default.Embed),o(l,[{key:"insertInto",value:function(t,e){0===t.children.length?function t(e,n,o){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,n);if(void 0===r){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,o)}if("value"in r)return r.value;var l=r.get;return void 0!==l?l.call(o):void 0}(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"insertInto",this).call(this,t,e):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),l);function l(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,l),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}function a(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}i.blotName="break",i.tagName="BR",e.default=i},function(t,e,n){"use strict";function o(t,e){var n=document.createElement("a");n.href=t;var o=n.href.slice(0,n.href.indexOf(":"));return-1<e.indexOf(o)}Object.defineProperty(e,"__esModule",{value:!0}),e.sanitize=e.default=void 0;function l(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:l(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var r=function(t,e,n){return e&&u(t.prototype,e),n&&u(t,n),t},i=n(5),a=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(s,(i&&i.__esModule?i:{default:i}).default),r(s,[{key:"format",value:function(t,e){if(t!==this.statics.blotName||!e)return l(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"format",this).call(this,t,e);e=this.constructor.sanitize(e),this.domNode.setAttribute("href",e)}}],[{key:"create",value:function(t){var e=l(s.__proto__||Object.getPrototypeOf(s),"create",this).call(this,t);return t=this.sanitize(t),e.setAttribute("href",t),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e}},{key:"formats",value:function(t){return t.getAttribute("href")}},{key:"sanitize",value:function(t){return o(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}}]),s);function s(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,s),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}function u(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}a.blotName="link",a.tagName="A",a.SANITIZED_URL="about:blank",a.PROTOCOL_WHITELIST=["http","https","mailto","tel"],e.default=a,e.sanitize=o},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function r(t,e){t.setAttribute(e,!("true"===t.getAttribute(e)))}Object.defineProperty(e,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l=function(t,e,n){return e&&h(t.prototype,e),n&&h(t,n),t},a=o(n(25)),s=o(n(106)),u=0,c=(l(f,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded"),r(this.label,"aria-expanded"),r(this.options,"aria-hidden")}},{key:"buildItem",value:function(t){var e=this,n=document.createElement("span");return n.tabIndex="0",n.setAttribute("role","button"),n.classList.add("ql-picker-item"),t.hasAttribute("value")&&n.setAttribute("data-value",t.getAttribute("value")),t.textContent&&n.setAttribute("data-label",t.textContent),n.addEventListener("click",function(){e.selectItem(n,!0)}),n.addEventListener("keydown",function(t){switch(t.keyCode){case a.default.keys.ENTER:e.selectItem(n,!0),t.preventDefault();break;case a.default.keys.ESCAPE:e.escape(),t.preventDefault()}}),n}},{key:"buildLabel",value:function(){var t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML=s.default,t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}},{key:"buildOptions",value:function(){var n=this,o=document.createElement("span");o.classList.add("ql-picker-options"),o.setAttribute("aria-hidden","true"),o.tabIndex="-1",o.id="ql-picker-options-"+u,u+=1,this.label.setAttribute("aria-controls",o.id),this.options=o,[].slice.call(this.select.options).forEach(function(t){var e=n.buildItem(t);o.appendChild(e),!0===t.selected&&n.selectItem(e)}),this.container.appendChild(o)}},{key:"buildPicker",value:function(){var e=this;[].slice.call(this.select.attributes).forEach(function(t){e.container.setAttribute(t.name,t.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}},{key:"escape",value:function(){var t=this;this.close(),setTimeout(function(){return t.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(t){var e,n=1<arguments.length&&void 0!==arguments[1]&&arguments[1],o=this.container.querySelector(".ql-selected");t!==o&&(null!=o&&o.classList.remove("ql-selected"),null!=t&&(t.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(t.parentNode.children,t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),n))&&("function"==typeof Event?this.select.dispatchEvent(new Event("change")):"object"===("undefined"==typeof Event?"undefined":i(Event))&&((e=document.createEvent("Event")).initEvent("change",!0,!0),this.select.dispatchEvent(e)),this.close())}},{key:"update",value:function(){var t,e=void 0;-1<this.select.selectedIndex?(t=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex],e=this.select.options[this.select.selectedIndex],this.selectItem(t)):this.selectItem(null);var n=null!=e&&e!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",n)}}]),f);function f(t){var e=this;(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")})(this,f),this.select=t,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",function(){e.togglePicker()}),this.label.addEventListener("keydown",function(t){switch(t.keyCode){case a.default.keys.ENTER:e.togglePicker();break;case a.default.keys.ESCAPE:e.escape(),t.preventDefault()}}),this.select.addEventListener("change",this.update.bind(this))}function h(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}e.default=c},function(t,e,n){"use strict";function i(e){var n=u.find(e);if(null==n)try{n=u.create(e)}catch(t){n=u.create(u.Scope.INLINE),[].slice.call(e.childNodes).forEach(function(t){n.domNode.appendChild(t)}),e.parentNode&&e.parentNode.replaceChild(n.domNode,e),n.attach()}return n}var o,r=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var l,a=n(47),s=n(27),u=n(1),c=(l=s.default,r(f,l),f.prototype.appendChild=function(t){this.insertBefore(t)},f.prototype.attach=function(){l.prototype.attach.call(this),this.children.forEach(function(t){t.attach()})},f.prototype.build=function(){var n=this;this.children=new a.default,[].slice.call(this.domNode.childNodes).reverse().forEach(function(t){try{var e=i(t);n.insertBefore(e,n.children.head||void 0)}catch(t){if(t instanceof u.ParchmentError)return;throw t}})},f.prototype.deleteAt=function(t,e){if(0===t&&e===this.length())return this.remove();this.children.forEachAt(t,e,function(t,e,n){t.deleteAt(e,n)})},f.prototype.descendant=function(t,e){var n=this.children.find(e),o=n[0],r=n[1];return null==t.blotName&&t(o)||null!=t.blotName&&o instanceof t?[o,r]:o instanceof f?o.descendant(t,r):[null,-1]},f.prototype.descendants=function(o,t,e){void 0===t&&(t=0),void 0===e&&(e=Number.MAX_VALUE);var r=[],i=e;return this.children.forEachAt(t,e,function(t,e,n){(null==o.blotName&&o(t)||null!=o.blotName&&t instanceof o)&&r.push(t),t instanceof f&&(r=r.concat(t.descendants(o,e,i))),i-=n}),r},f.prototype.detach=function(){this.children.forEach(function(t){t.detach()}),l.prototype.detach.call(this)},f.prototype.formatAt=function(t,e,o,r){this.children.forEachAt(t,e,function(t,e,n){t.formatAt(e,n,o,r)})},f.prototype.insertAt=function(t,e,n){var o,r=this.children.find(t),i=r[0],l=r[1];i?i.insertAt(l,e,n):(o=null==n?u.create("text",e):u.create(e,n),this.appendChild(o))},f.prototype.insertBefore=function(e,t){if(null!=this.statics.allowedChildren&&!this.statics.allowedChildren.some(function(t){return e instanceof t}))throw new u.ParchmentError("Cannot insert "+e.statics.blotName+" into "+this.statics.blotName);e.insertInto(this,t)},f.prototype.length=function(){return this.children.reduce(function(t,e){return t+e.length()},0)},f.prototype.moveChildren=function(e,n){this.children.forEach(function(t){e.insertBefore(t,n)})},f.prototype.optimize=function(t){var e;l.prototype.optimize.call(this,t),0===this.children.length&&(null!=this.statics.defaultChild?(e=u.create(this.statics.defaultChild),this.appendChild(e),e.optimize(t)):this.remove())},f.prototype.path=function(t,e){void 0===e&&(e=!1);var n=this.children.find(t,e),o=n[0],r=n[1],i=[[this,t]];return o instanceof f?i.concat(o.path(r,e)):(null!=o&&i.push([o,r]),i)},f.prototype.removeChild=function(t){this.children.remove(t)},f.prototype.replace=function(t){t instanceof f&&t.moveChildren(this),l.prototype.replace.call(this,t)},f.prototype.split=function(t,o){if(void 0===o&&(o=!1),!o){if(0===t)return this;if(t===this.length())return this.next}var r=this.clone();return this.parent.insertBefore(r,this.next),this.children.forEachAt(t,this.length(),function(t,e,n){t=t.split(e,o),r.appendChild(t)}),r},f.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},f.prototype.update=function(t,e){var o=this,n=[],r=[];t.forEach(function(t){t.target===o.domNode&&"childList"===t.type&&(n.push.apply(n,t.addedNodes),r.push.apply(r,t.removedNodes))}),r.forEach(function(t){var e;null!=t.parentNode&&"IFRAME"!==t.tagName&&document.body.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY||null!=(e=u.find(t))&&(null!=e.domNode.parentNode&&e.domNode.parentNode!==o.domNode||e.detach())}),n.filter(function(t){return t.parentNode==o.domNode}).sort(function(t,e){return t===e?0:t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(t){var e=null;null!=t.nextSibling&&(e=u.find(t.nextSibling));var n=i(t);n.next==e&&null!=n.next||(null!=n.parent&&n.parent.removeChild(o),o.insertBefore(n,e||void 0))})},f);function f(t){var e=l.call(this,t)||this;return e.build(),e}e.default=c},function(t,e,n){"use strict";var o,r=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i,l=n(11),a=n(28),s=n(17),u=n(1),c=(i=s.default,r(f,i),f.formats=function(t){return"string"==typeof this.tagName||(Array.isArray(this.tagName)?t.tagName.toLowerCase():void 0)},f.prototype.format=function(t,e){var n=u.query(t);n instanceof l.default?this.attributes.attribute(n,e):e&&(null==n||t===this.statics.blotName&&this.formats()[t]===e||this.replaceWith(t,e))},f.prototype.formats=function(){var t=this.attributes.values(),e=this.statics.formats(this.domNode);return null!=e&&(t[this.statics.blotName]=e),t},f.prototype.replaceWith=function(t,e){var n=i.prototype.replaceWith.call(this,t,e);return this.attributes.copy(n),n},f.prototype.update=function(t,e){var n=this;i.prototype.update.call(this,t,e),t.some(function(t){return t.target===n.domNode&&"attributes"===t.type})&&this.attributes.build()},f.prototype.wrap=function(t,e){var n=i.prototype.wrap.call(this,t,e);return n instanceof f&&n.statics.scope===this.statics.scope&&this.attributes.move(n),n},f);function f(t){var e=i.call(this,t)||this;return e.attributes=new a.default(e.domNode),e}e.default=c},function(t,e,n){"use strict";var o,r=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i,l=n(27),a=n(1),s=(i=l.default,r(u,i),u.value=function(t){return!0},u.prototype.index=function(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1},u.prototype.position=function(t,e){var n=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return 0<t&&(n+=1),[this.parent.domNode,n]},u.prototype.value=function(){var t={};return t[this.statics.blotName]=this.statics.value(this.domNode)||!0,t},u.scope=a.Scope.INLINE_BLOT,u);function u(){return null!==i&&i.apply(this,arguments)||this}e.default=s},function(t,e,n){function o(t){this.ops=t,this.index=0,this.offset=0}var r=n(12),i=n(2),l={attributes:{compose:function(t,e,n){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});var o=i(!0,{},e);for(var r in n||(o=Object.keys(o).reduce(function(t,e){return null!=o[e]&&(t[e]=o[e]),t},{})),t)void 0!==t[r]&&void 0===e[r]&&(o[r]=t[r]);return 0<Object.keys(o).length?o:void 0},diff:function(n,o){"object"!=typeof n&&(n={}),"object"!=typeof o&&(o={});var t=Object.keys(n).concat(Object.keys(o)).reduce(function(t,e){return r(n[e],o[e])||(t[e]=void 0===o[e]?null:o[e]),t},{});return 0<Object.keys(t).length?t:void 0},transform:function(n,o,t){if("object"!=typeof n)return o;if("object"==typeof o){if(!t)return o;var e=Object.keys(o).reduce(function(t,e){return void 0===n[e]&&(t[e]=o[e]),t},{});return 0<Object.keys(e).length?e:void 0}}},iterator:function(t){return new o(t)},length:function(t){return"number"==typeof t.delete?t.delete:"number"==typeof t.retain?t.retain:"string"==typeof t.insert?t.insert.length:1}};o.prototype.hasNext=function(){return this.peekLength()<1/0},o.prototype.next=function(t){t=t||1/0;var e=this.ops[this.index];if(e){var n=this.offset,o=l.length(e);if(o-n<=t?(t=o-n,this.index+=1,this.offset=0):this.offset+=t,"number"==typeof e.delete)return{delete:t};var r={};return e.attributes&&(r.attributes=e.attributes),"number"==typeof e.retain?r.retain=t:"string"==typeof e.insert?r.insert=e.insert.substr(n,t):r.insert=e.insert,r}return{retain:1/0}},o.prototype.peek=function(){return this.ops[this.index]},o.prototype.peekLength=function(){return this.ops[this.index]?l.length(this.ops[this.index])-this.offset:1/0},o.prototype.peekType=function(){return this.ops[this.index]?"number"==typeof this.ops[this.index].delete?"delete":"number"==typeof this.ops[this.index].retain?"retain":"insert":"retain"},o.prototype.rest=function(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);var t=this.offset,e=this.index,n=this.next(),o=this.ops.slice(this.index);return this.offset=t,this.index=e,[n].concat(o)}return[]},t.exports=l},function(t,e){var n=function(){"use strict";function m(t,e){return null!=e&&t instanceof e}function _(t,p,e,d,y){"object"==typeof p&&(e=p.depth,d=p.prototype,y=p.includeNonEnumerable,p=p.circular);var v=[],b=[],g="undefined"!=typeof Buffer;return void 0===p&&(p=!0),void 0===e&&(e=1/0),function r(t,i){if(null===t)return null;if(0===i)return t;var l,e,n;if("object"!=typeof t)return t;if(m(t,w))l=new w;else if(m(t,x))l=new x;else if(m(t,k))l=new k(function(e,n){t.then(function(t){e(r(t,i-1))},function(t){n(r(t,i-1))})});else if(_.__isArray(t))l=[];else if(_.__isRegExp(t))l=new RegExp(t.source,O(t)),t.lastIndex&&(l.lastIndex=t.lastIndex);else if(_.__isDate(t))l=new Date(t.getTime());else{if(g&&Buffer.isBuffer(t))return l=Buffer.allocUnsafe?Buffer.allocUnsafe(t.length):new Buffer(t.length),t.copy(l),l;m(t,Error)?l=Object.create(t):void 0===d?(e=Object.getPrototypeOf(t),l=Object.create(e)):(l=Object.create(d),e=d)}if(p){var o=v.indexOf(t);if(-1!=o)return b[o];v.push(t),b.push(l)}for(var a in m(t,w)&&t.forEach(function(t,e){var n=r(e,i-1),o=r(t,i-1);l.set(n,o)}),m(t,x)&&t.forEach(function(t){var e=r(t,i-1);l.add(e)}),t)e&&(n=Object.getOwnPropertyDescriptor(e,a)),n&&null==n.set||(l[a]=r(t[a],i-1));if(Object.getOwnPropertySymbols)for(var s=Object.getOwnPropertySymbols(t),a=0;a<s.length;a++){var u=s[a];(f=Object.getOwnPropertyDescriptor(t,u))&&!f.enumerable&&!y||(l[u]=r(t[u],i-1),f.enumerable||Object.defineProperty(l,u,{enumerable:!1}))}if(y)for(var c=Object.getOwnPropertyNames(t),a=0;a<c.length;a++){var f,h=c[a];(f=Object.getOwnPropertyDescriptor(t,h))&&f.enumerable||(l[h]=r(t[h],i-1),Object.defineProperty(l,h,{enumerable:!1}))}return l}(t,e)}function e(t){return Object.prototype.toString.call(t)}function O(t){var e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),e}var w,x,k;try{w=Map}catch(m){w=function(){}}try{x=Set}catch(m){x=function(){}}try{k=Promise}catch(m){k=function(){}}return _.clonePrototype=function(t){if(null===t)return null;function e(){}return e.prototype=t,new e},_.__objToStr=e,_.__isDate=function(t){return"object"==typeof t&&"[object Date]"===e(t)},_.__isArray=function(t){return"object"==typeof t&&"[object Array]"===e(t)},_.__isRegExp=function(t){return"object"==typeof t&&"[object RegExp]"===e(t)},_.__getRegExpFlags=O,_}();"object"==typeof t&&t.exports&&(t.exports=n)},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function i(t){if(Array.isArray(t)){for(var e=0,n=Array(t.length);e<t.length;e++)n[e]=t[e];return n}return Array.from(t)}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function l(t,e){try{e.parentNode}catch(t){return}return e instanceof Text&&(e=e.parentNode),t.contains(e)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.Range=void 0;var b=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],o=!0,r=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(o=(l=a.next()).done)&&(n.push(l.value),!e||n.length!==e);o=!0);}catch(t){r=!0,i=t}finally{try{!o&&a.return&&a.return()}finally{if(r)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},a=function(t,e,n){return e&&v(t.prototype,e),n&&v(t,n),t},s=o(n(0)),u=o(n(21)),c=o(n(12)),f=o(n(9)),h=(0,o(n(10)).default)("quill:selection"),p=function t(e){var n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;r(this,t),this.index=e,this.length=n},d=(a(y,[{key:"handleComposition",value:function(){var e=this;this.root.addEventListener("compositionstart",function(){e.composing=!0}),this.root.addEventListener("compositionend",function(){if(e.composing=!1,e.cursor.parent){var t=e.cursor.restore();if(!t)return;setTimeout(function(){e.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)},1)}})}},{key:"handleDragging",value:function(){var t=this;this.emitter.listenDOM("mousedown",document.body,function(){t.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,function(){t.mouseDown=!1,t.update(f.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(t,e){if(null==this.scroll.whitelist||this.scroll.whitelist[t]){this.scroll.update();var n=this.getNativeRange();if(null!=n&&n.native.collapsed&&!s.default.query(t,s.default.Scope.BLOCK)){if(n.start.node!==this.cursor.textNode){var o,r=s.default.find(n.start.node,!1);if(null==r)return;r instanceof s.default.Leaf?(o=r.split(n.start.offset),r.parent.insertBefore(this.cursor,o)):r.insertBefore(this.cursor,n.start.node),this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,n=this.scroll.length();t=Math.min(t,n-1),e=Math.min(t+e,n-1)-t;var o=void 0,r=this.scroll.leaf(t),i=b(r,2),l=i[0],a=i[1];if(null==l)return null;var s=l.position(a,!0),u=b(s,2);o=u[0],a=u[1];var c=document.createRange();if(0<e){c.setStart(o,a);var f=this.scroll.leaf(t+e),h=b(f,2),l=h[0],a=h[1];if(null==l)return null;var p=l.position(a,!0),d=b(p,2),o=d[0];return a=d[1],c.setEnd(o,a),c.getBoundingClientRect()}var y="left",v=void 0;return o instanceof Text?(a<o.data.length?(c.setStart(o,a),c.setEnd(o,a+1)):(c.setStart(o,a-1),c.setEnd(o,a),y="right"),v=c.getBoundingClientRect()):(v=l.domNode.getBoundingClientRect(),0<a&&(y="right")),{bottom:v.top+v.height,height:v.height,left:v[y],right:v[y],top:v.top,width:0}}},{key:"getNativeRange",value:function(){var t=document.getSelection();if(null==t||t.rangeCount<=0)return null;var e=t.getRangeAt(0);if(null==e)return null;var n=this.normalizeNative(e);return h.info("getNativeRange",n),n}},{key:"getRange",value:function(){var t=this.getNativeRange();return null==t?[null,null]:[this.normalizedToRange(t),t]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(t){var l=this,e=[[t.start.node,t.start.offset]];t.native.collapsed||e.push([t.end.node,t.end.offset]);var n=e.map(function(t){var e=b(t,2),n=e[0],o=e[1],r=s.default.find(n,!0),i=r.offset(l.scroll);return 0===o?i:r instanceof s.default.Container?i+r.length():i+r.index(n,o)}),o=Math.min(Math.max.apply(Math,i(n)),this.scroll.length()-1),r=Math.min.apply(Math,[o].concat(i(n)));return new p(r,o-r)}},{key:"normalizeNative",value:function(t){if(!l(this.root,t.startContainer)||!t.collapsed&&!l(this.root,t.endContainer))return null;var e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach(function(t){for(var e=t.node,n=t.offset;!(e instanceof Text)&&0<e.childNodes.length;)if(e.childNodes.length>n)e=e.childNodes[n],n=0;else{if(e.childNodes.length!==n)break;n=(e=e.lastChild)instanceof Text?e.data.length:e.childNodes.length+1}t.node=e,t.offset=n}),e}},{key:"rangeToNative",value:function(t){var u=this,e=t.collapsed?[t.index]:[t.index,t.index+t.length],c=[],f=this.scroll.length();return e.forEach(function(t,e){t=Math.min(f-1,t);var n=u.scroll.leaf(t),o=b(n,2),r=o[0],i=o[1],l=r.position(i,0!==e),a=b(l,2),s=a[0],i=a[1];c.push(s,i)}),c.length<2&&(c=c.concat(c)),c}},{key:"scrollIntoView",value:function(t){var e,n,o,r,i,l,a,s=this.lastRange;null==s||null!=(e=this.getBounds(s.index,s.length))&&(n=this.scroll.length()-1,o=this.scroll.line(Math.min(s.index,n)),l=r=b(o,1)[0],0<s.length&&(i=this.scroll.line(Math.min(s.index+s.length,n)),l=b(i,1)[0]),null!=r&&null!=l&&(a=t.getBoundingClientRect(),e.top<a.top?t.scrollTop-=a.top-e.top:e.bottom>a.bottom&&(t.scrollTop+=e.bottom-a.bottom)))}},{key:"setNativeRange",value:function(t,e){var n,o,r,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:t,l=3<arguments.length&&void 0!==arguments[3]?arguments[3]:e,a=4<arguments.length&&void 0!==arguments[4]&&arguments[4];h.info("setNativeRange",t,e,i,l),(null==t||null!=this.root.parentNode&&null!=t.parentNode&&null!=i.parentNode)&&null!=(n=document.getSelection())&&(null!=t?(this.hasFocus()||this.root.focus(),null!=(o=(this.getNativeRange()||{}).native)&&!a&&t===o.startContainer&&e===o.startOffset&&i===o.endContainer&&l===o.endOffset||("BR"==t.tagName&&(e=[].indexOf.call(t.parentNode.childNodes,t),t=t.parentNode),"BR"==i.tagName&&(l=[].indexOf.call(i.parentNode.childNodes,i),i=i.parentNode),(r=document.createRange()).setStart(t,e),r.setEnd(i,l),n.removeAllRanges(),n.addRange(r))):(n.removeAllRanges(),this.root.blur(),document.body.focus()))}},{key:"setRange",value:function(t){var e,n=1<arguments.length&&void 0!==arguments[1]&&arguments[1],o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:f.default.sources.API;"string"==typeof n&&(o=n,n=!1),h.info("setRange",t),null!=t?(e=this.rangeToNative(t),this.setNativeRange.apply(this,i(e).concat([n]))):this.setNativeRange(null),this.update(o)}},{key:"update",value:function(){var t,e,n,o=0<arguments.length&&void 0!==arguments[0]?arguments[0]:f.default.sources.USER,r=this.lastRange,i=this.getRange(),l=b(i,2),a=l[0],s=l[1];this.lastRange=a,null!=this.lastRange&&(this.savedRange=this.lastRange),(0,c.default)(r,this.lastRange)||(!this.composing&&null!=s&&s.native.collapsed&&s.start.node!==this.cursor.textNode&&this.cursor.restore(),e=[f.default.events.SELECTION_CHANGE,(0,u.default)(this.lastRange),(0,u.default)(r),o],(t=this.emitter).emit.apply(t,[f.default.events.EDITOR_CHANGE].concat(e)),o!==f.default.sources.SILENT&&(n=this.emitter).emit.apply(n,e))}}]),y);function y(t,e){var a=this;r(this,y),this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=s.default.create("cursor",this),this.lastRange=this.savedRange=new p(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,function(){a.mouseDown||setTimeout(a.update.bind(a,f.default.sources.USER),1)}),this.emitter.on(f.default.events.EDITOR_CHANGE,function(t,e){t===f.default.events.TEXT_CHANGE&&0<e.length()&&a.update(f.default.sources.SILENT)}),this.emitter.on(f.default.events.SCROLL_BEFORE_UPDATE,function(){var t;!a.hasFocus()||null!=(t=a.getNativeRange())&&t.start.node!==a.cursor.textNode&&a.emitter.once(f.default.events.SCROLL_UPDATE,function(){try{a.setNativeRange(t.start.node,t.start.offset,t.end.node,t.end.offset)}catch(t){}})}),this.emitter.on(f.default.events.SCROLL_OPTIMIZE,function(t,e){var n,o,r,i,l;e.range&&(o=(n=e.range).startNode,r=n.startOffset,i=n.endNode,l=n.endOffset,a.setNativeRange(o,r,i,l))}),this.update(f.default.sources.SILENT)}function v(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}e.Range=p,e.default=d},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}Object.defineProperty(e,"__esModule",{value:!0});var r=o(n(0)),i=n(3),l=o(i),a=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(s,r.default.Container),s);function s(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,s),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}a.allowedChildren=[l.default,i.BlockEmbed,a],e.default=a},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ColorStyle=e.ColorClass=e.ColorAttributor=void 0;var o=function(t,e,n){return e&&c(t.prototype,e),n&&c(t,n),t},r=n(0),i=r&&r.__esModule?r:{default:r},l=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(u,i.default.Attributor.Style),o(u,[{key:"value",value:function(t){var e=function t(e,n,o){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,n);if(void 0===r){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,o)}if("value"in r)return r.value;var l=r.get;return void 0!==l?l.call(o):void 0}(u.prototype.__proto__||Object.getPrototypeOf(u.prototype),"value",this).call(this,t);return e.startsWith("rgb(")?"#"+(e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,"")).split(",").map(function(t){return("00"+parseInt(t).toString(16)).slice(-2)}).join(""):e}}]),u),a=new i.default.Attributor.Class("color","ql-color",{scope:i.default.Scope.INLINE}),s=new l("color","color",{scope:i.default.Scope.INLINE});function u(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}function c(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}e.ColorAttributor=l,e.ColorClass=a,e.ColorStyle=s},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function i(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function r(o,r){var t=o===j.keys.LEFT?"prefix":"suffix",e={key:o,shiftKey:r,altKey:null};return i(e,t,/^$/),i(e,"handler",function(t){var e=t.index;o===j.keys.RIGHT&&(e+=t.length+1);var n=this.quill.getLeaf(e);return!(O(n,1)[0]instanceof x.default.Embed&&(o===j.keys.LEFT?r?this.quill.setSelection(t.index-1,t.length+1,g.default.sources.USER):this.quill.setSelection(t.index-1,g.default.sources.USER):r?this.quill.setSelection(t.index,t.length+1,g.default.sources.USER):this.quill.setSelection(t.index+t.length+1,g.default.sources.USER),1))}),e}function l(t,e){var n,o,r,i,l,a,s,u;0===t.index||this.quill.getLength()<=1||(n=this.quill.getLine(t.index),o=O(n,1)[0],s={},0===e.offset&&(r=this.quill.getLine(t.index-1),null!=(i=O(r,1)[0])&&1<i.length()&&(l=o.formats(),a=this.quill.getFormat(t.index-1,1),s=b.default.attributes.diff(l,a)||{})),u=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1,this.quill.deleteText(t.index-u,u,g.default.sources.USER),0<Object.keys(s).length&&this.quill.formatLine(t.index-u,u,s,g.default.sources.USER),this.quill.focus())}function a(t,e){var n,o,r,i,l,a,s,u,c=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;t.index>=this.quill.getLength()-c||(s={},u=0,n=this.quill.getLine(t.index),o=O(n,1)[0],e.offset>=o.length()-1&&(r=this.quill.getLine(t.index+1),(i=O(r,1)[0])&&(l=o.formats(),a=this.quill.getFormat(t.index,1),s=b.default.attributes.diff(l,a)||{},u=i.length())),this.quill.deleteText(t.index,c,g.default.sources.USER),0<Object.keys(s).length&&this.quill.formatLine(t.index+u-1,c,s,g.default.sources.USER))}function s(t){var e,n,o=this.quill.getLines(t),r={};1<o.length&&(e=o[0].formats(),n=o[o.length-1].formats(),r=b.default.attributes.diff(n,e)||{}),this.quill.deleteText(t,g.default.sources.USER),0<Object.keys(r).length&&this.quill.formatLine(t.index,1,r,g.default.sources.USER),this.quill.setSelection(t.index,g.default.sources.SILENT),this.quill.focus()}function u(t,n){var e=this;0<t.length&&this.quill.scroll.deleteAt(t.index,t.length);var o=Object.keys(n.format).reduce(function(t,e){return x.default.query(e,x.default.Scope.BLOCK)&&!Array.isArray(n.format[e])&&(t[e]=n.format[e]),t},{});this.quill.insertText(t.index,"\n",o,g.default.sources.USER),this.quill.setSelection(t.index+1,g.default.sources.SILENT),this.quill.focus(),Object.keys(n.format).forEach(function(t){null==o[t]&&(Array.isArray(n.format[t])||"link"!==t&&e.quill.format(t,n.format[t],g.default.sources.USER))})}function c(h){return{key:j.keys.TAB,shiftKey:!h,format:{"code-block":!0},handler:function(t){var e,n,o,r,i=x.default.query("code-block"),l=t.index,a=t.length,s=this.quill.scroll.descendant(i,l),u=O(s,2),c=u[0],f=u[1];null!=c&&(e=this.quill.getIndex(c),n=c.newlineIndex(f,!0)+1,o=c.newlineIndex(e+f+a),r=c.domNode.textContent.slice(n,o).split("\n"),f=0,r.forEach(function(t,e){h?(c.insertAt(n+f,i.TAB),f+=i.TAB.length,0===e?l+=i.TAB.length:a+=i.TAB.length):t.startsWith(i.TAB)&&(c.deleteAt(n+f,i.TAB.length),f-=i.TAB.length,0===e?l-=i.TAB.length:a-=i.TAB.length),f+=t.length+1}),this.quill.update(g.default.sources.USER),this.quill.setSelection(l,a,g.default.sources.SILENT))}}}function f(n){return{key:n[0].toUpperCase(),shortKey:!0,handler:function(t,e){this.quill.format(n,!e.format[n],g.default.sources.USER)}}}function h(t){if("string"==typeof t||"number"==typeof t)return h({key:t});if("object"===(void 0===t?"undefined":_(t))&&(t=(0,d.default)(t,!1)),"string"==typeof t.key)if(null!=j.keys[t.key.toUpperCase()])t.key=j.keys[t.key.toUpperCase()];else{if(1!==t.key.length)return null;t.key=t.key.toUpperCase().charCodeAt(0)}return t.shortKey&&(t[N]=t.shortKey,delete t.shortKey),t}Object.defineProperty(e,"__esModule",{value:!0}),e.SHORTKEY=e.default=void 0;var _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],o=!0,r=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(o=(l=a.next()).done)&&(n.push(l.value),!e||n.length!==e);o=!0);}catch(t){r=!0,i=t}finally{try{!o&&a.return&&a.return()}finally{if(r)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},p=function(t,e,n){return e&&q(t.prototype,e),n&&q(t,n),t},d=o(n(21)),w=o(n(12)),y=o(n(2)),v=o(n(4)),b=o(n(20)),x=o(n(0)),g=o(n(6)),m=o(n(10)),k=o(n(7)),E=(0,m.default)("quill:keyboard"),N=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",j=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(A,k.default),p(A,null,[{key:"match",value:function(e,n){return n=h(n),!["altKey","ctrlKey","metaKey","shiftKey"].some(function(t){return!!n[t]!==e[t]&&null!==n[t]})&&n.key===(e.which||e.keyCode)}}]),p(A,[{key:"addBinding",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},o=h(t);if(null==o||null==o.key)return E.warn("Attempted to add invalid keyboard binding",o);"function"==typeof e&&(e={handler:e}),"function"==typeof n&&(n={handler:n}),o=(0,y.default)(o,e,n),this.bindings[o.key]=this.bindings[o.key]||[],this.bindings[o.key].push(o)}},{key:"listen",value:function(){var m=this;this.quill.root.addEventListener("keydown",function(e){var t,n,o,r,i,l,a,s,u,c,f,h,p,d,y,v,b,g;e.defaultPrevented||(t=e.which||e.keyCode,0===(n=(m.bindings[t]||[]).filter(function(t){return A.match(e,t)})).length||null!=(o=m.quill.getSelection())&&m.quill.hasFocus()&&(r=m.quill.getLine(o.index),l=(i=O(r,2))[0],a=i[1],s=m.quill.getLeaf(o.index),c=(u=O(s,2))[0],f=u[1],h=0===o.length?[c,f]:m.quill.getLeaf(o.index+o.length),d=(p=O(h,2))[0],y=p[1],v=c instanceof x.default.Text?c.value().slice(0,f):"",b=d instanceof x.default.Text?d.value().slice(y):"",g={collapsed:0===o.length,empty:0===o.length&&l.length()<=1,format:m.quill.getFormat(o),offset:a,prefix:v,suffix:b},n.some(function(e){if(null!=e.collapsed&&e.collapsed!==g.collapsed)return!1;if(null!=e.empty&&e.empty!==g.empty)return!1;if(null!=e.offset&&e.offset!==g.offset)return!1;if(Array.isArray(e.format)){if(e.format.every(function(t){return null==g.format[t]}))return!1}else if("object"===_(e.format)&&!Object.keys(e.format).every(function(t){return!0===e.format[t]?null!=g.format[t]:!1===e.format[t]?null==g.format[t]:(0,w.default)(e.format[t],g.format[t])}))return!1;return!(null!=e.prefix&&!e.prefix.test(g.prefix)||null!=e.suffix&&!e.suffix.test(g.suffix)||!0===e.handler.call(m,o,g))})&&e.preventDefault()))})}}]),A);function A(e,t){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,A);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(A.__proto__||Object.getPrototypeOf(A)).call(this,e,t));return n.bindings={},Object.keys(n.options.bindings).forEach(function(t){("list autofill"!==t||null==e.scroll.whitelist||e.scroll.whitelist.list)&&n.options.bindings[t]&&n.addBinding(n.options.bindings[t])}),n.addBinding({key:A.keys.ENTER,shiftKey:null},u),n.addBinding({key:A.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){}),/Firefox/i.test(navigator.userAgent)?(n.addBinding({key:A.keys.BACKSPACE},{collapsed:!0},l),n.addBinding({key:A.keys.DELETE},{collapsed:!0},a)):(n.addBinding({key:A.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},l),n.addBinding({key:A.keys.DELETE},{collapsed:!0,suffix:/^.?$/},a)),n.addBinding({key:A.keys.BACKSPACE},{collapsed:!1},s),n.addBinding({key:A.keys.DELETE},{collapsed:!1},s),n.addBinding({key:A.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},l),n.listen(),n}function q(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}j.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},j.DEFAULTS={bindings:{bold:f("bold"),italic:f("italic"),underline:f("underline"),indent:{key:j.keys.TAB,format:["blockquote","indent","list"],handler:function(t,e){if(e.collapsed&&0!==e.offset)return!0;this.quill.format("indent","+1",g.default.sources.USER)}},outdent:{key:j.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(t,e){if(e.collapsed&&0!==e.offset)return!0;this.quill.format("indent","-1",g.default.sources.USER)}},"outdent backspace":{key:j.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(t,e){null!=e.format.indent?this.quill.format("indent","-1",g.default.sources.USER):null!=e.format.list&&this.quill.format("list",!1,g.default.sources.USER)}},"indent code-block":c(!0),"outdent code-block":c(!1),"remove tab":{key:j.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(t){this.quill.deleteText(t.index-1,1,g.default.sources.USER)}},tab:{key:j.keys.TAB,handler:function(t){this.quill.history.cutoff();var e=(new v.default).retain(t.index).delete(t.length).insert("\t");this.quill.updateContents(e,g.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index+1,g.default.sources.SILENT)}},"list empty enter":{key:j.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(t,e){this.quill.format("list",!1,g.default.sources.USER),e.format.indent&&this.quill.format("indent",!1,g.default.sources.USER)}},"checklist enter":{key:j.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(t){var e=this.quill.getLine(t.index),n=O(e,2),o=n[0],r=n[1],i=(0,y.default)({},o.formats(),{list:"checked"}),l=(new v.default).retain(t.index).insert("\n",i).retain(o.length()-r-1).retain(1,{list:"unchecked"});this.quill.updateContents(l,g.default.sources.USER),this.quill.setSelection(t.index+1,g.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:j.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(t,e){var n=this.quill.getLine(t.index),o=O(n,2),r=o[0],i=o[1],l=(new v.default).retain(t.index).insert("\n",e.format).retain(r.length()-i-1).retain(1,{header:null});this.quill.updateContents(l,g.default.sources.USER),this.quill.setSelection(t.index+1,g.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(t,e){var n=e.prefix.length,o=this.quill.getLine(t.index),r=O(o,2),i=r[0],l=r[1];if(n<l)return!0;var a=void 0;switch(e.prefix.trim()){case"[]":case"[ ]":a="unchecked";break;case"[x]":a="checked";break;case"-":case"*":a="bullet";break;default:a="ordered"}this.quill.insertText(t.index," ",g.default.sources.USER),this.quill.history.cutoff();var s=(new v.default).retain(t.index-l).delete(n+1).retain(i.length()-2-l).retain(1,{list:a});this.quill.updateContents(s,g.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index-n,g.default.sources.SILENT)}},"code exit":{key:j.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(t){var e=this.quill.getLine(t.index),n=O(e,2),o=n[0],r=n[1],i=(new v.default).retain(t.index+o.length()-r-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(i,g.default.sources.USER)}},"embed left":r(j.keys.LEFT,!1),"embed left shift":r(j.keys.LEFT,!0),"embed right":r(j.keys.RIGHT,!1),"embed right shift":r(j.keys.RIGHT,!0)}},e.default=j,e.SHORTKEY=N},function(t,e,n){"use strict";t.exports={align:{"":n(75),center:n(76),right:n(77),justify:n(78)},background:n(79),blockquote:n(80),bold:n(81),clean:n(82),code:n(40),"code-block":n(40),color:n(83),direction:{"":n(84),rtl:n(85)},float:{center:n(86),full:n(87),left:n(88),right:n(89)},formula:n(90),header:{1:n(91),2:n(92)},italic:n(93),image:n(94),indent:{"+1":n(95),"-1":n(96)},link:n(97),list:{ordered:n(98),bullet:n(99),check:n(100)},script:{sub:n(101),super:n(102)},strike:n(103),underline:n(104),video:n(105)}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=n(1),o=(Object.defineProperty(r.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),r.create=function(t){if(null==this.tagName)throw new l.ParchmentError("Blot definition missing tagName");var e=Array.isArray(this.tagName)?("string"==typeof t&&(t=t.toUpperCase(),parseInt(t).toString()===t&&(t=parseInt(t))),"number"==typeof t?document.createElement(this.tagName[t-1]):-1<this.tagName.indexOf(t)?document.createElement(t):document.createElement(this.tagName[0])):document.createElement(this.tagName);return this.className&&e.classList.add(this.className),e},r.prototype.attach=function(){null!=this.parent&&(this.scroll=this.parent.scroll)},r.prototype.clone=function(){var t=this.domNode.cloneNode(!1);return l.create(t)},r.prototype.detach=function(){null!=this.parent&&this.parent.removeChild(this),delete this.domNode[l.DATA_KEY]},r.prototype.deleteAt=function(t,e){this.isolate(t,e).remove()},r.prototype.formatAt=function(t,e,n,o){var r,i=this.isolate(t,e);null!=l.query(n,l.Scope.BLOT)&&o?i.wrap(n,o):null!=l.query(n,l.Scope.ATTRIBUTE)&&(r=l.create(this.statics.scope),i.wrap(r),r.format(n,o))},r.prototype.insertAt=function(t,e,n){var o=null==n?l.create("text",e):l.create(e,n),r=this.split(t);this.parent.insertBefore(o,r)},r.prototype.insertInto=function(t,e){void 0===e&&(e=null),null!=this.parent&&this.parent.children.remove(this);var n=null;t.children.insertBefore(this,e),null!=e&&(n=e.domNode),this.domNode.parentNode==t.domNode&&this.domNode.nextSibling==n||t.domNode.insertBefore(this.domNode,n),this.parent=t,this.attach()},r.prototype.isolate=function(t,e){var n=this.split(t);return n.split(e),n},r.prototype.length=function(){return 1},r.prototype.offset=function(t){return void 0===t&&(t=this.parent),null==this.parent||this==t?0:this.parent.children.offset(this)+this.parent.offset(t)},r.prototype.optimize=function(t){null!=this.domNode[l.DATA_KEY]&&delete this.domNode[l.DATA_KEY].mutations},r.prototype.remove=function(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},r.prototype.replace=function(t){null!=t.parent&&(t.parent.insertBefore(this,t.next),t.remove())},r.prototype.replaceWith=function(t,e){var n="string"==typeof t?l.create(t,e):t;return n.replace(this),n},r.prototype.split=function(t,e){return 0===t?this:this.next},r.prototype.update=function(t,e){},r.prototype.wrap=function(t,e){var n="string"==typeof t?l.create(t,e):t;return null!=this.parent&&this.parent.insertBefore(n,this.next),n.appendChild(this),n},r.blotName="abstract",r);function r(t){this.domNode=t,this.domNode[l.DATA_KEY]={blot:this}}e.default=o},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(11),i=n(29),l=n(30),a=n(1),o=(s.prototype.attribute=function(t,e){e?t.add(this.domNode,e)&&(null!=t.value(this.domNode)?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])},s.prototype.build=function(){var n=this;this.attributes={};var t=r.default.keys(this.domNode),e=i.default.keys(this.domNode),o=l.default.keys(this.domNode);t.concat(e).concat(o).forEach(function(t){var e=a.query(t,a.Scope.ATTRIBUTE);e instanceof r.default&&(n.attributes[e.attrName]=e)})},s.prototype.copy=function(n){var o=this;Object.keys(this.attributes).forEach(function(t){var e=o.attributes[t].value(o.domNode);n.format(t,e)})},s.prototype.move=function(t){var e=this;this.copy(t),Object.keys(this.attributes).forEach(function(t){e.attributes[t].remove(e.domNode)}),this.attributes={}},s.prototype.values=function(){var n=this;return Object.keys(this.attributes).reduce(function(t,e){return t[e]=n.attributes[e].value(n.domNode),t},{})},s);function s(t){this.attributes={},this.domNode=t,this.build()}e.default=o},function(t,e,n){"use strict";function o(t,e){return(t.getAttribute("class")||"").split(/\s+/).filter(function(t){return 0===t.indexOf(e+"-")})}var r,i=this&&this.__extends||(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var l,a=n(11),s=(l=a.default,i(u,l),u.keys=function(t){return(t.getAttribute("class")||"").split(/\s+/).map(function(t){return t.split("-").slice(0,-1).join("-")})},u.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(this.remove(t),t.classList.add(this.keyName+"-"+e),!0)},u.prototype.remove=function(e){o(e,this.keyName).forEach(function(t){e.classList.remove(t)}),0===e.classList.length&&e.removeAttribute("class")},u.prototype.value=function(t){var e=(o(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""},u);function u(){return null!==l&&l.apply(this,arguments)||this}e.default=s},function(t,e,n){"use strict";function o(t){var e=t.split("-"),n=e.slice(1).map(function(t){return t[0].toUpperCase()+t.slice(1)}).join("");return e[0]+n}var r,i=this&&this.__extends||(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var l,a=n(11),s=(l=a.default,i(u,l),u.keys=function(t){return(t.getAttribute("style")||"").split(";").map(function(t){return t.split(":")[0].trim()})},u.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(t.style[o(this.keyName)]=e,!0)},u.prototype.remove=function(t){t.style[o(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")},u.prototype.value=function(t){var e=t.style[o(this.keyName)];return this.canAdd(t,e)?e:""},u);function u(){return null!==l&&l.apply(this,arguments)||this}e.default=s},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}Object.defineProperty(e,"__esModule",{value:!0});function l(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:l(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var u=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],o=!0,r=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(o=(l=a.next()).done)&&(n.push(l.value),!e||n.length!==e);o=!0);}catch(t){r=!0,i=t}finally{try{!o&&a.return&&a.return()}finally{if(r)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},r=function(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),t},c=o(n(0)),f=o(n(8)),i=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(h,c.default.Embed),r(h,null,[{key:"value",value:function(){}}]),r(h,[{key:"detach",value:function(){null!=this.parent&&this.parent.removeChild(this)}},{key:"format",value:function(t,e){if(0!==this._length)return l(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"format",this).call(this,t,e);for(var n=this,o=0;null!=n&&n.statics.scope!==c.default.Scope.BLOCK_BLOT;)o+=n.offset(n.parent),n=n.parent;null!=n&&(this._length=h.CONTENTS.length,n.optimize(),n.formatAt(o,h.CONTENTS.length,t,e),this._length=0)}},{key:"index",value:function(t,e){return t===this.textNode?0:l(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"index",this).call(this,t,e)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){l(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!this.selection.composing&&null!=this.parent){var t,e,n=this.textNode,o=this.selection.getNativeRange(),r=void 0,i=void 0,l=void 0;for(null!=o&&o.start.node===n&&o.end.node===n&&(t=[n,o.start.offset,o.end.offset],r=t[0],i=t[1],l=t[2]);null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==h.CONTENTS&&(e=this.textNode.data.split(h.CONTENTS).join(""),this.next instanceof f.default?(r=this.next.domNode,this.next.insertAt(0,e),this.textNode.data=h.CONTENTS):(this.textNode.data=e,this.parent.insertBefore(c.default.create(this.textNode),this),this.textNode=document.createTextNode(h.CONTENTS),this.domNode.appendChild(this.textNode))),this.remove(),null!=i){var a=[i,l].map(function(t){return Math.max(0,Math.min(r.data.length,t-1))}),s=u(a,2);return i=s[0],l=s[1],{startNode:r,startOffset:i,endNode:r,endOffset:l}}}}},{key:"update",value:function(t,e){var n,o=this;!t.some(function(t){return"characterData"===t.type&&t.target===o.textNode})||(n=this.restore())&&(e.range=n)}},{key:"value",value:function(){return""}}]),h);function h(t,e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,h);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,t));return n.selection=e,n.textNode=document.createTextNode(h.CONTENTS),n.domNode.appendChild(n.textNode),n._length=0,n}function a(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}i.blotName="cursor",i.className="ql-cursor",i.tagName="span",i.CONTENTS="\ufeff",e.default=i},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=(function(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}(r,[{key:"init",value:function(){var e=this;Object.keys(this.options.modules).forEach(function(t){null==e.modules[t]&&e.addModule(t)})}},{key:"addModule",value:function(t){var e=this.quill.constructor.import("modules/"+t);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}}]),r);function r(t,e){(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")})(this,r),this.quill=t,this.options=e,this.modules={}}function i(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}o.DEFAULTS={modules:{}},o.themes={default:o},e.default=o},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}Object.defineProperty(e,"__esModule",{value:!0});var r=function(t,e,n){return e&&c(t.prototype,e),n&&c(t,n),t},i=o(n(0)),l=o(n(8)),a="\ufeff",s=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(u,i.default.Embed),r(u,[{key:"index",value:function(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:function t(e,n,o){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,n);if(void 0===r){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,o)}if("value"in r)return r.value;var l=r.get;return void 0!==l?l.call(o):void 0}(u.prototype.__proto__||Object.getPrototypeOf(u.prototype),"index",this).call(this,t,e)}},{key:"restore",value:function(t){var e,n=void 0,o=void 0,r=t.data.split(a).join("");return t===this.leftGuard?n=this.prev instanceof l.default?(e=this.prev.length(),this.prev.insertAt(e,r),{startNode:this.prev.domNode,startOffset:e+r.length}):(o=document.createTextNode(r),this.parent.insertBefore(i.default.create(o),this),{startNode:o,startOffset:r.length}):t===this.rightGuard&&(n=this.next instanceof l.default?(this.next.insertAt(0,r),{startNode:this.next.domNode,startOffset:r.length}):(o=document.createTextNode(r),this.parent.insertBefore(i.default.create(o),this.next),{startNode:o,startOffset:r.length})),t.data=a,n}},{key:"update",value:function(t,n){var o=this;t.forEach(function(t){var e;"characterData"!==t.type||t.target!==o.leftGuard&&t.target!==o.rightGuard||(e=o.restore(t.target))&&(n.range=e)})}}]),u);function u(t){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u);var e=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(u.__proto__||Object.getPrototypeOf(u)).call(this,t));return e.contentNode=document.createElement("span"),e.contentNode.setAttribute("contenteditable",!1),[].slice.call(e.domNode.childNodes).forEach(function(t){e.contentNode.appendChild(t)}),e.leftGuard=document.createTextNode(a),e.rightGuard=document.createTextNode(a),e.domNode.appendChild(e.leftGuard),e.domNode.appendChild(e.contentNode),e.domNode.appendChild(e.rightGuard),e}function c(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}e.default=s},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AlignStyle=e.AlignClass=e.AlignAttribute=void 0;var o=n(0),r=o&&o.__esModule?o:{default:o},i={scope:r.default.Scope.BLOCK,whitelist:["right","center","justify"]},l=new r.default.Attributor.Attribute("align","align",i),a=new r.default.Attributor.Class("align","ql-align",i),s=new r.default.Attributor.Style("align","text-align",i);e.AlignAttribute=l,e.AlignClass=a,e.AlignStyle=s},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BackgroundStyle=e.BackgroundClass=void 0;var o=n(0),r=o&&o.__esModule?o:{default:o},i=n(24),l=new r.default.Attributor.Class("background","ql-bg",{scope:r.default.Scope.INLINE}),a=new i.ColorAttributor("background","background-color",{scope:r.default.Scope.INLINE});e.BackgroundClass=l,e.BackgroundStyle=a},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.DirectionStyle=e.DirectionClass=e.DirectionAttribute=void 0;var o=n(0),r=o&&o.__esModule?o:{default:o},i={scope:r.default.Scope.BLOCK,whitelist:["rtl"]},l=new r.default.Attributor.Attribute("direction","dir",i),a=new r.default.Attributor.Class("direction","ql-direction",i),s=new r.default.Attributor.Style("direction","direction",i);e.DirectionAttribute=l,e.DirectionClass=a,e.DirectionStyle=s},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.FontClass=e.FontStyle=void 0;var o=function(t,e,n){return e&&c(t.prototype,e),n&&c(t,n),t},r=n(0),i=r&&r.__esModule?r:{default:r},l={scope:i.default.Scope.INLINE,whitelist:["serif","monospace"]},a=new i.default.Attributor.Class("font","ql-font",l),s=new(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(u,i.default.Attributor.Style),o(u,[{key:"value",value:function(t){return function t(e,n,o){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,n);if(void 0===r){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,o)}if("value"in r)return r.value;var l=r.get;return void 0!==l?l.call(o):void 0}(u.prototype.__proto__||Object.getPrototypeOf(u.prototype),"value",this).call(this,t).replace(/["']/g,"")}}]),u)("font","font-family",l);function u(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}function c(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}e.FontStyle=s,e.FontClass=a},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SizeStyle=e.SizeClass=void 0;var o=n(0),r=o&&o.__esModule?o:{default:o},i=new r.default.Attributor.Class("size","ql-size",{scope:r.default.Scope.INLINE,whitelist:["small","large","huge"]}),l=new r.default.Attributor.Style("size","font-size",{scope:r.default.Scope.INLINE,whitelist:["10px","18px","32px"]});e.SizeClass=i,e.SizeStyle=l},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});function l(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:l(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var o=function(t,e,n){return e&&s(t.prototype,e),n&&s(t,n),t},r=n(5),i=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(a,(r&&r.__esModule?r:{default:r}).default),o(a,[{key:"optimize",value:function(t){l(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"optimize",this).call(this,t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return l(a.__proto__||Object.getPrototypeOf(a),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),a);function a(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}function s(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}i.blotName="bold",i.tagName=["STRONG","B"],e.default=i},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});function l(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:l(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var o=function(t,e,n){return e&&s(t.prototype,e),n&&s(t,n),t},r=n(16),i=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(a,(r&&r.__esModule?r:{default:r}).default),o(a,[{key:"buildItem",value:function(t){var e=l(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"buildItem",this).call(this,t);return e.style.backgroundColor=t.getAttribute("value")||"",e}},{key:"selectItem",value:function(t,e){l(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"selectItem",this).call(this,t,e);var n=this.label.querySelector(".ql-color-label"),o=t&&t.getAttribute("data-value")||"";n&&("line"===n.tagName?n.style.stroke=o:n.style.fill=o)}}]),a);function a(t,e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,t));return n.label.innerHTML=e,n.container.classList.add("ql-color-picker"),[].slice.call(n.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(t){t.classList.add("ql-primary")}),n}function s(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}e.default=i},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=function(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),t},r=n(16),i=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(l,(r&&r.__esModule?r:{default:r}).default),o(l,[{key:"selectItem",value:function(t,e){(function t(e,n,o){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,n);if(void 0===r){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,o)}if("value"in r)return r.value;var l=r.get;return void 0!==l?l.call(o):void 0})(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"selectItem",this).call(this,t,e),t=t||this.defaultItem,this.label.innerHTML=t.innerHTML}}]),l);function l(t,e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,l);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(l.__proto__||Object.getPrototypeOf(l)).call(this,t));return n.container.classList.add("ql-icon-picker"),[].forEach.call(n.container.querySelectorAll(".ql-picker-item"),function(t){t.innerHTML=e[t.getAttribute("data-value")||""]}),n.defaultItem=n.container.querySelector(".ql-selected"),n.selectItem(n.defaultItem),n}function a(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}e.default=i},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=(function(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}(r,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(t){var e=t.left+t.width/2-this.root.offsetWidth/2,n=t.bottom+this.quill.root.scrollTop;this.root.style.left=e+"px",this.root.style.top=n+"px",this.root.classList.remove("ql-flip");var o,r,i=this.boundsContainer.getBoundingClientRect(),l=this.root.getBoundingClientRect(),a=0;return l.right>i.right&&(a=i.right-l.right,this.root.style.left=e+a+"px"),l.left<i.left&&(a=i.left-l.left,this.root.style.left=e+a+"px"),l.bottom>i.bottom&&(o=l.bottom-l.top,r=t.bottom-t.top+o,this.root.style.top=n-r+"px",this.root.classList.add("ql-flip")),a}},{key:"show",value:function(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}]),r);function r(t,e){var n=this;(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")})(this,r),this.quill=t,this.boundsContainer=e||document.body,this.root=t.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){n.root.style.marginTop=-1*n.quill.root.scrollTop+"px"}),this.hide()}function i(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}e.default=o},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function l(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}function a(n,t,e){var o=2<arguments.length&&void 0!==e&&e;t.forEach(function(t){var e=document.createElement("option");t===o?e.setAttribute("selected","selected"):e.setAttribute("value",t),n.appendChild(e)})}Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.BaseTooltip=void 0;var s=function(t,e,n){return e&&E(t.prototype,e),n&&E(t,n),t},u=o(n(2)),c=o(n(4)),f=o(n(9)),h=o(n(25)),p=o(n(32)),d=o(n(41)),y=o(n(42)),v=o(n(16)),b=o(n(43)),g=[!1,"center","right","justify"],m=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],_=[!1,"serif","monospace"],O=["1","2","3",!1],w=["small",!1,"large","huge"],x=(l(k,p.default),s(k,[{key:"addModule",value:function(t){var e=function t(e,n,o){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,n);if(void 0===r){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,o)}if("value"in r)return r.value;var l=r.get;return void 0!==l?l.call(o):void 0}(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"addModule",this).call(this,t);return"toolbar"===t&&this.extendToolbar(e),e}},{key:"buildButtons",value:function(t,o){t.forEach(function(n){(n.getAttribute("class")||"").split(/\s+/).forEach(function(t){var e;t.startsWith("ql-")&&(t=t.slice("ql-".length),null!=o[t])&&("direction"===t?n.innerHTML=o[t][""]+o[t].rtl:"string"==typeof o[t]?n.innerHTML=o[t]:null!=(e=n.value||"")&&o[t][e]&&(n.innerHTML=o[t][e]))})})}},{key:"buildPickers",value:function(t,n){var e=this;this.pickers=t.map(function(t){if(t.classList.contains("ql-align"))return null==t.querySelector("option")&&a(t,g),new y.default(t,n.align);if(t.classList.contains("ql-background")||t.classList.contains("ql-color")){var e=t.classList.contains("ql-background")?"background":"color";return null==t.querySelector("option")&&a(t,m,"background"==e?"#ffffff":"#000000"),new d.default(t,n[e])}return null==t.querySelector("option")&&(t.classList.contains("ql-font")?a(t,_):t.classList.contains("ql-header")?a(t,O):t.classList.contains("ql-size")&&a(t,w)),new v.default(t)}),this.quill.on(f.default.events.EDITOR_CHANGE,function(){e.pickers.forEach(function(t){t.update()})})}}]),k);function k(n,t){r(this,k);var o=i(this,(k.__proto__||Object.getPrototypeOf(k)).call(this,n,t));return n.emitter.listenDOM("click",document.body,function t(e){if(!document.body.contains(n.root))return document.body.removeEventListener("click",t);null==o.tooltip||o.tooltip.root.contains(e.target)||document.activeElement===o.tooltip.textbox||o.quill.hasFocus()||o.tooltip.hide(),null!=o.pickers&&o.pickers.forEach(function(t){t.container.contains(e.target)||t.close()})}),o}function E(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}x.DEFAULTS=(0,u.default)(!0,{},p.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var n=this,o=this.container.querySelector("input.ql-image[type=file]");null==o&&((o=document.createElement("input")).setAttribute("type","file"),o.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),o.classList.add("ql-image"),o.addEventListener("change",function(){var t;null!=o.files&&null!=o.files[0]&&((t=new FileReader).onload=function(t){var e=n.quill.getSelection(!0);n.quill.updateContents((new c.default).retain(e.index).delete(e.length).insert({image:t.target.result}),f.default.sources.USER),n.quill.setSelection(e.index+1,f.default.sources.SILENT),o.value=""},t.readAsDataURL(o.files[0]))}),this.container.appendChild(o)),o.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});var N=(l(j,b.default),s(j,[{key:"listen",value:function(){var e=this;this.textbox.addEventListener("keydown",function(t){h.default.match(t,"enter")?(e.save(),t.preventDefault()):h.default.match(t,"escape")&&(e.cancel(),t.preventDefault())})}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"link",e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),null!=e?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value=""),this.position(this.quill.getBounds(this.quill.selection.savedRange)),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+t)||""),this.root.setAttribute("data-mode",t)}},{key:"restoreFocus",value:function(){var t=this.quill.scrollingContainer.scrollTop;this.quill.focus(),this.quill.scrollingContainer.scrollTop=t}},{key:"save",value:function(){var t,e,n=this.textbox.value;switch(this.root.getAttribute("data-mode")){case"link":var o=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",n,f.default.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",n,f.default.sources.USER)),this.quill.root.scrollTop=o;break;case"video":n=(e=(t=n).match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||t.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/))?(e[1]||"https")+"://www.youtube.com/embed/"+e[2]+"?showinfo=0":(e=t.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(e[1]||"https")+"://player.vimeo.com/video/"+e[2]+"/":t;case"formula":if(!n)break;var r,i=this.quill.getSelection(!0);null!=i&&(r=i.index+i.length,this.quill.insertEmbed(r,this.root.getAttribute("data-mode"),n,f.default.sources.USER),"formula"===this.root.getAttribute("data-mode")&&this.quill.insertText(r+1," ",f.default.sources.USER),this.quill.setSelection(r+2,f.default.sources.USER))}this.textbox.value="",this.hide()}}]),j);function j(t,e){r(this,j);var n=i(this,(j.__proto__||Object.getPrototypeOf(j)).call(this,t,e));return n.textbox=n.root.querySelector('input[type="text"]'),n.listen(),n}e.BaseTooltip=N,e.default=x},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}Object.defineProperty(e,"__esModule",{value:!0});var r=o(n(46)),i=n(34),l=n(36),a=n(62),s=o(n(63)),u=o(n(64)),c=n(65),f=o(c),h=n(35),p=n(24),d=n(37),y=n(38),v=o(n(39)),b=o(n(66)),g=o(n(15)),m=o(n(67)),_=o(n(68)),O=o(n(69)),w=o(n(70)),x=o(n(71)),k=n(13),E=o(k),N=o(n(72)),j=o(n(73)),A=o(n(74)),q=o(n(26)),T=o(n(16)),P=o(n(41)),S=o(n(42)),C=o(n(43)),L=o(n(107)),M=o(n(108));r.default.register({"attributors/attribute/direction":l.DirectionAttribute,"attributors/class/align":i.AlignClass,"attributors/class/background":h.BackgroundClass,"attributors/class/color":p.ColorClass,"attributors/class/direction":l.DirectionClass,"attributors/class/font":d.FontClass,"attributors/class/size":y.SizeClass,"attributors/style/align":i.AlignStyle,"attributors/style/background":h.BackgroundStyle,"attributors/style/color":p.ColorStyle,"attributors/style/direction":l.DirectionStyle,"attributors/style/font":d.FontStyle,"attributors/style/size":y.SizeStyle},!0),r.default.register({"formats/align":i.AlignClass,"formats/direction":l.DirectionClass,"formats/indent":a.IndentClass,"formats/background":h.BackgroundStyle,"formats/color":p.ColorStyle,"formats/font":d.FontClass,"formats/size":y.SizeClass,"formats/blockquote":s.default,"formats/code-block":E.default,"formats/header":u.default,"formats/list":f.default,"formats/bold":v.default,"formats/code":k.Code,"formats/italic":b.default,"formats/link":g.default,"formats/script":m.default,"formats/strike":_.default,"formats/underline":O.default,"formats/image":w.default,"formats/video":x.default,"formats/list/item":c.ListItem,"modules/formula":N.default,"modules/syntax":j.default,"modules/toolbar":A.default,"themes/bubble":L.default,"themes/snow":M.default,"ui/icons":q.default,"ui/picker":T.default,"ui/icon-picker":S.default,"ui/color-picker":P.default,"ui/tooltip":C.default},!0),e.default=r.default},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}Object.defineProperty(e,"__esModule",{value:!0});var r=o(n(0)),i=o(n(6)),l=n(3),a=o(l),s=o(n(14)),u=o(n(23)),c=o(n(31)),f=o(n(33)),h=o(n(5)),p=o(n(59)),d=o(n(8)),y=o(n(60)),v=o(n(61)),b=o(n(25));i.default.register({"blots/block":a.default,"blots/block/embed":l.BlockEmbed,"blots/break":s.default,"blots/container":u.default,"blots/cursor":c.default,"blots/embed":f.default,"blots/inline":h.default,"blots/scroll":p.default,"blots/text":d.default,"modules/clipboard":y.default,"modules/history":v.default,"modules/keyboard":b.default}),r.default.register(a.default,s.default,c.default,h.default,p.default,d.default),e.default=i.default},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=(r.prototype.append=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this.insertBefore(t[0],null),1<t.length&&this.append.apply(this,t.slice(1))},r.prototype.contains=function(t){for(var e,n=this.iterator();e=n();)if(e===t)return!0;return!1},r.prototype.insertBefore=function(t,e){t&&(null!=(t.next=e)?(t.prev=e.prev,null!=e.prev&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):null!=this.tail?((this.tail.next=t).prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)},r.prototype.offset=function(t){for(var e=0,n=this.head;null!=n;){if(n===t)return e;e+=n.length(),n=n.next}return-1},r.prototype.remove=function(t){this.contains(t)&&(null!=t.prev&&(t.prev.next=t.next),null!=t.next&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),--this.length)},r.prototype.iterator=function(e){return void 0===e&&(e=this.head),function(){var t=e;return null!=e&&(e=e.next),t}},r.prototype.find=function(t,e){void 0===e&&(e=!1);for(var n,o=this.iterator();n=o();){var r=n.length();if(t<r||e&&t===r&&(null==n.next||0!==n.next.length()))return[n,t];t-=r}return[null,0]},r.prototype.forEach=function(t){for(var e,n=this.iterator();e=n();)t(e)},r.prototype.forEachAt=function(t,e,n){if(!(e<=0))for(var o,r=this.find(t),i=r[0],l=t-r[1],a=this.iterator(i);(o=a())&&l<t+e;){var s=o.length();l<t?n(o,t-l,Math.min(e,l+s-t)):n(o,0,Math.min(s,t+e-l)),l+=s}},r.prototype.map=function(n){return this.reduce(function(t,e){return t.push(n(e)),t},[])},r.prototype.reduce=function(t,e){for(var n,o=this.iterator();n=o();)e=t(e,n);return e},r);function r(){this.head=this.tail=null,this.length=0}e.default=o},function(t,e,n){"use strict";var o,r=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var s,u=n(17),c=n(1),i={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},l=(s=u.default,r(a,s),a.prototype.detach=function(){s.prototype.detach.call(this),this.observer.disconnect()},a.prototype.deleteAt=function(t,e){this.update(),0===t&&e===this.length()?this.children.forEach(function(t){t.remove()}):s.prototype.deleteAt.call(this,t,e)},a.prototype.formatAt=function(t,e,n,o){this.update(),s.prototype.formatAt.call(this,t,e,n,o)},a.prototype.insertAt=function(t,e,n){this.update(),s.prototype.insertAt.call(this,t,e,n)},a.prototype.optimize=function(t,e){var n=this;void 0===t&&(t=[]),void 0===e&&(e={}),s.prototype.optimize.call(this,e);for(var o=[].slice.call(this.observer.takeRecords());0<o.length;)t.push(o.pop());for(var r=function(t,e){void 0===e&&(e=!0),null!=t&&t!==n&&null!=t.domNode.parentNode&&(null==t.domNode[c.DATA_KEY].mutations&&(t.domNode[c.DATA_KEY].mutations=[]),e&&r(t.parent))},i=function(t){null!=t.domNode[c.DATA_KEY]&&null!=t.domNode[c.DATA_KEY].mutations&&(t instanceof u.default&&t.children.forEach(i),t.optimize(e))},l=t,a=0;0<l.length;a+=1){if(100<=a)throw new Error("[Parchment] Maximum optimize iterations reached");for(l.forEach(function(t){var e=c.find(t.target,!0);null!=e&&(e.domNode===t.target&&("childList"===t.type?(r(c.find(t.previousSibling,!1)),[].forEach.call(t.addedNodes,function(t){var e=c.find(t,!1);r(e,!1),e instanceof u.default&&e.children.forEach(function(t){r(t,!1)})})):"attributes"===t.type&&r(e.prev)),r(e))}),this.children.forEach(i),o=(l=[].slice.call(this.observer.takeRecords())).slice();0<o.length;)t.push(o.pop())}},a.prototype.update=function(t,e){var n=this;void 0===e&&(e={}),(t=t||this.observer.takeRecords()).map(function(t){var e=c.find(t.target,!0);return null==e?null:null==e.domNode[c.DATA_KEY].mutations?(e.domNode[c.DATA_KEY].mutations=[t],e):(e.domNode[c.DATA_KEY].mutations.push(t),null)}).forEach(function(t){null!=t&&t!==n&&null!=t.domNode[c.DATA_KEY]&&t.update(t.domNode[c.DATA_KEY].mutations||[],e)}),null!=this.domNode[c.DATA_KEY].mutations&&s.prototype.update.call(this,this.domNode[c.DATA_KEY].mutations,e),this.optimize(t,e)},a.blotName="scroll",a.defaultChild="block",a.scope=c.Scope.BLOCK_BLOT,a.tagName="DIV",a);function a(t){var e=s.call(this,t)||this;return(e.scroll=e).observer=new MutationObserver(function(t){e.update(t)}),e.observer.observe(e.domNode,i),e.attach(),e}e.default=l},function(t,e,n){"use strict";var o,r=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i,l=n(18),a=n(1),s=(i=l.default,r(u,i),u.formats=function(t){if(t.tagName!==u.tagName)return i.formats.call(this,t)},u.prototype.format=function(t,e){var n=this;t!==this.statics.blotName||e?i.prototype.format.call(this,t,e):(this.children.forEach(function(t){t instanceof l.default||(t=t.wrap(u.blotName,!0)),n.attributes.copy(t)}),this.unwrap())},u.prototype.formatAt=function(t,e,n,o){null!=this.formats()[n]||a.query(n,a.Scope.ATTRIBUTE)?this.isolate(t,e).format(n,o):i.prototype.formatAt.call(this,t,e,n,o)},u.prototype.optimize=function(t){i.prototype.optimize.call(this,t);var e=this.formats();if(0===Object.keys(e).length)return this.unwrap();var n=this.next;n instanceof u&&n.prev===this&&function(t,e){if(Object.keys(t).length===Object.keys(e).length){for(var n in t)if(t[n]!==e[n])return;return 1}}(e,n.formats())&&(n.moveChildren(this),n.remove())},u.blotName="inline",u.scope=a.Scope.INLINE_BLOT,u.tagName="SPAN",u);function u(){return null!==i&&i.apply(this,arguments)||this}e.default=s},function(t,e,n){"use strict";var o,r=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i,l=n(18),a=n(1),s=(i=l.default,r(u,i),u.formats=function(t){var e=a.query(u.blotName).tagName;if(t.tagName!==e)return i.formats.call(this,t)},u.prototype.format=function(t,e){null!=a.query(t,a.Scope.BLOCK)&&(t!==this.statics.blotName||e?i.prototype.format.call(this,t,e):this.replaceWith(u.blotName))},u.prototype.formatAt=function(t,e,n,o){null!=a.query(n,a.Scope.BLOCK)?this.format(n,o):i.prototype.formatAt.call(this,t,e,n,o)},u.prototype.insertAt=function(t,e,n){var o,r;null==n||null!=a.query(e,a.Scope.INLINE)?i.prototype.insertAt.call(this,t,e,n):(o=this.split(t),r=a.create(e,n),o.parent.insertBefore(r,o))},u.prototype.update=function(t,e){navigator.userAgent.match(/Trident/)?this.build():i.prototype.update.call(this,t,e)},u.blotName="block",u.scope=a.Scope.BLOCK_BLOT,u.tagName="P",u);function u(){return null!==i&&i.apply(this,arguments)||this}e.default=s},function(t,e,n){"use strict";var o,r=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i,l=n(19),a=(i=l.default,r(s,i),s.formats=function(t){},s.prototype.format=function(t,e){i.prototype.formatAt.call(this,0,this.length(),t,e)},s.prototype.formatAt=function(t,e,n,o){0===t&&e===this.length()?this.format(n,o):i.prototype.formatAt.call(this,t,e,n,o)},s.prototype.formats=function(){return this.statics.formats(this.domNode)},s);function s(){return null!==i&&i.apply(this,arguments)||this}e.default=a},function(t,e,n){"use strict";var o,r=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var i,l=n(19),a=n(1),s=(i=l.default,r(u,i),u.create=function(t){return document.createTextNode(t)},u.value=function(t){var e=t.data;return e.normalize&&(e=e.normalize()),e},u.prototype.deleteAt=function(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)},u.prototype.index=function(t,e){return this.domNode===t?e:-1},u.prototype.insertAt=function(t,e,n){null==n?(this.text=this.text.slice(0,t)+e+this.text.slice(t),this.domNode.data=this.text):i.prototype.insertAt.call(this,t,e,n)},u.prototype.length=function(){return this.text.length},u.prototype.optimize=function(t){i.prototype.optimize.call(this,t),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof u&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},u.prototype.position=function(t,e){return void 0===e&&(e=!1),[this.domNode,t]},u.prototype.split=function(t,e){if(void 0===e&&(e=!1),!e){if(0===t)return this;if(t===this.length())return this.next}var n=a.create(this.domNode.splitText(t));return this.parent.insertBefore(n,this.next),this.text=this.statics.value(this.domNode),n},u.prototype.update=function(t,e){var n=this;t.some(function(t){return"characterData"===t.type&&t.target===n.domNode})&&(this.text=this.statics.value(this.domNode))},u.prototype.value=function(){return this.text},u.blotName="text",u.scope=a.Scope.INLINE_BLOT,u);function u(t){var e=i.call(this,t)||this;return e.text=e.statics.value(e.domNode),e}e.default=s},function(t,e,n){"use strict";var o,r=document.createElement("div");r.classList.toggle("test-class",!1),r.classList.contains("test-class")&&(o=DOMTokenList.prototype.toggle,DOMTokenList.prototype.toggle=function(t,e){return 1<arguments.length&&!this.contains(t)==!e?e:o.call(this,t)}),String.prototype.startsWith||(String.prototype.startsWith=function(t,e){return e=e||0,this.substr(e,t.length)===t}),String.prototype.endsWith||(String.prototype.endsWith=function(t,e){var n=this.toString();("number"!=typeof e||!isFinite(e)||Math.floor(e)!==e||e>n.length)&&(e=n.length),e-=t.length;var o=n.indexOf(t,e);return-1!==o&&o===e}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(t){if(null===this)throw new TypeError("Array.prototype.find called on null or undefined");if("function"!=typeof t)throw new TypeError("predicate must be a function");for(var e,n=Object(this),o=n.length>>>0,r=arguments[1],i=0;i<o;i++)if(e=n[i],t.call(r,e,i,n))return e}}),document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)})},function(t,e){function d(t,e,n){if(t==e)return t?[[b,t]]:[];(n<0||t.length<n)&&(n=null);var o=y(t,e),r=t.substring(0,o),o=v(t=t.substring(o),e=e.substring(o)),i=t.substring(t.length-o),l=function(t,e){var n;if(!t)return[[j,e]];if(!e)return[[N,t]];var o=t.length>e.length?t:e,r=t.length>e.length?e:t,i=o.indexOf(r);if(-1!=i)return n=[[j,o.substring(0,i)],[b,r],[j,o.substring(i+r.length)]],t.length>e.length&&(n[0][0]=n[2][0]=N),n;if(1==r.length)return[[N,t],[j,e]];var l=function(t,e){function n(t,e,n){for(var o,r,i,l,a=t.substring(n,n+Math.floor(t.length/4)),s=-1,u="";-1!=(s=e.indexOf(a,s+1));){var c=y(t.substring(n),e.substring(s)),f=v(t.substring(0,n),e.substring(0,s));u.length<f+c&&(u=e.substring(s-f,s)+e.substring(s,s+c),o=t.substring(0,n-f),r=t.substring(n+c),i=e.substring(0,s-f),l=e.substring(s+c))}return 2*u.length>=t.length?[o,r,i,l,u]:null}var o=t.length>e.length?t:e,r=t.length>e.length?e:t;if(o.length<4||2*r.length<o.length)return null;var i,l,a,s,u,c=n(o,r,Math.ceil(o.length/4)),f=n(o,r,Math.ceil(o.length/2));return c||f?(i=!f||c&&c[4].length>f[4].length?c:f,t.length>e.length?(l=i[0],a=i[1],s=i[2],u=i[3]):(s=i[0],u=i[1],l=i[2],a=i[3]),[l,a,s,u,i[4]]):null}(t,e);if(l){var a=l[0],s=l[1],u=l[2],c=l[3],f=l[4],h=d(a,u),p=d(s,c);return h.concat([[b,f]],p)}return function(t,e){for(var n=t.length,o=e.length,r=Math.ceil((n+o)/2),i=r,l=2*r,a=new Array(l),s=new Array(l),u=0;u<l;u++)a[u]=-1,s[u]=-1;a[i+1]=0,s[i+1]=0;for(var c=n-o,f=c%2!=0,h=0,p=0,d=0,y=0,v=0;v<r;v++){for(var b=-v+h;b<=v-p;b+=2){for(var g=i+b,m=(k=b==-v||b!=v&&a[g-1]<a[g+1]?a[g+1]:a[g-1]+1)-b;k<n&&m<o&&t.charAt(k)==e.charAt(m);)k++,m++;if(a[g]=k,n<k)p+=2;else if(o<m)h+=2;else if(f){if(0<=(w=i+c-b)&&w<l&&-1!=s[w])if((O=n-s[w])<=k)return E(t,e,k,m)}}for(var _=-v+d;_<=v-y;_+=2){for(var O,w=i+_,x=(O=_==-v||_!=v&&s[w-1]<s[w+1]?s[w+1]:s[w-1]+1)-_;O<n&&x<o&&t.charAt(n-O-1)==e.charAt(o-x-1);)O++,x++;if(s[w]=O,n<O)y+=2;else if(o<x)d+=2;else if(!f){if(0<=(g=i+c-_)&&g<l&&-1!=a[g]){var k=a[g],m=i+k-g;if((O=n-O)<=k)return E(t,e,k,m)}}}}return[[N,t],[j,e]]}(t,e)}(t=t.substring(0,t.length-o),e=e.substring(0,e.length-o));return r&&l.unshift([b,r]),i&&l.push([b,i]),function t(e){e.push([b,""]);for(var n,o=0,r=0,i=0,l="",a="";o<e.length;)switch(e[o][0]){case j:i++,a+=e[o][1],o++;break;case N:r++,l+=e[o][1],o++;break;case b:1<r+i?(0!==r&&0!==i&&(0!==(n=y(a,l))&&(0<o-r-i&&e[o-r-i-1][0]==b?e[o-r-i-1][1]+=a.substring(0,n):(e.splice(0,0,[b,a.substring(0,n)]),o++),a=a.substring(n),l=l.substring(n)),0!==(n=v(a,l))&&(e[o][1]=a.substring(a.length-n)+e[o][1],a=a.substring(0,a.length-n),l=l.substring(0,l.length-n))),0===r?e.splice(o-i,r+i,[j,a]):0===i?e.splice(o-r,r+i,[N,l]):e.splice(o-r-i,r+i,[N,l],[j,a]),o=o-r-i+(r?1:0)+(i?1:0)+1):0!==o&&e[o-1][0]==b?(e[o-1][1]+=e[o][1],e.splice(o,1)):o++,r=i=0,a=l=""}""===e[e.length-1][1]&&e.pop();var s=!1;for(o=1;o<e.length-1;)e[o-1][0]==b&&e[o+1][0]==b&&(e[o][1].substring(e[o][1].length-e[o-1][1].length)==e[o-1][1]?(e[o][1]=e[o-1][1]+e[o][1].substring(0,e[o][1].length-e[o-1][1].length),e[o+1][1]=e[o-1][1]+e[o+1][1],e.splice(o-1,1),s=!0):e[o][1].substring(0,e[o+1][1].length)==e[o+1][1]&&(e[o-1][1]+=e[o+1][1],e[o][1]=e[o][1].substring(e[o+1][1].length)+e[o+1][1],e.splice(o+1,1),s=!0)),o++;s&&t(e)}(l),null!=n&&(l=function(t,e){var n=function(t,e){if(0===e)return[b,t];for(var n=0,o=0;o<t.length;o++){var r=t[o];if(r[0]===N||r[0]===b){var i=n+r[1].length;if(e===i)return[o+1,t];if(e<i){t=t.slice();var l=e-n,a=[r[0],r[1].slice(0,l)],s=[r[0],r[1].slice(l)];return t.splice(o,1,a,s),[o+1,t]}n=i}}throw new Error("cursor_pos is out of bounds!")}(t,e),o=n[1],r=n[0],i=o[r],l=o[r+1];if(null==i)return t;if(i[0]!==b)return t;if(null!=l&&i[1]+l[1]===l[1]+i[1])return o.splice(r,2,l,i),s(o,r,2);if(null==l||0!==l[1].indexOf(i[1]))return t;o.splice(r,2,[l[0],i[1]],[0,i[1]]);var a=l[1].slice(i[1].length);return 0<a.length&&o.splice(r+2,0,[l[0],a]),s(o,r,3)}(l,n)),function(t){function e(t){return 56320<=t.charCodeAt(0)&&t.charCodeAt(0)<=57343}for(var n=!1,o=2;o<t.length;o+=1)t[o-2][0]===b&&function(t){return 55296<=t.charCodeAt(t.length-1)&&t.charCodeAt(t.length-1)<=56319}(t[o-2][1])&&t[o-1][0]===N&&e(t[o-1][1])&&t[o][0]===j&&e(t[o][1])&&(n=!0,t[o-1][1]=t[o-2][1].slice(-1)+t[o-1][1],t[o][1]=t[o-2][1].slice(-1)+t[o][1],t[o-2][1]=t[o-2][1].slice(0,-1));if(!n)return t;for(var r=[],o=0;o<t.length;o+=1)0<t[o][1].length&&r.push(t[o]);return r}(l)}function E(t,e,n,o){var r=t.substring(0,n),i=e.substring(0,o),l=t.substring(n),a=e.substring(o),s=d(r,i),u=d(l,a);return s.concat(u)}function y(t,e){if(!t||!e||t.charAt(0)!=e.charAt(0))return 0;for(var n=0,o=Math.min(t.length,e.length),r=o,i=0;n<r;)t.substring(i,r)==e.substring(i,r)?i=n=r:o=r,r=Math.floor((o-n)/2+n);return r}function v(t,e){if(!t||!e||t.charAt(t.length-1)!=e.charAt(e.length-1))return 0;for(var n=0,o=Math.min(t.length,e.length),r=o,i=0;n<r;)t.substring(t.length-r,t.length-i)==e.substring(e.length-r,e.length-i)?i=n=r:o=r,r=Math.floor((o-n)/2+n);return r}function s(t,e,n){for(var o,r,i=e+n-1;0<=i&&e-1<=i;i--){i+1<t.length&&(o=t[i],r=t[i+1],o[0]===r[1]&&t.splice(i,2,[o[0],o[1]+r[1]]))}return t}var N=-1,j=1,b=0,n=d;n.INSERT=j,n.DELETE=N,n.EQUAL=b,t.exports=n},function(t,e){function n(t){var e=[];for(var n in t)e.push(n);return e}(t.exports="function"==typeof Object.keys?Object.keys:n).shim=n},function(t,e){function n(t){return"[object Arguments]"==Object.prototype.toString.call(t)}function o(t){return t&&"object"==typeof t&&"number"==typeof t.length&&Object.prototype.hasOwnProperty.call(t,"callee")&&!Object.prototype.propertyIsEnumerable.call(t,"callee")||!1}var r="[object Arguments]"==function(){return Object.prototype.toString.call(arguments)}();(e=t.exports=r?n:o).supported=n,e.unsupported=o},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}Object.defineProperty(e,"__esModule",{value:!0});var v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},b=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],o=!0,r=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(o=(l=a.next()).done)&&(n.push(l.value),!e||n.length!==e);o=!0);}catch(t){r=!0,i=t}finally{try{!o&&a.return&&a.return()}finally{if(r)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},r=function(t,e,n){return e&&s(t.prototype,e),n&&s(t,n),t},f=o(n(4)),g=o(n(20)),m=o(n(0)),h=o(n(13)),c=o(n(31)),_=n(3),O=o(_),i=o(n(14)),u=o(n(21)),p=o(n(12)),w=o(n(2)),d=/^[ -~]*$/,l=(r(a,[{key:"applyDelta",value:function(t){var p=this,d=!1;this.scroll.update();var y=this.scroll.length();return this.scroll.batchStart(),(t=t.reduce(function(t,e){if(1===e.insert){var n=(0,u.default)(e.attributes);return delete n.image,t.insert({image:e.attributes.image},n)}if(null==e.attributes||!0!==e.attributes.list&&!0!==e.attributes.bullet||((e=(0,u.default)(e)).attributes.list?e.attributes.list="ordered":(e.attributes.list="bullet",delete e.attributes.bullet)),"string"!=typeof e.insert)return t.push(e);var o=e.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n");return t.insert(o,e.attributes)},new f.default)).reduce(function(e,t){var n=t.retain||t.delete||t.insert.length||1,o=t.attributes||{};if(null!=t.insert){if("string"==typeof t.insert){var r=t.insert;r.endsWith("\n")&&d&&(d=!1,r=r.slice(0,-1)),y<=e&&!r.endsWith("\n")&&(d=!0),p.scroll.insertAt(e,r);var i,l,a=p.scroll.line(e),s=b(a,2),u=s[0],c=s[1],f=(0,w.default)({},(0,_.bubbleFormats)(u));u instanceof O.default&&(i=u.descendant(m.default.Leaf,c),l=b(i,1)[0],f=(0,w.default)(f,(0,_.bubbleFormats)(l))),o=g.default.attributes.diff(f,o)||{}}else if("object"===v(t.insert)){var h=Object.keys(t.insert)[0];if(null==h)return e;p.scroll.insertAt(e,h,t.insert[h])}y+=n}return Object.keys(o).forEach(function(t){p.scroll.formatAt(e,n,t,o[t])}),e+n},0),t.reduce(function(t,e){return"number"==typeof e.delete?(p.scroll.deleteAt(t,e.delete),t):t+(e.retain||e.insert.length||1)},0),this.scroll.batchEnd(),this.update(t)}},{key:"deleteText",value:function(t,e){return this.scroll.deleteAt(t,e),this.update((new f.default).retain(t).delete(e))}},{key:"formatLine",value:function(l,e){var a=this,s=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};return this.scroll.update(),Object.keys(s).forEach(function(r){var t,i;null!=a.scroll.whitelist&&!a.scroll.whitelist[r]||(t=a.scroll.lines(l,Math.max(e,1)),i=e,t.forEach(function(t){var e,n,o=t.length();t instanceof h.default?(e=l-t.offset(a.scroll),n=t.newlineIndex(e+i)-e+1,t.formatAt(e,n,r,s[r])):t.format(r,s[r]),i-=o}))}),this.scroll.optimize(),this.update((new f.default).retain(l).retain(e,(0,u.default)(s)))}},{key:"formatText",value:function(e,n){var o=this,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};return Object.keys(r).forEach(function(t){o.scroll.formatAt(e,n,t,r[t])}),this.update((new f.default).retain(e).retain(n,(0,u.default)(r)))}},{key:"getContents",value:function(t,e){return this.delta.slice(t,t+e)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(t,e){return t.concat(e.delta())},new f.default)}},{key:"getFormat",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,n=[],o=[];0===e?this.scroll.path(t).forEach(function(t){var e=b(t,1)[0];e instanceof O.default?n.push(e):e instanceof m.default.Leaf&&o.push(e)}):(n=this.scroll.lines(t,e),o=this.scroll.descendants(m.default.Leaf,t,e));var r=[n,o].map(function(t){if(0===t.length)return{};for(var e=(0,_.bubbleFormats)(t.shift());0<Object.keys(e).length;){var n=t.shift();if(null==n)return e;e=function(n,o){return Object.keys(o).reduce(function(t,e){return null==n[e]||(o[e]===n[e]?t[e]=o[e]:Array.isArray(o[e])?o[e].indexOf(n[e])<0&&(t[e]=o[e].concat([n[e]])):t[e]=[o[e],n[e]]),t},{})}((0,_.bubbleFormats)(n),e)}return e});return w.default.apply(w.default,r)}},{key:"getText",value:function(t,e){return this.getContents(t,e).filter(function(t){return"string"==typeof t.insert}).map(function(t){return t.insert}).join("")}},{key:"insertEmbed",value:function(t,e,n){return this.scroll.insertAt(t,e,n),this.update((new f.default).retain(t).insert((i=n,(r=e)in(o={})?Object.defineProperty(o,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):o[r]=i,o)));var o,r,i}},{key:"insertText",value:function(e,n){var o=this,r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};return n=n.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),this.scroll.insertAt(e,n),Object.keys(r).forEach(function(t){o.scroll.formatAt(e,n.length,t,r[t])}),this.update((new f.default).retain(e).insert(n,(0,u.default)(r)))}},{key:"isBlank",value:function(){if(0==this.scroll.children.length)return!0;if(1<this.scroll.children.length)return!1;var t=this.scroll.children.head;return t.statics.blotName===O.default.blotName&&!(1<t.children.length)&&t.children.head instanceof i.default}},{key:"removeFormat",value:function(t,e){var n=this.getText(t,e),o=this.scroll.line(t+e),r=b(o,2),i=r[0],l=r[1],a=0,s=new f.default;null!=i&&(a=i instanceof h.default?i.newlineIndex(l)-l+1:i.length()-l,s=i.delta().slice(l,l+a-1).insert("\n"));var u=this.getContents(t,e+a).diff((new f.default).insert(n).concat(s)),c=(new f.default).retain(t).concat(u);return this.applyDelta(c)}},{key:"update",value:function(t){var e,n,o,r,i,l,a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:[],s=2<arguments.length&&void 0!==arguments[2]?arguments[2]:void 0,u=this.delta;return 1===a.length&&"characterData"===a[0].type&&a[0].target.data.match(d)&&m.default.find(a[0].target)?(e=m.default.find(a[0].target),n=(0,_.bubbleFormats)(e),o=e.offset(this.scroll),r=a[0].oldValue.replace(c.default.CONTENTS,""),i=(new f.default).insert(r),l=(new f.default).insert(e.value()),t=(new f.default).retain(o).concat(i.diff(l,s)).reduce(function(t,e){return e.insert?t.insert(e.insert,n):t.push(e)},new f.default),this.delta=u.compose(t)):(this.delta=this.getDelta(),t&&(0,p.default)(u.compose(t),this.delta)||(t=u.diff(this.delta,s))),t}}]),a);function a(t){(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")})(this,a),this.scroll=t,this.delta=this.getDelta()}function s(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}e.default=l},function(t,e){"use strict";function u(){}function i(t,e,n){this.fn=t,this.context=e,this.once=n||!1}function n(){this._events=new u,this._eventsCount=0}var o=Object.prototype.hasOwnProperty,p="~";Object.create&&(u.prototype=Object.create(null),(new u).__proto__||(p=!1)),n.prototype.eventNames=function(){var t,e,n=[];if(0===this._eventsCount)return n;for(e in t=this._events)o.call(t,e)&&n.push(p?e.slice(1):e);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(t)):n},n.prototype.listeners=function(t,e){var n=p?p+t:t,o=this._events[n];if(e)return!!o;if(!o)return[];if(o.fn)return[o.fn];for(var r=0,i=o.length,l=new Array(i);r<i;r++)l[r]=o[r].fn;return l},n.prototype.emit=function(t,e,n,o,r,i){var l=p?p+t:t;if(!this._events[l])return!1;var a,s=this._events[l],u=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),u){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,r),!0;case 6:return s.fn.call(s.context,e,n,o,r,i),!0}for(h=1,a=new Array(u-1);h<u;h++)a[h-1]=arguments[h];s.fn.apply(s.context,a)}else for(var c,f=s.length,h=0;h<f;h++)switch(s[h].once&&this.removeListener(t,s[h].fn,void 0,!0),u){case 1:s[h].fn.call(s[h].context);break;case 2:s[h].fn.call(s[h].context,e);break;case 3:s[h].fn.call(s[h].context,e,n);break;case 4:s[h].fn.call(s[h].context,e,n,o);break;default:if(!a)for(c=1,a=new Array(u-1);c<u;c++)a[c-1]=arguments[c];s[h].fn.apply(s[h].context,a)}return!0},n.prototype.on=function(t,e,n){var o=new i(e,n||this),r=p?p+t:t;return this._events[r]?this._events[r].fn?this._events[r]=[this._events[r],o]:this._events[r].push(o):(this._events[r]=o,this._eventsCount++),this},n.prototype.once=function(t,e,n){var o=new i(e,n||this,!0),r=p?p+t:t;return this._events[r]?this._events[r].fn?this._events[r]=[this._events[r],o]:this._events[r].push(o):(this._events[r]=o,this._eventsCount++),this},n.prototype.removeListener=function(t,e,n,o){var r=p?p+t:t;if(!this._events[r])return this;if(!e)return 0==--this._eventsCount?this._events=new u:delete this._events[r],this;var i=this._events[r];if(i.fn)i.fn!==e||o&&!i.once||n&&i.context!==n||(0==--this._eventsCount?this._events=new u:delete this._events[r]);else{for(var l=0,a=[],s=i.length;l<s;l++)(i[l].fn!==e||o&&!i[l].once||n&&i[l].context!==n)&&a.push(i[l]);a.length?this._events[r]=1===a.length?a[0]:a:0==--this._eventsCount?this._events=new u:delete this._events[r]}return this},n.prototype.removeAllListeners=function(t){var e;return t?(e=p?p+t:t,this._events[e]&&(0==--this._eventsCount?this._events=new u:delete this._events[e])):(this._events=new u,this._eventsCount=0),this},n.prototype.off=n.prototype.removeListener,n.prototype.addListener=n.prototype.on,n.prototype.setMaxListeners=function(){return this},n.prefixed=p,n.EventEmitter=n,void 0!==t&&(t.exports=n)},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function l(t){return t instanceof s.default||t instanceof p.BlockEmbed}Object.defineProperty(e,"__esModule",{value:!0});function f(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:f(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var h=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],o=!0,r=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(o=(l=a.next()).done)&&(n.push(l.value),!e||n.length!==e);o=!0);}catch(t){r=!0,i=t}finally{try{!o&&a.return&&a.return()}finally{if(r)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},r=function(t,e,n){return e&&b(t.prototype,e),n&&b(t,n),t},a=o(n(0)),i=o(n(9)),p=n(3),s=o(p),d=o(n(14)),y=o(n(13)),u=o(n(23)),c=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(v,a.default.Scroll),r(v,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(t,e){var n,o=this.line(t),r=h(o,2),i=r[0],l=r[1],a=this.line(t+e),s=h(a,1)[0];if(f(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"deleteAt",this).call(this,t,e),null!=s&&i!==s&&0<l){if(i instanceof p.BlockEmbed||s instanceof p.BlockEmbed)return void this.optimize();if(i instanceof y.default){var u=i.newlineIndex(i.length(),!0);if(-1<u&&(i=i.split(u+1))===s)return void this.optimize()}else s instanceof y.default&&-1<(n=s.newlineIndex(0))&&s.split(n+1);var c=s.children.head instanceof d.default?null:s.children.head;i.moveChildren(s,c),i.remove()}this.optimize()}},{key:"enable",value:function(){var t=!(0<arguments.length&&void 0!==arguments[0])||arguments[0];this.domNode.setAttribute("contenteditable",t)}},{key:"formatAt",value:function(t,e,n,o){null!=this.whitelist&&!this.whitelist[n]||(f(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"formatAt",this).call(this,t,e,n,o),this.optimize())}},{key:"insertAt",value:function(t,e,n){var o,r;null!=n&&null!=this.whitelist&&!this.whitelist[e]||(t>=this.length()?null==n||null==a.default.query(e,a.default.Scope.BLOCK)?(o=a.default.create(this.statics.defaultChild),this.appendChild(o),null==n&&e.endsWith("\n")&&(e=e.slice(0,-1)),o.insertAt(0,e,n)):(r=a.default.create(e,n),this.appendChild(r)):f(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"insertAt",this).call(this,t,e,n),this.optimize())}},{key:"insertBefore",value:function(t,e){var n;t.statics.scope===a.default.Scope.INLINE_BLOT&&((n=a.default.create(this.statics.defaultChild)).appendChild(t),t=n),f(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"insertBefore",this).call(this,t,e)}},{key:"leaf",value:function(t){return this.path(t).pop()||[null,-1]}},{key:"line",value:function(t){return t===this.length()?this.line(t-1):this.descendant(l,t)}},{key:"lines",value:function(){return function o(t,e,n){var r=[],i=n;return t.children.forEachAt(e,n,function(t,e,n){l(t)?r.push(t):t instanceof a.default.Container&&(r=r.concat(o(t,e,i))),i-=n}),r}(this,0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,1<arguments.length&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE)}},{key:"optimize",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[],e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};!0!==this.batch&&(f(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"optimize",this).call(this,t,e),0<t.length&&this.emitter.emit(i.default.events.SCROLL_OPTIMIZE,t,e))}},{key:"path",value:function(t){return f(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"path",this).call(this,t).slice(1)}},{key:"update",value:function(t){var e;!0!==this.batch&&(e=i.default.sources.USER,"string"==typeof t&&(e=t),Array.isArray(t)||(t=this.observer.takeRecords()),0<t.length&&this.emitter.emit(i.default.events.SCROLL_BEFORE_UPDATE,e,t),f(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"update",this).call(this,t.concat([])),0<t.length&&this.emitter.emit(i.default.events.SCROLL_UPDATE,e,t))}}]),v);function v(t,e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,v);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,t));return n.emitter=e.emitter,Array.isArray(e.whitelist)&&(n.whitelist=e.whitelist.reduce(function(t,e){return t[e]=!0,t},{})),n.domNode.addEventListener("DOMNodeInserted",function(){}),n.optimize(),n.enable(),n}function b(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}c.blotName="scroll",c.className="ql-editor",c.tagName="DIV",c.defaultChild="block",c.allowedChildren=[s.default,p.BlockEmbed,u.default],e.default=c},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function l(t,n,o){return"object"===(void 0===n?"undefined":y(n))?Object.keys(n).reduce(function(t,e){return l(t,e,n[e])},t):t.reduce(function(t,e){return e.attributes&&e.attributes[n]?t.push(e):t.insert(e.insert,(0,g.default)({},s({},n,o),e.attributes))},new m.default)}function r(t){return t.nodeType!==Node.ELEMENT_NODE?{}:t["__ql-computed-style"]||(t["__ql-computed-style"]=window.getComputedStyle(t))}function u(t,e){for(var n="",o=t.ops.length-1;0<=o&&n.length<e.length;--o){var r=t.ops[o];if("string"!=typeof r.insert)break;n=r.insert+n}return n.slice(-1*e.length)===e}function i(t){return 0!==t.childNodes.length&&-1<["block","list-item"].indexOf(r(t).display)}function a(t,e,n){return l(n,t,!0)}function c(n,t){var e=_.default.Attributor.Attribute.keys(n),o=_.default.Attributor.Class.keys(n),r=_.default.Attributor.Style.keys(n),i={};return e.concat(o).concat(r).forEach(function(t){var e=_.default.query(t,_.default.Scope.ATTRIBUTE);null!=e&&(i[e.attrName]=e.value(n),i[e.attrName])||(null==(e=L[t])||e.attrName!==t&&e.keyName!==t||(i[e.attrName]=e.value(n)||void 0),null==(e=M[t])||e.attrName!==t&&e.keyName!==t||(e=M[t],i[e.attrName]=e.value(n)||void 0))}),0<Object.keys(i).length&&(t=l(t,i)),t}function f(t,e){var n,o,r=_.default.query(t);return null==r||(r.prototype instanceof _.default.Embed?(n={},null!=(o=r.value(t))&&(n[r.blotName]=o,e=(new m.default).insert(n,r.formats(t)))):"function"==typeof r.formats&&(e=l(e,r.blotName,r.formats(t)))),e}function h(t,e){return u(e,"\n")||(i(t)||0<e.length()&&t.nextSibling&&i(t.nextSibling))&&e.insert("\n"),e}function p(t,e){var n;return i(t)&&null!=t.nextElementSibling&&!u(e,"\n\n")&&(n=t.offsetHeight+parseFloat(r(t).marginTop)+parseFloat(r(t).marginBottom),t.nextElementSibling.offsetTop>t.offsetTop*****n&&e.insert("\n")),e}function d(t,e){var n,o=t.data;return"O:P"===t.parentNode.tagName?e.insert(o.trim()):0===o.trim().length&&t.parentNode.classList.contains("ql-clipboard")?e:(r(t.parentNode).whiteSpace.startsWith("pre")||(n=function(t,e){return(e=e.replace(/[^\u00a0]/g,"")).length<1&&t?" ":e},o=(o=o.replace(/\r\n/g," ").replace(/\n/g," ")).replace(/\s\s+/g,n.bind(n,!0)),(null==t.previousSibling&&i(t.parentNode)||null!=t.previousSibling&&i(t.previousSibling))&&(o=o.replace(/^\s+/,n.bind(n,!1))),(null==t.nextSibling&&i(t.parentNode)||null!=t.nextSibling&&i(t.nextSibling))&&(o=o.replace(/\s+$/,n.bind(n,!1)))),e.insert(o))}Object.defineProperty(e,"__esModule",{value:!0}),e.matchText=e.matchSpacing=e.matchNewline=e.matchBlot=e.matchAttributor=e.default=void 0;var y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],o=!0,r=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(o=(l=a.next()).done)&&(n.push(l.value),!e||n.length!==e);o=!0);}catch(t){r=!0,i=t}finally{try{!o&&a.return&&a.return()}finally{if(r)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},b=function(t,e,n){return e&&B(t.prototype,e),n&&B(t,n),t},g=o(n(2)),m=o(n(4)),_=o(n(0)),O=o(n(6)),w=o(n(10)),x=o(n(7)),k=n(34),E=n(35),N=o(n(13)),j=n(24),A=n(36),q=n(37),T=n(38),P=(0,w.default)("quill:clipboard"),S="__ql-matcher",C=[[Node.TEXT_NODE,d],[Node.TEXT_NODE,h],["br",function(t,e){return u(e,"\n")||e.insert("\n"),e}],[Node.ELEMENT_NODE,h],[Node.ELEMENT_NODE,f],[Node.ELEMENT_NODE,p],[Node.ELEMENT_NODE,c],[Node.ELEMENT_NODE,function(t,e){var n={},o=t.style||{};return o.fontStyle&&"italic"===r(t).fontStyle&&(n.italic=!0),o.fontWeight&&(r(t).fontWeight.startsWith("bold")||700<=parseInt(r(t).fontWeight))&&(n.bold=!0),0<Object.keys(n).length&&(e=l(e,n)),0<parseFloat(o.textIndent||0)&&(e=(new m.default).insert("\t").concat(e)),e}],["li",function(t,e){var n=_.default.query(t);if(null==n||"list-item"!==n.blotName||!u(e,"\n"))return e;for(var o=-1,r=t.parentNode;!r.classList.contains("ql-clipboard");)"list"===(_.default.query(r)||{}).blotName&&(o+=1),r=r.parentNode;return o<=0?e:e.compose((new m.default).retain(e.length()-1).retain(1,{indent:o}))}],["b",a.bind(a,"bold")],["i",a.bind(a,"italic")],["style",function(){return new m.default}]],L=[k.AlignAttribute,A.DirectionAttribute].reduce(function(t,e){return t[e.keyName]=e,t},{}),M=[k.AlignStyle,E.BackgroundStyle,j.ColorStyle,A.DirectionStyle,q.FontStyle,T.SizeStyle].reduce(function(t,e){return t[e.keyName]=e,t},{}),R=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(I,x.default),b(I,[{key:"addMatcher",value:function(t,e){this.matchers.push([t,e])}},{key:"convert",value:function(t){if("string"==typeof t)return this.container.innerHTML=t.replace(/\>\r?\n +\</g,"><"),this.convert();var e=this.quill.getFormat(this.quill.selection.savedRange.index);if(e[N.default.blotName]){var n=this.container.innerText;return this.container.innerHTML="",(new m.default).insert(n,s({},N.default.blotName,e[N.default.blotName]))}var o=this.prepareMatching(),r=v(o,2),i=r[0],l=r[1],a=function o(r,i,l){return r.nodeType===r.TEXT_NODE?l.reduce(function(t,e){return e(r,t)},new m.default):r.nodeType===r.ELEMENT_NODE?[].reduce.call(r.childNodes||[],function(t,n){var e=o(n,i,l);return n.nodeType===r.ELEMENT_NODE&&(e=i.reduce(function(t,e){return e(n,t)},e),e=(n[S]||[]).reduce(function(t,e){return e(n,t)},e)),t.concat(e)},new m.default):new m.default}(this.container,i,l);return u(a,"\n")&&null==a.ops[a.ops.length-1].attributes&&(a=a.compose((new m.default).retain(a.length()-1).delete(1))),P.log("convert",this.container.innerHTML,a),this.container.innerHTML="",a}},{key:"dangerouslyPasteHTML",value:function(t,e){var n,o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:O.default.sources.API;"string"==typeof t?(this.quill.setContents(this.convert(t),e),this.quill.setSelection(0,O.default.sources.SILENT)):(n=this.convert(e),this.quill.updateContents((new m.default).retain(t).concat(n),o),this.quill.setSelection(t+n.length(),O.default.sources.SILENT))}},{key:"onPaste",value:function(t){var e,n,o,r=this;!t.defaultPrevented&&this.quill.isEnabled()&&(e=this.quill.getSelection(),n=(new m.default).retain(e.index),o=this.quill.scrollingContainer.scrollTop,this.container.focus(),this.quill.selection.update(O.default.sources.SILENT),setTimeout(function(){n=n.concat(r.convert()).delete(e.length),r.quill.updateContents(n,O.default.sources.USER),r.quill.setSelection(n.length()-e.length,O.default.sources.SILENT),r.quill.scrollingContainer.scrollTop=o,r.quill.focus()},1))}},{key:"prepareMatching",value:function(){var r=this,i=[],l=[];return this.matchers.forEach(function(t){var e=v(t,2),n=e[0],o=e[1];switch(n){case Node.TEXT_NODE:l.push(o);break;case Node.ELEMENT_NODE:i.push(o);break;default:[].forEach.call(r.container.querySelectorAll(n),function(t){t[S]=t[S]||[],t[S].push(o)})}}),[i,l]}}]),I);function I(t,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,I);var i=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(I.__proto__||Object.getPrototypeOf(I)).call(this,t,r));return i.quill.root.addEventListener("paste",i.onPaste.bind(i)),i.container=i.quill.addContainer("ql-clipboard"),i.container.setAttribute("contenteditable",!0),i.container.setAttribute("tabindex",-1),i.matchers=[],C.concat(i.options.matchers).forEach(function(t){var e=v(t,2),n=e[0],o=e[1];!r.matchVisual&&o===p||i.addMatcher(n,o)}),i}function B(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}R.DEFAULTS={matchers:[],matchVisual:!0},e.default=R,e.matchAttributor=c,e.matchBlot=f,e.matchNewline=h,e.matchSpacing=p,e.matchText=d},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function r(t){var e,n,o=t.reduce(function(t,e){return t+(e.delete||0)},0),r=t.length()-o;return null!=(n=(e=t).ops[e.ops.length-1])&&(null!=n.insert?"string"==typeof n.insert&&n.insert.endsWith("\n"):null!=n.attributes&&Object.keys(n.attributes).some(function(t){return null!=l.default.query(t,l.default.Scope.BLOCK)}))&&--r,r}Object.defineProperty(e,"__esModule",{value:!0}),e.getLastChangeIndex=e.default=void 0;var i=function(t,e,n){return e&&c(t.prototype,e),n&&c(t,n),t},l=o(n(0)),a=o(n(6)),s=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(u,o(n(7)).default),i(u,[{key:"change",value:function(t,e){var n,o;0!==this.stack[t].length&&(n=this.stack[t].pop(),this.stack[e].push(n),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(n[t],a.default.sources.USER),this.ignoreChange=!1,o=r(n[t]),this.quill.setSelection(o))}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(t,e){var n,o,r;0!==t.ops.length&&(this.stack.redo=[],r=this.quill.getContents().diff(e),n=Date.now(),this.lastRecorded+this.options.delay>n&&0<this.stack.undo.length?(o=this.stack.undo.pop(),r=r.compose(o.undo),t=o.redo.compose(t)):this.lastRecorded=n,this.stack.undo.push({redo:t,undo:r}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(e){this.stack.undo.forEach(function(t){t.undo=e.transform(t.undo,!0),t.redo=e.transform(t.redo,!0)}),this.stack.redo.forEach(function(t){t.undo=e.transform(t.undo,!0),t.redo=e.transform(t.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]),u);function u(t,e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u);var r=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(u.__proto__||Object.getPrototypeOf(u)).call(this,t,e));return r.lastRecorded=0,r.ignoreChange=!1,r.clear(),r.quill.on(a.default.events.EDITOR_CHANGE,function(t,e,n,o){t!==a.default.events.TEXT_CHANGE||r.ignoreChange||(r.options.userOnly&&o!==a.default.sources.USER?r.transform(e):r.record(e,n))}),r.quill.keyboard.addBinding({key:"Z",shortKey:!0},r.undo.bind(r)),r.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},r.redo.bind(r)),/Win/i.test(navigator.platform)&&r.quill.keyboard.addBinding({key:"Y",shortKey:!0},r.redo.bind(r)),r}function c(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}s.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1},e.default=s,e.getLastChangeIndex=r},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.IndentClass=void 0;function l(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:l(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var o=function(t,e,n){return e&&u(t.prototype,e),n&&u(t,n),t},r=n(0),i=r&&r.__esModule?r:{default:r},a=new(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(s,i.default.Attributor.Class),o(s,[{key:"add",value:function(t,e){var n;return"+1"!==e&&"-1"!==e||(n=this.value(t)||0,e="+1"===e?n+1:n-1),0===e?(this.remove(t),!0):l(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"add",this).call(this,t,e)}},{key:"canAdd",value:function(t,e){return l(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"canAdd",this).call(this,t,e)||l(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"canAdd",this).call(this,t,parseInt(e))}},{key:"value",value:function(t){return parseInt(l(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"value",this).call(this,t))||void 0}}]),s)("indent","ql-indent",{scope:i.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});function s(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,s),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}function u(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}e.IndentClass=a},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=n(3),r=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(i,(o&&o.__esModule?o:{default:o}).default),i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}r.blotName="blockquote",r.tagName="blockquote",e.default=r},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=function(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),t},r=n(3),i=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(l,(r&&r.__esModule?r:{default:r}).default),o(l,null,[{key:"formats",value:function(t){return this.tagName.indexOf(t.tagName)+1}}]),l);function l(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,l),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}function a(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}i.blotName="header",i.tagName=["H1","H2","H3","H4","H5","H6"],e.default=i},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function l(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function r(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.ListItem=void 0;function a(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:a(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var s=function(t,e,n){return e&&d(t.prototype,e),n&&d(t,n),t},u=o(n(0)),c=o(n(3)),f=o(n(23)),h=(r(p,c.default),s(p,[{key:"format",value:function(t,e){t!==y.blotName||e?a(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"format",this).call(this,t,e):this.replaceWith(u.default.create(this.statics.scope))}},{key:"remove",value:function(){null==this.prev&&null==this.next?this.parent.remove():a(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(t,e){return this.parent.isolate(this.offset(this.parent),this.length()),t===this.parent.statics.blotName?(this.parent.replaceWith(t,e),this):(this.parent.unwrap(),a(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"replaceWith",this).call(this,t,e))}}],[{key:"formats",value:function(t){return t.tagName===this.tagName?void 0:a(p.__proto__||Object.getPrototypeOf(p),"formats",this).call(this,t)}}]),p);function p(){return i(this,p),l(this,(p.__proto__||Object.getPrototypeOf(p)).apply(this,arguments))}function d(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}h.blotName="list-item",h.tagName="LI";var y=(r(v,f.default),s(v,null,[{key:"create",value:function(t){var e="ordered"===t?"OL":"UL",n=a(v.__proto__||Object.getPrototypeOf(v),"create",this).call(this,e);return"checked"!==t&&"unchecked"!==t||n.setAttribute("data-checked","checked"===t),n}},{key:"formats",value:function(t){return"OL"===t.tagName?"ordered":"UL"===t.tagName?t.hasAttribute("data-checked")?"true"===t.getAttribute("data-checked")?"checked":"unchecked":"bullet":void 0}}]),s(v,[{key:"format",value:function(t,e){0<this.children.length&&this.children.tail.format(t,e)}},{key:"formats",value:function(){return t={},e=this.statics.blotName,n=this.statics.formats(this.domNode),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var t,e,n}},{key:"insertBefore",value:function(t,e){var n,o;t instanceof h?a(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"insertBefore",this).call(this,t,e):(n=null==e?this.length():e.offset(this),(o=this.split(n)).parent.insertBefore(t,o))}},{key:"optimize",value:function(t){a(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"optimize",this).call(this,t);var e=this.next;null!=e&&e.prev===this&&e.statics.blotName===this.statics.blotName&&e.domNode.tagName===this.domNode.tagName&&e.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(e.moveChildren(this),e.remove())}},{key:"replace",value:function(t){var e;t.statics.blotName!==this.statics.blotName&&(e=u.default.create(this.statics.defaultChild),t.moveChildren(e),this.appendChild(e)),a(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"replace",this).call(this,t)}}]),v);function v(o){i(this,v);function t(t){var e,n;t.target.parentNode===o&&(e=r.statics.formats(o),n=u.default.find(t.target),"checked"===e?n.format("list","unchecked"):"unchecked"===e&&n.format("list","checked"))}var r=l(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,o));return o.addEventListener("touchstart",t),o.addEventListener("mousedown",t),r}y.blotName="list",y.scope=u.default.Scope.BLOCK_BLOT,y.tagName=["OL","UL"],y.defaultChild="list-item",y.allowedChildren=[h],e.ListItem=h,e.default=y},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=n(39),r=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(i,(o&&o.__esModule?o:{default:o}).default),i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}r.blotName="italic",r.tagName=["EM","I"],e.default=r},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=function(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),t},r=n(5),i=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(l,(r&&r.__esModule?r:{default:r}).default),o(l,null,[{key:"create",value:function(t){return"super"===t?document.createElement("sup"):"sub"===t?document.createElement("sub"):function t(e,n,o){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,n);if(void 0===r){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,o)}if("value"in r)return r.value;var l=r.get;return void 0!==l?l.call(o):void 0}(l.__proto__||Object.getPrototypeOf(l),"create",this).call(this,t)}},{key:"formats",value:function(t){return"SUB"===t.tagName?"sub":"SUP"===t.tagName?"super":void 0}}]),l);function l(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,l),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}function a(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}i.blotName="script",i.tagName=["SUB","SUP"],e.default=i},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=n(5),r=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(i,(o&&o.__esModule?o:{default:o}).default),i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}r.blotName="strike",r.tagName="S",e.default=r},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=n(5),r=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(i,(o&&o.__esModule?o:{default:o}).default),i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}r.blotName="underline",r.tagName="U",e.default=r},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});function l(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:l(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var o=function(t,e,n){return e&&f(t.prototype,e),n&&f(t,n),t},r=n(0),i=r&&r.__esModule?r:{default:r},a=n(15),s=["alt","height","width"],u=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(c,i.default.Embed),o(c,[{key:"format",value:function(t,e){-1<s.indexOf(t)?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):l(c.prototype.__proto__||Object.getPrototypeOf(c.prototype),"format",this).call(this,t,e)}}],[{key:"create",value:function(t){var e=l(c.__proto__||Object.getPrototypeOf(c),"create",this).call(this,t);return"string"==typeof t&&e.setAttribute("src",this.sanitize(t)),e}},{key:"formats",value:function(n){return s.reduce(function(t,e){return n.hasAttribute(e)&&(t[e]=n.getAttribute(e)),t},{})}},{key:"match",value:function(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}},{key:"sanitize",value:function(t){return(0,a.sanitize)(t,["http","https","data"])?t:"//:0"}},{key:"value",value:function(t){return t.getAttribute("src")}}]),c);function c(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,c),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(c.__proto__||Object.getPrototypeOf(c)).apply(this,arguments))}function f(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}u.blotName="image",u.tagName="IMG",e.default=u},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});function l(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:l(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var o=function(t,e,n){return e&&f(t.prototype,e),n&&f(t,n),t},r=n(3),i=n(15),a=i&&i.__esModule?i:{default:i},s=["height","width"],u=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(c,r.BlockEmbed),o(c,[{key:"format",value:function(t,e){-1<s.indexOf(t)?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):l(c.prototype.__proto__||Object.getPrototypeOf(c.prototype),"format",this).call(this,t,e)}}],[{key:"create",value:function(t){var e=l(c.__proto__||Object.getPrototypeOf(c),"create",this).call(this,t);return e.setAttribute("frameborder","0"),e.setAttribute("allowfullscreen",!0),e.setAttribute("src",this.sanitize(t)),e}},{key:"formats",value:function(n){return s.reduce(function(t,e){return n.hasAttribute(e)&&(t[e]=n.getAttribute(e)),t},{})}},{key:"sanitize",value:function(t){return a.default.sanitize(t)}},{key:"value",value:function(t){return t.getAttribute("src")}}]),c);function c(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,c),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(c.__proto__||Object.getPrototypeOf(c)).apply(this,arguments))}function f(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}u.blotName="video",u.className="ql-video",u.tagName="IFRAME",e.default=u},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function l(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.FormulaBlot=void 0;var a=function(t,e,n){return e&&p(t.prototype,e),n&&p(t,n),t},s=o(n(33)),u=o(n(6)),c=o(n(7)),f=(l(h,s.default),a(h,null,[{key:"create",value:function(t){var e=function t(e,n,o){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,n);if(void 0===r){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,o)}if("value"in r)return r.value;var l=r.get;return void 0!==l?l.call(o):void 0}(h.__proto__||Object.getPrototypeOf(h),"create",this).call(this,t);return"string"==typeof t&&(window.katex.render(t,e,{throwOnError:!1,errorColor:"#f00"}),e.setAttribute("data-value",t)),e}},{key:"value",value:function(t){return t.getAttribute("data-value")}}]),h);function h(){return r(this,h),i(this,(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments))}function p(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}f.blotName="formula",f.className="ql-formula",f.tagName="SPAN";var d=(l(y,c.default),a(y,null,[{key:"register",value:function(){u.default.register(f,!0)}}]),y);function y(){r(this,y);var t=i(this,(y.__proto__||Object.getPrototypeOf(y)).call(this));if(null==window.katex)throw new Error("Formula module requires KaTeX.");return t}e.FormulaBlot=f,e.default=d},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function l(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.CodeToken=e.CodeBlock=void 0;var a=function(t,e,n){return e&&p(t.prototype,e),n&&p(t,n),t},s=o(n(0)),u=o(n(6)),c=o(n(7)),f=(l(h,o(n(13)).default),a(h,[{key:"replaceWith",value:function(t){this.domNode.textContent=this.domNode.textContent,this.attach(),function t(e,n,o){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,n);if(void 0===r){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,o)}if("value"in r)return r.value;var l=r.get;return void 0!==l?l.call(o):void 0}(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"replaceWith",this).call(this,t)}},{key:"highlight",value:function(t){var e=this.domNode.textContent;this.cachedText!==e&&((0<e.trim().length||null==this.cachedText)&&(this.domNode.innerHTML=t(e),this.domNode.normalize(),this.attach()),this.cachedText=e)}}]),h);function h(){return r(this,h),i(this,(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments))}function p(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}f.className="ql-syntax";var d=new s.default.Attributor.Class("token","hljs",{scope:s.default.Scope.INLINE}),y=(l(v,c.default),a(v,null,[{key:"register",value:function(){u.default.register(d,!0),u.default.register(f,!0)}}]),a(v,[{key:"highlight",value:function(){var t,e=this;this.quill.selection.composing||(this.quill.update(u.default.sources.USER),t=this.quill.getSelection(),this.quill.scroll.descendants(f).forEach(function(t){t.highlight(e.options.highlight)}),this.quill.update(u.default.sources.SILENT),null!=t&&this.quill.setSelection(t,u.default.sources.SILENT))}}]),v);function v(t,e){r(this,v);var n=i(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,t,e));if("function"!=typeof n.options.highlight)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var o=null;return n.quill.on(u.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(o),o=setTimeout(function(){n.highlight(),o=null},n.options.interval)}),n.highlight(),n}y.DEFAULTS={highlight:null==window.hljs?null:function(t){return window.hljs.highlightAuto(t).value},interval:1e3},e.CodeBlock=f,e.CodeToken=d,e.default=y},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function i(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function s(t,e,n){var o=document.createElement("button");o.setAttribute("type","button"),o.classList.add("ql-"+e),null!=n&&(o.value=n),t.appendChild(o)}function l(e,t){Array.isArray(t[0])||(t=[t]),t.forEach(function(t){var a=document.createElement("span");a.classList.add("ql-formats"),t.forEach(function(t){var e,n,o,r,i,l;"string"==typeof t?s(a,t):(n=t[e=Object.keys(t)[0]],Array.isArray(n)?(o=a,r=e,i=n,(l=document.createElement("select")).classList.add("ql-"+r),i.forEach(function(t){var e=document.createElement("option");!1!==t?e.setAttribute("value",t):e.setAttribute("selected","selected"),l.appendChild(e)}),o.appendChild(l)):s(a,e,n))}),e.appendChild(a)})}Object.defineProperty(e,"__esModule",{value:!0}),e.addControls=e.default=void 0;var f=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],o=!0,r=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(o=(l=a.next()).done)&&(n.push(l.value),!e||n.length!==e);o=!0);}catch(t){r=!0,i=t}finally{try{!o&&a.return&&a.return()}finally{if(r)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},r=function(t,e,n){return e&&b(t.prototype,e),n&&b(t,n),t},h=o(n(4)),p=o(n(0)),d=o(n(6)),a=o(n(10)),u=o(n(7)),y=(0,a.default)("quill:toolbar"),c=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(v,u.default),r(v,[{key:"addHandler",value:function(t,e){this.handlers[t]=e}},{key:"attach",value:function(s){var u=this,c=[].find.call(s.classList,function(t){return 0===t.indexOf("ql-")});if(c){if(c=c.slice("ql-".length),"BUTTON"===s.tagName&&s.setAttribute("type","button"),null==this.handlers[c]){if(null!=this.quill.scroll.whitelist&&null==this.quill.scroll.whitelist[c])return void y.warn("ignoring attaching to disabled format",c,s);if(null==p.default.query(c))return void y.warn("ignoring attaching to nonexistent format",c,s)}var t="SELECT"===s.tagName?"change":"click";s.addEventListener(t,function(t){var e=void 0;if("SELECT"===s.tagName){if(s.selectedIndex<0)return;var n=s.options[s.selectedIndex],e=!n.hasAttribute("selected")&&(n.value||!1)}else e=!s.classList.contains("ql-active")&&(s.value||!s.hasAttribute("value")),t.preventDefault();u.quill.focus();var o,r,i,l=u.quill.selection.getRange(),a=f(l,1)[0];if(null!=u.handlers[c])u.handlers[c].call(u,e);else if(p.default.query(c).prototype instanceof p.default.Embed){if(!(e=prompt("Enter "+c)))return;u.quill.updateContents((new h.default).retain(a.index).delete(a.length).insert((i=e,(r=c)in(o={})?Object.defineProperty(o,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):o[r]=i,o)),d.default.sources.USER)}else u.quill.format(c,e,d.default.sources.USER);u.update(a)}),this.controls.push([c,s])}}},{key:"update",value:function(a){var s=null==a?{}:this.quill.getFormat(a);this.controls.forEach(function(t){var e,n,o,r=f(t,2),i=r[0],l=r[1];"SELECT"===l.tagName?(e=void 0,null==a?e=null:null==s[i]?e=l.querySelector("option[selected]"):Array.isArray(s[i])||("string"==typeof(n=s[i])&&(n=n.replace(/\"/g,'\\"')),e=l.querySelector('option[value="'+n+'"]')),null==e?(l.value="",l.selectedIndex=-1):e.selected=!0):null==a?l.classList.remove("ql-active"):l.hasAttribute("value")?(o=s[i]===l.getAttribute("value")||null!=s[i]&&s[i].toString()===l.getAttribute("value")||null==s[i]&&!l.getAttribute("value"),l.classList.toggle("ql-active",o)):l.classList.toggle("ql-active",null!=s[i])})}}]),v);function v(t,e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,v);var n,o=i(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,t,e));if(Array.isArray(o.options.container)?(l(n=document.createElement("div"),o.options.container),t.container.parentNode.insertBefore(n,t.container),o.container=n):"string"==typeof o.options.container?o.container=document.querySelector(o.options.container):o.container=o.options.container,o.container instanceof HTMLElement)return o.container.classList.add("ql-toolbar"),o.controls=[],o.handlers={},Object.keys(o.options.handlers).forEach(function(t){o.addHandler(t,o.options.handlers[t])}),[].forEach.call(o.container.querySelectorAll("button, select"),function(t){o.attach(t)}),o.quill.on(d.default.events.EDITOR_CHANGE,function(t,e){t===d.default.events.SELECTION_CHANGE&&o.update(e)}),o.quill.on(d.default.events.SCROLL_OPTIMIZE,function(){var t=o.quill.selection.getRange(),e=f(t,1)[0];o.update(e)}),o;var r=y.error("Container required for toolbar",o.options);return i(o,r)}function b(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}c.DEFAULTS={},c.DEFAULTS={container:null,handlers:{clean:function(){var t,e=this,n=this.quill.getSelection();null!=n&&(0==n.length?(t=this.quill.getFormat(),Object.keys(t).forEach(function(t){null!=p.default.query(t,p.default.Scope.INLINE)&&e.quill.format(t,!1)})):this.quill.removeFormat(n,d.default.sources.USER))},direction:function(t){var e=this.quill.getFormat().align;"rtl"===t&&null==e?this.quill.format("align","right",d.default.sources.USER):t||"right"!==e||this.quill.format("align",!1,d.default.sources.USER),this.quill.format("direction",t,d.default.sources.USER)},indent:function(t){var e,n=this.quill.getSelection(),o=this.quill.getFormat(n),r=parseInt(o.indent||0);"+1"!==t&&"-1"!==t||(e="+1"===t?1:-1,"rtl"===o.direction&&(e*=-1),this.quill.format("indent",r+e,d.default.sources.USER))},link:function(t){!0===t&&(t=prompt("Enter link URL:")),this.quill.format("link",t,d.default.sources.USER)},list:function(t){var e=this.quill.getSelection(),n=this.quill.getFormat(e);"check"===t?"checked"===n.list||"unchecked"===n.list?this.quill.format("list",!1,d.default.sources.USER):this.quill.format("list","unchecked",d.default.sources.USER):this.quill.format("list",t,d.default.sources.USER)}}},e.default=c,e.addControls=l},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},function(t,e){t.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},function(t,e){t.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},function(t,e){t.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(t,e){t.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function l(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.BubbleTooltip=void 0;function a(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:a(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var s=function(t,e,n){return e&&g(t.prototype,e),n&&g(t,n),t},u=o(n(2)),c=o(n(9)),f=n(44),h=o(f),p=n(22),d=o(n(26)),y=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],v=(l(b,h.default),s(b,[{key:"extendToolbar",value:function(t){this.tooltip=new m(this.quill,this.options.bounds),this.tooltip.root.appendChild(t.container),this.buildButtons([].slice.call(t.container.querySelectorAll("button")),d.default),this.buildPickers([].slice.call(t.container.querySelectorAll("select")),d.default)}}]),b);function b(t,e){r(this,b),null!=e.modules.toolbar&&null==e.modules.toolbar.container&&(e.modules.toolbar.container=y);var n=i(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,t,e));return n.quill.container.classList.add("ql-bubble"),n}function g(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}v.DEFAULTS=(0,u.default)(!0,{},h.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(t){t?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var m=(l(_,f.BaseTooltip),s(_,[{key:"listen",value:function(){var e=this;a(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"listen",this).call(this),this.root.querySelector(".ql-close").addEventListener("click",function(){e.root.classList.remove("ql-editing")}),this.quill.on(c.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){var t;e.root.classList.contains("ql-hidden")||null!=(t=e.quill.getSelection())&&e.position(e.quill.getBounds(t))},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(t){var e=a(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"position",this).call(this,t),n=this.root.querySelector(".ql-tooltip-arrow");if(n.style.marginLeft="",0===e)return e;n.style.marginLeft=-1*e-n.offsetWidth/2+"px"}}]),_);function _(t,e){r(this,_);var u=i(this,(_.__proto__||Object.getPrototypeOf(_)).call(this,t,e));return u.quill.on(c.default.events.EDITOR_CHANGE,function(t,e,n,o){var r,i,l,a,s;t===c.default.events.SELECTION_CHANGE&&(null!=e&&0<e.length&&o===c.default.sources.USER?(u.show(),u.root.style.left="0px",u.root.style.width="",u.root.style.width=u.root.offsetWidth+"px",1===(r=u.quill.getLines(e.index,e.length)).length?u.position(u.quill.getBounds(e)):(i=r[r.length-1],l=u.quill.getIndex(i),a=Math.min(i.length()-1,e.index+e.length-l),s=u.quill.getBounds(new p.Range(l,a)),u.position(s))):document.activeElement!==u.textbox&&u.quill.hasFocus()&&u.hide())}),u}m.TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""),e.BubbleTooltip=m,e.default=v},function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{default:t}}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function l(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}Object.defineProperty(e,"__esModule",{value:!0});function a(t,e,n){null===t&&(t=Function.prototype);var o=Object.getOwnPropertyDescriptor(t,e);if(void 0===o){var r=Object.getPrototypeOf(t);return null===r?void 0:a(r,e,n)}if("value"in o)return o.value;var i=o.get;return void 0!==i?i.call(n):void 0}var u=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],o=!0,r=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(o=(l=a.next()).done)&&(n.push(l.value),!e||n.length!==e);o=!0);}catch(t){r=!0,i=t}finally{try{!o&&a.return&&a.return()}finally{if(r)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},s=function(t,e,n){return e&&_(t.prototype,e),n&&_(t,n),t},c=o(n(2)),f=o(n(9)),h=n(44),p=o(h),d=o(n(15)),y=n(22),v=o(n(26)),b=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],g=(l(m,p.default),s(m,[{key:"extendToolbar",value:function(n){n.container.classList.add("ql-snow"),this.buildButtons([].slice.call(n.container.querySelectorAll("button")),v.default),this.buildPickers([].slice.call(n.container.querySelectorAll("select")),v.default),this.tooltip=new O(this.quill,this.options.bounds),n.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(t,e){n.handlers.link.call(n,!e.format.link)})}}]),m);function m(t,e){r(this,m),null!=e.modules.toolbar&&null==e.modules.toolbar.container&&(e.modules.toolbar.container=b);var n=i(this,(m.__proto__||Object.getPrototypeOf(m)).call(this,t,e));return n.quill.container.classList.add("ql-snow"),n}function _(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}g.DEFAULTS=(0,c.default)(!0,{},p.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(t){if(t){var e=this.quill.getSelection();if(null==e||0==e.length)return;var n=this.quill.getText(e);/^\S+@\S+\.\S+$/.test(n)&&0!==n.indexOf("mailto:")&&(n="mailto:"+n),this.quill.theme.tooltip.edit("link",n)}else this.quill.format("link",!1)}}}}});var O=(l(w,h.BaseTooltip),s(w,[{key:"listen",value:function(){var s=this;a(w.prototype.__proto__||Object.getPrototypeOf(w.prototype),"listen",this).call(this),this.root.querySelector("a.ql-action").addEventListener("click",function(t){s.root.classList.contains("ql-editing")?s.save():s.edit("link",s.preview.textContent),t.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",function(t){var e;null!=s.linkRange&&(e=s.linkRange,s.restoreFocus(),s.quill.formatText(e,"link",!1,f.default.sources.USER),delete s.linkRange),t.preventDefault(),s.hide()}),this.quill.on(f.default.events.SELECTION_CHANGE,function(t,e,n){if(null!=t){if(0===t.length&&n===f.default.sources.USER){var o=s.quill.scroll.descendant(d.default,t.index),r=u(o,2),i=r[0],l=r[1];if(null!=i){s.linkRange=new y.Range(t.index-l,i.length());var a=d.default.formats(i.domNode);return s.preview.textContent=a,s.preview.setAttribute("href",a),s.show(),void s.position(s.quill.getBounds(s.linkRange))}}else delete s.linkRange;s.hide()}})}},{key:"show",value:function(){a(w.prototype.__proto__||Object.getPrototypeOf(w.prototype),"show",this).call(this),this.root.removeAttribute("data-mode")}}]),w);function w(t,e){r(this,w);var n=i(this,(w.__proto__||Object.getPrototypeOf(w)).call(this,t,e));return n.preview=n.root.querySelector("a.ql-preview"),n}O.TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""),e.default=g}],o.c=r,o.d=function(t,e,n){o.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:n})},o.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="",o(o.s=45).default;function o(t){if(r[t])return r[t].exports;var e=r[t]={i:t,l:!1,exports:{}};return n[t].call(e.exports,e,e.exports,o),e.l=!0,e.exports}var n,r});