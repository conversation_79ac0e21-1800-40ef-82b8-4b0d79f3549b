{"version": 3, "sources": ["webpack://signalR.protocols.msgpack/webpack/universalModuleDefinition", "webpack://signalR.protocols.msgpack/external \"signalR\"", "webpack://signalR.protocols.msgpack/webpack/bootstrap", "webpack://signalR.protocols.msgpack/webpack/runtime/define property getters", "webpack://signalR.protocols.msgpack/webpack/runtime/hasOwnProperty shorthand", "webpack://signalR.protocols.msgpack/webpack/runtime/make namespace object", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/utils/int.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/utils/utf8.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/ExtData.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/DecodeError.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/timestamp.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/ExtensionCodec.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/utils/typedArrays.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/Encoder.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/utils/prettyByte.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/CachedKeyDecoder.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/Decoder.mjs", "webpack://signalR.protocols.msgpack/src/BinaryMessageFormat.ts", "webpack://signalR.protocols.msgpack/src/Utils.ts", "webpack://signalR.protocols.msgpack/src/MessagePackHubProtocol.ts", "webpack://signalR.protocols.msgpack/src/index.ts", "webpack://signalR.protocols.msgpack/src/browser-index.ts"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,mEAAmE;AAC5G,CAAC;AACD,O;;;;;;;ACVA,gD;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,wCAAwC,yCAAyC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,sDAAsD,kBAAkB;WACxE;WACA,+CAA+C,cAAc;WAC7D,E;;;;;;;;;;;;;;;;;ACNA;AACO;AACP;AACA;AACO;AACP;AACA,oBAAoB;AACpB;AACA;AACA;AACO;AACP;AACA,oBAAoB;AACpB;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA,gC;;AC1BuC;AACvC;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP,MAAM,UAAU;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP,MAAM,UAAU;AAChB;AACA;AACA;AACO;AACP;AACA;AACA;AACA,iC;;AC7JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACkB;AACnB,oC;;ACXA,iBAAiB,SAAI,IAAI,SAAI;AAC7B;AACA;AACA,cAAc,gBAAgB,sCAAsC,iBAAiB,EAAE;AACvF,6BAA6B,8EAA8E;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,CAAC;AACsB;AACvB,wC;;AChCA;AACgD;AACK;AAC9C;AACP,0CAA0C;AAC1C,0CAA0C;AACnC;AACP;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA,QAAQ,QAAQ;AAChB;AACA;AACA;AACO;AACP;AACA;AACA;AACA,mBAAmB,YAAY;AAC/B;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA,oBAAoB;AACpB;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA,+BAA+B;AAC/B,sBAAsB,QAAQ;AAC9B;AACA,oBAAoB;AACpB;AACA;AACA,sBAAsB,WAAW;AACjC;AACA;AACO;AACP;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA,sC;;AChGA;AACwC;AACa;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,kBAAkB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,iCAAiC;AACxD;AACA;AACA;AACA;AACA;AACA,+BAA+B,OAAO;AACtC;AACA;AACA;AACA;AACA,uBAAuB,0BAA0B;AACjD;AACA;AACA;AACA;AACA;AACA,+BAA+B,OAAO;AACtC;AACA;AACA;AACA,8BAA8B,OAAO;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,OAAO;AAC9B;AACA;AACA;AACA;AACA,CAAC;AACyB;AAC1B,2C;;ACtEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,wC;;ACtBiG;AAC3C;AACA;AACK;AACpD;AACA;AACP;AACA;AACA,wCAAwC,kBAAkB,2BAA2B,CAAC;AACtF,iCAAiC,qBAAqB;AACtD,kCAAkC,8BAA8B;AAChE,2CAA2C,iDAAiD;AAC5F,kCAAkC,kBAAkB;AACpD,sCAAsC,sBAAsB;AAC5D,yCAAyC,yBAAyB;AAClE,6CAA6C,6BAA6B;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,sBAAsB;AAC9C,6BAA6B,SAAS;AACtC;AACA;AACA,YAAY,YAAY;AACxB;AACA;AACA;AACA,6BAA6B,SAAS;AACtC;AACA;AACA,YAAY,YAAY;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,gBAAgB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,sBAAsB;AACjE;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,oBAAoB;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,oBAAoB;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,SAAS;AACjB;AACA;AACA;AACA;AACA,QAAQ,QAAQ;AAChB;AACA;AACA;AACA,CAAC;AACkB;AACnB,oC;;ACtZO;AACP;AACA;AACA,uC;;ACHgD;AAChD;AACA;AACA;AACA;AACA,sCAAsC,uCAAuC;AAC7E,yCAAyC,8CAA8C;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,uBAAuB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,uBAAuB;AAChF;AACA;AACA,2BAA2B,gBAAgB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,YAAY;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAC2B;AAC5B,6C;;AC/DA,iBAAiB,SAAI,IAAI,SAAI;AAC7B,2BAA2B,+DAA+D,gBAAgB,EAAE,EAAE;AAC9G;AACA,mCAAmC,MAAM,6BAA6B,EAAE,YAAY,WAAW,EAAE;AACjG,kCAAkC,MAAM,iCAAiC,EAAE,YAAY,WAAW,EAAE;AACpG,+BAA+B,qFAAqF;AACpH;AACA,KAAK;AACL;AACA,mBAAmB,SAAI,IAAI,SAAI;AAC/B,aAAa,6BAA6B,0BAA0B,aAAa,EAAE,qBAAqB;AACxG,gBAAgB,qDAAqD,oEAAoE,aAAa,EAAE;AACxJ,sBAAsB,sBAAsB,qBAAqB,GAAG;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC,kCAAkC,SAAS;AAC3C,kCAAkC,WAAW,UAAU;AACvD,yCAAyC,cAAc;AACvD;AACA,6GAA6G,OAAO,UAAU;AAC9H,gFAAgF,iBAAiB,OAAO;AACxG,wDAAwD,gBAAgB,QAAQ,OAAO;AACvF,8CAA8C,gBAAgB,gBAAgB,OAAO;AACrF;AACA,iCAAiC;AACjC;AACA;AACA,SAAS,YAAY,aAAa,OAAO,EAAE,UAAU,WAAW;AAChE,mCAAmC,SAAS;AAC5C;AACA;AACA,qBAAqB,SAAI,IAAI,SAAI;AACjC;AACA;AACA,2GAA2G,sFAAsF,aAAa,EAAE;AAChN,sBAAsB,8BAA8B,gDAAgD,uDAAuD,EAAE,EAAE,GAAG;AAClK,4CAA4C,sCAAsC,UAAU,oBAAoB,EAAE,EAAE,UAAU;AAC9H;AACA,eAAe,SAAI,IAAI,SAAI,2BAA2B,sEAAsE;AAC5H,wBAAwB,SAAI,IAAI,SAAI;AACpC;AACA;AACA,iBAAiB,sFAAsF,aAAa,EAAE;AACtH,sBAAsB,gCAAgC,qCAAqC,0CAA0C,EAAE,EAAE,GAAG;AAC5I,2BAA2B,MAAM,eAAe,EAAE,YAAY,oBAAoB,EAAE;AACpF,sBAAsB,oGAAoG;AAC1H,6BAA6B,uBAAuB;AACpD,4BAA4B,wBAAwB;AACpD,2BAA2B,yDAAyD;AACpF;AACoD;AACE;AACY;AACoB;AACX;AACjB;AACV;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,iCAAiC,gBAAgB;AACjD;AACA;AACA,wCAAwC,kBAAkB,2BAA2B,CAAC;AACtF,iCAAiC,qBAAqB;AACtD,sCAAsC,gBAAgB,UAAU,CAAC;AACjE,sCAAsC,gBAAgB,UAAU,CAAC;AACjE,wCAAwC,kBAAkB,UAAU,CAAC;AACrE,sCAAsC,gBAAgB,UAAU,CAAC;AACjE,sCAAsC,gBAAgB,UAAU,CAAC;AACjE,oCAAoC,qCAAqC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,gBAAgB;AACrC,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,gBAAgB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+EAA+E,UAAU;AACzF;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,KAAK,EAAE,EAAwB;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,WAAW,8BAA8B,UAAU;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,WAAW;AAC7C;AACA;AACA,kCAAkC,WAAW;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,WAAW,oCAAoC,UAAU;AACvF;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,WAAW;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,SAAS;AACT;AACA;AACA;AACA,sBAAsB,WAAW;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,sBAAsB,WAAW;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,sBAAsB;AACpD,qBAAqB,YAAY;AACjC;AACA;AACA,qBAAqB,YAAY;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,WAAW;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,WAAW;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,SAAS;AAC7B;AACA;AACA;AACA;AACA,oBAAoB,QAAQ;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACkB;AACnB,oC;;;;AC7tBA,gEAAgE;AAChE,uEAAuE;AAEvE,2BAA2B;AAC3B,eAAe;AACR,MAAM,mBAAmB;IAE5B,iFAAiF;IACjF,uDAAuD;IAEhD,MAAM,CAAC,KAAK,CAAC,MAAkB;QAClC,IAAI,IAAI,GAAG,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,MAAM,CAAC;QAC9C,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,GAAG;YACC,IAAI,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;YAC3B,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;YACjB,IAAI,IAAI,GAAG,CAAC,EAAE;gBACV,QAAQ,IAAI,IAAI,CAAC;aACpB;YACD,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC5B,QACM,IAAI,GAAG,CAAC,EAAE;QAEjB,IAAI,GAAG,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,MAAM,CAAC;QAE1C,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;QACvD,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QACzB,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACrC,OAAO,MAAM,CAAC,MAAM,CAAC;IACzB,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,KAAkB;QAClC,MAAM,MAAM,GAAiB,EAAE,CAAC;QAChC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;QACzC,MAAM,mBAAmB,GAAG,CAAC,CAAC;QAC9B,MAAM,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE,CAAC;QAE3C,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,UAAU,GAAG;YAC7C,IAAI,QAAQ,GAAG,CAAC,CAAC;YACjB,IAAI,IAAI,GAAG,CAAC,CAAC;YACb,IAAI,QAAQ,CAAC;YACb,GAAG;gBACC,QAAQ,GAAG,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC;gBACzC,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAChE,QAAQ,EAAE,CAAC;aACd,QACM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;YAEvG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,QAAQ,GAAG,mBAAmB,EAAE;gBAC3D,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;aAChD;YAED,IAAI,QAAQ,KAAK,mBAAmB,IAAI,QAAQ,GAAG,CAAC,EAAE;gBAClD,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;aAClE;YAED,IAAI,UAAU,CAAC,UAAU,IAAI,CAAC,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC,EAAE;gBACrD,+CAA+C;gBAC/C,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK;oBACxB,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC;oBAC/D,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;aAC3E;iBAAM;gBACH,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;aAC1C;YAED,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC;SACrC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;;;ACtED,gEAAgE;AAChE,uEAAuE;AAEvE,+BAA+B;AAC/B,eAAe;AACR,SAAS,aAAa,CAAC,GAAQ;IAClC,OAAO,GAAG,IAAI,OAAO,WAAW,KAAK,WAAW;QAC5C,CAAC,GAAG,YAAY,WAAW;YAC3B,kEAAkE;YAClE,CAAC,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC;AACrE,CAAC;;;ACVD,gEAAgE;AAChE,uEAAuE;AAEnB;AAOxB;AAEgC;AACpB;AAExC,+DAA+D;AAE/D,wCAAwC;AACxC,+FAA+F;AAC/F,sDAAsD;AACtD,MAAM,uBAAuB,GAAe,IAAI,UAAU,CAAC,CAAC,IAAI,EAAE,kCAAgB,CAAC,CAAC,CAAC;AAErF,8CAA8C;AACvC,MAAM,sBAAsB;IAe/B;;;OAGG;IACH,YAAY,kBAAuC;QAlBnD,+GAA+G;QAC/F,SAAI,GAAW,aAAa,CAAC;QAC7C,mCAAmC;QACnB,YAAO,GAAW,CAAC,CAAC;QACpC,0CAA0C;QAC1B,mBAAc,GAAmB,uCAAqB,CAAC;QAEtD,iBAAY,GAAG,CAAC,CAAC;QACjB,gBAAW,GAAG,CAAC,CAAC;QAChB,mBAAc,GAAG,CAAC,CAAC;QAUhC,kBAAkB,GAAG,kBAAkB,IAAI,EAAE,CAAC;QAC9C,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CACvB,kBAAkB,CAAC,cAAc,EACjC,kBAAkB,CAAC,OAAO,EAC1B,kBAAkB,CAAC,QAAQ,EAC3B,kBAAkB,CAAC,iBAAiB,EACpC,kBAAkB,CAAC,QAAQ,EAC3B,kBAAkB,CAAC,YAAY,EAC/B,kBAAkB,CAAC,eAAe,EAClC,kBAAkB,CAAC,mBAAmB,CACzC,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CACvB,kBAAkB,CAAC,cAAc,EACjC,kBAAkB,CAAC,OAAO,EAC1B,kBAAkB,CAAC,YAAY,EAC/B,kBAAkB,CAAC,YAAY,EAC/B,kBAAkB,CAAC,cAAc,EACjC,kBAAkB,CAAC,YAAY,EAC/B,kBAAkB,CAAC,YAAY,CAClC,CAAC;IACN,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,KAAkB,EAAE,MAAe;QACpD,sHAAsH;QACtH,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;SAC3F;QAED,IAAI,MAAM,KAAK,IAAI,EAAE;YACjB,MAAM,GAAG,qCAAmB,CAAC;SAChC;QAED,MAAM,QAAQ,GAAG,yBAAyB,CAAC,KAAK,CAAC,CAAC;QAElD,MAAM,WAAW,GAAG,EAAE,CAAC;QACvB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC1D,gFAAgF;YAChF,IAAI,aAAa,EAAE;gBACf,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aACnC;SACJ;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,OAAmB;QACnC,QAAQ,OAAO,CAAC,IAAI,EAAE;YAClB,KAAK,wCAAsB;gBACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAA4B,CAAC,CAAC;YAC/D,KAAK,8CAA4B;gBAC7B,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAkC,CAAC,CAAC;YAC3E,KAAK,wCAAsB;gBACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAA4B,CAAC,CAAC;YAC/D,KAAK,wCAAsB;gBACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAA4B,CAAC,CAAC;YAC/D,KAAK,kCAAgB;gBACjB,OAAO,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;YAC9D,KAAK,8CAA4B;gBAC7B,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAkC,CAAC,CAAC;YAC3E;gBACI,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAChD;IACL,CAAC;IAEO,aAAa,CAAC,KAAiB,EAAE,MAAe;QACpD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACvC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAQ,CAAC;QACtD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,UAAU,YAAY,KAAK,CAAC,EAAE;YAC3D,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACvC;QAED,MAAM,WAAW,GAAG,UAAU,CAAC,CAAC,CAAgB,CAAC;QAEjD,QAAQ,WAAW,EAAE;YACjB,KAAK,wCAAsB;gBACvB,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,CAAC;YACpF,KAAK,wCAAsB;gBACvB,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,CAAC;YACpF,KAAK,wCAAsB;gBACvB,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,CAAC;YACpF,KAAK,kCAAgB;gBACjB,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAC/C,KAAK,mCAAiB;gBAClB,OAAO,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAChD;gBACI,6EAA6E;gBAC7E,MAAM,CAAC,GAAG,CAAC,sCAAoB,EAAE,wBAAwB,GAAG,WAAW,GAAG,YAAY,CAAC,CAAC;gBACxF,OAAO,IAAI,CAAC;SACnB;IACL,CAAC;IAEO,mBAAmB,CAAC,UAAiB;QACzC,+FAA+F;QAC/F,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;SACzD;QAED,OAAO;YACH,kCAAkC;YAClC,cAAc,EAAE,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YAClE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;YACpB,IAAI,EAAE,mCAAiB;SACZ,CAAC;IACpB,CAAC;IAEO,kBAAkB,CAAC,UAAiB;QACxC,+FAA+F;QAC/F,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACxD;QAED,OAAO;YACH,iCAAiC;YACjC,IAAI,EAAE,kCAAgB;SACX,CAAC;IACpB,CAAC;IAEO,wBAAwB,CAAC,OAAuB,EAAE,UAAiB;QACvE,+FAA+F;QAC/F,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;QAED,MAAM,YAAY,GAAG,UAAU,CAAC,CAAC,CAAW,CAAC;QAC7C,IAAI,YAAY,EAAE;YACd,OAAO;gBACH,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;gBACxB,OAAO;gBACP,YAAY;gBACZ,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,UAAU,CAAC,CAAC,CAAW;gBAC/B,IAAI,EAAE,wCAAsB;aAC/B,CAAC;SACL;aAAM;YACH,OAAO;gBACH,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;gBACxB,OAAO;gBACP,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;gBACrB,IAAI,EAAE,wCAAsB;aAC/B,CAAC;SACL;IAEL,CAAC;IAEO,wBAAwB,CAAC,OAAuB,EAAE,UAAiB;QACvE,+FAA+F;QAC/F,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;QAED,OAAO;YACH,OAAO;YACP,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC;YAC3B,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;YACnB,IAAI,EAAE,wCAAsB;SACV,CAAC;IAC3B,CAAC;IAEO,wBAAwB,CAAC,OAAuB,EAAE,UAAiB;QACvE,+FAA+F;QAC/F,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;QAED,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAEjC,IAAI,UAAU,KAAK,IAAI,CAAC,WAAW,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1D,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;QAED,IAAI,KAAyB,CAAC;QAC9B,IAAI,MAAW,CAAC;QAEhB,QAAQ,UAAU,EAAE;YAChB,KAAK,IAAI,CAAC,YAAY;gBAClB,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBACtB,MAAM;YACV,KAAK,IAAI,CAAC,cAAc;gBACpB,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBACvB,MAAM;SACb;QAED,MAAM,iBAAiB,GAAsB;YACzC,KAAK;YACL,OAAO;YACP,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC;YAC3B,MAAM;YACN,IAAI,EAAE,wCAAsB;SAC/B,CAAC;QAEF,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAEO,gBAAgB,CAAC,iBAAoC;QACzD,IAAI,OAAY,CAAC;QACjB,IAAI,iBAAiB,CAAC,SAAS,EAAE;YAC7B,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,wCAAsB,EAAE,iBAAiB,CAAC,OAAO,IAAI,EAAE,EAAE,iBAAiB,CAAC,YAAY,IAAI,IAAI;gBAC/H,iBAAiB,CAAC,MAAM,EAAE,iBAAiB,CAAC,SAAS,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC;SACxF;aAAM;YACH,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,wCAAsB,EAAE,iBAAiB,CAAC,OAAO,IAAI,EAAE,EAAE,iBAAiB,CAAC,YAAY,IAAI,IAAI;gBAC/H,iBAAiB,CAAC,MAAM,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC;SAC3D;QAED,OAAO,yBAAyB,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,sBAAsB,CAAC,uBAAgD;QAC3E,IAAI,OAAY,CAAC;QACjB,IAAI,uBAAuB,CAAC,SAAS,EAAE;YACnC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,8CAA4B,EAAE,uBAAuB,CAAC,OAAO,IAAI,EAAE,EAAE,uBAAuB,CAAC,YAAY;gBACzI,uBAAuB,CAAC,MAAM,EAAE,uBAAuB,CAAC,SAAS,EAAE,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC;SAC1G;aAAM;YACH,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,8CAA4B,EAAE,uBAAuB,CAAC,OAAO,IAAI,EAAE,EAAE,uBAAuB,CAAC,YAAY;gBACzI,uBAAuB,CAAC,MAAM,EAAE,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC;SACvE;QAED,OAAO,yBAAyB,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,gBAAgB,CAAC,iBAAoC;QACzD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,wCAAsB,EAAE,iBAAiB,CAAC,OAAO,IAAI,EAAE,EAAE,iBAAiB,CAAC,YAAY;YAC7H,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;QAEzB,OAAO,yBAAyB,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,gBAAgB,CAAC,iBAAoC;QACzD,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;QAEnI,IAAI,OAAY,CAAC;QACjB,QAAQ,UAAU,EAAE;YAChB,KAAK,IAAI,CAAC,YAAY;gBAClB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,wCAAsB,EAAE,iBAAiB,CAAC,OAAO,IAAI,EAAE,EAAE,iBAAiB,CAAC,YAAY,EAAE,UAAU,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC/J,MAAM;YACV,KAAK,IAAI,CAAC,WAAW;gBACjB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,wCAAsB,EAAE,iBAAiB,CAAC,OAAO,IAAI,EAAE,EAAE,iBAAiB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC;gBACtI,MAAM;YACV,KAAK,IAAI,CAAC,cAAc;gBACpB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,wCAAsB,EAAE,iBAAiB,CAAC,OAAO,IAAI,EAAE,EAAE,iBAAiB,CAAC,YAAY,EAAE,UAAU,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;gBAChK,MAAM;SACb;QAED,OAAO,yBAAyB,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,sBAAsB,CAAC,uBAAgD;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,8CAA4B,EAAE,uBAAuB,CAAC,OAAO,IAAI,EAAE,EAAE,uBAAuB,CAAC,YAAY,CAAC,CAAC,CAAC;QAElJ,OAAO,yBAAyB,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,YAAY,CAAC,UAAe;QAChC,MAAM,OAAO,GAAmB,UAAU,CAAC,CAAC,CAAmB,CAAC;QAChE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACvC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ;;;AC7TD,gEAAgE;AAChE,uEAAuE;AAEvE,6DAA6D;AAC7D,gEAAgE;AACzD,MAAM,OAAO,GAAG,iBAAiB,CAAC;AAEyB;;;ACPlE,gEAAgE;AAChE,uEAAuE;AAEvE,qHAAqH;AAE7F", "file": "signalr-protocol-msgpack.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"signalR\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"signalR\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"msgpack\"] = factory(require(\"signalR\"));\n\telse\n\t\troot[\"signalR\"] = root[\"signalR\"] || {}, root[\"signalR\"][\"protocols\"] = root[\"signalR\"][\"protocols\"] || {}, root[\"signalR\"][\"protocols\"][\"msgpack\"] = factory(root[\"signalR\"]);\n})(self, function(__WEBPACK_EXTERNAL_MODULE__1__) {\nreturn ", "module.exports = __WEBPACK_EXTERNAL_MODULE__1__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Integer Utility\nexport var UINT32_MAX = 4294967295;\n// DataView extension to handle int64 / uint64,\n// where the actual range is 53-bits integer (a.k.a. safe integer)\nexport function setUint64(view, offset, value) {\n    var high = value / 4294967296;\n    var low = value; // high bits are truncated by DataView\n    view.setUint32(offset, high);\n    view.setUint32(offset + 4, low);\n}\nexport function setInt64(view, offset, value) {\n    var high = Math.floor(value / 4294967296);\n    var low = value; // high bits are truncated by DataView\n    view.setUint32(offset, high);\n    view.setUint32(offset + 4, low);\n}\nexport function getInt64(view, offset) {\n    var high = view.getInt32(offset);\n    var low = view.getUint32(offset + 4);\n    return high * 4294967296 + low;\n}\nexport function getUint64(view, offset) {\n    var high = view.getUint32(offset);\n    var low = view.getUint32(offset + 4);\n    return high * 4294967296 + low;\n}\n//# sourceMappingURL=int.mjs.map", "import { UINT32_MAX } from \"./int.mjs\";\nvar TEXT_ENCODING_AVAILABLE = (typeof process === \"undefined\" || process.env[\"TEXT_ENCODING\"] !== \"never\") &&\n    typeof TextEncoder !== \"undefined\" &&\n    typeof TextDecoder !== \"undefined\";\nexport function utf8Count(str) {\n    var strLength = str.length;\n    var byteLength = 0;\n    var pos = 0;\n    while (pos < strLength) {\n        var value = str.charCodeAt(pos++);\n        if ((value & 0xffffff80) === 0) {\n            // 1-byte\n            byteLength++;\n            continue;\n        }\n        else if ((value & 0xfffff800) === 0) {\n            // 2-bytes\n            byteLength += 2;\n        }\n        else {\n            // handle surrogate pair\n            if (value >= 0xd800 && value <= 0xdbff) {\n                // high surrogate\n                if (pos < strLength) {\n                    var extra = str.charCodeAt(pos);\n                    if ((extra & 0xfc00) === 0xdc00) {\n                        ++pos;\n                        value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n                    }\n                }\n            }\n            if ((value & 0xffff0000) === 0) {\n                // 3-byte\n                byteLength += 3;\n            }\n            else {\n                // 4-byte\n                byteLength += 4;\n            }\n        }\n    }\n    return byteLength;\n}\nexport function utf8EncodeJs(str, output, outputOffset) {\n    var strLength = str.length;\n    var offset = outputOffset;\n    var pos = 0;\n    while (pos < strLength) {\n        var value = str.charCodeAt(pos++);\n        if ((value & 0xffffff80) === 0) {\n            // 1-byte\n            output[offset++] = value;\n            continue;\n        }\n        else if ((value & 0xfffff800) === 0) {\n            // 2-bytes\n            output[offset++] = ((value >> 6) & 0x1f) | 0xc0;\n        }\n        else {\n            // handle surrogate pair\n            if (value >= 0xd800 && value <= 0xdbff) {\n                // high surrogate\n                if (pos < strLength) {\n                    var extra = str.charCodeAt(pos);\n                    if ((extra & 0xfc00) === 0xdc00) {\n                        ++pos;\n                        value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n                    }\n                }\n            }\n            if ((value & 0xffff0000) === 0) {\n                // 3-byte\n                output[offset++] = ((value >> 12) & 0x0f) | 0xe0;\n                output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n            }\n            else {\n                // 4-byte\n                output[offset++] = ((value >> 18) & 0x07) | 0xf0;\n                output[offset++] = ((value >> 12) & 0x3f) | 0x80;\n                output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n            }\n        }\n        output[offset++] = (value & 0x3f) | 0x80;\n    }\n}\nvar sharedTextEncoder = TEXT_ENCODING_AVAILABLE ? new TextEncoder() : undefined;\nexport var TEXT_ENCODER_THRESHOLD = !TEXT_ENCODING_AVAILABLE\n    ? UINT32_MAX\n    : typeof process !== \"undefined\" && process.env[\"TEXT_ENCODING\"] !== \"force\"\n        ? 200\n        : 0;\nfunction utf8EncodeTEencode(str, output, outputOffset) {\n    output.set(sharedTextEncoder.encode(str), outputOffset);\n}\nfunction utf8EncodeTEencodeInto(str, output, outputOffset) {\n    sharedTextEncoder.encodeInto(str, output.subarray(outputOffset));\n}\nexport var utf8EncodeTE = (sharedTextEncoder === null || sharedTextEncoder === void 0 ? void 0 : sharedTextEncoder.encodeInto) ? utf8EncodeTEencodeInto : utf8EncodeTEencode;\nvar CHUNK_SIZE = 4096;\nexport function utf8DecodeJs(bytes, inputOffset, byteLength) {\n    var offset = inputOffset;\n    var end = offset + byteLength;\n    var units = [];\n    var result = \"\";\n    while (offset < end) {\n        var byte1 = bytes[offset++];\n        if ((byte1 & 0x80) === 0) {\n            // 1 byte\n            units.push(byte1);\n        }\n        else if ((byte1 & 0xe0) === 0xc0) {\n            // 2 bytes\n            var byte2 = bytes[offset++] & 0x3f;\n            units.push(((byte1 & 0x1f) << 6) | byte2);\n        }\n        else if ((byte1 & 0xf0) === 0xe0) {\n            // 3 bytes\n            var byte2 = bytes[offset++] & 0x3f;\n            var byte3 = bytes[offset++] & 0x3f;\n            units.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3);\n        }\n        else if ((byte1 & 0xf8) === 0xf0) {\n            // 4 bytes\n            var byte2 = bytes[offset++] & 0x3f;\n            var byte3 = bytes[offset++] & 0x3f;\n            var byte4 = bytes[offset++] & 0x3f;\n            var unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4;\n            if (unit > 0xffff) {\n                unit -= 0x10000;\n                units.push(((unit >>> 10) & 0x3ff) | 0xd800);\n                unit = 0xdc00 | (unit & 0x3ff);\n            }\n            units.push(unit);\n        }\n        else {\n            units.push(byte1);\n        }\n        if (units.length >= CHUNK_SIZE) {\n            result += String.fromCharCode.apply(String, units);\n            units.length = 0;\n        }\n    }\n    if (units.length > 0) {\n        result += String.fromCharCode.apply(String, units);\n    }\n    return result;\n}\nvar sharedTextDecoder = TEXT_ENCODING_AVAILABLE ? new TextDecoder() : null;\nexport var TEXT_DECODER_THRESHOLD = !TEXT_ENCODING_AVAILABLE\n    ? UINT32_MAX\n    : typeof process !== \"undefined\" && process.env[\"TEXT_DECODER\"] !== \"force\"\n        ? 200\n        : 0;\nexport function utf8DecodeTD(bytes, inputOffset, byteLength) {\n    var stringBytes = bytes.subarray(inputOffset, inputOffset + byteLength);\n    return sharedTextDecoder.decode(stringBytes);\n}\n//# sourceMappingURL=utf8.mjs.map", "/**\n * ExtData is used to handle Extension Types that are not registered to ExtensionCodec.\n */\nvar ExtData = /** @class */ (function () {\n    function ExtData(type, data) {\n        this.type = type;\n        this.data = data;\n    }\n    return ExtData;\n}());\nexport { ExtData };\n//# sourceMappingURL=ExtData.mjs.map", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar DecodeError = /** @class */ (function (_super) {\n    __extends(DecodeError, _super);\n    function DecodeError(message) {\n        var _this = _super.call(this, message) || this;\n        // fix the prototype chain in a cross-platform way\n        var proto = Object.create(DecodeError.prototype);\n        Object.setPrototypeOf(_this, proto);\n        Object.defineProperty(_this, \"name\", {\n            configurable: true,\n            enumerable: false,\n            value: DecodeError.name,\n        });\n        return _this;\n    }\n    return DecodeError;\n}(Error));\nexport { DecodeError };\n//# sourceMappingURL=DecodeError.mjs.map", "// https://github.com/msgpack/msgpack/blob/master/spec.md#timestamp-extension-type\nimport { DecodeError } from \"./DecodeError.mjs\";\nimport { getInt64, setInt64 } from \"./utils/int.mjs\";\nexport var EXT_TIMESTAMP = -1;\nvar TIMESTAMP32_MAX_SEC = 0x100000000 - 1; // 32-bit unsigned int\nvar TIMESTAMP64_MAX_SEC = 0x400000000 - 1; // 34-bit unsigned int\nexport function encodeTimeSpecToTimestamp(_a) {\n    var sec = _a.sec, nsec = _a.nsec;\n    if (sec >= 0 && nsec >= 0 && sec <= TIMESTAMP64_MAX_SEC) {\n        // Here sec >= 0 && nsec >= 0\n        if (nsec === 0 && sec <= TIMESTAMP32_MAX_SEC) {\n            // timestamp 32 = { sec32 (unsigned) }\n            var rv = new Uint8Array(4);\n            var view = new DataView(rv.buffer);\n            view.setUint32(0, sec);\n            return rv;\n        }\n        else {\n            // timestamp 64 = { nsec30 (unsigned), sec34 (unsigned) }\n            var secHigh = sec / 0x100000000;\n            var secLow = sec & 0xffffffff;\n            var rv = new Uint8Array(8);\n            var view = new DataView(rv.buffer);\n            // nsec30 | secHigh2\n            view.setUint32(0, (nsec << 2) | (secHigh & 0x3));\n            // secLow32\n            view.setUint32(4, secLow);\n            return rv;\n        }\n    }\n    else {\n        // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n        var rv = new Uint8Array(12);\n        var view = new DataView(rv.buffer);\n        view.setUint32(0, nsec);\n        setInt64(view, 4, sec);\n        return rv;\n    }\n}\nexport function encodeDateToTimeSpec(date) {\n    var msec = date.getTime();\n    var sec = Math.floor(msec / 1e3);\n    var nsec = (msec - sec * 1e3) * 1e6;\n    // Normalizes { sec, nsec } to ensure nsec is unsigned.\n    var nsecInSec = Math.floor(nsec / 1e9);\n    return {\n        sec: sec + nsecInSec,\n        nsec: nsec - nsecInSec * 1e9,\n    };\n}\nexport function encodeTimestampExtension(object) {\n    if (object instanceof Date) {\n        var timeSpec = encodeDateToTimeSpec(object);\n        return encodeTimeSpecToTimestamp(timeSpec);\n    }\n    else {\n        return null;\n    }\n}\nexport function decodeTimestampToTimeSpec(data) {\n    var view = new DataView(data.buffer, data.byteOffset, data.byteLength);\n    // data may be 32, 64, or 96 bits\n    switch (data.byteLength) {\n        case 4: {\n            // timestamp 32 = { sec32 }\n            var sec = view.getUint32(0);\n            var nsec = 0;\n            return { sec: sec, nsec: nsec };\n        }\n        case 8: {\n            // timestamp 64 = { nsec30, sec34 }\n            var nsec30AndSecHigh2 = view.getUint32(0);\n            var secLow32 = view.getUint32(4);\n            var sec = (nsec30AndSecHigh2 & 0x3) * 0x100000000 + secLow32;\n            var nsec = nsec30AndSecHigh2 >>> 2;\n            return { sec: sec, nsec: nsec };\n        }\n        case 12: {\n            // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n            var sec = getInt64(view, 4);\n            var nsec = view.getUint32(0);\n            return { sec: sec, nsec: nsec };\n        }\n        default:\n            throw new DecodeError(\"Unrecognized data size for timestamp (expected 4, 8, or 12): \" + data.length);\n    }\n}\nexport function decodeTimestampExtension(data) {\n    var timeSpec = decodeTimestampToTimeSpec(data);\n    return new Date(timeSpec.sec * 1e3 + timeSpec.nsec / 1e6);\n}\nexport var timestampExtension = {\n    type: EXT_TIMESTAMP,\n    encode: encodeTimestampExtension,\n    decode: decodeTimestampExtension,\n};\n//# sourceMappingURL=timestamp.mjs.map", "// ExtensionCodec to handle MessagePack extensions\nimport { ExtData } from \"./ExtData.mjs\";\nimport { timestampExtension } from \"./timestamp.mjs\";\nvar ExtensionCodec = /** @class */ (function () {\n    function ExtensionCodec() {\n        // built-in extensions\n        this.builtInEncoders = [];\n        this.builtInDecoders = [];\n        // custom extensions\n        this.encoders = [];\n        this.decoders = [];\n        this.register(timestampExtension);\n    }\n    ExtensionCodec.prototype.register = function (_a) {\n        var type = _a.type, encode = _a.encode, decode = _a.decode;\n        if (type >= 0) {\n            // custom extensions\n            this.encoders[type] = encode;\n            this.decoders[type] = decode;\n        }\n        else {\n            // built-in extensions\n            var index = 1 + type;\n            this.builtInEncoders[index] = encode;\n            this.builtInDecoders[index] = decode;\n        }\n    };\n    ExtensionCodec.prototype.tryToEncode = function (object, context) {\n        // built-in extensions\n        for (var i = 0; i < this.builtInEncoders.length; i++) {\n            var encodeExt = this.builtInEncoders[i];\n            if (encodeExt != null) {\n                var data = encodeExt(object, context);\n                if (data != null) {\n                    var type = -1 - i;\n                    return new ExtData(type, data);\n                }\n            }\n        }\n        // custom extensions\n        for (var i = 0; i < this.encoders.length; i++) {\n            var encodeExt = this.encoders[i];\n            if (encodeExt != null) {\n                var data = encodeExt(object, context);\n                if (data != null) {\n                    var type = i;\n                    return new ExtData(type, data);\n                }\n            }\n        }\n        if (object instanceof ExtData) {\n            // to keep ExtData as is\n            return object;\n        }\n        return null;\n    };\n    ExtensionCodec.prototype.decode = function (data, type, context) {\n        var decodeExt = type < 0 ? this.builtInDecoders[-1 - type] : this.decoders[type];\n        if (decodeExt) {\n            return decodeExt(data, type, context);\n        }\n        else {\n            // decode() does not fail, returns ExtData instead.\n            return new ExtData(type, data);\n        }\n    };\n    ExtensionCodec.defaultCodec = new ExtensionCodec();\n    return ExtensionCodec;\n}());\nexport { ExtensionCodec };\n//# sourceMappingURL=ExtensionCodec.mjs.map", "export function ensureUint8Array(buffer) {\n    if (buffer instanceof Uint8Array) {\n        return buffer;\n    }\n    else if (ArrayBuffer.isView(buffer)) {\n        return new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n    }\n    else if (buffer instanceof ArrayBuffer) {\n        return new Uint8Array(buffer);\n    }\n    else {\n        // ArrayLike<number>\n        return Uint8Array.from(buffer);\n    }\n}\nexport function createDataView(buffer) {\n    if (buffer instanceof ArrayBuffer) {\n        return new DataView(buffer);\n    }\n    var bufferView = ensureUint8Array(buffer);\n    return new DataView(bufferView.buffer, bufferView.byteOffset, bufferView.byteLength);\n}\n//# sourceMappingURL=typedArrays.mjs.map", "import { utf8EncodeJs, utf8Count, TEXT_ENCODER_THRESHOLD, utf8EncodeTE } from \"./utils/utf8.mjs\";\nimport { ExtensionCodec } from \"./ExtensionCodec.mjs\";\nimport { setInt64, setUint64 } from \"./utils/int.mjs\";\nimport { ensureUint8Array } from \"./utils/typedArrays.mjs\";\nexport var DEFAULT_MAX_DEPTH = 100;\nexport var DEFAULT_INITIAL_BUFFER_SIZE = 2048;\nvar Encoder = /** @class */ (function () {\n    function Encoder(extensionCodec, context, maxDepth, initialBufferSize, sortKeys, forceFloat32, ignoreUndefined, forceIntegerToFloat) {\n        if (extensionCodec === void 0) { extensionCodec = ExtensionCodec.defaultCodec; }\n        if (context === void 0) { context = undefined; }\n        if (maxDepth === void 0) { maxDepth = DEFAULT_MAX_DEPTH; }\n        if (initialBufferSize === void 0) { initialBufferSize = DEFAULT_INITIAL_BUFFER_SIZE; }\n        if (sortKeys === void 0) { sortKeys = false; }\n        if (forceFloat32 === void 0) { forceFloat32 = false; }\n        if (ignoreUndefined === void 0) { ignoreUndefined = false; }\n        if (forceIntegerToFloat === void 0) { forceIntegerToFloat = false; }\n        this.extensionCodec = extensionCodec;\n        this.context = context;\n        this.maxDepth = maxDepth;\n        this.initialBufferSize = initialBufferSize;\n        this.sortKeys = sortKeys;\n        this.forceFloat32 = forceFloat32;\n        this.ignoreUndefined = ignoreUndefined;\n        this.forceIntegerToFloat = forceIntegerToFloat;\n        this.pos = 0;\n        this.view = new DataView(new ArrayBuffer(this.initialBufferSize));\n        this.bytes = new Uint8Array(this.view.buffer);\n    }\n    Encoder.prototype.getUint8Array = function () {\n        return this.bytes.subarray(0, this.pos);\n    };\n    Encoder.prototype.reinitializeState = function () {\n        this.pos = 0;\n    };\n    Encoder.prototype.encode = function (object) {\n        this.reinitializeState();\n        this.doEncode(object, 1);\n        return this.getUint8Array();\n    };\n    Encoder.prototype.doEncode = function (object, depth) {\n        if (depth > this.maxDepth) {\n            throw new Error(\"Too deep objects in depth \" + depth);\n        }\n        if (object == null) {\n            this.encodeNil();\n        }\n        else if (typeof object === \"boolean\") {\n            this.encodeBoolean(object);\n        }\n        else if (typeof object === \"number\") {\n            this.encodeNumber(object);\n        }\n        else if (typeof object === \"string\") {\n            this.encodeString(object);\n        }\n        else {\n            this.encodeObject(object, depth);\n        }\n    };\n    Encoder.prototype.ensureBufferSizeToWrite = function (sizeToWrite) {\n        var requiredSize = this.pos + sizeToWrite;\n        if (this.view.byteLength < requiredSize) {\n            this.resizeBuffer(requiredSize * 2);\n        }\n    };\n    Encoder.prototype.resizeBuffer = function (newSize) {\n        var newBuffer = new ArrayBuffer(newSize);\n        var newBytes = new Uint8Array(newBuffer);\n        var newView = new DataView(newBuffer);\n        newBytes.set(this.bytes);\n        this.view = newView;\n        this.bytes = newBytes;\n    };\n    Encoder.prototype.encodeNil = function () {\n        this.writeU8(0xc0);\n    };\n    Encoder.prototype.encodeBoolean = function (object) {\n        if (object === false) {\n            this.writeU8(0xc2);\n        }\n        else {\n            this.writeU8(0xc3);\n        }\n    };\n    Encoder.prototype.encodeNumber = function (object) {\n        if (Number.isSafeInteger(object) && !this.forceIntegerToFloat) {\n            if (object >= 0) {\n                if (object < 0x80) {\n                    // positive fixint\n                    this.writeU8(object);\n                }\n                else if (object < 0x100) {\n                    // uint 8\n                    this.writeU8(0xcc);\n                    this.writeU8(object);\n                }\n                else if (object < 0x10000) {\n                    // uint 16\n                    this.writeU8(0xcd);\n                    this.writeU16(object);\n                }\n                else if (object < 0x100000000) {\n                    // uint 32\n                    this.writeU8(0xce);\n                    this.writeU32(object);\n                }\n                else {\n                    // uint 64\n                    this.writeU8(0xcf);\n                    this.writeU64(object);\n                }\n            }\n            else {\n                if (object >= -0x20) {\n                    // negative fixint\n                    this.writeU8(0xe0 | (object + 0x20));\n                }\n                else if (object >= -0x80) {\n                    // int 8\n                    this.writeU8(0xd0);\n                    this.writeI8(object);\n                }\n                else if (object >= -0x8000) {\n                    // int 16\n                    this.writeU8(0xd1);\n                    this.writeI16(object);\n                }\n                else if (object >= -0x80000000) {\n                    // int 32\n                    this.writeU8(0xd2);\n                    this.writeI32(object);\n                }\n                else {\n                    // int 64\n                    this.writeU8(0xd3);\n                    this.writeI64(object);\n                }\n            }\n        }\n        else {\n            // non-integer numbers\n            if (this.forceFloat32) {\n                // float 32\n                this.writeU8(0xca);\n                this.writeF32(object);\n            }\n            else {\n                // float 64\n                this.writeU8(0xcb);\n                this.writeF64(object);\n            }\n        }\n    };\n    Encoder.prototype.writeStringHeader = function (byteLength) {\n        if (byteLength < 32) {\n            // fixstr\n            this.writeU8(0xa0 + byteLength);\n        }\n        else if (byteLength < 0x100) {\n            // str 8\n            this.writeU8(0xd9);\n            this.writeU8(byteLength);\n        }\n        else if (byteLength < 0x10000) {\n            // str 16\n            this.writeU8(0xda);\n            this.writeU16(byteLength);\n        }\n        else if (byteLength < 0x100000000) {\n            // str 32\n            this.writeU8(0xdb);\n            this.writeU32(byteLength);\n        }\n        else {\n            throw new Error(\"Too long string: \" + byteLength + \" bytes in UTF-8\");\n        }\n    };\n    Encoder.prototype.encodeString = function (object) {\n        var maxHeaderSize = 1 + 4;\n        var strLength = object.length;\n        if (strLength > TEXT_ENCODER_THRESHOLD) {\n            var byteLength = utf8Count(object);\n            this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);\n            this.writeStringHeader(byteLength);\n            utf8EncodeTE(object, this.bytes, this.pos);\n            this.pos += byteLength;\n        }\n        else {\n            var byteLength = utf8Count(object);\n            this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);\n            this.writeStringHeader(byteLength);\n            utf8EncodeJs(object, this.bytes, this.pos);\n            this.pos += byteLength;\n        }\n    };\n    Encoder.prototype.encodeObject = function (object, depth) {\n        // try to encode objects with custom codec first of non-primitives\n        var ext = this.extensionCodec.tryToEncode(object, this.context);\n        if (ext != null) {\n            this.encodeExtension(ext);\n        }\n        else if (Array.isArray(object)) {\n            this.encodeArray(object, depth);\n        }\n        else if (ArrayBuffer.isView(object)) {\n            this.encodeBinary(object);\n        }\n        else if (typeof object === \"object\") {\n            this.encodeMap(object, depth);\n        }\n        else {\n            // symbol, function and other special object come here unless extensionCodec handles them.\n            throw new Error(\"Unrecognized object: \" + Object.prototype.toString.apply(object));\n        }\n    };\n    Encoder.prototype.encodeBinary = function (object) {\n        var size = object.byteLength;\n        if (size < 0x100) {\n            // bin 8\n            this.writeU8(0xc4);\n            this.writeU8(size);\n        }\n        else if (size < 0x10000) {\n            // bin 16\n            this.writeU8(0xc5);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // bin 32\n            this.writeU8(0xc6);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(\"Too large binary: \" + size);\n        }\n        var bytes = ensureUint8Array(object);\n        this.writeU8a(bytes);\n    };\n    Encoder.prototype.encodeArray = function (object, depth) {\n        var size = object.length;\n        if (size < 16) {\n            // fixarray\n            this.writeU8(0x90 + size);\n        }\n        else if (size < 0x10000) {\n            // array 16\n            this.writeU8(0xdc);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // array 32\n            this.writeU8(0xdd);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(\"Too large array: \" + size);\n        }\n        for (var _i = 0, object_1 = object; _i < object_1.length; _i++) {\n            var item = object_1[_i];\n            this.doEncode(item, depth + 1);\n        }\n    };\n    Encoder.prototype.countWithoutUndefined = function (object, keys) {\n        var count = 0;\n        for (var _i = 0, keys_1 = keys; _i < keys_1.length; _i++) {\n            var key = keys_1[_i];\n            if (object[key] !== undefined) {\n                count++;\n            }\n        }\n        return count;\n    };\n    Encoder.prototype.encodeMap = function (object, depth) {\n        var keys = Object.keys(object);\n        if (this.sortKeys) {\n            keys.sort();\n        }\n        var size = this.ignoreUndefined ? this.countWithoutUndefined(object, keys) : keys.length;\n        if (size < 16) {\n            // fixmap\n            this.writeU8(0x80 + size);\n        }\n        else if (size < 0x10000) {\n            // map 16\n            this.writeU8(0xde);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // map 32\n            this.writeU8(0xdf);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(\"Too large map object: \" + size);\n        }\n        for (var _i = 0, keys_2 = keys; _i < keys_2.length; _i++) {\n            var key = keys_2[_i];\n            var value = object[key];\n            if (!(this.ignoreUndefined && value === undefined)) {\n                this.encodeString(key);\n                this.doEncode(value, depth + 1);\n            }\n        }\n    };\n    Encoder.prototype.encodeExtension = function (ext) {\n        var size = ext.data.length;\n        if (size === 1) {\n            // fixext 1\n            this.writeU8(0xd4);\n        }\n        else if (size === 2) {\n            // fixext 2\n            this.writeU8(0xd5);\n        }\n        else if (size === 4) {\n            // fixext 4\n            this.writeU8(0xd6);\n        }\n        else if (size === 8) {\n            // fixext 8\n            this.writeU8(0xd7);\n        }\n        else if (size === 16) {\n            // fixext 16\n            this.writeU8(0xd8);\n        }\n        else if (size < 0x100) {\n            // ext 8\n            this.writeU8(0xc7);\n            this.writeU8(size);\n        }\n        else if (size < 0x10000) {\n            // ext 16\n            this.writeU8(0xc8);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // ext 32\n            this.writeU8(0xc9);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(\"Too large extension object: \" + size);\n        }\n        this.writeI8(ext.type);\n        this.writeU8a(ext.data);\n    };\n    Encoder.prototype.writeU8 = function (value) {\n        this.ensureBufferSizeToWrite(1);\n        this.view.setUint8(this.pos, value);\n        this.pos++;\n    };\n    Encoder.prototype.writeU8a = function (values) {\n        var size = values.length;\n        this.ensureBufferSizeToWrite(size);\n        this.bytes.set(values, this.pos);\n        this.pos += size;\n    };\n    Encoder.prototype.writeI8 = function (value) {\n        this.ensureBufferSizeToWrite(1);\n        this.view.setInt8(this.pos, value);\n        this.pos++;\n    };\n    Encoder.prototype.writeU16 = function (value) {\n        this.ensureBufferSizeToWrite(2);\n        this.view.setUint16(this.pos, value);\n        this.pos += 2;\n    };\n    Encoder.prototype.writeI16 = function (value) {\n        this.ensureBufferSizeToWrite(2);\n        this.view.setInt16(this.pos, value);\n        this.pos += 2;\n    };\n    Encoder.prototype.writeU32 = function (value) {\n        this.ensureBufferSizeToWrite(4);\n        this.view.setUint32(this.pos, value);\n        this.pos += 4;\n    };\n    Encoder.prototype.writeI32 = function (value) {\n        this.ensureBufferSizeToWrite(4);\n        this.view.setInt32(this.pos, value);\n        this.pos += 4;\n    };\n    Encoder.prototype.writeF32 = function (value) {\n        this.ensureBufferSizeToWrite(4);\n        this.view.setFloat32(this.pos, value);\n        this.pos += 4;\n    };\n    Encoder.prototype.writeF64 = function (value) {\n        this.ensureBufferSizeToWrite(8);\n        this.view.setFloat64(this.pos, value);\n        this.pos += 8;\n    };\n    Encoder.prototype.writeU64 = function (value) {\n        this.ensureBufferSizeToWrite(8);\n        setUint64(this.view, this.pos, value);\n        this.pos += 8;\n    };\n    Encoder.prototype.writeI64 = function (value) {\n        this.ensureBufferSizeToWrite(8);\n        setInt64(this.view, this.pos, value);\n        this.pos += 8;\n    };\n    return Encoder;\n}());\nexport { Encoder };\n//# sourceMappingURL=Encoder.mjs.map", "export function prettyByte(byte) {\n    return (byte < 0 ? \"-\" : \"\") + \"0x\" + Math.abs(byte).toString(16).padStart(2, \"0\");\n}\n//# sourceMappingURL=prettyByte.mjs.map", "import { utf8DecodeJs } from \"./utils/utf8.mjs\";\nvar DEFAULT_MAX_KEY_LENGTH = 16;\nvar DEFAULT_MAX_LENGTH_PER_KEY = 16;\nvar CachedKeyDecoder = /** @class */ (function () {\n    function CachedKeyDecoder(maxKeyLength, maxLengthPerKey) {\n        if (maxKeyLength === void 0) { maxKeyLength = DEFAULT_MAX_KEY_LENGTH; }\n        if (maxLengthPerKey === void 0) { maxLengthPerKey = DEFAULT_MAX_LENGTH_PER_KEY; }\n        this.maxKeyLength = maxKeyLength;\n        this.maxLengthPerKey = maxLengthPerKey;\n        this.hit = 0;\n        this.miss = 0;\n        // avoid `new Array(N)`, which makes a sparse array,\n        // because a sparse array is typically slower than a non-sparse array.\n        this.caches = [];\n        for (var i = 0; i < this.maxKeyLength; i++) {\n            this.caches.push([]);\n        }\n    }\n    CachedKeyDecoder.prototype.canBeCached = function (byteLength) {\n        return byteLength > 0 && byteLength <= this.maxKeyLength;\n    };\n    CachedKeyDecoder.prototype.find = function (bytes, inputOffset, byteLength) {\n        var records = this.caches[byteLength - 1];\n        FIND_CHUNK: for (var _i = 0, records_1 = records; _i < records_1.length; _i++) {\n            var record = records_1[_i];\n            var recordBytes = record.bytes;\n            for (var j = 0; j < byteLength; j++) {\n                if (recordBytes[j] !== bytes[inputOffset + j]) {\n                    continue FIND_CHUNK;\n                }\n            }\n            return record.str;\n        }\n        return null;\n    };\n    CachedKeyDecoder.prototype.store = function (bytes, value) {\n        var records = this.caches[bytes.length - 1];\n        var record = { bytes: bytes, str: value };\n        if (records.length >= this.maxLengthPerKey) {\n            // `records` are full!\n            // Set `record` to an arbitrary position.\n            records[(Math.random() * records.length) | 0] = record;\n        }\n        else {\n            records.push(record);\n        }\n    };\n    CachedKeyDecoder.prototype.decode = function (bytes, inputOffset, byteLength) {\n        var cachedValue = this.find(bytes, inputOffset, byteLength);\n        if (cachedValue != null) {\n            this.hit++;\n            return cachedValue;\n        }\n        this.miss++;\n        var str = utf8DecodeJs(bytes, inputOffset, byteLength);\n        // Ensure to copy a slice of bytes because the byte may be NodeJS Buffer and Buffer#slice() returns a reference to its internal ArrayBuffer.\n        var slicedCopyOfBytes = Uint8Array.prototype.slice.call(bytes, inputOffset, inputOffset + byteLength);\n        this.store(slicedCopyOfBytes, str);\n        return str;\n    };\n    return CachedKeyDecoder;\n}());\nexport { CachedKeyDecoder };\n//# sourceMappingURL=CachedKeyDecoder.mjs.map", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __asyncValues = (this && this.__asyncValues) || function (o) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var m = o[Symbol.asyncIterator], i;\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n};\nvar __await = (this && this.__await) || function (v) { return this instanceof __await ? (this.v = v, this) : new __await(v); }\nvar __asyncGenerator = (this && this.__asyncGenerator) || function (thisArg, _arguments, generator) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n    function fulfill(value) { resume(\"next\", value); }\n    function reject(value) { resume(\"throw\", value); }\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n};\nimport { prettyByte } from \"./utils/prettyByte.mjs\";\nimport { ExtensionCodec } from \"./ExtensionCodec.mjs\";\nimport { getInt64, getUint64, UINT32_MAX } from \"./utils/int.mjs\";\nimport { utf8DecodeJs, TEXT_DECODER_THRESHOLD, utf8DecodeTD } from \"./utils/utf8.mjs\";\nimport { createDataView, ensureUint8Array } from \"./utils/typedArrays.mjs\";\nimport { CachedKeyDecoder } from \"./CachedKeyDecoder.mjs\";\nimport { DecodeError } from \"./DecodeError.mjs\";\nvar isValidMapKeyType = function (key) {\n    var keyType = typeof key;\n    return keyType === \"string\" || keyType === \"number\";\n};\nvar HEAD_BYTE_REQUIRED = -1;\nvar EMPTY_VIEW = new DataView(new ArrayBuffer(0));\nvar EMPTY_BYTES = new Uint8Array(EMPTY_VIEW.buffer);\n// IE11: Hack to support IE11.\n// IE11: Drop this hack and just use RangeError when IE11 is obsolete.\nexport var DataViewIndexOutOfBoundsError = (function () {\n    try {\n        // IE11: The spec says it should throw RangeError,\n        // IE11: but in IE11 it throws TypeError.\n        EMPTY_VIEW.getInt8(0);\n    }\n    catch (e) {\n        return e.constructor;\n    }\n    throw new Error(\"never reached\");\n})();\nvar MORE_DATA = new DataViewIndexOutOfBoundsError(\"Insufficient data\");\nvar sharedCachedKeyDecoder = new CachedKeyDecoder();\nvar Decoder = /** @class */ (function () {\n    function Decoder(extensionCodec, context, maxStrLength, maxBinLength, maxArrayLength, maxMapLength, maxExtLength, keyDecoder) {\n        if (extensionCodec === void 0) { extensionCodec = ExtensionCodec.defaultCodec; }\n        if (context === void 0) { context = undefined; }\n        if (maxStrLength === void 0) { maxStrLength = UINT32_MAX; }\n        if (maxBinLength === void 0) { maxBinLength = UINT32_MAX; }\n        if (maxArrayLength === void 0) { maxArrayLength = UINT32_MAX; }\n        if (maxMapLength === void 0) { maxMapLength = UINT32_MAX; }\n        if (maxExtLength === void 0) { maxExtLength = UINT32_MAX; }\n        if (keyDecoder === void 0) { keyDecoder = sharedCachedKeyDecoder; }\n        this.extensionCodec = extensionCodec;\n        this.context = context;\n        this.maxStrLength = maxStrLength;\n        this.maxBinLength = maxBinLength;\n        this.maxArrayLength = maxArrayLength;\n        this.maxMapLength = maxMapLength;\n        this.maxExtLength = maxExtLength;\n        this.keyDecoder = keyDecoder;\n        this.totalPos = 0;\n        this.pos = 0;\n        this.view = EMPTY_VIEW;\n        this.bytes = EMPTY_BYTES;\n        this.headByte = HEAD_BYTE_REQUIRED;\n        this.stack = [];\n    }\n    Decoder.prototype.reinitializeState = function () {\n        this.totalPos = 0;\n        this.headByte = HEAD_BYTE_REQUIRED;\n        this.stack.length = 0;\n        // view, bytes, and pos will be re-initialized in setBuffer()\n    };\n    Decoder.prototype.setBuffer = function (buffer) {\n        this.bytes = ensureUint8Array(buffer);\n        this.view = createDataView(this.bytes);\n        this.pos = 0;\n    };\n    Decoder.prototype.appendBuffer = function (buffer) {\n        if (this.headByte === HEAD_BYTE_REQUIRED && !this.hasRemaining(1)) {\n            this.setBuffer(buffer);\n        }\n        else {\n            var remainingData = this.bytes.subarray(this.pos);\n            var newData = ensureUint8Array(buffer);\n            // concat remainingData + newData\n            var newBuffer = new Uint8Array(remainingData.length + newData.length);\n            newBuffer.set(remainingData);\n            newBuffer.set(newData, remainingData.length);\n            this.setBuffer(newBuffer);\n        }\n    };\n    Decoder.prototype.hasRemaining = function (size) {\n        return this.view.byteLength - this.pos >= size;\n    };\n    Decoder.prototype.createExtraByteError = function (posToShow) {\n        var _a = this, view = _a.view, pos = _a.pos;\n        return new RangeError(\"Extra \" + (view.byteLength - pos) + \" of \" + view.byteLength + \" byte(s) found at buffer[\" + posToShow + \"]\");\n    };\n    /**\n     * @throws {DecodeError}\n     * @throws {RangeError}\n     */\n    Decoder.prototype.decode = function (buffer) {\n        this.reinitializeState();\n        this.setBuffer(buffer);\n        var object = this.doDecodeSync();\n        if (this.hasRemaining(1)) {\n            throw this.createExtraByteError(this.pos);\n        }\n        return object;\n    };\n    Decoder.prototype.decodeMulti = function (buffer) {\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    this.reinitializeState();\n                    this.setBuffer(buffer);\n                    _a.label = 1;\n                case 1:\n                    if (!this.hasRemaining(1)) return [3 /*break*/, 3];\n                    return [4 /*yield*/, this.doDecodeSync()];\n                case 2:\n                    _a.sent();\n                    return [3 /*break*/, 1];\n                case 3: return [2 /*return*/];\n            }\n        });\n    };\n    Decoder.prototype.decodeAsync = function (stream) {\n        var stream_1, stream_1_1;\n        var e_1, _a;\n        return __awaiter(this, void 0, void 0, function () {\n            var decoded, object, buffer, e_1_1, _b, headByte, pos, totalPos;\n            return __generator(this, function (_c) {\n                switch (_c.label) {\n                    case 0:\n                        decoded = false;\n                        _c.label = 1;\n                    case 1:\n                        _c.trys.push([1, 6, 7, 12]);\n                        stream_1 = __asyncValues(stream);\n                        _c.label = 2;\n                    case 2: return [4 /*yield*/, stream_1.next()];\n                    case 3:\n                        if (!(stream_1_1 = _c.sent(), !stream_1_1.done)) return [3 /*break*/, 5];\n                        buffer = stream_1_1.value;\n                        if (decoded) {\n                            throw this.createExtraByteError(this.totalPos);\n                        }\n                        this.appendBuffer(buffer);\n                        try {\n                            object = this.doDecodeSync();\n                            decoded = true;\n                        }\n                        catch (e) {\n                            if (!(e instanceof DataViewIndexOutOfBoundsError)) {\n                                throw e; // rethrow\n                            }\n                            // fallthrough\n                        }\n                        this.totalPos += this.pos;\n                        _c.label = 4;\n                    case 4: return [3 /*break*/, 2];\n                    case 5: return [3 /*break*/, 12];\n                    case 6:\n                        e_1_1 = _c.sent();\n                        e_1 = { error: e_1_1 };\n                        return [3 /*break*/, 12];\n                    case 7:\n                        _c.trys.push([7, , 10, 11]);\n                        if (!(stream_1_1 && !stream_1_1.done && (_a = stream_1.return))) return [3 /*break*/, 9];\n                        return [4 /*yield*/, _a.call(stream_1)];\n                    case 8:\n                        _c.sent();\n                        _c.label = 9;\n                    case 9: return [3 /*break*/, 11];\n                    case 10:\n                        if (e_1) throw e_1.error;\n                        return [7 /*endfinally*/];\n                    case 11: return [7 /*endfinally*/];\n                    case 12:\n                        if (decoded) {\n                            if (this.hasRemaining(1)) {\n                                throw this.createExtraByteError(this.totalPos);\n                            }\n                            return [2 /*return*/, object];\n                        }\n                        _b = this, headByte = _b.headByte, pos = _b.pos, totalPos = _b.totalPos;\n                        throw new RangeError(\"Insufficient data in parsing \" + prettyByte(headByte) + \" at \" + totalPos + \" (\" + pos + \" in the current buffer)\");\n                }\n            });\n        });\n    };\n    Decoder.prototype.decodeArrayStream = function (stream) {\n        return this.decodeMultiAsync(stream, true);\n    };\n    Decoder.prototype.decodeStream = function (stream) {\n        return this.decodeMultiAsync(stream, false);\n    };\n    Decoder.prototype.decodeMultiAsync = function (stream, isArray) {\n        return __asyncGenerator(this, arguments, function decodeMultiAsync_1() {\n            var isArrayHeaderRequired, arrayItemsLeft, stream_2, stream_2_1, buffer, e_2, e_3_1;\n            var e_3, _a;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        isArrayHeaderRequired = isArray;\n                        arrayItemsLeft = -1;\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 13, 14, 19]);\n                        stream_2 = __asyncValues(stream);\n                        _b.label = 2;\n                    case 2: return [4 /*yield*/, __await(stream_2.next())];\n                    case 3:\n                        if (!(stream_2_1 = _b.sent(), !stream_2_1.done)) return [3 /*break*/, 12];\n                        buffer = stream_2_1.value;\n                        if (isArray && arrayItemsLeft === 0) {\n                            throw this.createExtraByteError(this.totalPos);\n                        }\n                        this.appendBuffer(buffer);\n                        if (isArrayHeaderRequired) {\n                            arrayItemsLeft = this.readArraySize();\n                            isArrayHeaderRequired = false;\n                            this.complete();\n                        }\n                        _b.label = 4;\n                    case 4:\n                        _b.trys.push([4, 9, , 10]);\n                        _b.label = 5;\n                    case 5:\n                        if (!true) return [3 /*break*/, 8];\n                        return [4 /*yield*/, __await(this.doDecodeSync())];\n                    case 6: return [4 /*yield*/, _b.sent()];\n                    case 7:\n                        _b.sent();\n                        if (--arrayItemsLeft === 0) {\n                            return [3 /*break*/, 8];\n                        }\n                        return [3 /*break*/, 5];\n                    case 8: return [3 /*break*/, 10];\n                    case 9:\n                        e_2 = _b.sent();\n                        if (!(e_2 instanceof DataViewIndexOutOfBoundsError)) {\n                            throw e_2; // rethrow\n                        }\n                        return [3 /*break*/, 10];\n                    case 10:\n                        this.totalPos += this.pos;\n                        _b.label = 11;\n                    case 11: return [3 /*break*/, 2];\n                    case 12: return [3 /*break*/, 19];\n                    case 13:\n                        e_3_1 = _b.sent();\n                        e_3 = { error: e_3_1 };\n                        return [3 /*break*/, 19];\n                    case 14:\n                        _b.trys.push([14, , 17, 18]);\n                        if (!(stream_2_1 && !stream_2_1.done && (_a = stream_2.return))) return [3 /*break*/, 16];\n                        return [4 /*yield*/, __await(_a.call(stream_2))];\n                    case 15:\n                        _b.sent();\n                        _b.label = 16;\n                    case 16: return [3 /*break*/, 18];\n                    case 17:\n                        if (e_3) throw e_3.error;\n                        return [7 /*endfinally*/];\n                    case 18: return [7 /*endfinally*/];\n                    case 19: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Decoder.prototype.doDecodeSync = function () {\n        DECODE: while (true) {\n            var headByte = this.readHeadByte();\n            var object = void 0;\n            if (headByte >= 0xe0) {\n                // negative fixint (111x xxxx) 0xe0 - 0xff\n                object = headByte - 0x100;\n            }\n            else if (headByte < 0xc0) {\n                if (headByte < 0x80) {\n                    // positive fixint (0xxx xxxx) 0x00 - 0x7f\n                    object = headByte;\n                }\n                else if (headByte < 0x90) {\n                    // fixmap (1000 xxxx) 0x80 - 0x8f\n                    var size = headByte - 0x80;\n                    if (size !== 0) {\n                        this.pushMapState(size);\n                        this.complete();\n                        continue DECODE;\n                    }\n                    else {\n                        object = {};\n                    }\n                }\n                else if (headByte < 0xa0) {\n                    // fixarray (1001 xxxx) 0x90 - 0x9f\n                    var size = headByte - 0x90;\n                    if (size !== 0) {\n                        this.pushArrayState(size);\n                        this.complete();\n                        continue DECODE;\n                    }\n                    else {\n                        object = [];\n                    }\n                }\n                else {\n                    // fixstr (101x xxxx) 0xa0 - 0xbf\n                    var byteLength = headByte - 0xa0;\n                    object = this.decodeUtf8String(byteLength, 0);\n                }\n            }\n            else if (headByte === 0xc0) {\n                // nil\n                object = null;\n            }\n            else if (headByte === 0xc2) {\n                // false\n                object = false;\n            }\n            else if (headByte === 0xc3) {\n                // true\n                object = true;\n            }\n            else if (headByte === 0xca) {\n                // float 32\n                object = this.readF32();\n            }\n            else if (headByte === 0xcb) {\n                // float 64\n                object = this.readF64();\n            }\n            else if (headByte === 0xcc) {\n                // uint 8\n                object = this.readU8();\n            }\n            else if (headByte === 0xcd) {\n                // uint 16\n                object = this.readU16();\n            }\n            else if (headByte === 0xce) {\n                // uint 32\n                object = this.readU32();\n            }\n            else if (headByte === 0xcf) {\n                // uint 64\n                object = this.readU64();\n            }\n            else if (headByte === 0xd0) {\n                // int 8\n                object = this.readI8();\n            }\n            else if (headByte === 0xd1) {\n                // int 16\n                object = this.readI16();\n            }\n            else if (headByte === 0xd2) {\n                // int 32\n                object = this.readI32();\n            }\n            else if (headByte === 0xd3) {\n                // int 64\n                object = this.readI64();\n            }\n            else if (headByte === 0xd9) {\n                // str 8\n                var byteLength = this.lookU8();\n                object = this.decodeUtf8String(byteLength, 1);\n            }\n            else if (headByte === 0xda) {\n                // str 16\n                var byteLength = this.lookU16();\n                object = this.decodeUtf8String(byteLength, 2);\n            }\n            else if (headByte === 0xdb) {\n                // str 32\n                var byteLength = this.lookU32();\n                object = this.decodeUtf8String(byteLength, 4);\n            }\n            else if (headByte === 0xdc) {\n                // array 16\n                var size = this.readU16();\n                if (size !== 0) {\n                    this.pushArrayState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = [];\n                }\n            }\n            else if (headByte === 0xdd) {\n                // array 32\n                var size = this.readU32();\n                if (size !== 0) {\n                    this.pushArrayState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = [];\n                }\n            }\n            else if (headByte === 0xde) {\n                // map 16\n                var size = this.readU16();\n                if (size !== 0) {\n                    this.pushMapState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = {};\n                }\n            }\n            else if (headByte === 0xdf) {\n                // map 32\n                var size = this.readU32();\n                if (size !== 0) {\n                    this.pushMapState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = {};\n                }\n            }\n            else if (headByte === 0xc4) {\n                // bin 8\n                var size = this.lookU8();\n                object = this.decodeBinary(size, 1);\n            }\n            else if (headByte === 0xc5) {\n                // bin 16\n                var size = this.lookU16();\n                object = this.decodeBinary(size, 2);\n            }\n            else if (headByte === 0xc6) {\n                // bin 32\n                var size = this.lookU32();\n                object = this.decodeBinary(size, 4);\n            }\n            else if (headByte === 0xd4) {\n                // fixext 1\n                object = this.decodeExtension(1, 0);\n            }\n            else if (headByte === 0xd5) {\n                // fixext 2\n                object = this.decodeExtension(2, 0);\n            }\n            else if (headByte === 0xd6) {\n                // fixext 4\n                object = this.decodeExtension(4, 0);\n            }\n            else if (headByte === 0xd7) {\n                // fixext 8\n                object = this.decodeExtension(8, 0);\n            }\n            else if (headByte === 0xd8) {\n                // fixext 16\n                object = this.decodeExtension(16, 0);\n            }\n            else if (headByte === 0xc7) {\n                // ext 8\n                var size = this.lookU8();\n                object = this.decodeExtension(size, 1);\n            }\n            else if (headByte === 0xc8) {\n                // ext 16\n                var size = this.lookU16();\n                object = this.decodeExtension(size, 2);\n            }\n            else if (headByte === 0xc9) {\n                // ext 32\n                var size = this.lookU32();\n                object = this.decodeExtension(size, 4);\n            }\n            else {\n                throw new DecodeError(\"Unrecognized type byte: \" + prettyByte(headByte));\n            }\n            this.complete();\n            var stack = this.stack;\n            while (stack.length > 0) {\n                // arrays and maps\n                var state = stack[stack.length - 1];\n                if (state.type === 0 /* ARRAY */) {\n                    state.array[state.position] = object;\n                    state.position++;\n                    if (state.position === state.size) {\n                        stack.pop();\n                        object = state.array;\n                    }\n                    else {\n                        continue DECODE;\n                    }\n                }\n                else if (state.type === 1 /* MAP_KEY */) {\n                    if (!isValidMapKeyType(object)) {\n                        throw new DecodeError(\"The type of key must be string or number but \" + typeof object);\n                    }\n                    if (object === \"__proto__\") {\n                        throw new DecodeError(\"The key __proto__ is not allowed\");\n                    }\n                    state.key = object;\n                    state.type = 2 /* MAP_VALUE */;\n                    continue DECODE;\n                }\n                else {\n                    // it must be `state.type === State.MAP_VALUE` here\n                    state.map[state.key] = object;\n                    state.readCount++;\n                    if (state.readCount === state.size) {\n                        stack.pop();\n                        object = state.map;\n                    }\n                    else {\n                        state.key = null;\n                        state.type = 1 /* MAP_KEY */;\n                        continue DECODE;\n                    }\n                }\n            }\n            return object;\n        }\n    };\n    Decoder.prototype.readHeadByte = function () {\n        if (this.headByte === HEAD_BYTE_REQUIRED) {\n            this.headByte = this.readU8();\n            // console.log(\"headByte\", prettyByte(this.headByte));\n        }\n        return this.headByte;\n    };\n    Decoder.prototype.complete = function () {\n        this.headByte = HEAD_BYTE_REQUIRED;\n    };\n    Decoder.prototype.readArraySize = function () {\n        var headByte = this.readHeadByte();\n        switch (headByte) {\n            case 0xdc:\n                return this.readU16();\n            case 0xdd:\n                return this.readU32();\n            default: {\n                if (headByte < 0xa0) {\n                    return headByte - 0x90;\n                }\n                else {\n                    throw new DecodeError(\"Unrecognized array type byte: \" + prettyByte(headByte));\n                }\n            }\n        }\n    };\n    Decoder.prototype.pushMapState = function (size) {\n        if (size > this.maxMapLength) {\n            throw new DecodeError(\"Max length exceeded: map length (\" + size + \") > maxMapLengthLength (\" + this.maxMapLength + \")\");\n        }\n        this.stack.push({\n            type: 1 /* MAP_KEY */,\n            size: size,\n            key: null,\n            readCount: 0,\n            map: {},\n        });\n    };\n    Decoder.prototype.pushArrayState = function (size) {\n        if (size > this.maxArrayLength) {\n            throw new DecodeError(\"Max length exceeded: array length (\" + size + \") > maxArrayLength (\" + this.maxArrayLength + \")\");\n        }\n        this.stack.push({\n            type: 0 /* ARRAY */,\n            size: size,\n            array: new Array(size),\n            position: 0,\n        });\n    };\n    Decoder.prototype.decodeUtf8String = function (byteLength, headerOffset) {\n        var _a;\n        if (byteLength > this.maxStrLength) {\n            throw new DecodeError(\"Max length exceeded: UTF-8 byte length (\" + byteLength + \") > maxStrLength (\" + this.maxStrLength + \")\");\n        }\n        if (this.bytes.byteLength < this.pos + headerOffset + byteLength) {\n            throw MORE_DATA;\n        }\n        var offset = this.pos + headerOffset;\n        var object;\n        if (this.stateIsMapKey() && ((_a = this.keyDecoder) === null || _a === void 0 ? void 0 : _a.canBeCached(byteLength))) {\n            object = this.keyDecoder.decode(this.bytes, offset, byteLength);\n        }\n        else if (byteLength > TEXT_DECODER_THRESHOLD) {\n            object = utf8DecodeTD(this.bytes, offset, byteLength);\n        }\n        else {\n            object = utf8DecodeJs(this.bytes, offset, byteLength);\n        }\n        this.pos += headerOffset + byteLength;\n        return object;\n    };\n    Decoder.prototype.stateIsMapKey = function () {\n        if (this.stack.length > 0) {\n            var state = this.stack[this.stack.length - 1];\n            return state.type === 1 /* MAP_KEY */;\n        }\n        return false;\n    };\n    Decoder.prototype.decodeBinary = function (byteLength, headOffset) {\n        if (byteLength > this.maxBinLength) {\n            throw new DecodeError(\"Max length exceeded: bin length (\" + byteLength + \") > maxBinLength (\" + this.maxBinLength + \")\");\n        }\n        if (!this.hasRemaining(byteLength + headOffset)) {\n            throw MORE_DATA;\n        }\n        var offset = this.pos + headOffset;\n        var object = this.bytes.subarray(offset, offset + byteLength);\n        this.pos += headOffset + byteLength;\n        return object;\n    };\n    Decoder.prototype.decodeExtension = function (size, headOffset) {\n        if (size > this.maxExtLength) {\n            throw new DecodeError(\"Max length exceeded: ext length (\" + size + \") > maxExtLength (\" + this.maxExtLength + \")\");\n        }\n        var extType = this.view.getInt8(this.pos + headOffset);\n        var data = this.decodeBinary(size, headOffset + 1 /* extType */);\n        return this.extensionCodec.decode(data, extType, this.context);\n    };\n    Decoder.prototype.lookU8 = function () {\n        return this.view.getUint8(this.pos);\n    };\n    Decoder.prototype.lookU16 = function () {\n        return this.view.getUint16(this.pos);\n    };\n    Decoder.prototype.lookU32 = function () {\n        return this.view.getUint32(this.pos);\n    };\n    Decoder.prototype.readU8 = function () {\n        var value = this.view.getUint8(this.pos);\n        this.pos++;\n        return value;\n    };\n    Decoder.prototype.readI8 = function () {\n        var value = this.view.getInt8(this.pos);\n        this.pos++;\n        return value;\n    };\n    Decoder.prototype.readU16 = function () {\n        var value = this.view.getUint16(this.pos);\n        this.pos += 2;\n        return value;\n    };\n    Decoder.prototype.readI16 = function () {\n        var value = this.view.getInt16(this.pos);\n        this.pos += 2;\n        return value;\n    };\n    Decoder.prototype.readU32 = function () {\n        var value = this.view.getUint32(this.pos);\n        this.pos += 4;\n        return value;\n    };\n    Decoder.prototype.readI32 = function () {\n        var value = this.view.getInt32(this.pos);\n        this.pos += 4;\n        return value;\n    };\n    Decoder.prototype.readU64 = function () {\n        var value = getUint64(this.view, this.pos);\n        this.pos += 8;\n        return value;\n    };\n    Decoder.prototype.readI64 = function () {\n        var value = getInt64(this.view, this.pos);\n        this.pos += 8;\n        return value;\n    };\n    Decoder.prototype.readF32 = function () {\n        var value = this.view.getFloat32(this.pos);\n        this.pos += 4;\n        return value;\n    };\n    Decoder.prototype.readF64 = function () {\n        var value = this.view.getFloat64(this.pos);\n        this.pos += 8;\n        return value;\n    };\n    return Decoder;\n}());\nexport { Decoder };\n//# sourceMappingURL=Decoder.mjs.map", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Not exported from index.\r\n/** @private */\r\nexport class BinaryMessageFormat {\r\n\r\n    // The length prefix of binary messages is encoded as VarInt. Read the comment in\r\n    // the BinaryMessageParser.TryParseMessage for details.\r\n\r\n    public static write(output: Uint8Array): ArrayBuffer {\r\n        let size = output.byteLength || output.length;\r\n        const lenBuffer = [];\r\n        do {\r\n            let sizePart = size & 0x7f;\r\n            size = size >> 7;\r\n            if (size > 0) {\r\n                sizePart |= 0x80;\r\n            }\r\n            lenBuffer.push(sizePart);\r\n        }\r\n        while (size > 0);\r\n\r\n        size = output.byteLength || output.length;\r\n\r\n        const buffer = new Uint8Array(lenBuffer.length + size);\r\n        buffer.set(lenBuffer, 0);\r\n        buffer.set(output, lenBuffer.length);\r\n        return buffer.buffer;\r\n    }\r\n\r\n    public static parse(input: ArrayBuffer): Uint8Array[] {\r\n        const result: Uint8Array[] = [];\r\n        const uint8Array = new Uint8Array(input);\r\n        const maxLengthPrefixSize = 5;\r\n        const numBitsToShift = [0, 7, 14, 21, 28 ];\r\n\r\n        for (let offset = 0; offset < input.byteLength;) {\r\n            let numBytes = 0;\r\n            let size = 0;\r\n            let byteRead;\r\n            do {\r\n                byteRead = uint8Array[offset + numBytes];\r\n                size = size | ((byteRead & 0x7f) << (numBitsToShift[numBytes]));\r\n                numBytes++;\r\n            }\r\n            while (numBytes < Math.min(maxLengthPrefixSize, input.byteLength - offset) && (byteRead & 0x80) !== 0);\r\n\r\n            if ((byteRead & 0x80) !== 0 && numBytes < maxLengthPrefixSize) {\r\n                throw new Error(\"Cannot read message size.\");\r\n            }\r\n\r\n            if (numBytes === maxLengthPrefixSize && byteRead > 7) {\r\n                throw new Error(\"Messages bigger than 2GB are not supported.\");\r\n            }\r\n\r\n            if (uint8Array.byteLength >= (offset + numBytes + size)) {\r\n                // IE does not support .slice() so use subarray\r\n                result.push(uint8Array.slice\r\n                    ? uint8Array.slice(offset + numBytes, offset + numBytes + size)\r\n                    : uint8Array.subarray(offset + numBytes, offset + numBytes + size));\r\n            } else {\r\n                throw new Error(\"Incomplete message.\");\r\n            }\r\n\r\n            offset = offset + numBytes + size;\r\n        }\r\n\r\n        return result;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Copied from signalr/Utils.ts\r\n/** @private */\r\nexport function isArrayBuffer(val: any): val is ArrayBuffer {\r\n    return val && typeof ArrayBuffer !== \"undefined\" &&\r\n        (val instanceof ArrayBuffer ||\r\n        // Sometimes we get an ArrayBuffer that doesn't satisfy instanceof\r\n        (val.constructor && val.constructor.name === \"ArrayBuffer\"));\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { Encoder, Decoder } from \"@msgpack/msgpack\";\r\n\r\nimport { MessagePackOptions } from \"./MessagePackOptions\";\r\n\r\nimport {\r\n    CancelInvocationMessage, CompletionMessage, HubMessage, IHubProtocol, ILogger, InvocationMessage,\r\n    LogLevel, MessageHeaders, MessageType, NullLogger, StreamInvocationMessage, StreamItemMessage, TransferFormat,\r\n} from \"@microsoft/signalr\";\r\n\r\nimport { BinaryMessageFormat } from \"./BinaryMessageFormat\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n\r\n// TypeDoc's @inheritDoc and @link don't work across modules :(\r\n\r\n// constant encoding of the ping message\r\n// see: https://github.com/aspnet/SignalR/blob/dev/specs/HubProtocol.md#ping-message-encoding-1\r\n// Don't use Uint8Array.from as IE does not support it\r\nconst SERIALIZED_PING_MESSAGE: Uint8Array = new Uint8Array([0x91, MessageType.Ping]);\r\n\r\n/** Implements the MessagePack Hub Protocol */\r\nexport class MessagePackHubProtocol implements IHubProtocol {\r\n    /** The name of the protocol. This is used by SignalR to resolve the protocol between the client and server. */\r\n    public readonly name: string = \"messagepack\";\r\n    /** The version of the protocol. */\r\n    public readonly version: number = 1;\r\n    /** The TransferFormat of the protocol. */\r\n    public readonly transferFormat: TransferFormat = TransferFormat.Binary;\r\n\r\n    private readonly _errorResult = 1;\r\n    private readonly _voidResult = 2;\r\n    private readonly _nonVoidResult = 3;\r\n\r\n    private readonly _encoder: Encoder<undefined>;\r\n    private readonly _decoder: Decoder<undefined>;\r\n\r\n    /**\r\n     *\r\n     * @param messagePackOptions MessagePack options passed to @msgpack/msgpack\r\n     */\r\n    constructor(messagePackOptions?: MessagePackOptions) {\r\n        messagePackOptions = messagePackOptions || {};\r\n        this._encoder = new Encoder(\r\n            messagePackOptions.extensionCodec,\r\n            messagePackOptions.context,\r\n            messagePackOptions.maxDepth,\r\n            messagePackOptions.initialBufferSize,\r\n            messagePackOptions.sortKeys,\r\n            messagePackOptions.forceFloat32,\r\n            messagePackOptions.ignoreUndefined,\r\n            messagePackOptions.forceIntegerToFloat,\r\n        );\r\n\r\n        this._decoder = new Decoder(\r\n            messagePackOptions.extensionCodec,\r\n            messagePackOptions.context,\r\n            messagePackOptions.maxStrLength,\r\n            messagePackOptions.maxBinLength,\r\n            messagePackOptions.maxArrayLength,\r\n            messagePackOptions.maxMapLength,\r\n            messagePackOptions.maxExtLength,\r\n        );\r\n    }\r\n\r\n    /** Creates an array of HubMessage objects from the specified serialized representation.\r\n     *\r\n     * @param {ArrayBuffer} input An ArrayBuffer containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    public parseMessages(input: ArrayBuffer, logger: ILogger): HubMessage[] {\r\n        // The interface does allow \"string\" to be passed in, but this implementation does not. So let's throw a useful error.\r\n        if (!(isArrayBuffer(input))) {\r\n            throw new Error(\"Invalid input for MessagePack hub protocol. Expected an ArrayBuffer.\");\r\n        }\r\n\r\n        if (logger === null) {\r\n            logger = NullLogger.instance;\r\n        }\r\n\r\n        const messages = BinaryMessageFormat.parse(input);\r\n\r\n        const hubMessages = [];\r\n        for (const message of messages) {\r\n            const parsedMessage = this._parseMessage(message, logger);\r\n            // Can be null for an unknown message. Unknown message is logged in parseMessage\r\n            if (parsedMessage) {\r\n                hubMessages.push(parsedMessage);\r\n            }\r\n        }\r\n\r\n        return hubMessages;\r\n    }\r\n\r\n    /** Writes the specified HubMessage to an ArrayBuffer and returns it.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {ArrayBuffer} An ArrayBuffer containing the serialized representation of the message.\r\n     */\r\n    public writeMessage(message: HubMessage): ArrayBuffer {\r\n        switch (message.type) {\r\n            case MessageType.Invocation:\r\n                return this._writeInvocation(message as InvocationMessage);\r\n            case MessageType.StreamInvocation:\r\n                return this._writeStreamInvocation(message as StreamInvocationMessage);\r\n            case MessageType.StreamItem:\r\n                return this._writeStreamItem(message as StreamItemMessage);\r\n            case MessageType.Completion:\r\n                return this._writeCompletion(message as CompletionMessage);\r\n            case MessageType.Ping:\r\n                return BinaryMessageFormat.write(SERIALIZED_PING_MESSAGE);\r\n            case MessageType.CancelInvocation:\r\n                return this._writeCancelInvocation(message as CancelInvocationMessage);\r\n            default:\r\n                throw new Error(\"Invalid message type.\");\r\n        }\r\n    }\r\n\r\n    private _parseMessage(input: Uint8Array, logger: ILogger): HubMessage | null {\r\n        if (input.length === 0) {\r\n            throw new Error(\"Invalid payload.\");\r\n        }\r\n\r\n        const properties = this._decoder.decode(input) as any;\r\n        if (properties.length === 0 || !(properties instanceof Array)) {\r\n            throw new Error(\"Invalid payload.\");\r\n        }\r\n\r\n        const messageType = properties[0] as MessageType;\r\n\r\n        switch (messageType) {\r\n            case MessageType.Invocation:\r\n                return this._createInvocationMessage(this._readHeaders(properties), properties);\r\n            case MessageType.StreamItem:\r\n                return this._createStreamItemMessage(this._readHeaders(properties), properties);\r\n            case MessageType.Completion:\r\n                return this._createCompletionMessage(this._readHeaders(properties), properties);\r\n            case MessageType.Ping:\r\n                return this._createPingMessage(properties);\r\n            case MessageType.Close:\r\n                return this._createCloseMessage(properties);\r\n            default:\r\n                // Future protocol changes can add message types, old clients can ignore them\r\n                logger.log(LogLevel.Information, \"Unknown message type '\" + messageType + \"' ignored.\");\r\n                return null;\r\n        }\r\n    }\r\n\r\n    private _createCloseMessage(properties: any[]): HubMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 2) {\r\n            throw new Error(\"Invalid payload for Close message.\");\r\n        }\r\n\r\n        return {\r\n            // Close messages have no headers.\r\n            allowReconnect: properties.length >= 3 ? properties[2] : undefined,\r\n            error: properties[1],\r\n            type: MessageType.Close,\r\n        } as HubMessage;\r\n    }\r\n\r\n    private _createPingMessage(properties: any[]): HubMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 1) {\r\n            throw new Error(\"Invalid payload for Ping message.\");\r\n        }\r\n\r\n        return {\r\n            // Ping messages have no headers.\r\n            type: MessageType.Ping,\r\n        } as HubMessage;\r\n    }\r\n\r\n    private _createInvocationMessage(headers: MessageHeaders, properties: any[]): InvocationMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 5) {\r\n            throw new Error(\"Invalid payload for Invocation message.\");\r\n        }\r\n\r\n        const invocationId = properties[2] as string;\r\n        if (invocationId) {\r\n            return {\r\n                arguments: properties[4],\r\n                headers,\r\n                invocationId,\r\n                streamIds: [],\r\n                target: properties[3] as string,\r\n                type: MessageType.Invocation,\r\n            };\r\n        } else {\r\n            return {\r\n                arguments: properties[4],\r\n                headers,\r\n                streamIds: [],\r\n                target: properties[3],\r\n                type: MessageType.Invocation,\r\n            };\r\n        }\r\n\r\n    }\r\n\r\n    private _createStreamItemMessage(headers: MessageHeaders, properties: any[]): StreamItemMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 4) {\r\n            throw new Error(\"Invalid payload for StreamItem message.\");\r\n        }\r\n\r\n        return {\r\n            headers,\r\n            invocationId: properties[2],\r\n            item: properties[3],\r\n            type: MessageType.StreamItem,\r\n        } as StreamItemMessage;\r\n    }\r\n\r\n    private _createCompletionMessage(headers: MessageHeaders, properties: any[]): CompletionMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 4) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        const resultKind = properties[3];\r\n\r\n        if (resultKind !== this._voidResult && properties.length < 5) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        let error: string | undefined;\r\n        let result: any;\r\n\r\n        switch (resultKind) {\r\n            case this._errorResult:\r\n                error = properties[4];\r\n                break;\r\n            case this._nonVoidResult:\r\n                result = properties[4];\r\n                break;\r\n        }\r\n\r\n        const completionMessage: CompletionMessage = {\r\n            error,\r\n            headers,\r\n            invocationId: properties[2],\r\n            result,\r\n            type: MessageType.Completion,\r\n        };\r\n\r\n        return completionMessage;\r\n    }\r\n\r\n    private _writeInvocation(invocationMessage: InvocationMessage): ArrayBuffer {\r\n        let payload: any;\r\n        if (invocationMessage.streamIds) {\r\n            payload = this._encoder.encode([MessageType.Invocation, invocationMessage.headers || {}, invocationMessage.invocationId || null,\r\n            invocationMessage.target, invocationMessage.arguments, invocationMessage.streamIds]);\r\n        } else {\r\n            payload = this._encoder.encode([MessageType.Invocation, invocationMessage.headers || {}, invocationMessage.invocationId || null,\r\n            invocationMessage.target, invocationMessage.arguments]);\r\n        }\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _writeStreamInvocation(streamInvocationMessage: StreamInvocationMessage): ArrayBuffer {\r\n        let payload: any;\r\n        if (streamInvocationMessage.streamIds) {\r\n            payload = this._encoder.encode([MessageType.StreamInvocation, streamInvocationMessage.headers || {}, streamInvocationMessage.invocationId,\r\n            streamInvocationMessage.target, streamInvocationMessage.arguments, streamInvocationMessage.streamIds]);\r\n        } else {\r\n            payload = this._encoder.encode([MessageType.StreamInvocation, streamInvocationMessage.headers || {}, streamInvocationMessage.invocationId,\r\n            streamInvocationMessage.target, streamInvocationMessage.arguments]);\r\n        }\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _writeStreamItem(streamItemMessage: StreamItemMessage): ArrayBuffer {\r\n        const payload = this._encoder.encode([MessageType.StreamItem, streamItemMessage.headers || {}, streamItemMessage.invocationId,\r\n        streamItemMessage.item]);\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _writeCompletion(completionMessage: CompletionMessage): ArrayBuffer {\r\n        const resultKind = completionMessage.error ? this._errorResult : completionMessage.result ? this._nonVoidResult : this._voidResult;\r\n\r\n        let payload: any;\r\n        switch (resultKind) {\r\n            case this._errorResult:\r\n                payload = this._encoder.encode([MessageType.Completion, completionMessage.headers || {}, completionMessage.invocationId, resultKind, completionMessage.error]);\r\n                break;\r\n            case this._voidResult:\r\n                payload = this._encoder.encode([MessageType.Completion, completionMessage.headers || {}, completionMessage.invocationId, resultKind]);\r\n                break;\r\n            case this._nonVoidResult:\r\n                payload = this._encoder.encode([MessageType.Completion, completionMessage.headers || {}, completionMessage.invocationId, resultKind, completionMessage.result]);\r\n                break;\r\n        }\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _writeCancelInvocation(cancelInvocationMessage: CancelInvocationMessage): ArrayBuffer {\r\n        const payload = this._encoder.encode([MessageType.CancelInvocation, cancelInvocationMessage.headers || {}, cancelInvocationMessage.invocationId]);\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _readHeaders(properties: any): MessageHeaders {\r\n        const headers: MessageHeaders = properties[1] as MessageHeaders;\r\n        if (typeof headers !== \"object\") {\r\n            throw new Error(\"Invalid headers.\");\r\n        }\r\n        return headers;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Version token that will be replaced by the prepack command\r\n/** The version of the SignalR Message Pack protocol library. */\r\nexport const VERSION = \"0.0.0-DEV_BUILD\";\r\n\r\nexport { MessagePackHubProtocol } from \"./MessagePackHubProtocol\";\r\n\r\nexport { MessagePackOptions } from \"./MessagePackOptions\";\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This is where we add any polyfills we'll need for the browser. It is the entry module for browser-specific builds.\r\n\r\nexport * from \"./index\";\r\n"], "sourceRoot": ""}