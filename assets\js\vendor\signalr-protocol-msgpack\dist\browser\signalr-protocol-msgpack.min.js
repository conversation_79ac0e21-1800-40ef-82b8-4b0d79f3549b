var e,t;e=self,t=function(e){return(()=>{var t=[,t=>{t.exports=e}],r={};function i(e){var n=r[e];if(void 0!==n)return n.exports;var s=r[e]={exports:{}};return t[e](s,s.exports,i),s.exports}i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return(()=>{i.r(n),i.d(n,{MessagePackHubProtocol:()=>R,VERSION:()=>O});var e=4294967295;function t(e,t,r){var i=Math.floor(r/4294967296),n=r;e.setUint32(t,i),e.setUint32(t+4,n)}function r(e,t){return 4294967296*e.getInt32(t)+e.getUint32(t+4)}var s=("undefined"==typeof process||"never"!==process.env.TEXT_ENCODING)&&"undefined"!=typeof TextEncoder&&"undefined"!=typeof TextDecoder;function o(e){for(var t=e.length,r=0,i=0;i<t;){var n=e.charCodeAt(i++);if(0!=(4294967168&n))if(0==(4294965248&n))r+=2;else{if(n>=55296&&n<=56319&&i<t){var s=e.charCodeAt(i);56320==(64512&s)&&(++i,n=((1023&n)<<10)+(1023&s)+65536)}r+=0==(4294901760&n)?3:4}else r++}return r}var a=s?new TextEncoder:void 0,h=s?"undefined"!=typeof process&&"force"!==process.env.TEXT_ENCODING?200:0:e,c=(null==a?void 0:a.encodeInto)?function(e,t,r){a.encodeInto(e,t.subarray(r))}:function(e,t,r){t.set(a.encode(e),r)};function u(e,t,r){for(var i=t,n=i+r,s=[],o="";i<n;){var a=e[i++];if(0==(128&a))s.push(a);else if(192==(224&a)){var h=63&e[i++];s.push((31&a)<<6|h)}else if(224==(240&a)){h=63&e[i++];var c=63&e[i++];s.push((31&a)<<12|h<<6|c)}else if(240==(248&a)){var u=(7&a)<<18|(h=63&e[i++])<<12|(c=63&e[i++])<<6|63&e[i++];u>65535&&(u-=65536,s.push(u>>>10&1023|55296),u=56320|1023&u),s.push(u)}else s.push(a);s.length>=4096&&(o+=String.fromCharCode.apply(String,s),s.length=0)}return s.length>0&&(o+=String.fromCharCode.apply(String,s)),o}var f,p=s?new TextDecoder:null,l=s?"undefined"!=typeof process&&"force"!==process.env.TEXT_DECODER?200:0:e,d=function(e,t){this.type=e,this.data=t},y=(f=function(e,t){return(f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}f(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),w=function(e){function t(r){var i=e.call(this,r)||this,n=Object.create(t.prototype);return Object.setPrototypeOf(i,n),Object.defineProperty(i,"name",{configurable:!0,enumerable:!1,value:t.name}),i}return y(t,e),t}(Error),g={type:-1,encode:function(e){var r,i,n,s;return e instanceof Date?function(e){var r,i=e.sec,n=e.nsec;if(i>=0&&n>=0&&i<=17179869183){if(0===n&&i<=4294967295){var s=new Uint8Array(4);return(r=new DataView(s.buffer)).setUint32(0,i),s}var o=i/4294967296,a=4294967295&i;return s=new Uint8Array(8),(r=new DataView(s.buffer)).setUint32(0,n<<2|3&o),r.setUint32(4,a),s}return s=new Uint8Array(12),(r=new DataView(s.buffer)).setUint32(0,n),t(r,4,i),s}((r=e.getTime(),i=Math.floor(r/1e3),n=1e6*(r-1e3*i),s=Math.floor(n/1e9),{sec:i+s,nsec:n-1e9*s})):null},decode:function(e){var t=function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);switch(e.byteLength){case 4:return{sec:t.getUint32(0),nsec:0};case 8:var i=t.getUint32(0);return{sec:4294967296*(3&i)+t.getUint32(4),nsec:i>>>2};case 12:return{sec:r(t,4),nsec:t.getUint32(0)};default:throw new w("Unrecognized data size for timestamp (expected 4, 8, or 12): "+e.length)}}(e);return new Date(1e3*t.sec+t.nsec/1e6)}},v=function(){function e(){this.builtInEncoders=[],this.builtInDecoders=[],this.encoders=[],this.decoders=[],this.register(g)}return e.prototype.register=function(e){var t=e.type,r=e.encode,i=e.decode;if(t>=0)this.encoders[t]=r,this.decoders[t]=i;else{var n=1+t;this.builtInEncoders[n]=r,this.builtInDecoders[n]=i}},e.prototype.tryToEncode=function(e,t){for(var r=0;r<this.builtInEncoders.length;r++)if(null!=(i=this.builtInEncoders[r])&&null!=(n=i(e,t)))return new d(-1-r,n);for(r=0;r<this.encoders.length;r++){var i,n;if(null!=(i=this.encoders[r])&&null!=(n=i(e,t)))return new d(r,n)}return e instanceof d?e:null},e.prototype.decode=function(e,t,r){var i=t<0?this.builtInDecoders[-1-t]:this.decoders[t];return i?i(e,t,r):new d(t,e)},e.defaultCodec=new e,e}();function m(e){return e instanceof Uint8Array?e:ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e instanceof ArrayBuffer?new Uint8Array(e):Uint8Array.from(e)}var b=function(){function e(e,t,r,i,n,s,o,a){void 0===e&&(e=v.defaultCodec),void 0===t&&(t=void 0),void 0===r&&(r=100),void 0===i&&(i=2048),void 0===n&&(n=!1),void 0===s&&(s=!1),void 0===o&&(o=!1),void 0===a&&(a=!1),this.extensionCodec=e,this.context=t,this.maxDepth=r,this.initialBufferSize=i,this.sortKeys=n,this.forceFloat32=s,this.ignoreUndefined=o,this.forceIntegerToFloat=a,this.pos=0,this.view=new DataView(new ArrayBuffer(this.initialBufferSize)),this.bytes=new Uint8Array(this.view.buffer)}return e.prototype.getUint8Array=function(){return this.bytes.subarray(0,this.pos)},e.prototype.reinitializeState=function(){this.pos=0},e.prototype.encode=function(e){return this.reinitializeState(),this.doEncode(e,1),this.getUint8Array()},e.prototype.doEncode=function(e,t){if(t>this.maxDepth)throw new Error("Too deep objects in depth "+t);null==e?this.encodeNil():"boolean"==typeof e?this.encodeBoolean(e):"number"==typeof e?this.encodeNumber(e):"string"==typeof e?this.encodeString(e):this.encodeObject(e,t)},e.prototype.ensureBufferSizeToWrite=function(e){var t=this.pos+e;this.view.byteLength<t&&this.resizeBuffer(2*t)},e.prototype.resizeBuffer=function(e){var t=new ArrayBuffer(e),r=new Uint8Array(t),i=new DataView(t);r.set(this.bytes),this.view=i,this.bytes=r},e.prototype.encodeNil=function(){this.writeU8(192)},e.prototype.encodeBoolean=function(e){!1===e?this.writeU8(194):this.writeU8(195)},e.prototype.encodeNumber=function(e){Number.isSafeInteger(e)&&!this.forceIntegerToFloat?e>=0?e<128?this.writeU8(e):e<256?(this.writeU8(204),this.writeU8(e)):e<65536?(this.writeU8(205),this.writeU16(e)):e<4294967296?(this.writeU8(206),this.writeU32(e)):(this.writeU8(207),this.writeU64(e)):e>=-32?this.writeU8(224|e+32):e>=-128?(this.writeU8(208),this.writeI8(e)):e>=-32768?(this.writeU8(209),this.writeI16(e)):e>=-2147483648?(this.writeU8(210),this.writeI32(e)):(this.writeU8(211),this.writeI64(e)):this.forceFloat32?(this.writeU8(202),this.writeF32(e)):(this.writeU8(203),this.writeF64(e))},e.prototype.writeStringHeader=function(e){if(e<32)this.writeU8(160+e);else if(e<256)this.writeU8(217),this.writeU8(e);else if(e<65536)this.writeU8(218),this.writeU16(e);else{if(!(e<4294967296))throw new Error("Too long string: "+e+" bytes in UTF-8");this.writeU8(219),this.writeU32(e)}},e.prototype.encodeString=function(e){if(e.length>h){var t=o(e);this.ensureBufferSizeToWrite(5+t),this.writeStringHeader(t),c(e,this.bytes,this.pos),this.pos+=t}else t=o(e),this.ensureBufferSizeToWrite(5+t),this.writeStringHeader(t),function(e,t,r){for(var i=e.length,n=r,s=0;s<i;){var o=e.charCodeAt(s++);if(0!=(4294967168&o)){if(0==(4294965248&o))t[n++]=o>>6&31|192;else{if(o>=55296&&o<=56319&&s<i){var a=e.charCodeAt(s);56320==(64512&a)&&(++s,o=((1023&o)<<10)+(1023&a)+65536)}0==(4294901760&o)?(t[n++]=o>>12&15|224,t[n++]=o>>6&63|128):(t[n++]=o>>18&7|240,t[n++]=o>>12&63|128,t[n++]=o>>6&63|128)}t[n++]=63&o|128}else t[n++]=o}}(e,this.bytes,this.pos),this.pos+=t},e.prototype.encodeObject=function(e,t){var r=this.extensionCodec.tryToEncode(e,this.context);if(null!=r)this.encodeExtension(r);else if(Array.isArray(e))this.encodeArray(e,t);else if(ArrayBuffer.isView(e))this.encodeBinary(e);else{if("object"!=typeof e)throw new Error("Unrecognized object: "+Object.prototype.toString.apply(e));this.encodeMap(e,t)}},e.prototype.encodeBinary=function(e){var t=e.byteLength;if(t<256)this.writeU8(196),this.writeU8(t);else if(t<65536)this.writeU8(197),this.writeU16(t);else{if(!(t<4294967296))throw new Error("Too large binary: "+t);this.writeU8(198),this.writeU32(t)}var r=m(e);this.writeU8a(r)},e.prototype.encodeArray=function(e,t){var r=e.length;if(r<16)this.writeU8(144+r);else if(r<65536)this.writeU8(220),this.writeU16(r);else{if(!(r<4294967296))throw new Error("Too large array: "+r);this.writeU8(221),this.writeU32(r)}for(var i=0,n=e;i<n.length;i++){var s=n[i];this.doEncode(s,t+1)}},e.prototype.countWithoutUndefined=function(e,t){for(var r=0,i=0,n=t;i<n.length;i++)void 0!==e[n[i]]&&r++;return r},e.prototype.encodeMap=function(e,t){var r=Object.keys(e);this.sortKeys&&r.sort();var i=this.ignoreUndefined?this.countWithoutUndefined(e,r):r.length;if(i<16)this.writeU8(128+i);else if(i<65536)this.writeU8(222),this.writeU16(i);else{if(!(i<4294967296))throw new Error("Too large map object: "+i);this.writeU8(223),this.writeU32(i)}for(var n=0,s=r;n<s.length;n++){var o=s[n],a=e[o];this.ignoreUndefined&&void 0===a||(this.encodeString(o),this.doEncode(a,t+1))}},e.prototype.encodeExtension=function(e){var t=e.data.length;if(1===t)this.writeU8(212);else if(2===t)this.writeU8(213);else if(4===t)this.writeU8(214);else if(8===t)this.writeU8(215);else if(16===t)this.writeU8(216);else if(t<256)this.writeU8(199),this.writeU8(t);else if(t<65536)this.writeU8(200),this.writeU16(t);else{if(!(t<4294967296))throw new Error("Too large extension object: "+t);this.writeU8(201),this.writeU32(t)}this.writeI8(e.type),this.writeU8a(e.data)},e.prototype.writeU8=function(e){this.ensureBufferSizeToWrite(1),this.view.setUint8(this.pos,e),this.pos++},e.prototype.writeU8a=function(e){var t=e.length;this.ensureBufferSizeToWrite(t),this.bytes.set(e,this.pos),this.pos+=t},e.prototype.writeI8=function(e){this.ensureBufferSizeToWrite(1),this.view.setInt8(this.pos,e),this.pos++},e.prototype.writeU16=function(e){this.ensureBufferSizeToWrite(2),this.view.setUint16(this.pos,e),this.pos+=2},e.prototype.writeI16=function(e){this.ensureBufferSizeToWrite(2),this.view.setInt16(this.pos,e),this.pos+=2},e.prototype.writeU32=function(e){this.ensureBufferSizeToWrite(4),this.view.setUint32(this.pos,e),this.pos+=4},e.prototype.writeI32=function(e){this.ensureBufferSizeToWrite(4),this.view.setInt32(this.pos,e),this.pos+=4},e.prototype.writeF32=function(e){this.ensureBufferSizeToWrite(4),this.view.setFloat32(this.pos,e),this.pos+=4},e.prototype.writeF64=function(e){this.ensureBufferSizeToWrite(8),this.view.setFloat64(this.pos,e),this.pos+=8},e.prototype.writeU64=function(e){this.ensureBufferSizeToWrite(8),function(e,t,r){var i=r/4294967296,n=r;e.setUint32(t,i),e.setUint32(t+4,n)}(this.view,this.pos,e),this.pos+=8},e.prototype.writeI64=function(e){this.ensureBufferSizeToWrite(8),t(this.view,this.pos,e),this.pos+=8},e}();function U(e){return(e<0?"-":"")+"0x"+Math.abs(e).toString(16).padStart(2,"0")}var I=function(){function e(e,t){void 0===e&&(e=16),void 0===t&&(t=16),this.maxKeyLength=e,this.maxLengthPerKey=t,this.hit=0,this.miss=0,this.caches=[];for(var r=0;r<this.maxKeyLength;r++)this.caches.push([])}return e.prototype.canBeCached=function(e){return e>0&&e<=this.maxKeyLength},e.prototype.find=function(e,t,r){e:for(var i=0,n=this.caches[r-1];i<n.length;i++){for(var s=n[i],o=s.bytes,a=0;a<r;a++)if(o[a]!==e[t+a])continue e;return s.str}return null},e.prototype.store=function(e,t){var r=this.caches[e.length-1],i={bytes:e,str:t};r.length>=this.maxLengthPerKey?r[Math.random()*r.length|0]=i:r.push(i)},e.prototype.decode=function(e,t,r){var i=this.find(e,t,r);if(null!=i)return this.hit++,i;this.miss++;var n=u(e,t,r),s=Uint8Array.prototype.slice.call(e,t,t+r);return this.store(s,n),n},e}(),x=function(e,t,r,i){return new(r||(r=Promise))((function(n,s){function o(e){try{h(i.next(e))}catch(e){s(e)}}function a(e){try{h(i.throw(e))}catch(e){s(e)}}function h(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}h((i=i.apply(e,t||[])).next())}))},S=function(e,t){var r,i,n,s,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,i&&(n=2&s[0]?i.return:s[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,s[1])).done)return n;switch(i=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,i=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!((n=(n=o.trys).length>0&&n[n.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){o.label=s[1];break}if(6===s[0]&&o.label<n[1]){o.label=n[1],n=s;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(s);break}n[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],i=0}finally{r=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}},E=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e="function"==typeof __values?__values(e):e[Symbol.iterator](),t={},i("next"),i("throw"),i("return"),t[Symbol.asyncIterator]=function(){return this},t);function i(r){t[r]=e[r]&&function(t){return new Promise((function(i,n){!function(e,t,r,i){Promise.resolve(i).then((function(t){e({value:t,done:r})}),t)}(i,n,(t=e[r](t)).done,t.value)}))}}},_=function(e){return this instanceof _?(this.v=e,this):new _(e)},M=function(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i,n=r.apply(e,t||[]),s=[];return i={},o("next"),o("throw"),o("return"),i[Symbol.asyncIterator]=function(){return this},i;function o(e){n[e]&&(i[e]=function(t){return new Promise((function(r,i){s.push([e,t,r,i])>1||a(e,t)}))})}function a(e,t){try{(r=n[e](t)).value instanceof _?Promise.resolve(r.value.v).then(h,c):u(s[0][2],r)}catch(e){u(s[0][3],e)}var r}function h(e){a("next",e)}function c(e){a("throw",e)}function u(e,t){e(t),s.shift(),s.length&&a(s[0][0],s[0][1])}},T=new DataView(new ArrayBuffer(0)),B=new Uint8Array(T.buffer),A=function(){try{T.getInt8(0)}catch(e){return e.constructor}throw new Error("never reached")}(),L=new A("Insufficient data"),C=new I,k=function(){function t(t,r,i,n,s,o,a,h){void 0===t&&(t=v.defaultCodec),void 0===r&&(r=void 0),void 0===i&&(i=e),void 0===n&&(n=e),void 0===s&&(s=e),void 0===o&&(o=e),void 0===a&&(a=e),void 0===h&&(h=C),this.extensionCodec=t,this.context=r,this.maxStrLength=i,this.maxBinLength=n,this.maxArrayLength=s,this.maxMapLength=o,this.maxExtLength=a,this.keyDecoder=h,this.totalPos=0,this.pos=0,this.view=T,this.bytes=B,this.headByte=-1,this.stack=[]}return t.prototype.reinitializeState=function(){this.totalPos=0,this.headByte=-1,this.stack.length=0},t.prototype.setBuffer=function(e){this.bytes=m(e),this.view=function(e){if(e instanceof ArrayBuffer)return new DataView(e);var t=m(e);return new DataView(t.buffer,t.byteOffset,t.byteLength)}(this.bytes),this.pos=0},t.prototype.appendBuffer=function(e){if(-1!==this.headByte||this.hasRemaining(1)){var t=this.bytes.subarray(this.pos),r=m(e),i=new Uint8Array(t.length+r.length);i.set(t),i.set(r,t.length),this.setBuffer(i)}else this.setBuffer(e)},t.prototype.hasRemaining=function(e){return this.view.byteLength-this.pos>=e},t.prototype.createExtraByteError=function(e){var t=this.view,r=this.pos;return new RangeError("Extra "+(t.byteLength-r)+" of "+t.byteLength+" byte(s) found at buffer["+e+"]")},t.prototype.decode=function(e){this.reinitializeState(),this.setBuffer(e);var t=this.doDecodeSync();if(this.hasRemaining(1))throw this.createExtraByteError(this.pos);return t},t.prototype.decodeMulti=function(e){return S(this,(function(t){switch(t.label){case 0:this.reinitializeState(),this.setBuffer(e),t.label=1;case 1:return this.hasRemaining(1)?[4,this.doDecodeSync()]:[3,3];case 2:return t.sent(),[3,1];case 3:return[2]}}))},t.prototype.decodeAsync=function(e){var t,r,i,n;return x(this,void 0,void 0,(function(){var s,o,a,h,c,u,f,p;return S(this,(function(l){switch(l.label){case 0:s=!1,l.label=1;case 1:l.trys.push([1,6,7,12]),t=E(e),l.label=2;case 2:return[4,t.next()];case 3:if((r=l.sent()).done)return[3,5];if(a=r.value,s)throw this.createExtraByteError(this.totalPos);this.appendBuffer(a);try{o=this.doDecodeSync(),s=!0}catch(e){if(!(e instanceof A))throw e}this.totalPos+=this.pos,l.label=4;case 4:return[3,2];case 5:return[3,12];case 6:return h=l.sent(),i={error:h},[3,12];case 7:return l.trys.push([7,,10,11]),r&&!r.done&&(n=t.return)?[4,n.call(t)]:[3,9];case 8:l.sent(),l.label=9;case 9:return[3,11];case 10:if(i)throw i.error;return[7];case 11:return[7];case 12:if(s){if(this.hasRemaining(1))throw this.createExtraByteError(this.totalPos);return[2,o]}throw u=(c=this).headByte,f=c.pos,p=c.totalPos,new RangeError("Insufficient data in parsing "+U(u)+" at "+p+" ("+f+" in the current buffer)")}}))}))},t.prototype.decodeArrayStream=function(e){return this.decodeMultiAsync(e,!0)},t.prototype.decodeStream=function(e){return this.decodeMultiAsync(e,!1)},t.prototype.decodeMultiAsync=function(e,t){return M(this,arguments,(function(){var r,i,n,s,o,a,h,c,u;return S(this,(function(f){switch(f.label){case 0:r=t,i=-1,f.label=1;case 1:f.trys.push([1,13,14,19]),n=E(e),f.label=2;case 2:return[4,_(n.next())];case 3:if((s=f.sent()).done)return[3,12];if(o=s.value,t&&0===i)throw this.createExtraByteError(this.totalPos);this.appendBuffer(o),r&&(i=this.readArraySize(),r=!1,this.complete()),f.label=4;case 4:f.trys.push([4,9,,10]),f.label=5;case 5:return[4,_(this.doDecodeSync())];case 6:return[4,f.sent()];case 7:return f.sent(),0==--i?[3,8]:[3,5];case 8:return[3,10];case 9:if(!((a=f.sent())instanceof A))throw a;return[3,10];case 10:this.totalPos+=this.pos,f.label=11;case 11:return[3,2];case 12:return[3,19];case 13:return h=f.sent(),c={error:h},[3,19];case 14:return f.trys.push([14,,17,18]),s&&!s.done&&(u=n.return)?[4,_(u.call(n))]:[3,16];case 15:f.sent(),f.label=16;case 16:return[3,18];case 17:if(c)throw c.error;return[7];case 18:return[7];case 19:return[2]}}))}))},t.prototype.doDecodeSync=function(){e:for(;;){var e=this.readHeadByte(),t=void 0;if(e>=224)t=e-256;else if(e<192)if(e<128)t=e;else if(e<144){if(0!=(i=e-128)){this.pushMapState(i),this.complete();continue e}t={}}else if(e<160){if(0!=(i=e-144)){this.pushArrayState(i),this.complete();continue e}t=[]}else{var r=e-160;t=this.decodeUtf8String(r,0)}else if(192===e)t=null;else if(194===e)t=!1;else if(195===e)t=!0;else if(202===e)t=this.readF32();else if(203===e)t=this.readF64();else if(204===e)t=this.readU8();else if(205===e)t=this.readU16();else if(206===e)t=this.readU32();else if(207===e)t=this.readU64();else if(208===e)t=this.readI8();else if(209===e)t=this.readI16();else if(210===e)t=this.readI32();else if(211===e)t=this.readI64();else if(217===e)r=this.lookU8(),t=this.decodeUtf8String(r,1);else if(218===e)r=this.lookU16(),t=this.decodeUtf8String(r,2);else if(219===e)r=this.lookU32(),t=this.decodeUtf8String(r,4);else if(220===e){if(0!==(i=this.readU16())){this.pushArrayState(i),this.complete();continue e}t=[]}else if(221===e){if(0!==(i=this.readU32())){this.pushArrayState(i),this.complete();continue e}t=[]}else if(222===e){if(0!==(i=this.readU16())){this.pushMapState(i),this.complete();continue e}t={}}else if(223===e){if(0!==(i=this.readU32())){this.pushMapState(i),this.complete();continue e}t={}}else if(196===e){var i=this.lookU8();t=this.decodeBinary(i,1)}else if(197===e)i=this.lookU16(),t=this.decodeBinary(i,2);else if(198===e)i=this.lookU32(),t=this.decodeBinary(i,4);else if(212===e)t=this.decodeExtension(1,0);else if(213===e)t=this.decodeExtension(2,0);else if(214===e)t=this.decodeExtension(4,0);else if(215===e)t=this.decodeExtension(8,0);else if(216===e)t=this.decodeExtension(16,0);else if(199===e)i=this.lookU8(),t=this.decodeExtension(i,1);else if(200===e)i=this.lookU16(),t=this.decodeExtension(i,2);else{if(201!==e)throw new w("Unrecognized type byte: "+U(e));i=this.lookU32(),t=this.decodeExtension(i,4)}this.complete();for(var n=this.stack;n.length>0;){var s=n[n.length-1];if(0===s.type){if(s.array[s.position]=t,s.position++,s.position!==s.size)continue e;n.pop(),t=s.array}else{if(1===s.type){if(o=void 0,"string"!=(o=typeof t)&&"number"!==o)throw new w("The type of key must be string or number but "+typeof t);if("__proto__"===t)throw new w("The key __proto__ is not allowed");s.key=t,s.type=2;continue e}if(s.map[s.key]=t,s.readCount++,s.readCount!==s.size){s.key=null,s.type=1;continue e}n.pop(),t=s.map}}return t}var o},t.prototype.readHeadByte=function(){return-1===this.headByte&&(this.headByte=this.readU8()),this.headByte},t.prototype.complete=function(){this.headByte=-1},t.prototype.readArraySize=function(){var e=this.readHeadByte();switch(e){case 220:return this.readU16();case 221:return this.readU32();default:if(e<160)return e-144;throw new w("Unrecognized array type byte: "+U(e))}},t.prototype.pushMapState=function(e){if(e>this.maxMapLength)throw new w("Max length exceeded: map length ("+e+") > maxMapLengthLength ("+this.maxMapLength+")");this.stack.push({type:1,size:e,key:null,readCount:0,map:{}})},t.prototype.pushArrayState=function(e){if(e>this.maxArrayLength)throw new w("Max length exceeded: array length ("+e+") > maxArrayLength ("+this.maxArrayLength+")");this.stack.push({type:0,size:e,array:new Array(e),position:0})},t.prototype.decodeUtf8String=function(e,t){var r;if(e>this.maxStrLength)throw new w("Max length exceeded: UTF-8 byte length ("+e+") > maxStrLength ("+this.maxStrLength+")");if(this.bytes.byteLength<this.pos+t+e)throw L;var i,n=this.pos+t;return i=this.stateIsMapKey()&&(null===(r=this.keyDecoder)||void 0===r?void 0:r.canBeCached(e))?this.keyDecoder.decode(this.bytes,n,e):e>l?function(e,t,r){var i=e.subarray(t,t+r);return p.decode(i)}(this.bytes,n,e):u(this.bytes,n,e),this.pos+=t+e,i},t.prototype.stateIsMapKey=function(){return this.stack.length>0&&1===this.stack[this.stack.length-1].type},t.prototype.decodeBinary=function(e,t){if(e>this.maxBinLength)throw new w("Max length exceeded: bin length ("+e+") > maxBinLength ("+this.maxBinLength+")");if(!this.hasRemaining(e+t))throw L;var r=this.pos+t,i=this.bytes.subarray(r,r+e);return this.pos+=t+e,i},t.prototype.decodeExtension=function(e,t){if(e>this.maxExtLength)throw new w("Max length exceeded: ext length ("+e+") > maxExtLength ("+this.maxExtLength+")");var r=this.view.getInt8(this.pos+t),i=this.decodeBinary(e,t+1);return this.extensionCodec.decode(i,r,this.context)},t.prototype.lookU8=function(){return this.view.getUint8(this.pos)},t.prototype.lookU16=function(){return this.view.getUint16(this.pos)},t.prototype.lookU32=function(){return this.view.getUint32(this.pos)},t.prototype.readU8=function(){var e=this.view.getUint8(this.pos);return this.pos++,e},t.prototype.readI8=function(){var e=this.view.getInt8(this.pos);return this.pos++,e},t.prototype.readU16=function(){var e=this.view.getUint16(this.pos);return this.pos+=2,e},t.prototype.readI16=function(){var e=this.view.getInt16(this.pos);return this.pos+=2,e},t.prototype.readU32=function(){var e=this.view.getUint32(this.pos);return this.pos+=4,e},t.prototype.readI32=function(){var e=this.view.getInt32(this.pos);return this.pos+=4,e},t.prototype.readU64=function(){var e,t,r=(e=this.view,t=this.pos,4294967296*e.getUint32(t)+e.getUint32(t+4));return this.pos+=8,r},t.prototype.readI64=function(){var e=r(this.view,this.pos);return this.pos+=8,e},t.prototype.readF32=function(){var e=this.view.getFloat32(this.pos);return this.pos+=4,e},t.prototype.readF64=function(){var e=this.view.getFloat64(this.pos);return this.pos+=8,e},t}(),z=i(1);class D{static write(e){let t=e.byteLength||e.length;const r=[];do{let e=127&t;t>>=7,t>0&&(e|=128),r.push(e)}while(t>0);t=e.byteLength||e.length;const i=new Uint8Array(r.length+t);return i.set(r,0),i.set(e,r.length),i.buffer}static parse(e){const t=[],r=new Uint8Array(e),i=[0,7,14,21,28];for(let n=0;n<e.byteLength;){let s,o=0,a=0;do{s=r[n+o],a|=(127&s)<<i[o],o++}while(o<Math.min(5,e.byteLength-n)&&0!=(128&s));if(0!=(128&s)&&o<5)throw new Error("Cannot read message size.");if(5===o&&s>7)throw new Error("Messages bigger than 2GB are not supported.");if(!(r.byteLength>=n+o+a))throw new Error("Incomplete message.");t.push(r.slice?r.slice(n+o,n+o+a):r.subarray(n+o,n+o+a)),n=n+o+a}return t}}const P=new Uint8Array([145,z.MessageType.Ping]);class R{constructor(e){this.name="messagepack",this.version=1,this.transferFormat=z.TransferFormat.Binary,this._errorResult=1,this._voidResult=2,this._nonVoidResult=3,e=e||{},this._encoder=new b(e.extensionCodec,e.context,e.maxDepth,e.initialBufferSize,e.sortKeys,e.forceFloat32,e.ignoreUndefined,e.forceIntegerToFloat),this._decoder=new k(e.extensionCodec,e.context,e.maxStrLength,e.maxBinLength,e.maxArrayLength,e.maxMapLength,e.maxExtLength)}parseMessages(e,t){if(!(r=e)||"undefined"==typeof ArrayBuffer||!(r instanceof ArrayBuffer||r.constructor&&"ArrayBuffer"===r.constructor.name))throw new Error("Invalid input for MessagePack hub protocol. Expected an ArrayBuffer.");var r;null===t&&(t=z.NullLogger.instance);const i=D.parse(e),n=[];for(const e of i){const r=this._parseMessage(e,t);r&&n.push(r)}return n}writeMessage(e){switch(e.type){case z.MessageType.Invocation:return this._writeInvocation(e);case z.MessageType.StreamInvocation:return this._writeStreamInvocation(e);case z.MessageType.StreamItem:return this._writeStreamItem(e);case z.MessageType.Completion:return this._writeCompletion(e);case z.MessageType.Ping:return D.write(P);case z.MessageType.CancelInvocation:return this._writeCancelInvocation(e);default:throw new Error("Invalid message type.")}}_parseMessage(e,t){if(0===e.length)throw new Error("Invalid payload.");const r=this._decoder.decode(e);if(0===r.length||!(r instanceof Array))throw new Error("Invalid payload.");const i=r[0];switch(i){case z.MessageType.Invocation:return this._createInvocationMessage(this._readHeaders(r),r);case z.MessageType.StreamItem:return this._createStreamItemMessage(this._readHeaders(r),r);case z.MessageType.Completion:return this._createCompletionMessage(this._readHeaders(r),r);case z.MessageType.Ping:return this._createPingMessage(r);case z.MessageType.Close:return this._createCloseMessage(r);default:return t.log(z.LogLevel.Information,"Unknown message type '"+i+"' ignored."),null}}_createCloseMessage(e){if(e.length<2)throw new Error("Invalid payload for Close message.");return{allowReconnect:e.length>=3?e[2]:void 0,error:e[1],type:z.MessageType.Close}}_createPingMessage(e){if(e.length<1)throw new Error("Invalid payload for Ping message.");return{type:z.MessageType.Ping}}_createInvocationMessage(e,t){if(t.length<5)throw new Error("Invalid payload for Invocation message.");const r=t[2];return r?{arguments:t[4],headers:e,invocationId:r,streamIds:[],target:t[3],type:z.MessageType.Invocation}:{arguments:t[4],headers:e,streamIds:[],target:t[3],type:z.MessageType.Invocation}}_createStreamItemMessage(e,t){if(t.length<4)throw new Error("Invalid payload for StreamItem message.");return{headers:e,invocationId:t[2],item:t[3],type:z.MessageType.StreamItem}}_createCompletionMessage(e,t){if(t.length<4)throw new Error("Invalid payload for Completion message.");const r=t[3];if(r!==this._voidResult&&t.length<5)throw new Error("Invalid payload for Completion message.");let i,n;switch(r){case this._errorResult:i=t[4];break;case this._nonVoidResult:n=t[4]}return{error:i,headers:e,invocationId:t[2],result:n,type:z.MessageType.Completion}}_writeInvocation(e){let t;return t=e.streamIds?this._encoder.encode([z.MessageType.Invocation,e.headers||{},e.invocationId||null,e.target,e.arguments,e.streamIds]):this._encoder.encode([z.MessageType.Invocation,e.headers||{},e.invocationId||null,e.target,e.arguments]),D.write(t.slice())}_writeStreamInvocation(e){let t;return t=e.streamIds?this._encoder.encode([z.MessageType.StreamInvocation,e.headers||{},e.invocationId,e.target,e.arguments,e.streamIds]):this._encoder.encode([z.MessageType.StreamInvocation,e.headers||{},e.invocationId,e.target,e.arguments]),D.write(t.slice())}_writeStreamItem(e){const t=this._encoder.encode([z.MessageType.StreamItem,e.headers||{},e.invocationId,e.item]);return D.write(t.slice())}_writeCompletion(e){const t=e.error?this._errorResult:e.result?this._nonVoidResult:this._voidResult;let r;switch(t){case this._errorResult:r=this._encoder.encode([z.MessageType.Completion,e.headers||{},e.invocationId,t,e.error]);break;case this._voidResult:r=this._encoder.encode([z.MessageType.Completion,e.headers||{},e.invocationId,t]);break;case this._nonVoidResult:r=this._encoder.encode([z.MessageType.Completion,e.headers||{},e.invocationId,t,e.result])}return D.write(r.slice())}_writeCancelInvocation(e){const t=this._encoder.encode([z.MessageType.CancelInvocation,e.headers||{},e.invocationId]);return D.write(t.slice())}_readHeaders(e){const t=e[1];if("object"!=typeof t)throw new Error("Invalid headers.");return t}}const O="6.0.6"})(),n})()},"object"==typeof exports&&"object"==typeof module?module.exports=t(require("signalR")):"function"==typeof define&&define.amd?define(["signalR"],t):"object"==typeof exports?exports.msgpack=t(require("signalR")):(e.signalR=e.signalR||{},e.signalR.protocols=e.signalR.protocols||{},e.signalR.protocols.msgpack=t(e.signalR));
//# sourceMappingURL=signalr-protocol-msgpack.min.js.map