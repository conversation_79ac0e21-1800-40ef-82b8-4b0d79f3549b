{"version": 3, "sources": ["webpack://signalR.protocols.msgpack/webpack/universalModuleDefinition", "webpack://signalR.protocols.msgpack/external \"signalR\"", "webpack://signalR.protocols.msgpack/webpack/bootstrap", "webpack://signalR.protocols.msgpack/webpack/runtime/define property getters", "webpack://signalR.protocols.msgpack/webpack/runtime/hasOwnProperty shorthand", "webpack://signalR.protocols.msgpack/webpack/runtime/make namespace object", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/utils/int.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/utils/utf8.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/DecodeError.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/ExtData.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/timestamp.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/ExtensionCodec.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/utils/typedArrays.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/Encoder.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/utils/prettyByte.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/CachedKeyDecoder.mjs", "webpack://signalR.protocols.msgpack/node_modules/@msgpack/msgpack/dist.es5+esm/Decoder.mjs", "webpack://signalR.protocols.msgpack/src/BinaryMessageFormat.ts", "webpack://signalR.protocols.msgpack/src/MessagePackHubProtocol.ts", "webpack://signalR.protocols.msgpack/src/Utils.ts", "webpack://signalR.protocols.msgpack/src/index.ts"], "names": ["root", "factory", "self", "__WEBPACK_EXTERNAL_MODULE__1__", "module", "exports", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "d", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "r", "Symbol", "toStringTag", "value", "UINT32_MAX", "setInt64", "view", "offset", "high", "Math", "floor", "low", "setUint32", "getInt64", "getInt32", "getUint32", "TEXT_ENCODING_AVAILABLE", "process", "env", "TextEncoder", "TextDecoder", "utf8Count", "str", "str<PERSON><PERSON><PERSON>", "length", "byteLength", "pos", "charCodeAt", "extra", "sharedTextEncoder", "TEXT_ENCODER_THRESHOLD", "utf8EncodeTE", "encodeInto", "output", "outputOffset", "subarray", "set", "encode", "utf8DecodeJs", "bytes", "inputOffset", "end", "units", "result", "byte1", "push", "byte2", "byte3", "unit", "String", "fromCharCode", "apply", "extendStatics", "sharedTextDecoder", "TEXT_DECODER_THRESHOLD", "ExtData", "type", "data", "this", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "TypeError", "__", "constructor", "create", "DecodeError", "_super", "message", "_this", "proto", "configurable", "name", "Error", "timestampExtension", "object", "msec", "sec", "nsec", "nsecInSec", "Date", "_a", "rv", "Uint8Array", "DataView", "buffer", "secHigh", "secLow", "encodeTimeSpecToTimestamp", "getTime", "decode", "timeSpec", "byteOffset", "nsec30AndSecHigh2", "decodeTimestampToTimeSpec", "ExtensionCodec", "builtInEncoders", "builtInDecoders", "encoders", "decoders", "register", "index", "tryToEncode", "context", "i", "encodeExt", "decodeExt", "defaultCodec", "ensureUint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "from", "Encoder", "extensionCodec", "max<PERSON><PERSON><PERSON>", "initialBufferSize", "sortKeys", "forceFloat32", "ignoreUndefined", "forceIntegerToFloat", "getUint8Array", "reinitializeState", "doEncode", "depth", "encodeNil", "encodeBoolean", "encodeNumber", "encodeString", "encodeObject", "ensureBufferSizeToWrite", "sizeToWrite", "requiredSize", "resize<PERSON>uffer", "newSize", "new<PERSON>uffer", "newBytes", "newView", "writeU8", "Number", "isSafeInteger", "writeU16", "writeU32", "writeU64", "writeI8", "writeI16", "writeI32", "writeI64", "writeF32", "writeF64", "writeStringHeader", "utf8EncodeJs", "ext", "encodeExtension", "isArray", "encodeArray", "encodeBinary", "toString", "encodeMap", "size", "writeU8a", "_i", "object_1", "item", "countWithoutUndefined", "keys", "count", "keys_1", "sort", "keys_2", "setUint8", "values", "setInt8", "setUint16", "setInt16", "setInt32", "setFloat32", "setFloat64", "setUint64", "prettyByte", "byte", "abs", "padStart", "Cached<PERSON>eyDecoder", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hit", "miss", "caches", "canBeCached", "find", "FIND_CHUNK", "records_1", "record", "recordBytes", "j", "store", "records", "random", "cachedValue", "slicedCopyOfBytes", "slice", "__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "step", "next", "e", "rejected", "done", "then", "__generator", "body", "f", "y", "t", "g", "_", "label", "sent", "trys", "ops", "verb", "throw", "return", "iterator", "n", "v", "op", "pop", "__asyncValues", "asyncIterator", "m", "__values", "settle", "__await", "__asyncGenerator", "q", "a", "resume", "fulfill", "shift", "EMPTY_VIEW", "EMPTY_BYTES", "DataViewIndexOutOfBoundsError", "getInt8", "MORE_DATA", "sharedCachedKeyDecoder", "Decoder", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxExt<PERSON><PERSON><PERSON>", "keyDecoder", "totalPos", "headByte", "stack", "<PERSON><PERSON><PERSON><PERSON>", "bufferView", "createDataView", "append<PERSON><PERSON>er", "hasRemaining", "remainingData", "newData", "createExtraByteError", "posToShow", "RangeError", "doDecodeSync", "decodeMulti", "decodeAsync", "stream", "stream_1", "stream_1_1", "e_1", "decoded", "e_1_1", "_b", "_c", "error", "decodeArrayStream", "decodeMultiAsync", "decodeStream", "arguments", "isArrayHeaderRequired", "arrayItemsLeft", "stream_2", "stream_2_1", "e_2", "e_3_1", "e_3", "readArraySize", "complete", "DECODE", "readHeadByte", "pushMapState", "pushArrayState", "decodeUtf8String", "readF32", "readF64", "readU8", "readU16", "readU32", "readU64", "readI8", "readI16", "readI32", "readI64", "lookU8", "lookU16", "lookU32", "decodeBinary", "decodeExtension", "state", "array", "position", "keyType", "map", "readCount", "headerOffset", "stateIsMapKey", "stringBytes", "utf8DecodeTD", "headOffset", "extType", "getUint8", "getUint16", "getInt16", "getFloat32", "getFloat64", "BinaryMessageFormat", "[object Object]", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sizePart", "input", "uint8Array", "numBitsToShift", "byteRead", "numBytes", "min", "SERIALIZED_PING_MESSAGE", "external_signalR_", "MessageType", "<PERSON>", "MessagePackHubProtocol", "messagePackOptions", "version", "transferFormat", "TransferFormat", "Binary", "_errorResult", "_voidResult", "_nonVoidResult", "_encoder", "_decoder", "logger", "val", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instance", "messages", "parse", "hubMessages", "parsedMessage", "_parseMessage", "Invocation", "_writeInvocation", "StreamInvocation", "_writeStreamInvocation", "StreamItem", "_writeStreamItem", "Completion", "_writeCompletion", "write", "CancelInvocation", "_writeCancelInvocation", "properties", "messageType", "_createInvocationMessage", "_readHeaders", "_createStreamItemMessage", "_createCompletionMessage", "_createPingMessage", "Close", "_createCloseMessage", "log", "LogLevel", "Information", "allowReconnect", "headers", "invocationId", "streamIds", "target", "resultKind", "invocationMessage", "payload", "streamInvocationMessage", "streamItemMessage", "completionMessage", "cancelInvocationMessage", "VERSION", "require", "define", "amd"], "mappings": "AAAA,IAAAA,EAAAC,EAAAD,EASCE,KATDD,EASC,SAAAE,GACD,wBCVAC,EAAAC,QAAAF,ICCAG,EAAA,GAGA,SAAAC,EAAAC,GAEA,IAAAC,EAAAH,EAAAE,GACA,QAAAE,IAAAD,EACA,OAAAA,EAAAJ,QAGA,IAAAD,EAAAE,EAAAE,GAAA,CAGAH,QAAA,IAOA,OAHAM,EAAAH,GAAAJ,EAAAA,EAAAC,QAAAE,GAGAH,EAAAC,QCpBAE,EAAAK,EAAA,CAAAP,EAAAQ,KACA,IAAA,IAAAC,KAAAD,EACAN,EAAAQ,EAAAF,EAAAC,KAAAP,EAAAQ,EAAAV,EAAAS,IACAE,OAAAC,eAAAZ,EAAAS,EAAA,CAAwCI,YAAA,EAAAC,IAAAN,EAAAC,MCJxCP,EAAAQ,EAAA,CAAAK,EAAAC,IAAAL,OAAAM,UAAAC,eAAAC,KAAAJ,EAAAC,GCCAd,EAAAkB,EAAApB,IACA,oBAAAqB,QAAAA,OAAAC,aACAX,OAAAC,eAAAZ,EAAAqB,OAAAC,YAAA,CAAsDC,MAAA,WAEtDZ,OAAAC,eAAAZ,EAAA,aAAA,CAA+CuB,OAAA,qFCJxC,IAAAC,EAAA,WASA,SAAAC,EAAAC,EAAAC,EAAAJ,GACP,IAAAK,EAAAC,KAAAC,MAAAP,EAAA,YACAQ,EAAAR,EACAG,EAAAM,UAAAL,EAAAC,GACAF,EAAAM,UAAAL,EAAA,EAAAI,GAEO,SAAAE,EAAAP,EAAAC,GAGP,OAAA,WAFAD,EAAAQ,SAAAP,GACAD,EAAAS,UAAAR,EAAA,GCjBA,IAAAS,GAAA,oBAAAC,SAAA,UAAAA,QAAAC,IAAA,gBACA,oBAAAC,aACA,oBAAAC,YACO,SAAAC,EAAAC,GAIP,IAHA,IAAAC,EAAAD,EAAAE,OACAC,EAAA,EACAC,EAAA,EACAA,EAAAH,GAAA,CACA,IAAApB,EAAAmB,EAAAK,WAAAD,KACA,GAAA,IAAA,WAAAvB,GAKA,GAAA,IAAA,WAAAA,GAEAsB,GAAA,MAEA,CAEA,GAAAtB,GAAA,OAAAA,GAAA,OAEAuB,EAAAH,EAAA,CACA,IAAAK,EAAAN,EAAAK,WAAAD,GACA,QAAA,MAAAE,OACAF,EACAvB,IAAA,KAAAA,IAAA,KAAA,KAAAyB,GAAA,OAMAH,GAFA,IAAA,WAAAtB,GAEA,EAIA,OAzBAsB,IA6BA,OAAAA,EA4CA,IAAAI,EAAAb,EAAA,IAAAG,iBAAAlC,EACO6C,EAAAd,EAEP,oBAAAC,SAAA,UAAAA,QAAAC,IAAA,cACA,IACA,EAHMd,EAUC2B,GAAAF,MAAAA,OAAA,EAAAA,EAAAG,YAHP,SAAAV,EAAAW,EAAAC,GACAL,EAAAG,WAAAV,EAAAW,EAAAE,SAAAD,KAJA,SAAAZ,EAAAW,EAAAC,GACAD,EAAAG,IAAAP,EAAAQ,OAAAf,GAAAY,IAOO,SAAAI,EAAAC,EAAAC,EAAAf,GAKP,IAJA,IAAAlB,EAAAiC,EACAC,EAAAlC,EAAAkB,EACAiB,EAAA,GACAC,EAAA,GACApC,EAAAkC,GAAA,CACA,IAAAG,EAAAL,EAAAhC,KACA,GAAA,IAAA,IAAAqC,GAEAF,EAAAG,KAAAD,QAEA,GAAA,MAAA,IAAAA,GAAA,CAEA,IAAAE,EAAA,GAAAP,EAAAhC,KACAmC,EAAAG,MAAA,GAAAD,IAAA,EAAAE,QAEA,GAAA,MAAA,IAAAF,GAAA,CAEAE,EAAA,GAAAP,EAAAhC,KAAA,IACAwC,EAAA,GAAAR,EAAAhC,KACAmC,EAAAG,MAAA,GAAAD,IAAA,GAAAE,GAAA,EAAAC,QAEA,GAAA,MAAA,IAAAH,GAAA,CAEA,IAGAI,GAAA,EAAAJ,IAAA,IAHAE,EAAA,GAAAP,EAAAhC,OAGA,IAFAwC,EAAA,GAAAR,EAAAhC,OAEA,EADA,GAAAgC,EAAAhC,KAEAyC,EAAA,QACAA,GAAA,MACAN,EAAAG,KAAAG,IAAA,GAAA,KAAA,OACAA,EAAA,MAAA,KAAAA,GAEAN,EAAAG,KAAAG,QAGAN,EAAAG,KAAAD,GAEAF,EAAAlB,QAvCA,OAwCAmB,GAAAM,OAAAC,aAAAC,MAAAF,OAAAP,GACAA,EAAAlB,OAAA,GAMA,OAHAkB,EAAAlB,OAAA,IACAmB,GAAAM,OAAAC,aAAAC,MAAAF,OAAAP,IAEAC,EAEA,IClJAS,EDkJAC,EAAArC,EAAA,IAAAI,YAAA,KACOkC,EAAAtC,EAEP,oBAAAC,SAAA,UAAAA,QAAAC,IAAA,aACA,IACA,EAHMd,EElJNmD,EACA,SAAAC,EAAAC,GACAC,KAAAF,KAAAA,EACAE,KAAAD,KAAAA,GDNAE,GACAP,EAAA,SAAAjE,EAAAyE,GAIA,OAHAR,EAAA7D,OAAAsE,gBACA,CAAcC,UAAA,cAAgBC,OAAA,SAAA5E,EAAAyE,GAAsCzE,EAAA2E,UAAAF,IACpE,SAAAzE,EAAAyE,GAA6B,IAAA,IAAAI,KAAAJ,EAAArE,OAAAM,UAAAC,eAAAC,KAAA6D,EAAAI,KAAA7E,EAAA6E,GAAAJ,EAAAI,MAC7B7E,EAAAyE,IAEA,SAAAzE,EAAAyE,GACA,GAAA,mBAAAA,GAAA,OAAAA,EACA,MAAA,IAAAK,UAAA,uBAAAhB,OAAAW,GAAA,iCAEA,SAAAM,IAAuBR,KAAAS,YAAAhF,EADvBiE,EAAAjE,EAAAyE,GAEAzE,EAAAU,UAAA,OAAA+D,EAAArE,OAAA6E,OAAAR,IAAAM,EAAArE,UAAA+D,EAAA/D,UAAA,IAAAqE,KAGAG,EAAA,SAAAC,GAEA,SAAAD,EAAAE,GACA,IAAAC,EAAAF,EAAAvE,KAAA2D,KAAAa,IAAAb,KAEAe,EAAAlF,OAAA6E,OAAAC,EAAAxE,WAOA,OANAN,OAAAsE,eAAAW,EAAAC,GACAlF,OAAAC,eAAAgF,EAAA,OAAA,CACAE,cAAA,EACAjF,YAAA,EACAU,MAAAkE,EAAAM,OAEAH,EAEA,OAbAb,EAAAU,EAAAC,GAaAD,EAdA,CAeCO,OE6DMC,EAAA,CACPrB,MAzFO,EA0FPnB,OA3CO,SAAAyC,GAEP,IAZAC,EACAC,EACAC,EAEAC,EAOA,OAAAJ,aAAAK,KA7CO,SAAAC,GACP,IA0BA9E,EA1BA0E,EAAAI,EAAAJ,IAAAC,EAAAG,EAAAH,KACA,GAAAD,GAAA,GAAAC,GAAA,GAAAD,GAHA,YAGA,CAEA,GAAA,IAAAC,GAAAD,GANA,WAMA,CAEA,IAAAK,EAAA,IAAAC,WAAA,GAGA,OAFAhF,EAAA,IAAAiF,SAAAF,EAAAG,SACA5E,UAAA,EAAAoE,GACAK,EAIA,IAAAI,EAAAT,EAAA,WACAU,EAAA,WAAAV,EAOA,OANAK,EAAA,IAAAC,WAAA,IACAhF,EAAA,IAAAiF,SAAAF,EAAAG,SAEA5E,UAAA,EAAAqE,GAAA,EAAA,EAAAQ,GAEAnF,EAAAM,UAAA,EAAA8E,GACAL,EASA,OAJAA,EAAA,IAAAC,WAAA,KACAhF,EAAA,IAAAiF,SAAAF,EAAAG,SACA5E,UAAA,EAAAqE,GACQ5E,EAAQC,EAAA,EAAA0E,GAChBK,EAiBAM,EAbAZ,EAYAD,EAZAc,UACAZ,EAAAvE,KAAAC,MAAAqE,EAAA,KACAE,EAAA,KAAAF,EAAA,IAAAC,GAEAE,EAAAzE,KAAAC,MAAAuE,EAAA,KACA,CACAD,IAAAA,EAAAE,EACAD,KAAAA,EAAA,IAAAC,KASA,MAsCAW,OAPO,SAAApC,GACP,IAAAqC,EA7BO,SAAArC,GACP,IAAAnD,EAAA,IAAAiF,SAAA9B,EAAA+B,OAAA/B,EAAAsC,WAAAtC,EAAAhC,YAEA,OAAAgC,EAAAhC,YACA,KAAA,EAIA,MAAA,CAAoBuD,IAFpB1E,EAAAS,UAAA,GAEoBkE,KADpB,GAGA,KAAA,EAEA,IAAAe,EAAA1F,EAAAS,UAAA,GAIA,MAAA,CAAoBiE,IAFpB,YAAA,EAAAgB,GADA1F,EAAAS,UAAA,GAGoBkE,KADpBe,IAAA,GAGA,KAAA,GAIA,MAAA,CAAoBhB,IAFEnE,EAAQP,EAAA,GAEV2E,KADpB3E,EAAAS,UAAA,IAGA,QACA,MAAA,IAAsBsD,EAAW,gEAAAZ,EAAAjC,SAIjCyE,CAAAxC,GACA,OAAA,IAAA0B,KAAA,IAAAW,EAAAd,IAAAc,EAAAb,KAAA,OCtFAiB,EAAA,WACA,SAAAA,IAEAxC,KAAAyC,gBAAA,GACAzC,KAAA0C,gBAAA,GAEA1C,KAAA2C,SAAA,GACA3C,KAAA4C,SAAA,GACA5C,KAAA6C,SAAsB1B,GAwDtB,OAtDAqB,EAAArG,UAAA0G,SAAA,SAAAnB,GACA,IAAA5B,EAAA4B,EAAA5B,KAAAnB,EAAA+C,EAAA/C,OAAAwD,EAAAT,EAAAS,OACA,GAAArC,GAAA,EAEAE,KAAA2C,SAAA7C,GAAAnB,EACAqB,KAAA4C,SAAA9C,GAAAqC,MAEA,CAEA,IAAAW,EAAA,EAAAhD,EACAE,KAAAyC,gBAAAK,GAAAnE,EACAqB,KAAA0C,gBAAAI,GAAAX,IAGAK,EAAArG,UAAA4G,YAAA,SAAA3B,EAAA4B,GAEA,IAAA,IAAAC,EAAA,EAAuBA,EAAAjD,KAAAyC,gBAAA3E,OAAiCmF,IAExD,GAAA,OADAC,EAAAlD,KAAAyC,gBAAAQ,KAGA,OADAlD,EAAAmD,EAAA9B,EAAA4B,IAGA,OAAA,IAA+BnD,GAD/B,EAAAoD,EACsClD,GAKtC,IAAAkD,EAAA,EAAuBA,EAAAjD,KAAA2C,SAAA7E,OAA0BmF,IAAA,CACjD,IAAAC,EAEAnD,EADA,GAAA,OADAmD,EAAAlD,KAAA2C,SAAAM,KAGA,OADAlD,EAAAmD,EAAA9B,EAAA4B,IAGA,OAAA,IAA+BnD,EAD/BoD,EACsClD,GAItC,OAAAqB,aAA8BvB,EAE9BuB,EAEA,MAEAoB,EAAArG,UAAAgG,OAAA,SAAApC,EAAAD,EAAAkD,GACA,IAAAG,EAAArD,EAAA,EAAAE,KAAA0C,iBAAA,EAAA5C,GAAAE,KAAA4C,SAAA9C,GACA,OAAAqD,EACAA,EAAApD,EAAAD,EAAAkD,GAIA,IAAuBnD,EAAOC,EAAAC,IAG9ByC,EAAAY,aAAA,IAAAZ,EACAA,EAhEA,GCHO,SAAAa,EAAAvB,GACP,OAAAA,aAAAF,WACAE,EAEAwB,YAAAC,OAAAzB,GACA,IAAAF,WAAAE,EAAAA,OAAAA,EAAAO,WAAAP,EAAA/D,YAEA+D,aAAAwB,YACA,IAAA1B,WAAAE,GAIAF,WAAA4B,KAAA1B,GCRO,IAEP2B,EAAA,WACA,SAAAA,EAAAC,EAAAV,EAAAW,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,QACA,IAAAN,IAAwCA,EAAkBlB,EAAAY,mBAC1D,IAAAJ,IAAiCA,OAAAzH,QACjC,IAAAoI,IAAkCA,EAN3B,UAOP,IAAAC,IAA2CA,EANpC,WAOP,IAAAC,IAAkCA,GAAA,QAClC,IAAAC,IAAsCA,GAAA,QACtC,IAAAC,IAAyCA,GAAA,QACzC,IAAAC,IAA6CA,GAAA,GAC7ChE,KAAA0D,eAAAA,EACA1D,KAAAgD,QAAAA,EACAhD,KAAA2D,SAAAA,EACA3D,KAAA4D,kBAAAA,EACA5D,KAAA6D,SAAAA,EACA7D,KAAA8D,aAAAA,EACA9D,KAAA+D,gBAAAA,EACA/D,KAAAgE,oBAAAA,EACAhE,KAAAhC,IAAA,EACAgC,KAAApD,KAAA,IAAAiF,SAAA,IAAAyB,YAAAtD,KAAA4D,oBACA5D,KAAAnB,MAAA,IAAA+C,WAAA5B,KAAApD,KAAAkF,QAyXA,OAvXA2B,EAAAtH,UAAA8H,cAAA,WACA,OAAAjE,KAAAnB,MAAAJ,SAAA,EAAAuB,KAAAhC,MAEAyF,EAAAtH,UAAA+H,kBAAA,WACAlE,KAAAhC,IAAA,GAEAyF,EAAAtH,UAAAwC,OAAA,SAAAyC,GAGA,OAFApB,KAAAkE,oBACAlE,KAAAmE,SAAA/C,EAAA,GACApB,KAAAiE,iBAEAR,EAAAtH,UAAAgI,SAAA,SAAA/C,EAAAgD,GACA,GAAAA,EAAApE,KAAA2D,SACA,MAAA,IAAAzC,MAAA,6BAAAkD,GAEA,MAAAhD,EACApB,KAAAqE,YAEA,kBAAAjD,EACApB,KAAAsE,cAAAlD,GAEA,iBAAAA,EACApB,KAAAuE,aAAAnD,GAEA,iBAAAA,EACApB,KAAAwE,aAAApD,GAGApB,KAAAyE,aAAArD,EAAAgD,IAGAX,EAAAtH,UAAAuI,wBAAA,SAAAC,GACA,IAAAC,EAAA5E,KAAAhC,IAAA2G,EACA3E,KAAApD,KAAAmB,WAAA6G,GACA5E,KAAA6E,aAAA,EAAAD,IAGAnB,EAAAtH,UAAA0I,aAAA,SAAAC,GACA,IAAAC,EAAA,IAAAzB,YAAAwB,GACAE,EAAA,IAAApD,WAAAmD,GACAE,EAAA,IAAApD,SAAAkD,GACAC,EAAAtG,IAAAsB,KAAAnB,OACAmB,KAAApD,KAAAqI,EACAjF,KAAAnB,MAAAmG,GAEAvB,EAAAtH,UAAAkI,UAAA,WACArE,KAAAkF,QAAA,MAEAzB,EAAAtH,UAAAmI,cAAA,SAAAlD,IACA,IAAAA,EACApB,KAAAkF,QAAA,KAGAlF,KAAAkF,QAAA,MAGAzB,EAAAtH,UAAAoI,aAAA,SAAAnD,GACA+D,OAAAC,cAAAhE,KAAApB,KAAAgE,oBACA5C,GAAA,EACAA,EAAA,IAEApB,KAAAkF,QAAA9D,GAEAA,EAAA,KAEApB,KAAAkF,QAAA,KACAlF,KAAAkF,QAAA9D,IAEAA,EAAA,OAEApB,KAAAkF,QAAA,KACAlF,KAAAqF,SAAAjE,IAEAA,EAAA,YAEApB,KAAAkF,QAAA,KACAlF,KAAAsF,SAAAlE,KAIApB,KAAAkF,QAAA,KACAlF,KAAAuF,SAAAnE,IAIAA,IAAA,GAEApB,KAAAkF,QAAA,IAAA9D,EAAA,IAEAA,IAAA,KAEApB,KAAAkF,QAAA,KACAlF,KAAAwF,QAAApE,IAEAA,IAAA,OAEApB,KAAAkF,QAAA,KACAlF,KAAAyF,SAAArE,IAEAA,IAAA,YAEApB,KAAAkF,QAAA,KACAlF,KAAA0F,SAAAtE,KAIApB,KAAAkF,QAAA,KACAlF,KAAA2F,SAAAvE,IAMApB,KAAA8D,cAEA9D,KAAAkF,QAAA,KACAlF,KAAA4F,SAAAxE,KAIApB,KAAAkF,QAAA,KACAlF,KAAA6F,SAAAzE,KAIAqC,EAAAtH,UAAA2J,kBAAA,SAAA/H,GACA,GAAAA,EAAA,GAEAiC,KAAAkF,QAAA,IAAAnH,QAEA,GAAAA,EAAA,IAEAiC,KAAAkF,QAAA,KACAlF,KAAAkF,QAAAnH,QAEA,GAAAA,EAAA,MAEAiC,KAAAkF,QAAA,KACAlF,KAAAqF,SAAAtH,OAEA,CAAA,KAAAA,EAAA,YAMA,MAAA,IAAAmD,MAAA,oBAAAnD,EAAA,mBAJAiC,KAAAkF,QAAA,KACAlF,KAAAsF,SAAAvH,KAMA0F,EAAAtH,UAAAqI,aAAA,SAAApD,GAGA,GADAA,EAAAtD,OACwBM,EAAsB,CAC9C,IAAAL,EAA6BJ,EAASyD,GACtCpB,KAAA0E,wBAJA,EAIA3G,GACAiC,KAAA8F,kBAAA/H,GACYM,EAAY+C,EAAApB,KAAAnB,MAAAmB,KAAAhC,KACxBgC,KAAAhC,KAAAD,OAGAA,EAA6BJ,EAASyD,GACtCpB,KAAA0E,wBAXA,EAWA3G,GACAiC,KAAA8F,kBAAA/H,GNnJO,SAAAH,EAAAW,EAAAC,GAIP,IAHA,IAAAX,EAAAD,EAAAE,OACAjB,EAAA2B,EACAR,EAAA,EACAA,EAAAH,GAAA,CACA,IAAApB,EAAAmB,EAAAK,WAAAD,KACA,GAAA,IAAA,WAAAvB,GAAA,CAKA,GAAA,IAAA,WAAAA,GAEA8B,EAAA1B,KAAAJ,GAAA,EAAA,GAAA,QAEA,CAEA,GAAAA,GAAA,OAAAA,GAAA,OAEAuB,EAAAH,EAAA,CACA,IAAAK,EAAAN,EAAAK,WAAAD,GACA,QAAA,MAAAE,OACAF,EACAvB,IAAA,KAAAA,IAAA,KAAA,KAAAyB,GAAA,OAIA,IAAA,WAAAzB,IAEA8B,EAAA1B,KAAAJ,GAAA,GAAA,GAAA,IACA8B,EAAA1B,KAAAJ,GAAA,EAAA,GAAA,MAIA8B,EAAA1B,KAAAJ,GAAA,GAAA,EAAA,IACA8B,EAAA1B,KAAAJ,GAAA,GAAA,GAAA,IACA8B,EAAA1B,KAAAJ,GAAA,EAAA,GAAA,KAGA8B,EAAA1B,KAAA,GAAAJ,EAAA,SA/BA8B,EAAA1B,KAAAJ,GM4IYsJ,CAAY3E,EAAApB,KAAAnB,MAAAmB,KAAAhC,KACxBgC,KAAAhC,KAAAD,GAGA0F,EAAAtH,UAAAsI,aAAA,SAAArD,EAAAgD,GAEA,IAAA4B,EAAAhG,KAAA0D,eAAAX,YAAA3B,EAAApB,KAAAgD,SACA,GAAA,MAAAgD,EACAhG,KAAAiG,gBAAAD,QAEA,GAAA3F,MAAA6F,QAAA9E,GACApB,KAAAmG,YAAA/E,EAAAgD,QAEA,GAAAd,YAAAC,OAAAnC,GACApB,KAAAoG,aAAAhF,OAEA,CAAA,GAAA,iBAAAA,EAKA,MAAA,IAAAF,MAAA,wBAAArF,OAAAM,UAAAkK,SAAA5G,MAAA2B,IAJApB,KAAAsG,UAAAlF,EAAAgD,KAOAX,EAAAtH,UAAAiK,aAAA,SAAAhF,GACA,IAAAmF,EAAAnF,EAAArD,WACA,GAAAwI,EAAA,IAEAvG,KAAAkF,QAAA,KACAlF,KAAAkF,QAAAqB,QAEA,GAAAA,EAAA,MAEAvG,KAAAkF,QAAA,KACAlF,KAAAqF,SAAAkB,OAEA,CAAA,KAAAA,EAAA,YAMA,MAAA,IAAArF,MAAA,qBAAAqF,GAJAvG,KAAAkF,QAAA,KACAlF,KAAAsF,SAAAiB,GAKA,IAAA1H,EAAoBwE,EAAgBjC,GACpCpB,KAAAwG,SAAA3H,IAEA4E,EAAAtH,UAAAgK,YAAA,SAAA/E,EAAAgD,GACA,IAAAmC,EAAAnF,EAAAtD,OACA,GAAAyI,EAAA,GAEAvG,KAAAkF,QAAA,IAAAqB,QAEA,GAAAA,EAAA,MAEAvG,KAAAkF,QAAA,KACAlF,KAAAqF,SAAAkB,OAEA,CAAA,KAAAA,EAAA,YAMA,MAAA,IAAArF,MAAA,oBAAAqF,GAJAvG,KAAAkF,QAAA,KACAlF,KAAAsF,SAAAiB,GAKA,IAAA,IAAAE,EAAA,EAAAC,EAAAtF,EAA2CqF,EAAAC,EAAA5I,OAAsB2I,IAAA,CACjE,IAAAE,EAAAD,EAAAD,GACAzG,KAAAmE,SAAAwC,EAAAvC,EAAA,KAGAX,EAAAtH,UAAAyK,sBAAA,SAAAxF,EAAAyF,GAEA,IADA,IAAAC,EAAA,EACAL,EAAA,EAAAM,EAAAF,EAAuCJ,EAAAM,EAAAjJ,OAAoB2I,SAE3DlL,IAAA6F,EADA2F,EAAAN,KAEAK,IAGA,OAAAA,GAEArD,EAAAtH,UAAAmK,UAAA,SAAAlF,EAAAgD,GACA,IAAAyC,EAAAhL,OAAAgL,KAAAzF,GACApB,KAAA6D,UACAgD,EAAAG,OAEA,IAAAT,EAAAvG,KAAA+D,gBAAA/D,KAAA4G,sBAAAxF,EAAAyF,GAAAA,EAAA/I,OACA,GAAAyI,EAAA,GAEAvG,KAAAkF,QAAA,IAAAqB,QAEA,GAAAA,EAAA,MAEAvG,KAAAkF,QAAA,KACAlF,KAAAqF,SAAAkB,OAEA,CAAA,KAAAA,EAAA,YAMA,MAAA,IAAArF,MAAA,yBAAAqF,GAJAvG,KAAAkF,QAAA,KACAlF,KAAAsF,SAAAiB,GAKA,IAAA,IAAAE,EAAA,EAAAQ,EAAAJ,EAAuCJ,EAAAQ,EAAAnJ,OAAoB2I,IAAA,CAC3D,IAAA9K,EAAAsL,EAAAR,GACAhK,EAAA2E,EAAAzF,GACAqE,KAAA+D,sBAAAxI,IAAAkB,IACAuD,KAAAwE,aAAA7I,GACAqE,KAAAmE,SAAA1H,EAAA2H,EAAA,MAIAX,EAAAtH,UAAA8J,gBAAA,SAAAD,GACA,IAAAO,EAAAP,EAAAjG,KAAAjC,OACA,GAAA,IAAAyI,EAEAvG,KAAAkF,QAAA,UAEA,GAAA,IAAAqB,EAEAvG,KAAAkF,QAAA,UAEA,GAAA,IAAAqB,EAEAvG,KAAAkF,QAAA,UAEA,GAAA,IAAAqB,EAEAvG,KAAAkF,QAAA,UAEA,GAAA,KAAAqB,EAEAvG,KAAAkF,QAAA,UAEA,GAAAqB,EAAA,IAEAvG,KAAAkF,QAAA,KACAlF,KAAAkF,QAAAqB,QAEA,GAAAA,EAAA,MAEAvG,KAAAkF,QAAA,KACAlF,KAAAqF,SAAAkB,OAEA,CAAA,KAAAA,EAAA,YAMA,MAAA,IAAArF,MAAA,+BAAAqF,GAJAvG,KAAAkF,QAAA,KACAlF,KAAAsF,SAAAiB,GAKAvG,KAAAwF,QAAAQ,EAAAlG,MACAE,KAAAwG,SAAAR,EAAAjG,OAEA0D,EAAAtH,UAAA+I,QAAA,SAAAzI,GACAuD,KAAA0E,wBAAA,GACA1E,KAAApD,KAAAsK,SAAAlH,KAAAhC,IAAAvB,GACAuD,KAAAhC,OAEAyF,EAAAtH,UAAAqK,SAAA,SAAAW,GACA,IAAAZ,EAAAY,EAAArJ,OACAkC,KAAA0E,wBAAA6B,GACAvG,KAAAnB,MAAAH,IAAAyI,EAAAnH,KAAAhC,KACAgC,KAAAhC,KAAAuI,GAEA9C,EAAAtH,UAAAqJ,QAAA,SAAA/I,GACAuD,KAAA0E,wBAAA,GACA1E,KAAApD,KAAAwK,QAAApH,KAAAhC,IAAAvB,GACAuD,KAAAhC,OAEAyF,EAAAtH,UAAAkJ,SAAA,SAAA5I,GACAuD,KAAA0E,wBAAA,GACA1E,KAAApD,KAAAyK,UAAArH,KAAAhC,IAAAvB,GACAuD,KAAAhC,KAAA,GAEAyF,EAAAtH,UAAAsJ,SAAA,SAAAhJ,GACAuD,KAAA0E,wBAAA,GACA1E,KAAApD,KAAA0K,SAAAtH,KAAAhC,IAAAvB,GACAuD,KAAAhC,KAAA,GAEAyF,EAAAtH,UAAAmJ,SAAA,SAAA7I,GACAuD,KAAA0E,wBAAA,GACA1E,KAAApD,KAAAM,UAAA8C,KAAAhC,IAAAvB,GACAuD,KAAAhC,KAAA,GAEAyF,EAAAtH,UAAAuJ,SAAA,SAAAjJ,GACAuD,KAAA0E,wBAAA,GACA1E,KAAApD,KAAA2K,SAAAvH,KAAAhC,IAAAvB,GACAuD,KAAAhC,KAAA,GAEAyF,EAAAtH,UAAAyJ,SAAA,SAAAnJ,GACAuD,KAAA0E,wBAAA,GACA1E,KAAApD,KAAA4K,WAAAxH,KAAAhC,IAAAvB,GACAuD,KAAAhC,KAAA,GAEAyF,EAAAtH,UAAA0J,SAAA,SAAApJ,GACAuD,KAAA0E,wBAAA,GACA1E,KAAApD,KAAA6K,WAAAzH,KAAAhC,IAAAvB,GACAuD,KAAAhC,KAAA,GAEAyF,EAAAtH,UAAAoJ,SAAA,SAAA9I,GACAuD,KAAA0E,wBAAA,GPtYO,SAAA9H,EAAAC,EAAAJ,GACP,IAAAK,EAAAL,EAAA,WACAQ,EAAAR,EACAG,EAAAM,UAAAL,EAAAC,GACAF,EAAAM,UAAAL,EAAA,EAAAI,GOmYQyK,CAAS1H,KAAApD,KAAAoD,KAAAhC,IAAAvB,GACjBuD,KAAAhC,KAAA,GAEAyF,EAAAtH,UAAAwJ,SAAA,SAAAlJ,GACAuD,KAAA0E,wBAAA,GACQ/H,EAAQqD,KAAApD,KAAAoD,KAAAhC,IAAAvB,GAChBuD,KAAAhC,KAAA,GAEAyF,EA7YA,GCNO,SAAAkE,EAAAC,GACP,OAAAA,EAAA,EAAA,IAAA,IAAA,KAAA7K,KAAA8K,IAAAD,GAAAvB,SAAA,IAAAyB,SAAA,EAAA,KCAA,IAEAC,EAAA,WACA,SAAAA,EAAAC,EAAAC,QACA,IAAAD,IAAsCA,EAJtC,SAKA,IAAAC,IAAyCA,EAJzC,IAKAjI,KAAAgI,aAAAA,EACAhI,KAAAiI,gBAAAA,EACAjI,KAAAkI,IAAA,EACAlI,KAAAmI,KAAA,EAGAnI,KAAAoI,OAAA,GACA,IAAA,IAAAnF,EAAA,EAAuBA,EAAAjD,KAAAgI,aAAuB/E,IAC9CjD,KAAAoI,OAAAjJ,KAAA,IA6CA,OA1CA4I,EAAA5L,UAAAkM,YAAA,SAAAtK,GACA,OAAAA,EAAA,GAAAA,GAAAiC,KAAAgI,cAEAD,EAAA5L,UAAAmM,KAAA,SAAAzJ,EAAAC,EAAAf,GAEAwK,EAAA,IAAA,IAAA9B,EAAA,EAAA+B,EADAxI,KAAAoI,OAAArK,EAAA,GACyD0I,EAAA+B,EAAA1K,OAAuB2I,IAAA,CAGhF,IAFA,IAAAgC,EAAAD,EAAA/B,GACAiC,EAAAD,EAAA5J,MACA8J,EAAA,EAA2BA,EAAA5K,EAAgB4K,IAC3C,GAAAD,EAAAC,KAAA9J,EAAAC,EAAA6J,GACA,SAAAJ,EAGA,OAAAE,EAAA7K,IAEA,OAAA,MAEAmK,EAAA5L,UAAAyM,MAAA,SAAA/J,EAAApC,GACA,IAAAoM,EAAA7I,KAAAoI,OAAAvJ,EAAAf,OAAA,GACA2K,EAAA,CAAsB5J,MAAAA,EAAAjB,IAAAnB,GACtBoM,EAAA/K,QAAAkC,KAAAiI,gBAGAY,EAAA9L,KAAA+L,SAAAD,EAAA/K,OAAA,GAAA2K,EAGAI,EAAA1J,KAAAsJ,IAGAV,EAAA5L,UAAAgG,OAAA,SAAAtD,EAAAC,EAAAf,GACA,IAAAgL,EAAA/I,KAAAsI,KAAAzJ,EAAAC,EAAAf,GACA,GAAA,MAAAgL,EAEA,OADA/I,KAAAkI,MACAa,EAEA/I,KAAAmI,OACA,IAAAvK,EAAkBgB,EAAYC,EAAAC,EAAAf,GAE9BiL,EAAApH,WAAAzF,UAAA8M,MAAA5M,KAAAwC,EAAAC,EAAAA,EAAAf,GAEA,OADAiC,KAAA4I,MAAAI,EAAApL,GACAA,GAEAmK,EAzDA,GCHAmB,EAA6B,SAAAC,EAAAC,EAAAC,EAAAC,GAE7B,OAAA,IAAAD,IAAAA,EAAAE,WAAA,SAAAC,EAAAC,GACA,SAAAC,EAAAjN,GAAmC,IAAMkN,EAAAL,EAAAM,KAAAnN,IAA+B,MAAAoN,GAAYJ,EAAAI,IACpF,SAAAC,EAAArN,GAAkC,IAAMkN,EAAAL,EAAA,MAAA7M,IAAmC,MAAAoN,GAAYJ,EAAAI,IACvF,SAAAF,EAAA1K,GAJA,IAAAxC,EAI+BwC,EAAA8K,KAAAP,EAAAvK,EAAAxC,QAJ/BA,EAI+BwC,EAAAxC,MAJJA,aAAA4M,EAAA5M,EAAA,IAAA4M,GAAA,SAAAG,GAA+DA,EAAA/M,OAI3DuN,KAAAN,EAAAI,GAC/BH,GAAAL,EAAAA,EAAA7J,MAAA0J,EAAAC,GAAA,KAAAQ,YAGAK,EAA+B,SAAAd,EAAAe,GAC/B,IAAwGC,EAAAC,EAAAC,EAAAC,EAAxGC,EAAA,CAAaC,MAAA,EAAAC,KAAA,WAA6B,GAAA,EAAAJ,EAAA,GAAA,MAAAA,EAAA,GAA0B,OAAAA,EAAA,IAAeK,KAAA,GAAAC,IAAA,IACnF,OAAAL,EAAA,CAAgBV,KAAAgB,EAAA,GAAAC,MAAAD,EAAA,GAAAE,OAAAF,EAAA,IAAqD,mBAAArO,SAAA+N,EAAA/N,OAAAwO,UAAA,WAAoE,OAAA/K,OAAesK,EACxJ,SAAAM,EAAAI,GAAsB,OAAA,SAAAC,GAAsB,OAC5C,SAAAC,GACA,GAAAf,EAAA,MAAA,IAAA5J,UAAA,mCACA,KAAAgK,OACA,GAAAJ,EAAA,EAAAC,IAAAC,EAAA,EAAAa,EAAA,GAAAd,EAAA,OAAAc,EAAA,GAAAd,EAAA,SAAAC,EAAAD,EAAA,SAAAC,EAAAhO,KAAA+N,GAAA,GAAAA,EAAAR,SAAAS,EAAAA,EAAAhO,KAAA+N,EAAAc,EAAA,KAAAnB,KAAA,OAAAM,EAEA,OADAD,EAAA,EAAAC,IAAAa,EAAA,CAAA,EAAAA,EAAA,GAAAb,EAAA5N,QACAyO,EAAA,IACA,KAAA,EAAA,KAAA,EAAAb,EAAAa,EAAuC,MACvC,KAAA,EAAkC,OAAlCX,EAAAC,QAAkC,CAAS/N,MAAAyO,EAAA,GAAAnB,MAAA,GAC3C,KAAA,EAAAQ,EAAAC,QAAkCJ,EAAAc,EAAA,GAAWA,EAAA,CAAA,GAAU,SACvD,KAAA,EAAAA,EAAAX,EAAAI,IAAAQ,MAAyCZ,EAAAG,KAAAS,MAAc,SACvD,QACA,MAAAd,GAAAA,EAAAE,EAAAG,MAAA5M,OAAA,GAAAuM,EAAAA,EAAAvM,OAAA,KAAA,IAAAoN,EAAA,IAAA,IAAAA,EAAA,IAAA,CAA6GX,EAAA,EAAO,SACpH,GAAA,IAAAW,EAAA,MAAAb,GAAAa,EAAA,GAAAb,EAAA,IAAAa,EAAA,GAAAb,EAAA,IAAA,CAAgFE,EAAAC,MAAAU,EAAA,GAAiB,MACjG,GAAA,IAAAA,EAAA,IAAAX,EAAAC,MAAAH,EAAA,GAAA,CAAwDE,EAAAC,MAAAH,EAAA,GAAgBA,EAAAa,EAAQ,MAChF,GAAAb,GAAAE,EAAAC,MAAAH,EAAA,GAAA,CAA8CE,EAAAC,MAAAH,EAAA,GAAgBE,EAAAI,IAAAxL,KAAA+L,GAAgB,MAC9Eb,EAAA,IAAAE,EAAAI,IAAAQ,MACAZ,EAAAG,KAAAS,MAAiC,SAEjCD,EAAAhB,EAAA7N,KAAA8M,EAAAoB,GACS,MAAAV,GAAYqB,EAAA,CAAA,EAAArB,GAAaO,EAAA,EAAS,QAAUD,EAAAE,EAAA,EACrD,GAAA,EAAAa,EAAA,GAAA,MAAAA,EAAA,GAAmC,MAAA,CAASzO,MAAAyO,EAAA,GAAAA,EAAA,QAAA,EAAAnB,MAAA,GArBAJ,CAAA,CAAAqB,EAAAC,OAwB5CG,EAAiC,SAAAxP,GACjC,IAAAW,OAAA8O,cAAA,MAAA,IAAA9K,UAAA,wCACA,IAAA0C,EAAAqI,EAAA1P,EAAAW,OAAA8O,eACA,OAAAC,EAAAA,EAAAjP,KAAAT,IAAAA,EAAA,mBAAA2P,SAAAA,SAAA3P,GAAAA,EAAAW,OAAAwO,YAAA9H,EAAA,GAA2G2H,EAAA,QAAAA,EAAA,SAAAA,EAAA,UAAA3H,EAAA1G,OAAA8O,eAAA,WAAsF,OAAArL,MAAeiD,GAChN,SAAA2H,EAAAI,GAAsB/H,EAAA+H,GAAApP,EAAAoP,IAAA,SAAAC,GAA8B,OAAA,IAAA1B,SAAA,SAAAC,EAAAC,IACpD,SAAAD,EAAAC,EAAAhO,EAAAwP,GAA4C1B,QAAAC,QAAAyB,GAAAjB,MAAA,SAAAiB,GAAsCzB,EAAA,CAAU/M,MAAAwO,EAAAlB,KAAAtO,MAAwBgO,GADhB+B,CAAAhC,EAAAC,GAAAwB,EAAArP,EAAAoP,GAAAC,IAAAlB,KAAAkB,EAAAxO,aAGpGgP,EAA2B,SAAAR,GAA2B,OAAAjL,gBAAAyL,GAAAzL,KAAAiL,EAAAA,EAAAjL,MAAA,IAAAyL,EAAAR,IACtDS,EAAoC,SAAAvC,EAAAC,EAAAE,GACpC,IAAA/M,OAAA8O,cAAA,MAAA,IAAA9K,UAAA,wCACA,IAAA0C,EAAAqH,EAAAhB,EAAA7J,MAAA0J,EAAAC,GAAA,IAAAuC,EAAA,GACA,OAAA1I,EAAA,GAAiB2H,EAAA,QAAAA,EAAA,SAAAA,EAAA,UAAA3H,EAAA1G,OAAA8O,eAAA,WAAsF,OAAArL,MAAeiD,EACtH,SAAA2H,EAAAI,GAAsBV,EAAAU,KAAA/H,EAAA+H,GAAA,SAAAC,GAAgC,OAAA,IAAA1B,SAAA,SAAAqC,EAAA1L,GAAqCyL,EAAAxM,KAAA,CAAA6L,EAAAC,EAAAW,EAAA1L,IAAA,GAAA2L,EAAAb,EAAAC,QAC3F,SAAAY,EAAAb,EAAAC,GAA2B,KAC3B3O,EADiCgO,EAAAU,GAAAC,IACXxO,iBAAAgP,EAAAlC,QAAAC,QAAAlN,EAAAG,MAAAwO,GAAAjB,KAAA8B,EAAArC,GAAA+B,EAAAG,EAAA,GAAA,GAAArP,GAD4B,MAAAuN,GAAY2B,EAAAG,EAAA,GAAA,GAAA9B,GAC9D,IAAAvN,EACA,SAAAwP,EAAArP,GAA6BoP,EAAA,OAAApP,GAC7B,SAAAgN,EAAAhN,GAA4BoP,EAAA,QAAApP,GAC5B,SAAA+O,EAAArB,EAAAc,GAA2Bd,EAAAc,GAAAU,EAAAI,QAAAJ,EAAA7N,QAAA+N,EAAAF,EAAA,GAAA,GAAAA,EAAA,GAAA,MAc3BK,EAAA,IAAAnK,SAAA,IAAAyB,YAAA,IACA2I,EAAA,IAAArK,WAAAoK,EAAAlK,QAGOoK,EAAA,WACP,IAGAF,EAAAG,QAAA,GAEA,MAAAtC,GACA,OAAAA,EAAApJ,YAEA,MAAA,IAAAS,MAAA,iBATO,GAWPkL,EAAA,IAAAF,EAAA,qBACAG,EAAA,IAAiCtE,EACjCuE,EAAA,WACA,SAAAA,EAAA5I,EAAAV,EAAAuJ,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,QACA,IAAAlJ,IAAwCA,EAAkBlB,EAAAY,mBAC1D,IAAAJ,IAAiCA,OAAAzH,QACjC,IAAAgR,IAAsCA,EAAgB7P,QACtD,IAAA8P,IAAsCA,EAAgB9P,QACtD,IAAA+P,IAAwCA,EAAkB/P,QAC1D,IAAAgQ,IAAsCA,EAAgBhQ,QACtD,IAAAiQ,IAAsCA,EAAgBjQ,QACtD,IAAAkQ,IAAoCA,EAAAP,GACpCrM,KAAA0D,eAAAA,EACA1D,KAAAgD,QAAAA,EACAhD,KAAAuM,aAAAA,EACAvM,KAAAwM,aAAAA,EACAxM,KAAAyM,eAAAA,EACAzM,KAAA0M,aAAAA,EACA1M,KAAA2M,aAAAA,EACA3M,KAAA4M,WAAAA,EACA5M,KAAA6M,SAAA,EACA7M,KAAAhC,IAAA,EACAgC,KAAApD,KAAAoP,EACAhM,KAAAnB,MAAAoN,EACAjM,KAAA8M,UAxCA,EAyCA9M,KAAA+M,MAAA,GA+mBA,OA7mBAT,EAAAnQ,UAAA+H,kBAAA,WACAlE,KAAA6M,SAAA,EACA7M,KAAA8M,UA7CA,EA8CA9M,KAAA+M,MAAAjP,OAAA,GAGAwO,EAAAnQ,UAAA6Q,UAAA,SAAAlL,GACA9B,KAAAnB,MAAqBwE,EAAgBvB,GACrC9B,KAAApD,KJtGO,SAAAkF,GACP,GAAAA,aAAAwB,YACA,OAAA,IAAAzB,SAAAC,GAEA,IAAAmL,EAAA5J,EAAAvB,GACA,OAAA,IAAAD,SAAAoL,EAAAnL,OAAAmL,EAAA5K,WAAA4K,EAAAlP,YIiGoBmP,CAAclN,KAAAnB,OAClCmB,KAAAhC,IAAA,GAEAsO,EAAAnQ,UAAAgR,aAAA,SAAArL,GACA,IAvDA,IAuDA9B,KAAA8M,UAAA9M,KAAAoN,aAAA,GAGA,CACA,IAAAC,EAAArN,KAAAnB,MAAAJ,SAAAuB,KAAAhC,KACAsP,EAA0BjK,EAAgBvB,GAE1CiD,EAAA,IAAAnD,WAAAyL,EAAAvP,OAAAwP,EAAAxP,QACAiH,EAAArG,IAAA2O,GACAtI,EAAArG,IAAA4O,EAAAD,EAAAvP,QACAkC,KAAAgN,UAAAjI,QATA/E,KAAAgN,UAAAlL,IAYAwK,EAAAnQ,UAAAiR,aAAA,SAAA7G,GACA,OAAAvG,KAAApD,KAAAmB,WAAAiC,KAAAhC,KAAAuI,GAEA+F,EAAAnQ,UAAAoR,qBAAA,SAAAC,GACA,IAAA5Q,EAAAoD,KAAApD,KAAAoB,EAAAgC,KAAAhC,IACA,OAAA,IAAAyP,WAAA,UAAA7Q,EAAAmB,WAAAC,GAAA,OAAApB,EAAAmB,WAAA,4BAAAyP,EAAA,MAMAlB,EAAAnQ,UAAAgG,OAAA,SAAAL,GACA9B,KAAAkE,oBACAlE,KAAAgN,UAAAlL,GACA,IAAAV,EAAApB,KAAA0N,eACA,GAAA1N,KAAAoN,aAAA,GACA,MAAApN,KAAAuN,qBAAAvN,KAAAhC,KAEA,OAAAoD,GAEAkL,EAAAnQ,UAAAwR,YAAA,SAAA7L,GACA,OAAAmI,EAAAjK,MAAA,SAAA0B,GACA,OAAAA,EAAA8I,OACA,KAAA,EACAxK,KAAAkE,oBACAlE,KAAAgN,UAAAlL,GACAJ,EAAA8I,MAAA,EACA,KAAA,EACA,OAAAxK,KAAAoN,aAAA,GACA,CAAA,EAAApN,KAAA0N,gBADA,CAAA,EAAA,GAEA,KAAA,EAEA,OADAhM,EAAA+I,OACA,CAAA,EAAA,GACA,KAAA,EAAA,MAAA,CAAA,QAIA6B,EAAAnQ,UAAAyR,YAAA,SAAAC,GACA,IAAAC,EAAAC,EACAC,EAAAtM,EACA,OAAAwH,EAAAlJ,UAAA,OAAA,GAAA,WACA,IAAAiO,EAAA7M,EAAAU,EAAAoM,EAAAC,EAAArB,EAAA9O,EAAA6O,EACA,OAAA5C,EAAAjK,MAAA,SAAAoO,GACA,OAAAA,EAAA5D,OACA,KAAA,EACAyD,GAAA,EACAG,EAAA5D,MAAA,EACA,KAAA,EACA4D,EAAA1D,KAAAvL,KAAA,CAAA,EAAA,EAAA,EAAA,KACA2O,EAAA1C,EAAAyC,GACAO,EAAA5D,MAAA,EACA,KAAA,EAAA,MAAA,CAAA,EAAAsD,EAAAlE,QACA,KAAA,EACA,IAAAmE,EAAAK,EAAA3D,QAAAV,KAAA,MAAA,CAAA,EAAA,GAEA,GADAjI,EAAAiM,EAAAtR,MACAwR,EACA,MAAAjO,KAAAuN,qBAAAvN,KAAA6M,UAEA7M,KAAAmN,aAAArL,GACA,IACAV,EAAApB,KAAA0N,eACAO,GAAA,EAEA,MAAApE,GACA,KAAAA,aAAAqC,GACA,MAAArC,EAIA7J,KAAA6M,UAAA7M,KAAAhC,IACAoQ,EAAA5D,MAAA,EACA,KAAA,EAAA,MAAA,CAAA,EAAA,GACA,KAAA,EAAA,MAAA,CAAA,EAAA,IACA,KAAA,EAGA,OAFA0D,EAAAE,EAAA3D,OACAuD,EAAA,CAA+BK,MAAAH,GAC/B,CAAA,EAAA,IACA,KAAA,EAEA,OADAE,EAAA1D,KAAAvL,KAAA,CAAA,EAAA,CAAA,GAAA,KACA4O,IAAAA,EAAAhE,OAAArI,EAAAoM,EAAAhD,QACA,CAAA,EAAApJ,EAAArF,KAAAyR,IADA,CAAA,EAAA,GAEA,KAAA,EACAM,EAAA3D,OACA2D,EAAA5D,MAAA,EACA,KAAA,EAAA,MAAA,CAAA,EAAA,IACA,KAAA,GACA,GAAAwD,EAAA,MAAAA,EAAAK,MACA,MAAA,CAAA,GACA,KAAA,GAAA,MAAA,CAAA,GACA,KAAA,GACA,GAAAJ,EAAA,CACA,GAAAjO,KAAAoN,aAAA,GACA,MAAApN,KAAAuN,qBAAAvN,KAAA6M,UAEA,MAAA,CAAA,EAAAzL,GAGA,MADA0L,GAAAqB,EAAAnO,MAAA8M,SAAA9O,EAAAmQ,EAAAnQ,IAAA6O,EAAAsB,EAAAtB,SACA,IAAAY,WAAA,gCAA+E9F,EAAUmF,GAAA,OAAAD,EAAA,KAAA7O,EAAA,mCAKzFsO,EAAAnQ,UAAAmS,kBAAA,SAAAT,GACA,OAAA7N,KAAAuO,iBAAAV,GAAA,IAEAvB,EAAAnQ,UAAAqS,aAAA,SAAAX,GACA,OAAA7N,KAAAuO,iBAAAV,GAAA,IAEAvB,EAAAnQ,UAAAoS,iBAAA,SAAAV,EAAA3H,GACA,OAAAwF,EAAA1L,KAAAyO,WAAA,WACA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA/M,EAAAgN,EAAAC,EACAC,EAAAtN,EACA,OAAAuI,EAAAjK,MAAA,SAAAmO,GACA,OAAAA,EAAA3D,OACA,KAAA,EACAkE,EAAAxI,EACAyI,GAAA,EACAR,EAAA3D,MAAA,EACA,KAAA,EACA2D,EAAAzD,KAAAvL,KAAA,CAAA,EAAA,GAAA,GAAA,KACAyP,EAAAxD,EAAAyC,GACAM,EAAA3D,MAAA,EACA,KAAA,EAAA,MAAA,CAAA,EAAAiB,EAAAmD,EAAAhF,SACA,KAAA,EACA,IAAAiF,EAAAV,EAAA1D,QAAAV,KAAA,MAAA,CAAA,EAAA,IAEA,GADAjI,EAAA+M,EAAApS,MACAyJ,GAAA,IAAAyI,EACA,MAAA3O,KAAAuN,qBAAAvN,KAAA6M,UAEA7M,KAAAmN,aAAArL,GACA4M,IACAC,EAAA3O,KAAAiP,gBACAP,GAAA,EACA1O,KAAAkP,YAEAf,EAAA3D,MAAA,EACA,KAAA,EACA2D,EAAAzD,KAAAvL,KAAA,CAAA,EAAA,EAAA,CAAA,KACAgP,EAAA3D,MAAA,EACA,KAAA,EAEA,MAAA,CAAA,EAAAiB,EAAAzL,KAAA0N,iBACA,KAAA,EAAA,MAAA,CAAA,EAAAS,EAAA1D,QACA,KAAA,EAEA,OADA0D,EAAA1D,OACA,KAAAkE,EACA,CAAA,EAAA,GAEA,CAAA,EAAA,GACA,KAAA,EAAA,MAAA,CAAA,EAAA,IACA,KAAA,EAEA,MADAG,EAAAX,EAAA1D,kBACAyB,GACA,MAAA4C,EAEA,MAAA,CAAA,EAAA,IACA,KAAA,GACA9O,KAAA6M,UAAA7M,KAAAhC,IACAmQ,EAAA3D,MAAA,GACA,KAAA,GAAA,MAAA,CAAA,EAAA,GACA,KAAA,GAAA,MAAA,CAAA,EAAA,IACA,KAAA,GAGA,OAFAuE,EAAAZ,EAAA1D,OACAuE,EAAA,CAA+BX,MAAAU,GAC/B,CAAA,EAAA,IACA,KAAA,GAEA,OADAZ,EAAAzD,KAAAvL,KAAA,CAAA,GAAA,CAAA,GAAA,KACA0P,IAAAA,EAAA9E,OAAArI,EAAAkN,EAAA9D,QACA,CAAA,EAAAW,EAAA/J,EAAArF,KAAAuS,KADA,CAAA,EAAA,IAEA,KAAA,GACAT,EAAA1D,OACA0D,EAAA3D,MAAA,GACA,KAAA,GAAA,MAAA,CAAA,EAAA,IACA,KAAA,GACA,GAAAwE,EAAA,MAAAA,EAAAX,MACA,MAAA,CAAA,GACA,KAAA,GAAA,MAAA,CAAA,GACA,KAAA,GAAA,MAAA,CAAA,WAKA/B,EAAAnQ,UAAAuR,aAAA,WACAyB,EAAA,OAAA,CACA,IAAArC,EAAA9M,KAAAoP,eACAhO,OAAA,EACA,GAAA0L,GAAA,IAEA1L,EAAA0L,EAAA,SAEA,GAAAA,EAAA,IACA,GAAAA,EAAA,IAEA1L,EAAA0L,OAEA,GAAAA,EAAA,IAAA,CAGA,GAAA,IADAvG,EAAAuG,EAAA,KACA,CACA9M,KAAAqP,aAAA9I,GACAvG,KAAAkP,WACA,SAAAC,EAGA/N,EAAA,QAGA,GAAA0L,EAAA,IAAA,CAGA,GAAA,IADAvG,EAAAuG,EAAA,KACA,CACA9M,KAAAsP,eAAA/I,GACAvG,KAAAkP,WACA,SAAAC,EAGA/N,EAAA,OAGA,CAEA,IAAArD,EAAA+O,EAAA,IACA1L,EAAApB,KAAAuP,iBAAAxR,EAAA,QAGA,GAAA,MAAA+O,EAEA1L,EAAA,UAEA,GAAA,MAAA0L,EAEA1L,GAAA,OAEA,GAAA,MAAA0L,EAEA1L,GAAA,OAEA,GAAA,MAAA0L,EAEA1L,EAAApB,KAAAwP,eAEA,GAAA,MAAA1C,EAEA1L,EAAApB,KAAAyP,eAEA,GAAA,MAAA3C,EAEA1L,EAAApB,KAAA0P,cAEA,GAAA,MAAA5C,EAEA1L,EAAApB,KAAA2P,eAEA,GAAA,MAAA7C,EAEA1L,EAAApB,KAAA4P,eAEA,GAAA,MAAA9C,EAEA1L,EAAApB,KAAA6P,eAEA,GAAA,MAAA/C,EAEA1L,EAAApB,KAAA8P,cAEA,GAAA,MAAAhD,EAEA1L,EAAApB,KAAA+P,eAEA,GAAA,MAAAjD,EAEA1L,EAAApB,KAAAgQ,eAEA,GAAA,MAAAlD,EAEA1L,EAAApB,KAAAiQ,eAEA,GAAA,MAAAnD,EAEA/O,EAAAiC,KAAAkQ,SACA9O,EAAApB,KAAAuP,iBAAAxR,EAAA,QAEA,GAAA,MAAA+O,EAEA/O,EAAAiC,KAAAmQ,UACA/O,EAAApB,KAAAuP,iBAAAxR,EAAA,QAEA,GAAA,MAAA+O,EAEA/O,EAAAiC,KAAAoQ,UACAhP,EAAApB,KAAAuP,iBAAAxR,EAAA,QAEA,GAAA,MAAA+O,EAAA,CAGA,GAAA,KADAvG,EAAAvG,KAAA2P,WACA,CACA3P,KAAAsP,eAAA/I,GACAvG,KAAAkP,WACA,SAAAC,EAGA/N,EAAA,QAGA,GAAA,MAAA0L,EAAA,CAGA,GAAA,KADAvG,EAAAvG,KAAA4P,WACA,CACA5P,KAAAsP,eAAA/I,GACAvG,KAAAkP,WACA,SAAAC,EAGA/N,EAAA,QAGA,GAAA,MAAA0L,EAAA,CAGA,GAAA,KADAvG,EAAAvG,KAAA2P,WACA,CACA3P,KAAAqP,aAAA9I,GACAvG,KAAAkP,WACA,SAAAC,EAGA/N,EAAA,QAGA,GAAA,MAAA0L,EAAA,CAGA,GAAA,KADAvG,EAAAvG,KAAA4P,WACA,CACA5P,KAAAqP,aAAA9I,GACAvG,KAAAkP,WACA,SAAAC,EAGA/N,EAAA,QAGA,GAAA,MAAA0L,EAAA,CAEA,IAAAvG,EAAAvG,KAAAkQ,SACA9O,EAAApB,KAAAqQ,aAAA9J,EAAA,QAEA,GAAA,MAAAuG,EAEAvG,EAAAvG,KAAAmQ,UACA/O,EAAApB,KAAAqQ,aAAA9J,EAAA,QAEA,GAAA,MAAAuG,EAEAvG,EAAAvG,KAAAoQ,UACAhP,EAAApB,KAAAqQ,aAAA9J,EAAA,QAEA,GAAA,MAAAuG,EAEA1L,EAAApB,KAAAsQ,gBAAA,EAAA,QAEA,GAAA,MAAAxD,EAEA1L,EAAApB,KAAAsQ,gBAAA,EAAA,QAEA,GAAA,MAAAxD,EAEA1L,EAAApB,KAAAsQ,gBAAA,EAAA,QAEA,GAAA,MAAAxD,EAEA1L,EAAApB,KAAAsQ,gBAAA,EAAA,QAEA,GAAA,MAAAxD,EAEA1L,EAAApB,KAAAsQ,gBAAA,GAAA,QAEA,GAAA,MAAAxD,EAEAvG,EAAAvG,KAAAkQ,SACA9O,EAAApB,KAAAsQ,gBAAA/J,EAAA,QAEA,GAAA,MAAAuG,EAEAvG,EAAAvG,KAAAmQ,UACA/O,EAAApB,KAAAsQ,gBAAA/J,EAAA,OAEA,CAAA,GAAA,MAAAuG,EAMA,MAAA,IAA0BnM,EAAW,2BAA8BgH,EAAUmF,IAJ7EvG,EAAAvG,KAAAoQ,UACAhP,EAAApB,KAAAsQ,gBAAA/J,EAAA,GAKAvG,KAAAkP,WAEA,IADA,IAAAnC,EAAA/M,KAAA+M,MACAA,EAAAjP,OAAA,GAAA,CAEA,IAAAyS,EAAAxD,EAAAA,EAAAjP,OAAA,GACA,GAAA,IAAAyS,EAAAzQ,KAAA,CAGA,GAFAyQ,EAAAC,MAAAD,EAAAE,UAAArP,EACAmP,EAAAE,WACAF,EAAAE,WAAAF,EAAAhK,KAKA,SAAA4I,EAJApC,EAAA5B,MACA/J,EAAAmP,EAAAC,UAMA,CAAA,GAAA,IAAAD,EAAAzQ,KAAA,CACA,GAjeA4Q,OAAAA,EACA,WADAA,SAieAtP,IAheA,WAAAsP,EAieA,MAAA,IAAkC/P,EAAW,uDAAAS,GAE7C,GAAA,cAAAA,EACA,MAAA,IAAkCT,EAAW,oCAE7C4P,EAAA5U,IAAAyF,EACAmP,EAAAzQ,KAAA,EACA,SAAAqP,EAMA,GAFAoB,EAAAI,IAAAJ,EAAA5U,KAAAyF,EACAmP,EAAAK,YACAL,EAAAK,YAAAL,EAAAhK,KAIA,CACAgK,EAAA5U,IAAA,KACA4U,EAAAzQ,KAAA,EACA,SAAAqP,EANApC,EAAA5B,MACA/J,EAAAmP,EAAAI,KASA,OAAAvP,EA3fA,IACAsP,GA6fApE,EAAAnQ,UAAAiT,aAAA,WAKA,OA/fA,IA2fApP,KAAA8M,WACA9M,KAAA8M,SAAA9M,KAAA0P,UAGA1P,KAAA8M,UAEAR,EAAAnQ,UAAA+S,SAAA,WACAlP,KAAA8M,UAlgBA,GAogBAR,EAAAnQ,UAAA8S,cAAA,WACA,IAAAnC,EAAA9M,KAAAoP,eACA,OAAAtC,GACA,KAAA,IACA,OAAA9M,KAAA2P,UACA,KAAA,IACA,OAAA3P,KAAA4P,UACA,QACA,GAAA9C,EAAA,IACA,OAAAA,EAAA,IAGA,MAAA,IAA8BnM,EAAW,iCAAoCgH,EAAUmF,MAKvFR,EAAAnQ,UAAAkT,aAAA,SAAA9I,GACA,GAAAA,EAAAvG,KAAA0M,aACA,MAAA,IAAsB/L,EAAW,oCAAA4F,EAAA,2BAAAvG,KAAA0M,aAAA,KAEjC1M,KAAA+M,MAAA5N,KAAA,CACAW,KAAA,EACAyG,KAAAA,EACA5K,IAAA,KACAiV,UAAA,EACAD,IAAA,MAGArE,EAAAnQ,UAAAmT,eAAA,SAAA/I,GACA,GAAAA,EAAAvG,KAAAyM,eACA,MAAA,IAAsB9L,EAAW,sCAAA4F,EAAA,uBAAAvG,KAAAyM,eAAA,KAEjCzM,KAAA+M,MAAA5N,KAAA,CACAW,KAAA,EACAyG,KAAAA,EACAiK,MAAA,IAAAnQ,MAAAkG,GACAkK,SAAA,KAGAnE,EAAAnQ,UAAAoT,iBAAA,SAAAxR,EAAA8S,GACA,IAAAnP,EACA,GAAA3D,EAAAiC,KAAAuM,aACA,MAAA,IAAsB5L,EAAW,2CAAA5C,EAAA,qBAAAiC,KAAAuM,aAAA,KAEjC,GAAAvM,KAAAnB,MAAAd,WAAAiC,KAAAhC,IAAA6S,EAAA9S,EACA,MAAAqO,EAEA,IACAhL,EADAvE,EAAAmD,KAAAhC,IAAA6S,EAYA,OATAzP,EADApB,KAAA8Q,kBAAA,QAAApP,EAAA1B,KAAA4M,kBAAA,IAAAlL,OAAA,EAAAA,EAAA2G,YAAAtK,IACAiC,KAAA4M,WAAAzK,OAAAnC,KAAAnB,MAAAhC,EAAAkB,GAEAA,EAA8B6B,ETlevB,SAAAf,EAAAC,EAAAf,GACP,IAAAgT,EAAAlS,EAAAJ,SAAAK,EAAAA,EAAAf,GACA,OAAA4B,EAAAwC,OAAA4O,GSieqBC,CAAYhR,KAAAnB,MAAAhC,EAAAkB,GAGZa,EAAYoB,KAAAnB,MAAAhC,EAAAkB,GAEjCiC,KAAAhC,KAAA6S,EAAA9S,EACAqD,GAEAkL,EAAAnQ,UAAA2U,cAAA,WACA,OAAA9Q,KAAA+M,MAAAjP,OAAA,GAEA,IADAkC,KAAA+M,MAAA/M,KAAA+M,MAAAjP,OAAA,GACAgC,MAIAwM,EAAAnQ,UAAAkU,aAAA,SAAAtS,EAAAkT,GACA,GAAAlT,EAAAiC,KAAAwM,aACA,MAAA,IAAsB7L,EAAW,oCAAA5C,EAAA,qBAAAiC,KAAAwM,aAAA,KAEjC,IAAAxM,KAAAoN,aAAArP,EAAAkT,GACA,MAAA7E,EAEA,IAAAvP,EAAAmD,KAAAhC,IAAAiT,EACA7P,EAAApB,KAAAnB,MAAAJ,SAAA5B,EAAAA,EAAAkB,GAEA,OADAiC,KAAAhC,KAAAiT,EAAAlT,EACAqD,GAEAkL,EAAAnQ,UAAAmU,gBAAA,SAAA/J,EAAA0K,GACA,GAAA1K,EAAAvG,KAAA2M,aACA,MAAA,IAAsBhM,EAAW,oCAAA4F,EAAA,qBAAAvG,KAAA2M,aAAA,KAEjC,IAAAuE,EAAAlR,KAAApD,KAAAuP,QAAAnM,KAAAhC,IAAAiT,GACAlR,EAAAC,KAAAqQ,aAAA9J,EAAA0K,EAAA,GACA,OAAAjR,KAAA0D,eAAAvB,OAAApC,EAAAmR,EAAAlR,KAAAgD,UAEAsJ,EAAAnQ,UAAA+T,OAAA,WACA,OAAAlQ,KAAApD,KAAAuU,SAAAnR,KAAAhC,MAEAsO,EAAAnQ,UAAAgU,QAAA,WACA,OAAAnQ,KAAApD,KAAAwU,UAAApR,KAAAhC,MAEAsO,EAAAnQ,UAAAiU,QAAA,WACA,OAAApQ,KAAApD,KAAAS,UAAA2C,KAAAhC,MAEAsO,EAAAnQ,UAAAuT,OAAA,WACA,IAAAjT,EAAAuD,KAAApD,KAAAuU,SAAAnR,KAAAhC,KAEA,OADAgC,KAAAhC,MACAvB,GAEA6P,EAAAnQ,UAAA2T,OAAA,WACA,IAAArT,EAAAuD,KAAApD,KAAAuP,QAAAnM,KAAAhC,KAEA,OADAgC,KAAAhC,MACAvB,GAEA6P,EAAAnQ,UAAAwT,QAAA,WACA,IAAAlT,EAAAuD,KAAApD,KAAAwU,UAAApR,KAAAhC,KAEA,OADAgC,KAAAhC,KAAA,EACAvB,GAEA6P,EAAAnQ,UAAA4T,QAAA,WACA,IAAAtT,EAAAuD,KAAApD,KAAAyU,SAAArR,KAAAhC,KAEA,OADAgC,KAAAhC,KAAA,EACAvB,GAEA6P,EAAAnQ,UAAAyT,QAAA,WACA,IAAAnT,EAAAuD,KAAApD,KAAAS,UAAA2C,KAAAhC,KAEA,OADAgC,KAAAhC,KAAA,EACAvB,GAEA6P,EAAAnQ,UAAA6T,QAAA,WACA,IAAAvT,EAAAuD,KAAApD,KAAAQ,SAAA4C,KAAAhC,KAEA,OADAgC,KAAAhC,KAAA,EACAvB,GAEA6P,EAAAnQ,UAAA0T,QAAA,WACA,IVlrBOjT,EAAAC,EUkrBPJ,GVlrBOG,EUkrBsBoD,KAAApD,KVlrBtBC,EUkrBsBmD,KAAAhC,IV/qB7B,WAFApB,EAAAS,UAAAR,GACAD,EAAAS,UAAAR,EAAA,IUkrBA,OADAmD,KAAAhC,KAAA,EACAvB,GAEA6P,EAAAnQ,UAAA8T,QAAA,WACA,IAAAxT,EAAoBU,EAAQ6C,KAAApD,KAAAoD,KAAAhC,KAE5B,OADAgC,KAAAhC,KAAA,EACAvB,GAEA6P,EAAAnQ,UAAAqT,QAAA,WACA,IAAA/S,EAAAuD,KAAApD,KAAA0U,WAAAtR,KAAAhC,KAEA,OADAgC,KAAAhC,KAAA,EACAvB,GAEA6P,EAAAnQ,UAAAsT,QAAA,WACA,IAAAhT,EAAAuD,KAAApD,KAAA2U,WAAAvR,KAAAhC,KAEA,OADAgC,KAAAhC,KAAA,EACAvB,GAEA6P,EAtoBA,UC/EO,MAAMkF,EAKFC,aAAalT,GAChB,IAAIgI,EAAOhI,EAAOR,YAAcQ,EAAOT,OACvC,MAAM4T,EAAY,GAClB,EAAG,CACC,IAAIC,EAAkB,IAAPpL,EACfA,IAAe,EACXA,EAAO,IACPoL,GAAY,KAEhBD,EAAUvS,KAAKwS,SAEZpL,EAAO,GAEdA,EAAOhI,EAAOR,YAAcQ,EAAOT,OAEnC,MAAMgE,EAAS,IAAIF,WAAW8P,EAAU5T,OAASyI,GAGjD,OAFAzE,EAAOpD,IAAIgT,EAAW,GACtB5P,EAAOpD,IAAIH,EAAQmT,EAAU5T,QACtBgE,EAAOA,OAGX2P,aAAaG,GAChB,MAAM3S,EAAuB,GACvB4S,EAAa,IAAIjQ,WAAWgQ,GAE5BE,EAAiB,CAAC,EAAG,EAAG,GAAI,GAAI,IAEtC,IAAK,IAAIjV,EAAS,EAAGA,EAAS+U,EAAM7T,YAAa,CAC7C,IAEIgU,EAFAC,EAAW,EACXzL,EAAO,EAEX,GACIwL,EAAWF,EAAWhV,EAASmV,GAC/BzL,IAA2B,IAAXwL,IAAqBD,EAAeE,GACpDA,UAEGA,EAAWjV,KAAKkV,IAZC,EAYwBL,EAAM7T,WAAalB,IAAiC,IAAV,IAAXkV,IAE/E,GAA0B,IAAV,IAAXA,IAA0BC,EAdP,EAepB,MAAM,IAAI9Q,MAAM,6BAGpB,GAlBwB,IAkBpB8Q,GAAoCD,EAAW,EAC/C,MAAM,IAAI7Q,MAAM,+CAGpB,KAAI2Q,EAAW9T,YAAelB,EAASmV,EAAWzL,GAM9C,MAAM,IAAIrF,MAAM,uBAJhBjC,EAAOE,KAAK0S,EAAW5I,MACjB4I,EAAW5I,MAAMpM,EAASmV,EAAUnV,EAASmV,EAAWzL,GACxDsL,EAAWpT,SAAS5B,EAASmV,EAAUnV,EAASmV,EAAWzL,IAKrE1J,EAASA,EAASmV,EAAWzL,EAGjC,OAAOtH,GChDf,MAAMiT,EAAsC,IAAItQ,WAAW,CAAC,IAAMuQ,EAAAC,YAAAC,OAG3D,MAAMC,EAmBTb,YAAYc,GAjBIvS,KAAAiB,KAAe,cAEfjB,KAAAwS,QAAkB,EAElBxS,KAAAyS,eAAiCN,EAAAO,eAAAC,OAEhC3S,KAAA4S,aAAe,EACf5S,KAAA6S,YAAc,EACd7S,KAAA8S,eAAiB,EAU9BP,EAAqBA,GAAsB,GAC3CvS,KAAK+S,SAAW,IAAItP,EAChB8O,EAAmB7O,eACnB6O,EAAmBvP,QACnBuP,EAAmB5O,SACnB4O,EAAmB3O,kBACnB2O,EAAmB1O,SACnB0O,EAAmBzO,aACnByO,EAAmBxO,gBACnBwO,EAAmBvO,qBAGvBhE,KAAKgT,SAAW,IAAI1G,EAChBiG,EAAmB7O,eACnB6O,EAAmBvP,QACnBuP,EAAmBhG,aACnBgG,EAAmB/F,aACnB+F,EAAmB9F,eACnB8F,EAAmB7F,aACnB6F,EAAmB5F,cASpB8E,cAAcG,EAAoBqB,GAErC,KCpEsBC,EDoEFtB,ICnEa,oBAAhBtO,eAChB4P,aAAe5P,aAEf4P,EAAIzS,aAAwC,gBAAzByS,EAAIzS,YAAYQ,MDiEhC,MAAM,IAAIC,MAAM,wECrErB,IAAuBgS,EDwEP,OAAXD,IACAA,EAASd,EAAAgB,WAAAC,UAGb,MAAMC,EAAW7B,EAAA8B,MAA0B1B,GAErC2B,EAAc,GACpB,IAAK,MAAM1S,KAAWwS,EAAU,CAC5B,MAAMG,EAAgBxT,KAAKyT,cAAc5S,EAASoS,GAE9CO,GACAD,EAAYpU,KAAKqU,GAIzB,OAAOD,EAQJ9B,aAAa5Q,GAChB,OAAQA,EAAQf,MACZ,KAAKqS,EAAAC,YAAAsB,WACD,OAAO1T,KAAK2T,iBAAiB9S,GACjC,KAAKsR,EAAAC,YAAAwB,iBACD,OAAO5T,KAAK6T,uBAAuBhT,GACvC,KAAKsR,EAAAC,YAAA0B,WACD,OAAO9T,KAAK+T,iBAAiBlT,GACjC,KAAKsR,EAAAC,YAAA4B,WACD,OAAOhU,KAAKiU,iBAAiBpT,GACjC,KAAKsR,EAAAC,YAAAC,KACD,OAAOb,EAAA0C,MAA0BhC,GACrC,KAAKC,EAAAC,YAAA+B,iBACD,OAAOnU,KAAKoU,uBAAuBvT,GACvC,QACI,MAAM,IAAIK,MAAM,0BAIpBuQ,cAAcG,EAAmBqB,GACrC,GAAqB,IAAjBrB,EAAM9T,OACN,MAAM,IAAIoD,MAAM,oBAGpB,MAAMmT,EAAarU,KAAKgT,SAAS7Q,OAAOyP,GACxC,GAA0B,IAAtByC,EAAWvW,UAAkBuW,aAAsBhU,OACnD,MAAM,IAAIa,MAAM,oBAGpB,MAAMoT,EAAcD,EAAW,GAE/B,OAAQC,GACJ,KAAKnC,EAAAC,YAAAsB,WACD,OAAO1T,KAAKuU,yBAAyBvU,KAAKwU,aAAaH,GAAaA,GACxE,KAAKlC,EAAAC,YAAA0B,WACD,OAAO9T,KAAKyU,yBAAyBzU,KAAKwU,aAAaH,GAAaA,GACxE,KAAKlC,EAAAC,YAAA4B,WACD,OAAOhU,KAAK0U,yBAAyB1U,KAAKwU,aAAaH,GAAaA,GACxE,KAAKlC,EAAAC,YAAAC,KACD,OAAOrS,KAAK2U,mBAAmBN,GACnC,KAAKlC,EAAAC,YAAAwC,MACD,OAAO5U,KAAK6U,oBAAoBR,GACpC,QAGI,OADApB,EAAO6B,IAAI3C,EAAA4C,SAAAC,YAAsB,yBAA2BV,EAAc,cACnE,MAIX7C,oBAAoB4C,GAExB,GAAIA,EAAWvW,OAAS,EACpB,MAAM,IAAIoD,MAAM,sCAGpB,MAAO,CAEH+T,eAAgBZ,EAAWvW,QAAU,EAAIuW,EAAW,QAAK9Y,EACzD8S,MAAOgG,EAAW,GAClBvU,KAAMqS,EAAAC,YAAAwC,OAINnD,mBAAmB4C,GAEvB,GAAIA,EAAWvW,OAAS,EACpB,MAAM,IAAIoD,MAAM,qCAGpB,MAAO,CAEHpB,KAAMqS,EAAAC,YAAAC,MAINZ,yBAAyByD,EAAyBb,GAEtD,GAAIA,EAAWvW,OAAS,EACpB,MAAM,IAAIoD,MAAM,2CAGpB,MAAMiU,EAAed,EAAW,GAChC,OAAIc,EACO,CACH1G,UAAW4F,EAAW,GACtBa,QAAAA,EACAC,aAAAA,EACAC,UAAW,GACXC,OAAQhB,EAAW,GACnBvU,KAAMqS,EAAAC,YAAAsB,YAGH,CACHjF,UAAW4F,EAAW,GACtBa,QAAAA,EACAE,UAAW,GACXC,OAAQhB,EAAW,GACnBvU,KAAMqS,EAAAC,YAAAsB,YAMVjC,yBAAyByD,EAAyBb,GAEtD,GAAIA,EAAWvW,OAAS,EACpB,MAAM,IAAIoD,MAAM,2CAGpB,MAAO,CACHgU,QAAAA,EACAC,aAAcd,EAAW,GACzB1N,KAAM0N,EAAW,GACjBvU,KAAMqS,EAAAC,YAAA0B,YAINrC,yBAAyByD,EAAyBb,GAEtD,GAAIA,EAAWvW,OAAS,EACpB,MAAM,IAAIoD,MAAM,2CAGpB,MAAMoU,EAAajB,EAAW,GAE9B,GAAIiB,IAAetV,KAAK6S,aAAewB,EAAWvW,OAAS,EACvD,MAAM,IAAIoD,MAAM,2CAGpB,IAAImN,EACApP,EAEJ,OAAQqW,GACJ,KAAKtV,KAAK4S,aACNvE,EAAQgG,EAAW,GACnB,MACJ,KAAKrU,KAAK8S,eACN7T,EAASoV,EAAW,GAY5B,MAR6C,CACzChG,MAAAA,EACA6G,QAAAA,EACAC,aAAcd,EAAW,GACzBpV,OAAAA,EACAa,KAAMqS,EAAAC,YAAA4B,YAMNvC,iBAAiB8D,GACrB,IAAIC,EASJ,OAPIA,EADAD,EAAkBH,UACRpV,KAAK+S,SAASpU,OAAO,CAACwT,EAAAC,YAAAsB,WAAwB6B,EAAkBL,SAAW,GAAIK,EAAkBJ,cAAgB,KAC3HI,EAAkBF,OAAQE,EAAkB9G,UAAW8G,EAAkBH,YAE/DpV,KAAK+S,SAASpU,OAAO,CAACwT,EAAAC,YAAAsB,WAAwB6B,EAAkBL,SAAW,GAAIK,EAAkBJ,cAAgB,KAC3HI,EAAkBF,OAAQE,EAAkB9G,YAGzC+C,EAAA0C,MAA0BsB,EAAQvM,SAGrCwI,uBAAuBgE,GAC3B,IAAID,EASJ,OAPIA,EADAC,EAAwBL,UACdpV,KAAK+S,SAASpU,OAAO,CAACwT,EAAAC,YAAAwB,iBAA8B6B,EAAwBP,SAAW,GAAIO,EAAwBN,aAC7HM,EAAwBJ,OAAQI,EAAwBhH,UAAWgH,EAAwBL,YAEjFpV,KAAK+S,SAASpU,OAAO,CAACwT,EAAAC,YAAAwB,iBAA8B6B,EAAwBP,SAAW,GAAIO,EAAwBN,aAC7HM,EAAwBJ,OAAQI,EAAwBhH,YAGrD+C,EAAA0C,MAA0BsB,EAAQvM,SAGrCwI,iBAAiBiE,GACrB,MAAMF,EAAUxV,KAAK+S,SAASpU,OAAO,CAACwT,EAAAC,YAAA0B,WAAwB4B,EAAkBR,SAAW,GAAIQ,EAAkBP,aACjHO,EAAkB/O,OAElB,OAAO6K,EAAA0C,MAA0BsB,EAAQvM,SAGrCwI,iBAAiBkE,GACrB,MAAML,EAAaK,EAAkBtH,MAAQrO,KAAK4S,aAAe+C,EAAkB1W,OAASe,KAAK8S,eAAiB9S,KAAK6S,YAEvH,IAAI2C,EACJ,OAAQF,GACJ,KAAKtV,KAAK4S,aACN4C,EAAUxV,KAAK+S,SAASpU,OAAO,CAACwT,EAAAC,YAAA4B,WAAwB2B,EAAkBT,SAAW,GAAIS,EAAkBR,aAAcG,EAAYK,EAAkBtH,QACvJ,MACJ,KAAKrO,KAAK6S,YACN2C,EAAUxV,KAAK+S,SAASpU,OAAO,CAACwT,EAAAC,YAAA4B,WAAwB2B,EAAkBT,SAAW,GAAIS,EAAkBR,aAAcG,IACzH,MACJ,KAAKtV,KAAK8S,eACN0C,EAAUxV,KAAK+S,SAASpU,OAAO,CAACwT,EAAAC,YAAA4B,WAAwB2B,EAAkBT,SAAW,GAAIS,EAAkBR,aAAcG,EAAYK,EAAkB1W,SAI/J,OAAOuS,EAAA0C,MAA0BsB,EAAQvM,SAGrCwI,uBAAuBmE,GAC3B,MAAMJ,EAAUxV,KAAK+S,SAASpU,OAAO,CAACwT,EAAAC,YAAA+B,iBAA8ByB,EAAwBV,SAAW,GAAIU,EAAwBT,eAEnI,OAAO3D,EAAA0C,MAA0BsB,EAAQvM,SAGrCwI,aAAa4C,GACjB,MAAMa,EAA0Bb,EAAW,GAC3C,GAAuB,iBAAZa,EACP,MAAM,IAAIhU,MAAM,oBAEpB,OAAOgU,GEtTR,MAAMW,EAAU,6BpBJvB,iBAAA3a,SAAA,iBAAAD,OACAA,OAAAC,QAAAJ,EAAAgb,QAAA,YACA,mBAAAC,QAAAA,OAAAC,IACAD,OAAA,CAAA,WAAAjb,GACA,iBAAAI,QACAA,QAAA,QAAAJ,EAAAgb,QAAA,aAEAjb,EAAA,QAAAA,EAAA,SAAA,GAAyCA,EAAA,QAAA,UAAAA,EAAA,QAAA,WAAA,GAAmEA,EAAA,QAAA,UAAA,QAAAC,EAAAD,EAAA", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"signalR\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"signalR\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"msgpack\"] = factory(require(\"signalR\"));\n\telse\n\t\troot[\"signalR\"] = root[\"signalR\"] || {}, root[\"signalR\"][\"protocols\"] = root[\"signalR\"][\"protocols\"] || {}, root[\"signalR\"][\"protocols\"][\"msgpack\"] = factory(root[\"signalR\"]);\n})(self, function(__WEBPACK_EXTERNAL_MODULE__1__) {\nreturn ", "module.exports = __WEBPACK_EXTERNAL_MODULE__1__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Integer Utility\nexport var UINT32_MAX = 4294967295;\n// DataView extension to handle int64 / uint64,\n// where the actual range is 53-bits integer (a.k.a. safe integer)\nexport function setUint64(view, offset, value) {\n    var high = value / 4294967296;\n    var low = value; // high bits are truncated by DataView\n    view.setUint32(offset, high);\n    view.setUint32(offset + 4, low);\n}\nexport function setInt64(view, offset, value) {\n    var high = Math.floor(value / 4294967296);\n    var low = value; // high bits are truncated by DataView\n    view.setUint32(offset, high);\n    view.setUint32(offset + 4, low);\n}\nexport function getInt64(view, offset) {\n    var high = view.getInt32(offset);\n    var low = view.getUint32(offset + 4);\n    return high * 4294967296 + low;\n}\nexport function getUint64(view, offset) {\n    var high = view.getUint32(offset);\n    var low = view.getUint32(offset + 4);\n    return high * 4294967296 + low;\n}\n//# sourceMappingURL=int.mjs.map", "import { UINT32_MAX } from \"./int.mjs\";\nvar TEXT_ENCODING_AVAILABLE = (typeof process === \"undefined\" || process.env[\"TEXT_ENCODING\"] !== \"never\") &&\n    typeof TextEncoder !== \"undefined\" &&\n    typeof TextDecoder !== \"undefined\";\nexport function utf8Count(str) {\n    var strLength = str.length;\n    var byteLength = 0;\n    var pos = 0;\n    while (pos < strLength) {\n        var value = str.charCodeAt(pos++);\n        if ((value & 0xffffff80) === 0) {\n            // 1-byte\n            byteLength++;\n            continue;\n        }\n        else if ((value & 0xfffff800) === 0) {\n            // 2-bytes\n            byteLength += 2;\n        }\n        else {\n            // handle surrogate pair\n            if (value >= 0xd800 && value <= 0xdbff) {\n                // high surrogate\n                if (pos < strLength) {\n                    var extra = str.charCodeAt(pos);\n                    if ((extra & 0xfc00) === 0xdc00) {\n                        ++pos;\n                        value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n                    }\n                }\n            }\n            if ((value & 0xffff0000) === 0) {\n                // 3-byte\n                byteLength += 3;\n            }\n            else {\n                // 4-byte\n                byteLength += 4;\n            }\n        }\n    }\n    return byteLength;\n}\nexport function utf8EncodeJs(str, output, outputOffset) {\n    var strLength = str.length;\n    var offset = outputOffset;\n    var pos = 0;\n    while (pos < strLength) {\n        var value = str.charCodeAt(pos++);\n        if ((value & 0xffffff80) === 0) {\n            // 1-byte\n            output[offset++] = value;\n            continue;\n        }\n        else if ((value & 0xfffff800) === 0) {\n            // 2-bytes\n            output[offset++] = ((value >> 6) & 0x1f) | 0xc0;\n        }\n        else {\n            // handle surrogate pair\n            if (value >= 0xd800 && value <= 0xdbff) {\n                // high surrogate\n                if (pos < strLength) {\n                    var extra = str.charCodeAt(pos);\n                    if ((extra & 0xfc00) === 0xdc00) {\n                        ++pos;\n                        value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n                    }\n                }\n            }\n            if ((value & 0xffff0000) === 0) {\n                // 3-byte\n                output[offset++] = ((value >> 12) & 0x0f) | 0xe0;\n                output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n            }\n            else {\n                // 4-byte\n                output[offset++] = ((value >> 18) & 0x07) | 0xf0;\n                output[offset++] = ((value >> 12) & 0x3f) | 0x80;\n                output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n            }\n        }\n        output[offset++] = (value & 0x3f) | 0x80;\n    }\n}\nvar sharedTextEncoder = TEXT_ENCODING_AVAILABLE ? new TextEncoder() : undefined;\nexport var TEXT_ENCODER_THRESHOLD = !TEXT_ENCODING_AVAILABLE\n    ? UINT32_MAX\n    : typeof process !== \"undefined\" && process.env[\"TEXT_ENCODING\"] !== \"force\"\n        ? 200\n        : 0;\nfunction utf8EncodeTEencode(str, output, outputOffset) {\n    output.set(sharedTextEncoder.encode(str), outputOffset);\n}\nfunction utf8EncodeTEencodeInto(str, output, outputOffset) {\n    sharedTextEncoder.encodeInto(str, output.subarray(outputOffset));\n}\nexport var utf8EncodeTE = (sharedTextEncoder === null || sharedTextEncoder === void 0 ? void 0 : sharedTextEncoder.encodeInto) ? utf8EncodeTEencodeInto : utf8EncodeTEencode;\nvar CHUNK_SIZE = 4096;\nexport function utf8DecodeJs(bytes, inputOffset, byteLength) {\n    var offset = inputOffset;\n    var end = offset + byteLength;\n    var units = [];\n    var result = \"\";\n    while (offset < end) {\n        var byte1 = bytes[offset++];\n        if ((byte1 & 0x80) === 0) {\n            // 1 byte\n            units.push(byte1);\n        }\n        else if ((byte1 & 0xe0) === 0xc0) {\n            // 2 bytes\n            var byte2 = bytes[offset++] & 0x3f;\n            units.push(((byte1 & 0x1f) << 6) | byte2);\n        }\n        else if ((byte1 & 0xf0) === 0xe0) {\n            // 3 bytes\n            var byte2 = bytes[offset++] & 0x3f;\n            var byte3 = bytes[offset++] & 0x3f;\n            units.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3);\n        }\n        else if ((byte1 & 0xf8) === 0xf0) {\n            // 4 bytes\n            var byte2 = bytes[offset++] & 0x3f;\n            var byte3 = bytes[offset++] & 0x3f;\n            var byte4 = bytes[offset++] & 0x3f;\n            var unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4;\n            if (unit > 0xffff) {\n                unit -= 0x10000;\n                units.push(((unit >>> 10) & 0x3ff) | 0xd800);\n                unit = 0xdc00 | (unit & 0x3ff);\n            }\n            units.push(unit);\n        }\n        else {\n            units.push(byte1);\n        }\n        if (units.length >= CHUNK_SIZE) {\n            result += String.fromCharCode.apply(String, units);\n            units.length = 0;\n        }\n    }\n    if (units.length > 0) {\n        result += String.fromCharCode.apply(String, units);\n    }\n    return result;\n}\nvar sharedTextDecoder = TEXT_ENCODING_AVAILABLE ? new TextDecoder() : null;\nexport var TEXT_DECODER_THRESHOLD = !TEXT_ENCODING_AVAILABLE\n    ? UINT32_MAX\n    : typeof process !== \"undefined\" && process.env[\"TEXT_DECODER\"] !== \"force\"\n        ? 200\n        : 0;\nexport function utf8DecodeTD(bytes, inputOffset, byteLength) {\n    var stringBytes = bytes.subarray(inputOffset, inputOffset + byteLength);\n    return sharedTextDecoder.decode(stringBytes);\n}\n//# sourceMappingURL=utf8.mjs.map", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar DecodeError = /** @class */ (function (_super) {\n    __extends(DecodeError, _super);\n    function DecodeError(message) {\n        var _this = _super.call(this, message) || this;\n        // fix the prototype chain in a cross-platform way\n        var proto = Object.create(DecodeError.prototype);\n        Object.setPrototypeOf(_this, proto);\n        Object.defineProperty(_this, \"name\", {\n            configurable: true,\n            enumerable: false,\n            value: DecodeError.name,\n        });\n        return _this;\n    }\n    return DecodeError;\n}(Error));\nexport { DecodeError };\n//# sourceMappingURL=DecodeError.mjs.map", "/**\n * ExtData is used to handle Extension Types that are not registered to ExtensionCodec.\n */\nvar ExtData = /** @class */ (function () {\n    function ExtData(type, data) {\n        this.type = type;\n        this.data = data;\n    }\n    return ExtData;\n}());\nexport { ExtData };\n//# sourceMappingURL=ExtData.mjs.map", "// https://github.com/msgpack/msgpack/blob/master/spec.md#timestamp-extension-type\nimport { DecodeError } from \"./DecodeError.mjs\";\nimport { getInt64, setInt64 } from \"./utils/int.mjs\";\nexport var EXT_TIMESTAMP = -1;\nvar TIMESTAMP32_MAX_SEC = 0x100000000 - 1; // 32-bit unsigned int\nvar TIMESTAMP64_MAX_SEC = 0x400000000 - 1; // 34-bit unsigned int\nexport function encodeTimeSpecToTimestamp(_a) {\n    var sec = _a.sec, nsec = _a.nsec;\n    if (sec >= 0 && nsec >= 0 && sec <= TIMESTAMP64_MAX_SEC) {\n        // Here sec >= 0 && nsec >= 0\n        if (nsec === 0 && sec <= TIMESTAMP32_MAX_SEC) {\n            // timestamp 32 = { sec32 (unsigned) }\n            var rv = new Uint8Array(4);\n            var view = new DataView(rv.buffer);\n            view.setUint32(0, sec);\n            return rv;\n        }\n        else {\n            // timestamp 64 = { nsec30 (unsigned), sec34 (unsigned) }\n            var secHigh = sec / 0x100000000;\n            var secLow = sec & 0xffffffff;\n            var rv = new Uint8Array(8);\n            var view = new DataView(rv.buffer);\n            // nsec30 | secHigh2\n            view.setUint32(0, (nsec << 2) | (secHigh & 0x3));\n            // secLow32\n            view.setUint32(4, secLow);\n            return rv;\n        }\n    }\n    else {\n        // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n        var rv = new Uint8Array(12);\n        var view = new DataView(rv.buffer);\n        view.setUint32(0, nsec);\n        setInt64(view, 4, sec);\n        return rv;\n    }\n}\nexport function encodeDateToTimeSpec(date) {\n    var msec = date.getTime();\n    var sec = Math.floor(msec / 1e3);\n    var nsec = (msec - sec * 1e3) * 1e6;\n    // Normalizes { sec, nsec } to ensure nsec is unsigned.\n    var nsecInSec = Math.floor(nsec / 1e9);\n    return {\n        sec: sec + nsecInSec,\n        nsec: nsec - nsecInSec * 1e9,\n    };\n}\nexport function encodeTimestampExtension(object) {\n    if (object instanceof Date) {\n        var timeSpec = encodeDateToTimeSpec(object);\n        return encodeTimeSpecToTimestamp(timeSpec);\n    }\n    else {\n        return null;\n    }\n}\nexport function decodeTimestampToTimeSpec(data) {\n    var view = new DataView(data.buffer, data.byteOffset, data.byteLength);\n    // data may be 32, 64, or 96 bits\n    switch (data.byteLength) {\n        case 4: {\n            // timestamp 32 = { sec32 }\n            var sec = view.getUint32(0);\n            var nsec = 0;\n            return { sec: sec, nsec: nsec };\n        }\n        case 8: {\n            // timestamp 64 = { nsec30, sec34 }\n            var nsec30AndSecHigh2 = view.getUint32(0);\n            var secLow32 = view.getUint32(4);\n            var sec = (nsec30AndSecHigh2 & 0x3) * 0x100000000 + secLow32;\n            var nsec = nsec30AndSecHigh2 >>> 2;\n            return { sec: sec, nsec: nsec };\n        }\n        case 12: {\n            // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n            var sec = getInt64(view, 4);\n            var nsec = view.getUint32(0);\n            return { sec: sec, nsec: nsec };\n        }\n        default:\n            throw new DecodeError(\"Unrecognized data size for timestamp (expected 4, 8, or 12): \" + data.length);\n    }\n}\nexport function decodeTimestampExtension(data) {\n    var timeSpec = decodeTimestampToTimeSpec(data);\n    return new Date(timeSpec.sec * 1e3 + timeSpec.nsec / 1e6);\n}\nexport var timestampExtension = {\n    type: EXT_TIMESTAMP,\n    encode: encodeTimestampExtension,\n    decode: decodeTimestampExtension,\n};\n//# sourceMappingURL=timestamp.mjs.map", "// ExtensionCodec to handle MessagePack extensions\nimport { ExtData } from \"./ExtData.mjs\";\nimport { timestampExtension } from \"./timestamp.mjs\";\nvar ExtensionCodec = /** @class */ (function () {\n    function ExtensionCodec() {\n        // built-in extensions\n        this.builtInEncoders = [];\n        this.builtInDecoders = [];\n        // custom extensions\n        this.encoders = [];\n        this.decoders = [];\n        this.register(timestampExtension);\n    }\n    ExtensionCodec.prototype.register = function (_a) {\n        var type = _a.type, encode = _a.encode, decode = _a.decode;\n        if (type >= 0) {\n            // custom extensions\n            this.encoders[type] = encode;\n            this.decoders[type] = decode;\n        }\n        else {\n            // built-in extensions\n            var index = 1 + type;\n            this.builtInEncoders[index] = encode;\n            this.builtInDecoders[index] = decode;\n        }\n    };\n    ExtensionCodec.prototype.tryToEncode = function (object, context) {\n        // built-in extensions\n        for (var i = 0; i < this.builtInEncoders.length; i++) {\n            var encodeExt = this.builtInEncoders[i];\n            if (encodeExt != null) {\n                var data = encodeExt(object, context);\n                if (data != null) {\n                    var type = -1 - i;\n                    return new ExtData(type, data);\n                }\n            }\n        }\n        // custom extensions\n        for (var i = 0; i < this.encoders.length; i++) {\n            var encodeExt = this.encoders[i];\n            if (encodeExt != null) {\n                var data = encodeExt(object, context);\n                if (data != null) {\n                    var type = i;\n                    return new ExtData(type, data);\n                }\n            }\n        }\n        if (object instanceof ExtData) {\n            // to keep ExtData as is\n            return object;\n        }\n        return null;\n    };\n    ExtensionCodec.prototype.decode = function (data, type, context) {\n        var decodeExt = type < 0 ? this.builtInDecoders[-1 - type] : this.decoders[type];\n        if (decodeExt) {\n            return decodeExt(data, type, context);\n        }\n        else {\n            // decode() does not fail, returns ExtData instead.\n            return new ExtData(type, data);\n        }\n    };\n    ExtensionCodec.defaultCodec = new ExtensionCodec();\n    return ExtensionCodec;\n}());\nexport { ExtensionCodec };\n//# sourceMappingURL=ExtensionCodec.mjs.map", "export function ensureUint8Array(buffer) {\n    if (buffer instanceof Uint8Array) {\n        return buffer;\n    }\n    else if (ArrayBuffer.isView(buffer)) {\n        return new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n    }\n    else if (buffer instanceof ArrayBuffer) {\n        return new Uint8Array(buffer);\n    }\n    else {\n        // ArrayLike<number>\n        return Uint8Array.from(buffer);\n    }\n}\nexport function createDataView(buffer) {\n    if (buffer instanceof ArrayBuffer) {\n        return new DataView(buffer);\n    }\n    var bufferView = ensureUint8Array(buffer);\n    return new DataView(bufferView.buffer, bufferView.byteOffset, bufferView.byteLength);\n}\n//# sourceMappingURL=typedArrays.mjs.map", "import { utf8EncodeJs, utf8Count, TEXT_ENCODER_THRESHOLD, utf8EncodeTE } from \"./utils/utf8.mjs\";\nimport { ExtensionCodec } from \"./ExtensionCodec.mjs\";\nimport { setInt64, setUint64 } from \"./utils/int.mjs\";\nimport { ensureUint8Array } from \"./utils/typedArrays.mjs\";\nexport var DEFAULT_MAX_DEPTH = 100;\nexport var DEFAULT_INITIAL_BUFFER_SIZE = 2048;\nvar Encoder = /** @class */ (function () {\n    function Encoder(extensionCodec, context, maxDepth, initialBufferSize, sortKeys, forceFloat32, ignoreUndefined, forceIntegerToFloat) {\n        if (extensionCodec === void 0) { extensionCodec = ExtensionCodec.defaultCodec; }\n        if (context === void 0) { context = undefined; }\n        if (maxDepth === void 0) { maxDepth = DEFAULT_MAX_DEPTH; }\n        if (initialBufferSize === void 0) { initialBufferSize = DEFAULT_INITIAL_BUFFER_SIZE; }\n        if (sortKeys === void 0) { sortKeys = false; }\n        if (forceFloat32 === void 0) { forceFloat32 = false; }\n        if (ignoreUndefined === void 0) { ignoreUndefined = false; }\n        if (forceIntegerToFloat === void 0) { forceIntegerToFloat = false; }\n        this.extensionCodec = extensionCodec;\n        this.context = context;\n        this.maxDepth = maxDepth;\n        this.initialBufferSize = initialBufferSize;\n        this.sortKeys = sortKeys;\n        this.forceFloat32 = forceFloat32;\n        this.ignoreUndefined = ignoreUndefined;\n        this.forceIntegerToFloat = forceIntegerToFloat;\n        this.pos = 0;\n        this.view = new DataView(new ArrayBuffer(this.initialBufferSize));\n        this.bytes = new Uint8Array(this.view.buffer);\n    }\n    Encoder.prototype.getUint8Array = function () {\n        return this.bytes.subarray(0, this.pos);\n    };\n    Encoder.prototype.reinitializeState = function () {\n        this.pos = 0;\n    };\n    Encoder.prototype.encode = function (object) {\n        this.reinitializeState();\n        this.doEncode(object, 1);\n        return this.getUint8Array();\n    };\n    Encoder.prototype.doEncode = function (object, depth) {\n        if (depth > this.maxDepth) {\n            throw new Error(\"Too deep objects in depth \" + depth);\n        }\n        if (object == null) {\n            this.encodeNil();\n        }\n        else if (typeof object === \"boolean\") {\n            this.encodeBoolean(object);\n        }\n        else if (typeof object === \"number\") {\n            this.encodeNumber(object);\n        }\n        else if (typeof object === \"string\") {\n            this.encodeString(object);\n        }\n        else {\n            this.encodeObject(object, depth);\n        }\n    };\n    Encoder.prototype.ensureBufferSizeToWrite = function (sizeToWrite) {\n        var requiredSize = this.pos + sizeToWrite;\n        if (this.view.byteLength < requiredSize) {\n            this.resizeBuffer(requiredSize * 2);\n        }\n    };\n    Encoder.prototype.resizeBuffer = function (newSize) {\n        var newBuffer = new ArrayBuffer(newSize);\n        var newBytes = new Uint8Array(newBuffer);\n        var newView = new DataView(newBuffer);\n        newBytes.set(this.bytes);\n        this.view = newView;\n        this.bytes = newBytes;\n    };\n    Encoder.prototype.encodeNil = function () {\n        this.writeU8(0xc0);\n    };\n    Encoder.prototype.encodeBoolean = function (object) {\n        if (object === false) {\n            this.writeU8(0xc2);\n        }\n        else {\n            this.writeU8(0xc3);\n        }\n    };\n    Encoder.prototype.encodeNumber = function (object) {\n        if (Number.isSafeInteger(object) && !this.forceIntegerToFloat) {\n            if (object >= 0) {\n                if (object < 0x80) {\n                    // positive fixint\n                    this.writeU8(object);\n                }\n                else if (object < 0x100) {\n                    // uint 8\n                    this.writeU8(0xcc);\n                    this.writeU8(object);\n                }\n                else if (object < 0x10000) {\n                    // uint 16\n                    this.writeU8(0xcd);\n                    this.writeU16(object);\n                }\n                else if (object < 0x100000000) {\n                    // uint 32\n                    this.writeU8(0xce);\n                    this.writeU32(object);\n                }\n                else {\n                    // uint 64\n                    this.writeU8(0xcf);\n                    this.writeU64(object);\n                }\n            }\n            else {\n                if (object >= -0x20) {\n                    // negative fixint\n                    this.writeU8(0xe0 | (object + 0x20));\n                }\n                else if (object >= -0x80) {\n                    // int 8\n                    this.writeU8(0xd0);\n                    this.writeI8(object);\n                }\n                else if (object >= -0x8000) {\n                    // int 16\n                    this.writeU8(0xd1);\n                    this.writeI16(object);\n                }\n                else if (object >= -0x80000000) {\n                    // int 32\n                    this.writeU8(0xd2);\n                    this.writeI32(object);\n                }\n                else {\n                    // int 64\n                    this.writeU8(0xd3);\n                    this.writeI64(object);\n                }\n            }\n        }\n        else {\n            // non-integer numbers\n            if (this.forceFloat32) {\n                // float 32\n                this.writeU8(0xca);\n                this.writeF32(object);\n            }\n            else {\n                // float 64\n                this.writeU8(0xcb);\n                this.writeF64(object);\n            }\n        }\n    };\n    Encoder.prototype.writeStringHeader = function (byteLength) {\n        if (byteLength < 32) {\n            // fixstr\n            this.writeU8(0xa0 + byteLength);\n        }\n        else if (byteLength < 0x100) {\n            // str 8\n            this.writeU8(0xd9);\n            this.writeU8(byteLength);\n        }\n        else if (byteLength < 0x10000) {\n            // str 16\n            this.writeU8(0xda);\n            this.writeU16(byteLength);\n        }\n        else if (byteLength < 0x100000000) {\n            // str 32\n            this.writeU8(0xdb);\n            this.writeU32(byteLength);\n        }\n        else {\n            throw new Error(\"Too long string: \" + byteLength + \" bytes in UTF-8\");\n        }\n    };\n    Encoder.prototype.encodeString = function (object) {\n        var maxHeaderSize = 1 + 4;\n        var strLength = object.length;\n        if (strLength > TEXT_ENCODER_THRESHOLD) {\n            var byteLength = utf8Count(object);\n            this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);\n            this.writeStringHeader(byteLength);\n            utf8EncodeTE(object, this.bytes, this.pos);\n            this.pos += byteLength;\n        }\n        else {\n            var byteLength = utf8Count(object);\n            this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);\n            this.writeStringHeader(byteLength);\n            utf8EncodeJs(object, this.bytes, this.pos);\n            this.pos += byteLength;\n        }\n    };\n    Encoder.prototype.encodeObject = function (object, depth) {\n        // try to encode objects with custom codec first of non-primitives\n        var ext = this.extensionCodec.tryToEncode(object, this.context);\n        if (ext != null) {\n            this.encodeExtension(ext);\n        }\n        else if (Array.isArray(object)) {\n            this.encodeArray(object, depth);\n        }\n        else if (ArrayBuffer.isView(object)) {\n            this.encodeBinary(object);\n        }\n        else if (typeof object === \"object\") {\n            this.encodeMap(object, depth);\n        }\n        else {\n            // symbol, function and other special object come here unless extensionCodec handles them.\n            throw new Error(\"Unrecognized object: \" + Object.prototype.toString.apply(object));\n        }\n    };\n    Encoder.prototype.encodeBinary = function (object) {\n        var size = object.byteLength;\n        if (size < 0x100) {\n            // bin 8\n            this.writeU8(0xc4);\n            this.writeU8(size);\n        }\n        else if (size < 0x10000) {\n            // bin 16\n            this.writeU8(0xc5);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // bin 32\n            this.writeU8(0xc6);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(\"Too large binary: \" + size);\n        }\n        var bytes = ensureUint8Array(object);\n        this.writeU8a(bytes);\n    };\n    Encoder.prototype.encodeArray = function (object, depth) {\n        var size = object.length;\n        if (size < 16) {\n            // fixarray\n            this.writeU8(0x90 + size);\n        }\n        else if (size < 0x10000) {\n            // array 16\n            this.writeU8(0xdc);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // array 32\n            this.writeU8(0xdd);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(\"Too large array: \" + size);\n        }\n        for (var _i = 0, object_1 = object; _i < object_1.length; _i++) {\n            var item = object_1[_i];\n            this.doEncode(item, depth + 1);\n        }\n    };\n    Encoder.prototype.countWithoutUndefined = function (object, keys) {\n        var count = 0;\n        for (var _i = 0, keys_1 = keys; _i < keys_1.length; _i++) {\n            var key = keys_1[_i];\n            if (object[key] !== undefined) {\n                count++;\n            }\n        }\n        return count;\n    };\n    Encoder.prototype.encodeMap = function (object, depth) {\n        var keys = Object.keys(object);\n        if (this.sortKeys) {\n            keys.sort();\n        }\n        var size = this.ignoreUndefined ? this.countWithoutUndefined(object, keys) : keys.length;\n        if (size < 16) {\n            // fixmap\n            this.writeU8(0x80 + size);\n        }\n        else if (size < 0x10000) {\n            // map 16\n            this.writeU8(0xde);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // map 32\n            this.writeU8(0xdf);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(\"Too large map object: \" + size);\n        }\n        for (var _i = 0, keys_2 = keys; _i < keys_2.length; _i++) {\n            var key = keys_2[_i];\n            var value = object[key];\n            if (!(this.ignoreUndefined && value === undefined)) {\n                this.encodeString(key);\n                this.doEncode(value, depth + 1);\n            }\n        }\n    };\n    Encoder.prototype.encodeExtension = function (ext) {\n        var size = ext.data.length;\n        if (size === 1) {\n            // fixext 1\n            this.writeU8(0xd4);\n        }\n        else if (size === 2) {\n            // fixext 2\n            this.writeU8(0xd5);\n        }\n        else if (size === 4) {\n            // fixext 4\n            this.writeU8(0xd6);\n        }\n        else if (size === 8) {\n            // fixext 8\n            this.writeU8(0xd7);\n        }\n        else if (size === 16) {\n            // fixext 16\n            this.writeU8(0xd8);\n        }\n        else if (size < 0x100) {\n            // ext 8\n            this.writeU8(0xc7);\n            this.writeU8(size);\n        }\n        else if (size < 0x10000) {\n            // ext 16\n            this.writeU8(0xc8);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // ext 32\n            this.writeU8(0xc9);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(\"Too large extension object: \" + size);\n        }\n        this.writeI8(ext.type);\n        this.writeU8a(ext.data);\n    };\n    Encoder.prototype.writeU8 = function (value) {\n        this.ensureBufferSizeToWrite(1);\n        this.view.setUint8(this.pos, value);\n        this.pos++;\n    };\n    Encoder.prototype.writeU8a = function (values) {\n        var size = values.length;\n        this.ensureBufferSizeToWrite(size);\n        this.bytes.set(values, this.pos);\n        this.pos += size;\n    };\n    Encoder.prototype.writeI8 = function (value) {\n        this.ensureBufferSizeToWrite(1);\n        this.view.setInt8(this.pos, value);\n        this.pos++;\n    };\n    Encoder.prototype.writeU16 = function (value) {\n        this.ensureBufferSizeToWrite(2);\n        this.view.setUint16(this.pos, value);\n        this.pos += 2;\n    };\n    Encoder.prototype.writeI16 = function (value) {\n        this.ensureBufferSizeToWrite(2);\n        this.view.setInt16(this.pos, value);\n        this.pos += 2;\n    };\n    Encoder.prototype.writeU32 = function (value) {\n        this.ensureBufferSizeToWrite(4);\n        this.view.setUint32(this.pos, value);\n        this.pos += 4;\n    };\n    Encoder.prototype.writeI32 = function (value) {\n        this.ensureBufferSizeToWrite(4);\n        this.view.setInt32(this.pos, value);\n        this.pos += 4;\n    };\n    Encoder.prototype.writeF32 = function (value) {\n        this.ensureBufferSizeToWrite(4);\n        this.view.setFloat32(this.pos, value);\n        this.pos += 4;\n    };\n    Encoder.prototype.writeF64 = function (value) {\n        this.ensureBufferSizeToWrite(8);\n        this.view.setFloat64(this.pos, value);\n        this.pos += 8;\n    };\n    Encoder.prototype.writeU64 = function (value) {\n        this.ensureBufferSizeToWrite(8);\n        setUint64(this.view, this.pos, value);\n        this.pos += 8;\n    };\n    Encoder.prototype.writeI64 = function (value) {\n        this.ensureBufferSizeToWrite(8);\n        setInt64(this.view, this.pos, value);\n        this.pos += 8;\n    };\n    return Encoder;\n}());\nexport { Encoder };\n//# sourceMappingURL=Encoder.mjs.map", "export function prettyByte(byte) {\n    return (byte < 0 ? \"-\" : \"\") + \"0x\" + Math.abs(byte).toString(16).padStart(2, \"0\");\n}\n//# sourceMappingURL=prettyByte.mjs.map", "import { utf8DecodeJs } from \"./utils/utf8.mjs\";\nvar DEFAULT_MAX_KEY_LENGTH = 16;\nvar DEFAULT_MAX_LENGTH_PER_KEY = 16;\nvar CachedKeyDecoder = /** @class */ (function () {\n    function CachedKeyDecoder(maxKeyLength, maxLengthPerKey) {\n        if (maxKeyLength === void 0) { maxKeyLength = DEFAULT_MAX_KEY_LENGTH; }\n        if (maxLengthPerKey === void 0) { maxLengthPerKey = DEFAULT_MAX_LENGTH_PER_KEY; }\n        this.maxKeyLength = maxKeyLength;\n        this.maxLengthPerKey = maxLengthPerKey;\n        this.hit = 0;\n        this.miss = 0;\n        // avoid `new Array(N)`, which makes a sparse array,\n        // because a sparse array is typically slower than a non-sparse array.\n        this.caches = [];\n        for (var i = 0; i < this.maxKeyLength; i++) {\n            this.caches.push([]);\n        }\n    }\n    CachedKeyDecoder.prototype.canBeCached = function (byteLength) {\n        return byteLength > 0 && byteLength <= this.maxKeyLength;\n    };\n    CachedKeyDecoder.prototype.find = function (bytes, inputOffset, byteLength) {\n        var records = this.caches[byteLength - 1];\n        FIND_CHUNK: for (var _i = 0, records_1 = records; _i < records_1.length; _i++) {\n            var record = records_1[_i];\n            var recordBytes = record.bytes;\n            for (var j = 0; j < byteLength; j++) {\n                if (recordBytes[j] !== bytes[inputOffset + j]) {\n                    continue FIND_CHUNK;\n                }\n            }\n            return record.str;\n        }\n        return null;\n    };\n    CachedKeyDecoder.prototype.store = function (bytes, value) {\n        var records = this.caches[bytes.length - 1];\n        var record = { bytes: bytes, str: value };\n        if (records.length >= this.maxLengthPerKey) {\n            // `records` are full!\n            // Set `record` to an arbitrary position.\n            records[(Math.random() * records.length) | 0] = record;\n        }\n        else {\n            records.push(record);\n        }\n    };\n    CachedKeyDecoder.prototype.decode = function (bytes, inputOffset, byteLength) {\n        var cachedValue = this.find(bytes, inputOffset, byteLength);\n        if (cachedValue != null) {\n            this.hit++;\n            return cachedValue;\n        }\n        this.miss++;\n        var str = utf8DecodeJs(bytes, inputOffset, byteLength);\n        // Ensure to copy a slice of bytes because the byte may be NodeJS Buffer and Buffer#slice() returns a reference to its internal ArrayBuffer.\n        var slicedCopyOfBytes = Uint8Array.prototype.slice.call(bytes, inputOffset, inputOffset + byteLength);\n        this.store(slicedCopyOfBytes, str);\n        return str;\n    };\n    return CachedKeyDecoder;\n}());\nexport { CachedKeyDecoder };\n//# sourceMappingURL=CachedKeyDecoder.mjs.map", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __asyncValues = (this && this.__asyncValues) || function (o) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var m = o[Symbol.asyncIterator], i;\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n};\nvar __await = (this && this.__await) || function (v) { return this instanceof __await ? (this.v = v, this) : new __await(v); }\nvar __asyncGenerator = (this && this.__asyncGenerator) || function (thisArg, _arguments, generator) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n    function fulfill(value) { resume(\"next\", value); }\n    function reject(value) { resume(\"throw\", value); }\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n};\nimport { prettyByte } from \"./utils/prettyByte.mjs\";\nimport { ExtensionCodec } from \"./ExtensionCodec.mjs\";\nimport { getInt64, getUint64, UINT32_MAX } from \"./utils/int.mjs\";\nimport { utf8DecodeJs, TEXT_DECODER_THRESHOLD, utf8DecodeTD } from \"./utils/utf8.mjs\";\nimport { createDataView, ensureUint8Array } from \"./utils/typedArrays.mjs\";\nimport { CachedKeyDecoder } from \"./CachedKeyDecoder.mjs\";\nimport { DecodeError } from \"./DecodeError.mjs\";\nvar isValidMapKeyType = function (key) {\n    var keyType = typeof key;\n    return keyType === \"string\" || keyType === \"number\";\n};\nvar HEAD_BYTE_REQUIRED = -1;\nvar EMPTY_VIEW = new DataView(new ArrayBuffer(0));\nvar EMPTY_BYTES = new Uint8Array(EMPTY_VIEW.buffer);\n// IE11: Hack to support IE11.\n// IE11: Drop this hack and just use RangeError when IE11 is obsolete.\nexport var DataViewIndexOutOfBoundsError = (function () {\n    try {\n        // IE11: The spec says it should throw RangeError,\n        // IE11: but in IE11 it throws TypeError.\n        EMPTY_VIEW.getInt8(0);\n    }\n    catch (e) {\n        return e.constructor;\n    }\n    throw new Error(\"never reached\");\n})();\nvar MORE_DATA = new DataViewIndexOutOfBoundsError(\"Insufficient data\");\nvar sharedCachedKeyDecoder = new CachedKeyDecoder();\nvar Decoder = /** @class */ (function () {\n    function Decoder(extensionCodec, context, maxStrLength, maxBinLength, maxArrayLength, maxMapLength, maxExtLength, keyDecoder) {\n        if (extensionCodec === void 0) { extensionCodec = ExtensionCodec.defaultCodec; }\n        if (context === void 0) { context = undefined; }\n        if (maxStrLength === void 0) { maxStrLength = UINT32_MAX; }\n        if (maxBinLength === void 0) { maxBinLength = UINT32_MAX; }\n        if (maxArrayLength === void 0) { maxArrayLength = UINT32_MAX; }\n        if (maxMapLength === void 0) { maxMapLength = UINT32_MAX; }\n        if (maxExtLength === void 0) { maxExtLength = UINT32_MAX; }\n        if (keyDecoder === void 0) { keyDecoder = sharedCachedKeyDecoder; }\n        this.extensionCodec = extensionCodec;\n        this.context = context;\n        this.maxStrLength = maxStrLength;\n        this.maxBinLength = maxBinLength;\n        this.maxArrayLength = maxArrayLength;\n        this.maxMapLength = maxMapLength;\n        this.maxExtLength = maxExtLength;\n        this.keyDecoder = keyDecoder;\n        this.totalPos = 0;\n        this.pos = 0;\n        this.view = EMPTY_VIEW;\n        this.bytes = EMPTY_BYTES;\n        this.headByte = HEAD_BYTE_REQUIRED;\n        this.stack = [];\n    }\n    Decoder.prototype.reinitializeState = function () {\n        this.totalPos = 0;\n        this.headByte = HEAD_BYTE_REQUIRED;\n        this.stack.length = 0;\n        // view, bytes, and pos will be re-initialized in setBuffer()\n    };\n    Decoder.prototype.setBuffer = function (buffer) {\n        this.bytes = ensureUint8Array(buffer);\n        this.view = createDataView(this.bytes);\n        this.pos = 0;\n    };\n    Decoder.prototype.appendBuffer = function (buffer) {\n        if (this.headByte === HEAD_BYTE_REQUIRED && !this.hasRemaining(1)) {\n            this.setBuffer(buffer);\n        }\n        else {\n            var remainingData = this.bytes.subarray(this.pos);\n            var newData = ensureUint8Array(buffer);\n            // concat remainingData + newData\n            var newBuffer = new Uint8Array(remainingData.length + newData.length);\n            newBuffer.set(remainingData);\n            newBuffer.set(newData, remainingData.length);\n            this.setBuffer(newBuffer);\n        }\n    };\n    Decoder.prototype.hasRemaining = function (size) {\n        return this.view.byteLength - this.pos >= size;\n    };\n    Decoder.prototype.createExtraByteError = function (posToShow) {\n        var _a = this, view = _a.view, pos = _a.pos;\n        return new RangeError(\"Extra \" + (view.byteLength - pos) + \" of \" + view.byteLength + \" byte(s) found at buffer[\" + posToShow + \"]\");\n    };\n    /**\n     * @throws {DecodeError}\n     * @throws {RangeError}\n     */\n    Decoder.prototype.decode = function (buffer) {\n        this.reinitializeState();\n        this.setBuffer(buffer);\n        var object = this.doDecodeSync();\n        if (this.hasRemaining(1)) {\n            throw this.createExtraByteError(this.pos);\n        }\n        return object;\n    };\n    Decoder.prototype.decodeMulti = function (buffer) {\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    this.reinitializeState();\n                    this.setBuffer(buffer);\n                    _a.label = 1;\n                case 1:\n                    if (!this.hasRemaining(1)) return [3 /*break*/, 3];\n                    return [4 /*yield*/, this.doDecodeSync()];\n                case 2:\n                    _a.sent();\n                    return [3 /*break*/, 1];\n                case 3: return [2 /*return*/];\n            }\n        });\n    };\n    Decoder.prototype.decodeAsync = function (stream) {\n        var stream_1, stream_1_1;\n        var e_1, _a;\n        return __awaiter(this, void 0, void 0, function () {\n            var decoded, object, buffer, e_1_1, _b, headByte, pos, totalPos;\n            return __generator(this, function (_c) {\n                switch (_c.label) {\n                    case 0:\n                        decoded = false;\n                        _c.label = 1;\n                    case 1:\n                        _c.trys.push([1, 6, 7, 12]);\n                        stream_1 = __asyncValues(stream);\n                        _c.label = 2;\n                    case 2: return [4 /*yield*/, stream_1.next()];\n                    case 3:\n                        if (!(stream_1_1 = _c.sent(), !stream_1_1.done)) return [3 /*break*/, 5];\n                        buffer = stream_1_1.value;\n                        if (decoded) {\n                            throw this.createExtraByteError(this.totalPos);\n                        }\n                        this.appendBuffer(buffer);\n                        try {\n                            object = this.doDecodeSync();\n                            decoded = true;\n                        }\n                        catch (e) {\n                            if (!(e instanceof DataViewIndexOutOfBoundsError)) {\n                                throw e; // rethrow\n                            }\n                            // fallthrough\n                        }\n                        this.totalPos += this.pos;\n                        _c.label = 4;\n                    case 4: return [3 /*break*/, 2];\n                    case 5: return [3 /*break*/, 12];\n                    case 6:\n                        e_1_1 = _c.sent();\n                        e_1 = { error: e_1_1 };\n                        return [3 /*break*/, 12];\n                    case 7:\n                        _c.trys.push([7, , 10, 11]);\n                        if (!(stream_1_1 && !stream_1_1.done && (_a = stream_1.return))) return [3 /*break*/, 9];\n                        return [4 /*yield*/, _a.call(stream_1)];\n                    case 8:\n                        _c.sent();\n                        _c.label = 9;\n                    case 9: return [3 /*break*/, 11];\n                    case 10:\n                        if (e_1) throw e_1.error;\n                        return [7 /*endfinally*/];\n                    case 11: return [7 /*endfinally*/];\n                    case 12:\n                        if (decoded) {\n                            if (this.hasRemaining(1)) {\n                                throw this.createExtraByteError(this.totalPos);\n                            }\n                            return [2 /*return*/, object];\n                        }\n                        _b = this, headByte = _b.headByte, pos = _b.pos, totalPos = _b.totalPos;\n                        throw new RangeError(\"Insufficient data in parsing \" + prettyByte(headByte) + \" at \" + totalPos + \" (\" + pos + \" in the current buffer)\");\n                }\n            });\n        });\n    };\n    Decoder.prototype.decodeArrayStream = function (stream) {\n        return this.decodeMultiAsync(stream, true);\n    };\n    Decoder.prototype.decodeStream = function (stream) {\n        return this.decodeMultiAsync(stream, false);\n    };\n    Decoder.prototype.decodeMultiAsync = function (stream, isArray) {\n        return __asyncGenerator(this, arguments, function decodeMultiAsync_1() {\n            var isArrayHeaderRequired, arrayItemsLeft, stream_2, stream_2_1, buffer, e_2, e_3_1;\n            var e_3, _a;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        isArrayHeaderRequired = isArray;\n                        arrayItemsLeft = -1;\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 13, 14, 19]);\n                        stream_2 = __asyncValues(stream);\n                        _b.label = 2;\n                    case 2: return [4 /*yield*/, __await(stream_2.next())];\n                    case 3:\n                        if (!(stream_2_1 = _b.sent(), !stream_2_1.done)) return [3 /*break*/, 12];\n                        buffer = stream_2_1.value;\n                        if (isArray && arrayItemsLeft === 0) {\n                            throw this.createExtraByteError(this.totalPos);\n                        }\n                        this.appendBuffer(buffer);\n                        if (isArrayHeaderRequired) {\n                            arrayItemsLeft = this.readArraySize();\n                            isArrayHeaderRequired = false;\n                            this.complete();\n                        }\n                        _b.label = 4;\n                    case 4:\n                        _b.trys.push([4, 9, , 10]);\n                        _b.label = 5;\n                    case 5:\n                        if (!true) return [3 /*break*/, 8];\n                        return [4 /*yield*/, __await(this.doDecodeSync())];\n                    case 6: return [4 /*yield*/, _b.sent()];\n                    case 7:\n                        _b.sent();\n                        if (--arrayItemsLeft === 0) {\n                            return [3 /*break*/, 8];\n                        }\n                        return [3 /*break*/, 5];\n                    case 8: return [3 /*break*/, 10];\n                    case 9:\n                        e_2 = _b.sent();\n                        if (!(e_2 instanceof DataViewIndexOutOfBoundsError)) {\n                            throw e_2; // rethrow\n                        }\n                        return [3 /*break*/, 10];\n                    case 10:\n                        this.totalPos += this.pos;\n                        _b.label = 11;\n                    case 11: return [3 /*break*/, 2];\n                    case 12: return [3 /*break*/, 19];\n                    case 13:\n                        e_3_1 = _b.sent();\n                        e_3 = { error: e_3_1 };\n                        return [3 /*break*/, 19];\n                    case 14:\n                        _b.trys.push([14, , 17, 18]);\n                        if (!(stream_2_1 && !stream_2_1.done && (_a = stream_2.return))) return [3 /*break*/, 16];\n                        return [4 /*yield*/, __await(_a.call(stream_2))];\n                    case 15:\n                        _b.sent();\n                        _b.label = 16;\n                    case 16: return [3 /*break*/, 18];\n                    case 17:\n                        if (e_3) throw e_3.error;\n                        return [7 /*endfinally*/];\n                    case 18: return [7 /*endfinally*/];\n                    case 19: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Decoder.prototype.doDecodeSync = function () {\n        DECODE: while (true) {\n            var headByte = this.readHeadByte();\n            var object = void 0;\n            if (headByte >= 0xe0) {\n                // negative fixint (111x xxxx) 0xe0 - 0xff\n                object = headByte - 0x100;\n            }\n            else if (headByte < 0xc0) {\n                if (headByte < 0x80) {\n                    // positive fixint (0xxx xxxx) 0x00 - 0x7f\n                    object = headByte;\n                }\n                else if (headByte < 0x90) {\n                    // fixmap (1000 xxxx) 0x80 - 0x8f\n                    var size = headByte - 0x80;\n                    if (size !== 0) {\n                        this.pushMapState(size);\n                        this.complete();\n                        continue DECODE;\n                    }\n                    else {\n                        object = {};\n                    }\n                }\n                else if (headByte < 0xa0) {\n                    // fixarray (1001 xxxx) 0x90 - 0x9f\n                    var size = headByte - 0x90;\n                    if (size !== 0) {\n                        this.pushArrayState(size);\n                        this.complete();\n                        continue DECODE;\n                    }\n                    else {\n                        object = [];\n                    }\n                }\n                else {\n                    // fixstr (101x xxxx) 0xa0 - 0xbf\n                    var byteLength = headByte - 0xa0;\n                    object = this.decodeUtf8String(byteLength, 0);\n                }\n            }\n            else if (headByte === 0xc0) {\n                // nil\n                object = null;\n            }\n            else if (headByte === 0xc2) {\n                // false\n                object = false;\n            }\n            else if (headByte === 0xc3) {\n                // true\n                object = true;\n            }\n            else if (headByte === 0xca) {\n                // float 32\n                object = this.readF32();\n            }\n            else if (headByte === 0xcb) {\n                // float 64\n                object = this.readF64();\n            }\n            else if (headByte === 0xcc) {\n                // uint 8\n                object = this.readU8();\n            }\n            else if (headByte === 0xcd) {\n                // uint 16\n                object = this.readU16();\n            }\n            else if (headByte === 0xce) {\n                // uint 32\n                object = this.readU32();\n            }\n            else if (headByte === 0xcf) {\n                // uint 64\n                object = this.readU64();\n            }\n            else if (headByte === 0xd0) {\n                // int 8\n                object = this.readI8();\n            }\n            else if (headByte === 0xd1) {\n                // int 16\n                object = this.readI16();\n            }\n            else if (headByte === 0xd2) {\n                // int 32\n                object = this.readI32();\n            }\n            else if (headByte === 0xd3) {\n                // int 64\n                object = this.readI64();\n            }\n            else if (headByte === 0xd9) {\n                // str 8\n                var byteLength = this.lookU8();\n                object = this.decodeUtf8String(byteLength, 1);\n            }\n            else if (headByte === 0xda) {\n                // str 16\n                var byteLength = this.lookU16();\n                object = this.decodeUtf8String(byteLength, 2);\n            }\n            else if (headByte === 0xdb) {\n                // str 32\n                var byteLength = this.lookU32();\n                object = this.decodeUtf8String(byteLength, 4);\n            }\n            else if (headByte === 0xdc) {\n                // array 16\n                var size = this.readU16();\n                if (size !== 0) {\n                    this.pushArrayState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = [];\n                }\n            }\n            else if (headByte === 0xdd) {\n                // array 32\n                var size = this.readU32();\n                if (size !== 0) {\n                    this.pushArrayState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = [];\n                }\n            }\n            else if (headByte === 0xde) {\n                // map 16\n                var size = this.readU16();\n                if (size !== 0) {\n                    this.pushMapState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = {};\n                }\n            }\n            else if (headByte === 0xdf) {\n                // map 32\n                var size = this.readU32();\n                if (size !== 0) {\n                    this.pushMapState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = {};\n                }\n            }\n            else if (headByte === 0xc4) {\n                // bin 8\n                var size = this.lookU8();\n                object = this.decodeBinary(size, 1);\n            }\n            else if (headByte === 0xc5) {\n                // bin 16\n                var size = this.lookU16();\n                object = this.decodeBinary(size, 2);\n            }\n            else if (headByte === 0xc6) {\n                // bin 32\n                var size = this.lookU32();\n                object = this.decodeBinary(size, 4);\n            }\n            else if (headByte === 0xd4) {\n                // fixext 1\n                object = this.decodeExtension(1, 0);\n            }\n            else if (headByte === 0xd5) {\n                // fixext 2\n                object = this.decodeExtension(2, 0);\n            }\n            else if (headByte === 0xd6) {\n                // fixext 4\n                object = this.decodeExtension(4, 0);\n            }\n            else if (headByte === 0xd7) {\n                // fixext 8\n                object = this.decodeExtension(8, 0);\n            }\n            else if (headByte === 0xd8) {\n                // fixext 16\n                object = this.decodeExtension(16, 0);\n            }\n            else if (headByte === 0xc7) {\n                // ext 8\n                var size = this.lookU8();\n                object = this.decodeExtension(size, 1);\n            }\n            else if (headByte === 0xc8) {\n                // ext 16\n                var size = this.lookU16();\n                object = this.decodeExtension(size, 2);\n            }\n            else if (headByte === 0xc9) {\n                // ext 32\n                var size = this.lookU32();\n                object = this.decodeExtension(size, 4);\n            }\n            else {\n                throw new DecodeError(\"Unrecognized type byte: \" + prettyByte(headByte));\n            }\n            this.complete();\n            var stack = this.stack;\n            while (stack.length > 0) {\n                // arrays and maps\n                var state = stack[stack.length - 1];\n                if (state.type === 0 /* ARRAY */) {\n                    state.array[state.position] = object;\n                    state.position++;\n                    if (state.position === state.size) {\n                        stack.pop();\n                        object = state.array;\n                    }\n                    else {\n                        continue DECODE;\n                    }\n                }\n                else if (state.type === 1 /* MAP_KEY */) {\n                    if (!isValidMapKeyType(object)) {\n                        throw new DecodeError(\"The type of key must be string or number but \" + typeof object);\n                    }\n                    if (object === \"__proto__\") {\n                        throw new DecodeError(\"The key __proto__ is not allowed\");\n                    }\n                    state.key = object;\n                    state.type = 2 /* MAP_VALUE */;\n                    continue DECODE;\n                }\n                else {\n                    // it must be `state.type === State.MAP_VALUE` here\n                    state.map[state.key] = object;\n                    state.readCount++;\n                    if (state.readCount === state.size) {\n                        stack.pop();\n                        object = state.map;\n                    }\n                    else {\n                        state.key = null;\n                        state.type = 1 /* MAP_KEY */;\n                        continue DECODE;\n                    }\n                }\n            }\n            return object;\n        }\n    };\n    Decoder.prototype.readHeadByte = function () {\n        if (this.headByte === HEAD_BYTE_REQUIRED) {\n            this.headByte = this.readU8();\n            // console.log(\"headByte\", prettyByte(this.headByte));\n        }\n        return this.headByte;\n    };\n    Decoder.prototype.complete = function () {\n        this.headByte = HEAD_BYTE_REQUIRED;\n    };\n    Decoder.prototype.readArraySize = function () {\n        var headByte = this.readHeadByte();\n        switch (headByte) {\n            case 0xdc:\n                return this.readU16();\n            case 0xdd:\n                return this.readU32();\n            default: {\n                if (headByte < 0xa0) {\n                    return headByte - 0x90;\n                }\n                else {\n                    throw new DecodeError(\"Unrecognized array type byte: \" + prettyByte(headByte));\n                }\n            }\n        }\n    };\n    Decoder.prototype.pushMapState = function (size) {\n        if (size > this.maxMapLength) {\n            throw new DecodeError(\"Max length exceeded: map length (\" + size + \") > maxMapLengthLength (\" + this.maxMapLength + \")\");\n        }\n        this.stack.push({\n            type: 1 /* MAP_KEY */,\n            size: size,\n            key: null,\n            readCount: 0,\n            map: {},\n        });\n    };\n    Decoder.prototype.pushArrayState = function (size) {\n        if (size > this.maxArrayLength) {\n            throw new DecodeError(\"Max length exceeded: array length (\" + size + \") > maxArrayLength (\" + this.maxArrayLength + \")\");\n        }\n        this.stack.push({\n            type: 0 /* ARRAY */,\n            size: size,\n            array: new Array(size),\n            position: 0,\n        });\n    };\n    Decoder.prototype.decodeUtf8String = function (byteLength, headerOffset) {\n        var _a;\n        if (byteLength > this.maxStrLength) {\n            throw new DecodeError(\"Max length exceeded: UTF-8 byte length (\" + byteLength + \") > maxStrLength (\" + this.maxStrLength + \")\");\n        }\n        if (this.bytes.byteLength < this.pos + headerOffset + byteLength) {\n            throw MORE_DATA;\n        }\n        var offset = this.pos + headerOffset;\n        var object;\n        if (this.stateIsMapKey() && ((_a = this.keyDecoder) === null || _a === void 0 ? void 0 : _a.canBeCached(byteLength))) {\n            object = this.keyDecoder.decode(this.bytes, offset, byteLength);\n        }\n        else if (byteLength > TEXT_DECODER_THRESHOLD) {\n            object = utf8DecodeTD(this.bytes, offset, byteLength);\n        }\n        else {\n            object = utf8DecodeJs(this.bytes, offset, byteLength);\n        }\n        this.pos += headerOffset + byteLength;\n        return object;\n    };\n    Decoder.prototype.stateIsMapKey = function () {\n        if (this.stack.length > 0) {\n            var state = this.stack[this.stack.length - 1];\n            return state.type === 1 /* MAP_KEY */;\n        }\n        return false;\n    };\n    Decoder.prototype.decodeBinary = function (byteLength, headOffset) {\n        if (byteLength > this.maxBinLength) {\n            throw new DecodeError(\"Max length exceeded: bin length (\" + byteLength + \") > maxBinLength (\" + this.maxBinLength + \")\");\n        }\n        if (!this.hasRemaining(byteLength + headOffset)) {\n            throw MORE_DATA;\n        }\n        var offset = this.pos + headOffset;\n        var object = this.bytes.subarray(offset, offset + byteLength);\n        this.pos += headOffset + byteLength;\n        return object;\n    };\n    Decoder.prototype.decodeExtension = function (size, headOffset) {\n        if (size > this.maxExtLength) {\n            throw new DecodeError(\"Max length exceeded: ext length (\" + size + \") > maxExtLength (\" + this.maxExtLength + \")\");\n        }\n        var extType = this.view.getInt8(this.pos + headOffset);\n        var data = this.decodeBinary(size, headOffset + 1 /* extType */);\n        return this.extensionCodec.decode(data, extType, this.context);\n    };\n    Decoder.prototype.lookU8 = function () {\n        return this.view.getUint8(this.pos);\n    };\n    Decoder.prototype.lookU16 = function () {\n        return this.view.getUint16(this.pos);\n    };\n    Decoder.prototype.lookU32 = function () {\n        return this.view.getUint32(this.pos);\n    };\n    Decoder.prototype.readU8 = function () {\n        var value = this.view.getUint8(this.pos);\n        this.pos++;\n        return value;\n    };\n    Decoder.prototype.readI8 = function () {\n        var value = this.view.getInt8(this.pos);\n        this.pos++;\n        return value;\n    };\n    Decoder.prototype.readU16 = function () {\n        var value = this.view.getUint16(this.pos);\n        this.pos += 2;\n        return value;\n    };\n    Decoder.prototype.readI16 = function () {\n        var value = this.view.getInt16(this.pos);\n        this.pos += 2;\n        return value;\n    };\n    Decoder.prototype.readU32 = function () {\n        var value = this.view.getUint32(this.pos);\n        this.pos += 4;\n        return value;\n    };\n    Decoder.prototype.readI32 = function () {\n        var value = this.view.getInt32(this.pos);\n        this.pos += 4;\n        return value;\n    };\n    Decoder.prototype.readU64 = function () {\n        var value = getUint64(this.view, this.pos);\n        this.pos += 8;\n        return value;\n    };\n    Decoder.prototype.readI64 = function () {\n        var value = getInt64(this.view, this.pos);\n        this.pos += 8;\n        return value;\n    };\n    Decoder.prototype.readF32 = function () {\n        var value = this.view.getFloat32(this.pos);\n        this.pos += 4;\n        return value;\n    };\n    Decoder.prototype.readF64 = function () {\n        var value = this.view.getFloat64(this.pos);\n        this.pos += 8;\n        return value;\n    };\n    return Decoder;\n}());\nexport { Decoder };\n//# sourceMappingURL=Decoder.mjs.map", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Not exported from index.\r\n/** @private */\r\nexport class BinaryMessageFormat {\r\n\r\n    // The length prefix of binary messages is encoded as VarInt. Read the comment in\r\n    // the BinaryMessageParser.TryParseMessage for details.\r\n\r\n    public static write(output: Uint8Array): ArrayBuffer {\r\n        let size = output.byteLength || output.length;\r\n        const lenBuffer = [];\r\n        do {\r\n            let sizePart = size & 0x7f;\r\n            size = size >> 7;\r\n            if (size > 0) {\r\n                sizePart |= 0x80;\r\n            }\r\n            lenBuffer.push(sizePart);\r\n        }\r\n        while (size > 0);\r\n\r\n        size = output.byteLength || output.length;\r\n\r\n        const buffer = new Uint8Array(lenBuffer.length + size);\r\n        buffer.set(lenBuffer, 0);\r\n        buffer.set(output, lenBuffer.length);\r\n        return buffer.buffer;\r\n    }\r\n\r\n    public static parse(input: ArrayBuffer): Uint8Array[] {\r\n        const result: Uint8Array[] = [];\r\n        const uint8Array = new Uint8Array(input);\r\n        const maxLengthPrefixSize = 5;\r\n        const numBitsToShift = [0, 7, 14, 21, 28 ];\r\n\r\n        for (let offset = 0; offset < input.byteLength;) {\r\n            let numBytes = 0;\r\n            let size = 0;\r\n            let byteRead;\r\n            do {\r\n                byteRead = uint8Array[offset + numBytes];\r\n                size = size | ((byteRead & 0x7f) << (numBitsToShift[numBytes]));\r\n                numBytes++;\r\n            }\r\n            while (numBytes < Math.min(maxLengthPrefixSize, input.byteLength - offset) && (byteRead & 0x80) !== 0);\r\n\r\n            if ((byteRead & 0x80) !== 0 && numBytes < maxLengthPrefixSize) {\r\n                throw new Error(\"Cannot read message size.\");\r\n            }\r\n\r\n            if (numBytes === maxLengthPrefixSize && byteRead > 7) {\r\n                throw new Error(\"Messages bigger than 2GB are not supported.\");\r\n            }\r\n\r\n            if (uint8Array.byteLength >= (offset + numBytes + size)) {\r\n                // IE does not support .slice() so use subarray\r\n                result.push(uint8Array.slice\r\n                    ? uint8Array.slice(offset + numBytes, offset + numBytes + size)\r\n                    : uint8Array.subarray(offset + numBytes, offset + numBytes + size));\r\n            } else {\r\n                throw new Error(\"Incomplete message.\");\r\n            }\r\n\r\n            offset = offset + numBytes + size;\r\n        }\r\n\r\n        return result;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { Encoder, Decoder } from \"@msgpack/msgpack\";\r\n\r\nimport { MessagePackOptions } from \"./MessagePackOptions\";\r\n\r\nimport {\r\n    CancelInvocationMessage, CompletionMessage, HubMessage, IHubProtocol, ILogger, InvocationMessage,\r\n    LogLevel, MessageHeaders, MessageType, NullLogger, StreamInvocationMessage, StreamItemMessage, TransferFormat,\r\n} from \"@microsoft/signalr\";\r\n\r\nimport { BinaryMessageFormat } from \"./BinaryMessageFormat\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n\r\n// TypeDoc's @inheritDoc and @link don't work across modules :(\r\n\r\n// constant encoding of the ping message\r\n// see: https://github.com/aspnet/SignalR/blob/dev/specs/HubProtocol.md#ping-message-encoding-1\r\n// Don't use Uint8Array.from as IE does not support it\r\nconst SERIALIZED_PING_MESSAGE: Uint8Array = new Uint8Array([0x91, MessageType.Ping]);\r\n\r\n/** Implements the MessagePack Hub Protocol */\r\nexport class MessagePackHubProtocol implements IHubProtocol {\r\n    /** The name of the protocol. This is used by SignalR to resolve the protocol between the client and server. */\r\n    public readonly name: string = \"messagepack\";\r\n    /** The version of the protocol. */\r\n    public readonly version: number = 1;\r\n    /** The TransferFormat of the protocol. */\r\n    public readonly transferFormat: TransferFormat = TransferFormat.Binary;\r\n\r\n    private readonly _errorResult = 1;\r\n    private readonly _voidResult = 2;\r\n    private readonly _nonVoidResult = 3;\r\n\r\n    private readonly _encoder: Encoder<undefined>;\r\n    private readonly _decoder: Decoder<undefined>;\r\n\r\n    /**\r\n     *\r\n     * @param messagePackOptions MessagePack options passed to @msgpack/msgpack\r\n     */\r\n    constructor(messagePackOptions?: MessagePackOptions) {\r\n        messagePackOptions = messagePackOptions || {};\r\n        this._encoder = new Encoder(\r\n            messagePackOptions.extensionCodec,\r\n            messagePackOptions.context,\r\n            messagePackOptions.maxDepth,\r\n            messagePackOptions.initialBufferSize,\r\n            messagePackOptions.sortKeys,\r\n            messagePackOptions.forceFloat32,\r\n            messagePackOptions.ignoreUndefined,\r\n            messagePackOptions.forceIntegerToFloat,\r\n        );\r\n\r\n        this._decoder = new Decoder(\r\n            messagePackOptions.extensionCodec,\r\n            messagePackOptions.context,\r\n            messagePackOptions.maxStrLength,\r\n            messagePackOptions.maxBinLength,\r\n            messagePackOptions.maxArrayLength,\r\n            messagePackOptions.maxMapLength,\r\n            messagePackOptions.maxExtLength,\r\n        );\r\n    }\r\n\r\n    /** Creates an array of HubMessage objects from the specified serialized representation.\r\n     *\r\n     * @param {ArrayBuffer} input An ArrayBuffer containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    public parseMessages(input: ArrayBuffer, logger: ILogger): HubMessage[] {\r\n        // The interface does allow \"string\" to be passed in, but this implementation does not. So let's throw a useful error.\r\n        if (!(isArrayBuffer(input))) {\r\n            throw new Error(\"Invalid input for MessagePack hub protocol. Expected an ArrayBuffer.\");\r\n        }\r\n\r\n        if (logger === null) {\r\n            logger = NullLogger.instance;\r\n        }\r\n\r\n        const messages = BinaryMessageFormat.parse(input);\r\n\r\n        const hubMessages = [];\r\n        for (const message of messages) {\r\n            const parsedMessage = this._parseMessage(message, logger);\r\n            // Can be null for an unknown message. Unknown message is logged in parseMessage\r\n            if (parsedMessage) {\r\n                hubMessages.push(parsedMessage);\r\n            }\r\n        }\r\n\r\n        return hubMessages;\r\n    }\r\n\r\n    /** Writes the specified HubMessage to an ArrayBuffer and returns it.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {ArrayBuffer} An ArrayBuffer containing the serialized representation of the message.\r\n     */\r\n    public writeMessage(message: HubMessage): ArrayBuffer {\r\n        switch (message.type) {\r\n            case MessageType.Invocation:\r\n                return this._writeInvocation(message as InvocationMessage);\r\n            case MessageType.StreamInvocation:\r\n                return this._writeStreamInvocation(message as StreamInvocationMessage);\r\n            case MessageType.StreamItem:\r\n                return this._writeStreamItem(message as StreamItemMessage);\r\n            case MessageType.Completion:\r\n                return this._writeCompletion(message as CompletionMessage);\r\n            case MessageType.Ping:\r\n                return BinaryMessageFormat.write(SERIALIZED_PING_MESSAGE);\r\n            case MessageType.CancelInvocation:\r\n                return this._writeCancelInvocation(message as CancelInvocationMessage);\r\n            default:\r\n                throw new Error(\"Invalid message type.\");\r\n        }\r\n    }\r\n\r\n    private _parseMessage(input: Uint8Array, logger: ILogger): HubMessage | null {\r\n        if (input.length === 0) {\r\n            throw new Error(\"Invalid payload.\");\r\n        }\r\n\r\n        const properties = this._decoder.decode(input) as any;\r\n        if (properties.length === 0 || !(properties instanceof Array)) {\r\n            throw new Error(\"Invalid payload.\");\r\n        }\r\n\r\n        const messageType = properties[0] as MessageType;\r\n\r\n        switch (messageType) {\r\n            case MessageType.Invocation:\r\n                return this._createInvocationMessage(this._readHeaders(properties), properties);\r\n            case MessageType.StreamItem:\r\n                return this._createStreamItemMessage(this._readHeaders(properties), properties);\r\n            case MessageType.Completion:\r\n                return this._createCompletionMessage(this._readHeaders(properties), properties);\r\n            case MessageType.Ping:\r\n                return this._createPingMessage(properties);\r\n            case MessageType.Close:\r\n                return this._createCloseMessage(properties);\r\n            default:\r\n                // Future protocol changes can add message types, old clients can ignore them\r\n                logger.log(LogLevel.Information, \"Unknown message type '\" + messageType + \"' ignored.\");\r\n                return null;\r\n        }\r\n    }\r\n\r\n    private _createCloseMessage(properties: any[]): HubMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 2) {\r\n            throw new Error(\"Invalid payload for Close message.\");\r\n        }\r\n\r\n        return {\r\n            // Close messages have no headers.\r\n            allowReconnect: properties.length >= 3 ? properties[2] : undefined,\r\n            error: properties[1],\r\n            type: MessageType.Close,\r\n        } as HubMessage;\r\n    }\r\n\r\n    private _createPingMessage(properties: any[]): HubMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 1) {\r\n            throw new Error(\"Invalid payload for Ping message.\");\r\n        }\r\n\r\n        return {\r\n            // Ping messages have no headers.\r\n            type: MessageType.Ping,\r\n        } as HubMessage;\r\n    }\r\n\r\n    private _createInvocationMessage(headers: MessageHeaders, properties: any[]): InvocationMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 5) {\r\n            throw new Error(\"Invalid payload for Invocation message.\");\r\n        }\r\n\r\n        const invocationId = properties[2] as string;\r\n        if (invocationId) {\r\n            return {\r\n                arguments: properties[4],\r\n                headers,\r\n                invocationId,\r\n                streamIds: [],\r\n                target: properties[3] as string,\r\n                type: MessageType.Invocation,\r\n            };\r\n        } else {\r\n            return {\r\n                arguments: properties[4],\r\n                headers,\r\n                streamIds: [],\r\n                target: properties[3],\r\n                type: MessageType.Invocation,\r\n            };\r\n        }\r\n\r\n    }\r\n\r\n    private _createStreamItemMessage(headers: MessageHeaders, properties: any[]): StreamItemMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 4) {\r\n            throw new Error(\"Invalid payload for StreamItem message.\");\r\n        }\r\n\r\n        return {\r\n            headers,\r\n            invocationId: properties[2],\r\n            item: properties[3],\r\n            type: MessageType.StreamItem,\r\n        } as StreamItemMessage;\r\n    }\r\n\r\n    private _createCompletionMessage(headers: MessageHeaders, properties: any[]): CompletionMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 4) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        const resultKind = properties[3];\r\n\r\n        if (resultKind !== this._voidResult && properties.length < 5) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        let error: string | undefined;\r\n        let result: any;\r\n\r\n        switch (resultKind) {\r\n            case this._errorResult:\r\n                error = properties[4];\r\n                break;\r\n            case this._nonVoidResult:\r\n                result = properties[4];\r\n                break;\r\n        }\r\n\r\n        const completionMessage: CompletionMessage = {\r\n            error,\r\n            headers,\r\n            invocationId: properties[2],\r\n            result,\r\n            type: MessageType.Completion,\r\n        };\r\n\r\n        return completionMessage;\r\n    }\r\n\r\n    private _writeInvocation(invocationMessage: InvocationMessage): ArrayBuffer {\r\n        let payload: any;\r\n        if (invocationMessage.streamIds) {\r\n            payload = this._encoder.encode([MessageType.Invocation, invocationMessage.headers || {}, invocationMessage.invocationId || null,\r\n            invocationMessage.target, invocationMessage.arguments, invocationMessage.streamIds]);\r\n        } else {\r\n            payload = this._encoder.encode([MessageType.Invocation, invocationMessage.headers || {}, invocationMessage.invocationId || null,\r\n            invocationMessage.target, invocationMessage.arguments]);\r\n        }\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _writeStreamInvocation(streamInvocationMessage: StreamInvocationMessage): ArrayBuffer {\r\n        let payload: any;\r\n        if (streamInvocationMessage.streamIds) {\r\n            payload = this._encoder.encode([MessageType.StreamInvocation, streamInvocationMessage.headers || {}, streamInvocationMessage.invocationId,\r\n            streamInvocationMessage.target, streamInvocationMessage.arguments, streamInvocationMessage.streamIds]);\r\n        } else {\r\n            payload = this._encoder.encode([MessageType.StreamInvocation, streamInvocationMessage.headers || {}, streamInvocationMessage.invocationId,\r\n            streamInvocationMessage.target, streamInvocationMessage.arguments]);\r\n        }\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _writeStreamItem(streamItemMessage: StreamItemMessage): ArrayBuffer {\r\n        const payload = this._encoder.encode([MessageType.StreamItem, streamItemMessage.headers || {}, streamItemMessage.invocationId,\r\n        streamItemMessage.item]);\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _writeCompletion(completionMessage: CompletionMessage): ArrayBuffer {\r\n        const resultKind = completionMessage.error ? this._errorResult : completionMessage.result ? this._nonVoidResult : this._voidResult;\r\n\r\n        let payload: any;\r\n        switch (resultKind) {\r\n            case this._errorResult:\r\n                payload = this._encoder.encode([MessageType.Completion, completionMessage.headers || {}, completionMessage.invocationId, resultKind, completionMessage.error]);\r\n                break;\r\n            case this._voidResult:\r\n                payload = this._encoder.encode([MessageType.Completion, completionMessage.headers || {}, completionMessage.invocationId, resultKind]);\r\n                break;\r\n            case this._nonVoidResult:\r\n                payload = this._encoder.encode([MessageType.Completion, completionMessage.headers || {}, completionMessage.invocationId, resultKind, completionMessage.result]);\r\n                break;\r\n        }\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _writeCancelInvocation(cancelInvocationMessage: CancelInvocationMessage): ArrayBuffer {\r\n        const payload = this._encoder.encode([MessageType.CancelInvocation, cancelInvocationMessage.headers || {}, cancelInvocationMessage.invocationId]);\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _readHeaders(properties: any): MessageHeaders {\r\n        const headers: MessageHeaders = properties[1] as MessageHeaders;\r\n        if (typeof headers !== \"object\") {\r\n            throw new Error(\"Invalid headers.\");\r\n        }\r\n        return headers;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Copied from signalr/Utils.ts\r\n/** @private */\r\nexport function isArrayBuffer(val: any): val is ArrayBuffer {\r\n    return val && typeof ArrayBuffer !== \"undefined\" &&\r\n        (val instanceof ArrayBuffer ||\r\n        // Sometimes we get an ArrayBuffer that doesn't satisfy instanceof\r\n        (val.constructor && val.constructor.name === \"ArrayBuffer\"));\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Version token that will be replaced by the prepack command\r\n/** The version of the SignalR Message Pack protocol library. */\r\nexport const VERSION = \"0.0.0-DEV_BUILD\";\r\n\r\nexport { MessagePackHubProtocol } from \"./MessagePackHubProtocol\";\r\n\r\nexport { MessagePackOptions } from \"./MessagePackOptions\";\r\n"]}