{"version": 3, "file": "BinaryMessageFormat.js", "sourceRoot": "", "sources": ["../../src/BinaryMessageFormat.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAEvE,2BAA2B;AAC3B,eAAe;AACf,MAAa,mBAAmB;IAE5B,iFAAiF;IACjF,uDAAuD;IAEhD,MAAM,CAAC,KAAK,CAAC,MAAkB;QAClC,IAAI,IAAI,GAAG,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,MAAM,CAAC;QAC9C,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,GAAG;YACC,IAAI,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;YAC3B,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;YACjB,IAAI,IAAI,GAAG,CAAC,EAAE;gBACV,QAAQ,IAAI,IAAI,CAAC;aACpB;YACD,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC5B,QACM,IAAI,GAAG,CAAC,EAAE;QAEjB,IAAI,GAAG,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,MAAM,CAAC;QAE1C,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;QACvD,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QACzB,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACrC,OAAO,MAAM,CAAC,MAAM,CAAC;IACzB,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,KAAkB;QAClC,MAAM,MAAM,GAAiB,EAAE,CAAC;QAChC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;QACzC,MAAM,mBAAmB,GAAG,CAAC,CAAC;QAC9B,MAAM,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE,CAAC;QAE3C,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,UAAU,GAAG;YAC7C,IAAI,QAAQ,GAAG,CAAC,CAAC;YACjB,IAAI,IAAI,GAAG,CAAC,CAAC;YACb,IAAI,QAAQ,CAAC;YACb,GAAG;gBACC,QAAQ,GAAG,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC;gBACzC,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAChE,QAAQ,EAAE,CAAC;aACd,QACM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;YAEvG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,QAAQ,GAAG,mBAAmB,EAAE;gBAC3D,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;aAChD;YAED,IAAI,QAAQ,KAAK,mBAAmB,IAAI,QAAQ,GAAG,CAAC,EAAE;gBAClD,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;aAClE;YAED,IAAI,UAAU,CAAC,UAAU,IAAI,CAAC,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC,EAAE;gBACrD,+CAA+C;gBAC/C,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK;oBACxB,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC;oBAC/D,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;aAC3E;iBAAM;gBACH,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;aAC1C;YAED,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC;SACrC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;AAjED,kDAiEC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Not exported from index.\r\n/** @private */\r\nexport class BinaryMessageFormat {\r\n\r\n    // The length prefix of binary messages is encoded as VarInt. Read the comment in\r\n    // the BinaryMessageParser.TryParseMessage for details.\r\n\r\n    public static write(output: Uint8Array): ArrayBuffer {\r\n        let size = output.byteLength || output.length;\r\n        const lenBuffer = [];\r\n        do {\r\n            let sizePart = size & 0x7f;\r\n            size = size >> 7;\r\n            if (size > 0) {\r\n                sizePart |= 0x80;\r\n            }\r\n            lenBuffer.push(sizePart);\r\n        }\r\n        while (size > 0);\r\n\r\n        size = output.byteLength || output.length;\r\n\r\n        const buffer = new Uint8Array(lenBuffer.length + size);\r\n        buffer.set(lenBuffer, 0);\r\n        buffer.set(output, lenBuffer.length);\r\n        return buffer.buffer;\r\n    }\r\n\r\n    public static parse(input: ArrayBuffer): Uint8Array[] {\r\n        const result: Uint8Array[] = [];\r\n        const uint8Array = new Uint8Array(input);\r\n        const maxLengthPrefixSize = 5;\r\n        const numBitsToShift = [0, 7, 14, 21, 28 ];\r\n\r\n        for (let offset = 0; offset < input.byteLength;) {\r\n            let numBytes = 0;\r\n            let size = 0;\r\n            let byteRead;\r\n            do {\r\n                byteRead = uint8Array[offset + numBytes];\r\n                size = size | ((byteRead & 0x7f) << (numBitsToShift[numBytes]));\r\n                numBytes++;\r\n            }\r\n            while (numBytes < Math.min(maxLengthPrefixSize, input.byteLength - offset) && (byteRead & 0x80) !== 0);\r\n\r\n            if ((byteRead & 0x80) !== 0 && numBytes < maxLengthPrefixSize) {\r\n                throw new Error(\"Cannot read message size.\");\r\n            }\r\n\r\n            if (numBytes === maxLengthPrefixSize && byteRead > 7) {\r\n                throw new Error(\"Messages bigger than 2GB are not supported.\");\r\n            }\r\n\r\n            if (uint8Array.byteLength >= (offset + numBytes + size)) {\r\n                // IE does not support .slice() so use subarray\r\n                result.push(uint8Array.slice\r\n                    ? uint8Array.slice(offset + numBytes, offset + numBytes + size)\r\n                    : uint8Array.subarray(offset + numBytes, offset + numBytes + size));\r\n            } else {\r\n                throw new Error(\"Incomplete message.\");\r\n            }\r\n\r\n            offset = offset + numBytes + size;\r\n        }\r\n\r\n        return result;\r\n    }\r\n}\r\n"]}