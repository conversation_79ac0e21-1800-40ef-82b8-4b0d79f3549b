{"version": 3, "file": "MessagePackHubProtocol.js", "sourceRoot": "", "sources": ["../../src/MessagePackHubProtocol.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAEvE,8CAAoD;AAIpD,gDAG4B;AAE5B,+DAA4D;AAC5D,mCAAwC;AAExC,+DAA+D;AAE/D,wCAAwC;AACxC,+FAA+F;AAC/F,sDAAsD;AACtD,MAAM,uBAAuB,GAAe,IAAI,UAAU,CAAC,CAAC,IAAI,EAAE,qBAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AAErF,8CAA8C;AAC9C,MAAa,sBAAsB;IAe/B;;;OAGG;IACH,YAAY,kBAAuC;QAlBnD,+GAA+G;QAC/F,SAAI,GAAW,aAAa,CAAC;QAC7C,mCAAmC;QACnB,YAAO,GAAW,CAAC,CAAC;QACpC,0CAA0C;QAC1B,mBAAc,GAAmB,wBAAc,CAAC,MAAM,CAAC;QAEtD,iBAAY,GAAG,CAAC,CAAC;QACjB,gBAAW,GAAG,CAAC,CAAC;QAChB,mBAAc,GAAG,CAAC,CAAC;QAUhC,kBAAkB,GAAG,kBAAkB,IAAI,EAAE,CAAC;QAC9C,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAO,CACvB,kBAAkB,CAAC,cAAc,EACjC,kBAAkB,CAAC,OAAO,EAC1B,kBAAkB,CAAC,QAAQ,EAC3B,kBAAkB,CAAC,iBAAiB,EACpC,kBAAkB,CAAC,QAAQ,EAC3B,kBAAkB,CAAC,YAAY,EAC/B,kBAAkB,CAAC,eAAe,EAClC,kBAAkB,CAAC,mBAAmB,CACzC,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAO,CACvB,kBAAkB,CAAC,cAAc,EACjC,kBAAkB,CAAC,OAAO,EAC1B,kBAAkB,CAAC,YAAY,EAC/B,kBAAkB,CAAC,YAAY,EAC/B,kBAAkB,CAAC,cAAc,EACjC,kBAAkB,CAAC,YAAY,EAC/B,kBAAkB,CAAC,YAAY,CAClC,CAAC;IACN,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,KAAkB,EAAE,MAAe;QACpD,sHAAsH;QACtH,IAAI,CAAC,CAAC,qBAAa,CAAC,KAAK,CAAC,CAAC,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;SAC3F;QAED,IAAI,MAAM,KAAK,IAAI,EAAE;YACjB,MAAM,GAAG,oBAAU,CAAC,QAAQ,CAAC;SAChC;QAED,MAAM,QAAQ,GAAG,yCAAmB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAElD,MAAM,WAAW,GAAG,EAAE,CAAC;QACvB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC1D,gFAAgF;YAChF,IAAI,aAAa,EAAE;gBACf,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aACnC;SACJ;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,OAAmB;QACnC,QAAQ,OAAO,CAAC,IAAI,EAAE;YAClB,KAAK,qBAAW,CAAC,UAAU;gBACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAA4B,CAAC,CAAC;YAC/D,KAAK,qBAAW,CAAC,gBAAgB;gBAC7B,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAkC,CAAC,CAAC;YAC3E,KAAK,qBAAW,CAAC,UAAU;gBACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAA4B,CAAC,CAAC;YAC/D,KAAK,qBAAW,CAAC,UAAU;gBACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAA4B,CAAC,CAAC;YAC/D,KAAK,qBAAW,CAAC,IAAI;gBACjB,OAAO,yCAAmB,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC9D,KAAK,qBAAW,CAAC,gBAAgB;gBAC7B,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAkC,CAAC,CAAC;YAC3E;gBACI,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAChD;IACL,CAAC;IAEO,aAAa,CAAC,KAAiB,EAAE,MAAe;QACpD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACvC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAQ,CAAC;QACtD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,UAAU,YAAY,KAAK,CAAC,EAAE;YAC3D,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACvC;QAED,MAAM,WAAW,GAAG,UAAU,CAAC,CAAC,CAAgB,CAAC;QAEjD,QAAQ,WAAW,EAAE;YACjB,KAAK,qBAAW,CAAC,UAAU;gBACvB,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,CAAC;YACpF,KAAK,qBAAW,CAAC,UAAU;gBACvB,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,CAAC;YACpF,KAAK,qBAAW,CAAC,UAAU;gBACvB,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,CAAC;YACpF,KAAK,qBAAW,CAAC,IAAI;gBACjB,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAC/C,KAAK,qBAAW,CAAC,KAAK;gBAClB,OAAO,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAChD;gBACI,6EAA6E;gBAC7E,MAAM,CAAC,GAAG,CAAC,kBAAQ,CAAC,WAAW,EAAE,wBAAwB,GAAG,WAAW,GAAG,YAAY,CAAC,CAAC;gBACxF,OAAO,IAAI,CAAC;SACnB;IACL,CAAC;IAEO,mBAAmB,CAAC,UAAiB;QACzC,+FAA+F;QAC/F,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;SACzD;QAED,OAAO;YACH,kCAAkC;YAClC,cAAc,EAAE,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YAClE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;YACpB,IAAI,EAAE,qBAAW,CAAC,KAAK;SACZ,CAAC;IACpB,CAAC;IAEO,kBAAkB,CAAC,UAAiB;QACxC,+FAA+F;QAC/F,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACxD;QAED,OAAO;YACH,iCAAiC;YACjC,IAAI,EAAE,qBAAW,CAAC,IAAI;SACX,CAAC;IACpB,CAAC;IAEO,wBAAwB,CAAC,OAAuB,EAAE,UAAiB;QACvE,+FAA+F;QAC/F,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;QAED,MAAM,YAAY,GAAG,UAAU,CAAC,CAAC,CAAW,CAAC;QAC7C,IAAI,YAAY,EAAE;YACd,OAAO;gBACH,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;gBACxB,OAAO;gBACP,YAAY;gBACZ,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,UAAU,CAAC,CAAC,CAAW;gBAC/B,IAAI,EAAE,qBAAW,CAAC,UAAU;aAC/B,CAAC;SACL;aAAM;YACH,OAAO;gBACH,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;gBACxB,OAAO;gBACP,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;gBACrB,IAAI,EAAE,qBAAW,CAAC,UAAU;aAC/B,CAAC;SACL;IAEL,CAAC;IAEO,wBAAwB,CAAC,OAAuB,EAAE,UAAiB;QACvE,+FAA+F;QAC/F,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;QAED,OAAO;YACH,OAAO;YACP,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC;YAC3B,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;YACnB,IAAI,EAAE,qBAAW,CAAC,UAAU;SACV,CAAC;IAC3B,CAAC;IAEO,wBAAwB,CAAC,OAAuB,EAAE,UAAiB;QACvE,+FAA+F;QAC/F,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;QAED,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAEjC,IAAI,UAAU,KAAK,IAAI,CAAC,WAAW,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1D,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;QAED,IAAI,KAAyB,CAAC;QAC9B,IAAI,MAAW,CAAC;QAEhB,QAAQ,UAAU,EAAE;YAChB,KAAK,IAAI,CAAC,YAAY;gBAClB,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBACtB,MAAM;YACV,KAAK,IAAI,CAAC,cAAc;gBACpB,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBACvB,MAAM;SACb;QAED,MAAM,iBAAiB,GAAsB;YACzC,KAAK;YACL,OAAO;YACP,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC;YAC3B,MAAM;YACN,IAAI,EAAE,qBAAW,CAAC,UAAU;SAC/B,CAAC;QAEF,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAEO,gBAAgB,CAAC,iBAAoC;QACzD,IAAI,OAAY,CAAC;QACjB,IAAI,iBAAiB,CAAC,SAAS,EAAE;YAC7B,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,qBAAW,CAAC,UAAU,EAAE,iBAAiB,CAAC,OAAO,IAAI,EAAE,EAAE,iBAAiB,CAAC,YAAY,IAAI,IAAI;gBAC/H,iBAAiB,CAAC,MAAM,EAAE,iBAAiB,CAAC,SAAS,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC;SACxF;aAAM;YACH,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,qBAAW,CAAC,UAAU,EAAE,iBAAiB,CAAC,OAAO,IAAI,EAAE,EAAE,iBAAiB,CAAC,YAAY,IAAI,IAAI;gBAC/H,iBAAiB,CAAC,MAAM,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC;SAC3D;QAED,OAAO,yCAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,sBAAsB,CAAC,uBAAgD;QAC3E,IAAI,OAAY,CAAC;QACjB,IAAI,uBAAuB,CAAC,SAAS,EAAE;YACnC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,qBAAW,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,OAAO,IAAI,EAAE,EAAE,uBAAuB,CAAC,YAAY;gBACzI,uBAAuB,CAAC,MAAM,EAAE,uBAAuB,CAAC,SAAS,EAAE,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC;SAC1G;aAAM;YACH,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,qBAAW,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,OAAO,IAAI,EAAE,EAAE,uBAAuB,CAAC,YAAY;gBACzI,uBAAuB,CAAC,MAAM,EAAE,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC;SACvE;QAED,OAAO,yCAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,gBAAgB,CAAC,iBAAoC;QACzD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,qBAAW,CAAC,UAAU,EAAE,iBAAiB,CAAC,OAAO,IAAI,EAAE,EAAE,iBAAiB,CAAC,YAAY;YAC7H,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;QAEzB,OAAO,yCAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,gBAAgB,CAAC,iBAAoC;QACzD,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;QAEnI,IAAI,OAAY,CAAC;QACjB,QAAQ,UAAU,EAAE;YAChB,KAAK,IAAI,CAAC,YAAY;gBAClB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,qBAAW,CAAC,UAAU,EAAE,iBAAiB,CAAC,OAAO,IAAI,EAAE,EAAE,iBAAiB,CAAC,YAAY,EAAE,UAAU,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC/J,MAAM;YACV,KAAK,IAAI,CAAC,WAAW;gBACjB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,qBAAW,CAAC,UAAU,EAAE,iBAAiB,CAAC,OAAO,IAAI,EAAE,EAAE,iBAAiB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC;gBACtI,MAAM;YACV,KAAK,IAAI,CAAC,cAAc;gBACpB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,qBAAW,CAAC,UAAU,EAAE,iBAAiB,CAAC,OAAO,IAAI,EAAE,EAAE,iBAAiB,CAAC,YAAY,EAAE,UAAU,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;gBAChK,MAAM;SACb;QAED,OAAO,yCAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,sBAAsB,CAAC,uBAAgD;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,qBAAW,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,OAAO,IAAI,EAAE,EAAE,uBAAuB,CAAC,YAAY,CAAC,CAAC,CAAC;QAElJ,OAAO,yCAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,YAAY,CAAC,UAAe;QAChC,MAAM,OAAO,GAAmB,UAAU,CAAC,CAAC,CAAmB,CAAC;QAChE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACvC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ;AAtSD,wDAsSC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { Encoder, Decoder } from \"@msgpack/msgpack\";\r\n\r\nimport { MessagePackOptions } from \"./MessagePackOptions\";\r\n\r\nimport {\r\n    CancelInvocationMessage, CompletionMessage, HubMessage, IHubProtocol, ILogger, InvocationMessage,\r\n    LogLevel, MessageHeaders, MessageType, NullLogger, StreamInvocationMessage, StreamItemMessage, TransferFormat,\r\n} from \"@microsoft/signalr\";\r\n\r\nimport { BinaryMessageFormat } from \"./BinaryMessageFormat\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n\r\n// TypeDoc's @inheritDoc and @link don't work across modules :(\r\n\r\n// constant encoding of the ping message\r\n// see: https://github.com/aspnet/SignalR/blob/dev/specs/HubProtocol.md#ping-message-encoding-1\r\n// Don't use Uint8Array.from as IE does not support it\r\nconst SERIALIZED_PING_MESSAGE: Uint8Array = new Uint8Array([0x91, MessageType.Ping]);\r\n\r\n/** Implements the MessagePack Hub Protocol */\r\nexport class MessagePackHubProtocol implements IHubProtocol {\r\n    /** The name of the protocol. This is used by SignalR to resolve the protocol between the client and server. */\r\n    public readonly name: string = \"messagepack\";\r\n    /** The version of the protocol. */\r\n    public readonly version: number = 1;\r\n    /** The TransferFormat of the protocol. */\r\n    public readonly transferFormat: TransferFormat = TransferFormat.Binary;\r\n\r\n    private readonly _errorResult = 1;\r\n    private readonly _voidResult = 2;\r\n    private readonly _nonVoidResult = 3;\r\n\r\n    private readonly _encoder: Encoder<undefined>;\r\n    private readonly _decoder: Decoder<undefined>;\r\n\r\n    /**\r\n     *\r\n     * @param messagePackOptions MessagePack options passed to @msgpack/msgpack\r\n     */\r\n    constructor(messagePackOptions?: MessagePackOptions) {\r\n        messagePackOptions = messagePackOptions || {};\r\n        this._encoder = new Encoder(\r\n            messagePackOptions.extensionCodec,\r\n            messagePackOptions.context,\r\n            messagePackOptions.maxDepth,\r\n            messagePackOptions.initialBufferSize,\r\n            messagePackOptions.sortKeys,\r\n            messagePackOptions.forceFloat32,\r\n            messagePackOptions.ignoreUndefined,\r\n            messagePackOptions.forceIntegerToFloat,\r\n        );\r\n\r\n        this._decoder = new Decoder(\r\n            messagePackOptions.extensionCodec,\r\n            messagePackOptions.context,\r\n            messagePackOptions.maxStrLength,\r\n            messagePackOptions.maxBinLength,\r\n            messagePackOptions.maxArrayLength,\r\n            messagePackOptions.maxMapLength,\r\n            messagePackOptions.maxExtLength,\r\n        );\r\n    }\r\n\r\n    /** Creates an array of HubMessage objects from the specified serialized representation.\r\n     *\r\n     * @param {ArrayBuffer} input An ArrayBuffer containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    public parseMessages(input: ArrayBuffer, logger: ILogger): HubMessage[] {\r\n        // The interface does allow \"string\" to be passed in, but this implementation does not. So let's throw a useful error.\r\n        if (!(isArrayBuffer(input))) {\r\n            throw new Error(\"Invalid input for MessagePack hub protocol. Expected an ArrayBuffer.\");\r\n        }\r\n\r\n        if (logger === null) {\r\n            logger = NullLogger.instance;\r\n        }\r\n\r\n        const messages = BinaryMessageFormat.parse(input);\r\n\r\n        const hubMessages = [];\r\n        for (const message of messages) {\r\n            const parsedMessage = this._parseMessage(message, logger);\r\n            // Can be null for an unknown message. Unknown message is logged in parseMessage\r\n            if (parsedMessage) {\r\n                hubMessages.push(parsedMessage);\r\n            }\r\n        }\r\n\r\n        return hubMessages;\r\n    }\r\n\r\n    /** Writes the specified HubMessage to an ArrayBuffer and returns it.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {ArrayBuffer} An ArrayBuffer containing the serialized representation of the message.\r\n     */\r\n    public writeMessage(message: HubMessage): ArrayBuffer {\r\n        switch (message.type) {\r\n            case MessageType.Invocation:\r\n                return this._writeInvocation(message as InvocationMessage);\r\n            case MessageType.StreamInvocation:\r\n                return this._writeStreamInvocation(message as StreamInvocationMessage);\r\n            case MessageType.StreamItem:\r\n                return this._writeStreamItem(message as StreamItemMessage);\r\n            case MessageType.Completion:\r\n                return this._writeCompletion(message as CompletionMessage);\r\n            case MessageType.Ping:\r\n                return BinaryMessageFormat.write(SERIALIZED_PING_MESSAGE);\r\n            case MessageType.CancelInvocation:\r\n                return this._writeCancelInvocation(message as CancelInvocationMessage);\r\n            default:\r\n                throw new Error(\"Invalid message type.\");\r\n        }\r\n    }\r\n\r\n    private _parseMessage(input: Uint8Array, logger: ILogger): HubMessage | null {\r\n        if (input.length === 0) {\r\n            throw new Error(\"Invalid payload.\");\r\n        }\r\n\r\n        const properties = this._decoder.decode(input) as any;\r\n        if (properties.length === 0 || !(properties instanceof Array)) {\r\n            throw new Error(\"Invalid payload.\");\r\n        }\r\n\r\n        const messageType = properties[0] as MessageType;\r\n\r\n        switch (messageType) {\r\n            case MessageType.Invocation:\r\n                return this._createInvocationMessage(this._readHeaders(properties), properties);\r\n            case MessageType.StreamItem:\r\n                return this._createStreamItemMessage(this._readHeaders(properties), properties);\r\n            case MessageType.Completion:\r\n                return this._createCompletionMessage(this._readHeaders(properties), properties);\r\n            case MessageType.Ping:\r\n                return this._createPingMessage(properties);\r\n            case MessageType.Close:\r\n                return this._createCloseMessage(properties);\r\n            default:\r\n                // Future protocol changes can add message types, old clients can ignore them\r\n                logger.log(LogLevel.Information, \"Unknown message type '\" + messageType + \"' ignored.\");\r\n                return null;\r\n        }\r\n    }\r\n\r\n    private _createCloseMessage(properties: any[]): HubMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 2) {\r\n            throw new Error(\"Invalid payload for Close message.\");\r\n        }\r\n\r\n        return {\r\n            // Close messages have no headers.\r\n            allowReconnect: properties.length >= 3 ? properties[2] : undefined,\r\n            error: properties[1],\r\n            type: MessageType.Close,\r\n        } as HubMessage;\r\n    }\r\n\r\n    private _createPingMessage(properties: any[]): HubMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 1) {\r\n            throw new Error(\"Invalid payload for Ping message.\");\r\n        }\r\n\r\n        return {\r\n            // Ping messages have no headers.\r\n            type: MessageType.Ping,\r\n        } as HubMessage;\r\n    }\r\n\r\n    private _createInvocationMessage(headers: MessageHeaders, properties: any[]): InvocationMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 5) {\r\n            throw new Error(\"Invalid payload for Invocation message.\");\r\n        }\r\n\r\n        const invocationId = properties[2] as string;\r\n        if (invocationId) {\r\n            return {\r\n                arguments: properties[4],\r\n                headers,\r\n                invocationId,\r\n                streamIds: [],\r\n                target: properties[3] as string,\r\n                type: MessageType.Invocation,\r\n            };\r\n        } else {\r\n            return {\r\n                arguments: properties[4],\r\n                headers,\r\n                streamIds: [],\r\n                target: properties[3],\r\n                type: MessageType.Invocation,\r\n            };\r\n        }\r\n\r\n    }\r\n\r\n    private _createStreamItemMessage(headers: MessageHeaders, properties: any[]): StreamItemMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 4) {\r\n            throw new Error(\"Invalid payload for StreamItem message.\");\r\n        }\r\n\r\n        return {\r\n            headers,\r\n            invocationId: properties[2],\r\n            item: properties[3],\r\n            type: MessageType.StreamItem,\r\n        } as StreamItemMessage;\r\n    }\r\n\r\n    private _createCompletionMessage(headers: MessageHeaders, properties: any[]): CompletionMessage {\r\n        // check minimum length to allow protocol to add items to the end of objects in future releases\r\n        if (properties.length < 4) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        const resultKind = properties[3];\r\n\r\n        if (resultKind !== this._voidResult && properties.length < 5) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        let error: string | undefined;\r\n        let result: any;\r\n\r\n        switch (resultKind) {\r\n            case this._errorResult:\r\n                error = properties[4];\r\n                break;\r\n            case this._nonVoidResult:\r\n                result = properties[4];\r\n                break;\r\n        }\r\n\r\n        const completionMessage: CompletionMessage = {\r\n            error,\r\n            headers,\r\n            invocationId: properties[2],\r\n            result,\r\n            type: MessageType.Completion,\r\n        };\r\n\r\n        return completionMessage;\r\n    }\r\n\r\n    private _writeInvocation(invocationMessage: InvocationMessage): ArrayBuffer {\r\n        let payload: any;\r\n        if (invocationMessage.streamIds) {\r\n            payload = this._encoder.encode([MessageType.Invocation, invocationMessage.headers || {}, invocationMessage.invocationId || null,\r\n            invocationMessage.target, invocationMessage.arguments, invocationMessage.streamIds]);\r\n        } else {\r\n            payload = this._encoder.encode([MessageType.Invocation, invocationMessage.headers || {}, invocationMessage.invocationId || null,\r\n            invocationMessage.target, invocationMessage.arguments]);\r\n        }\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _writeStreamInvocation(streamInvocationMessage: StreamInvocationMessage): ArrayBuffer {\r\n        let payload: any;\r\n        if (streamInvocationMessage.streamIds) {\r\n            payload = this._encoder.encode([MessageType.StreamInvocation, streamInvocationMessage.headers || {}, streamInvocationMessage.invocationId,\r\n            streamInvocationMessage.target, streamInvocationMessage.arguments, streamInvocationMessage.streamIds]);\r\n        } else {\r\n            payload = this._encoder.encode([MessageType.StreamInvocation, streamInvocationMessage.headers || {}, streamInvocationMessage.invocationId,\r\n            streamInvocationMessage.target, streamInvocationMessage.arguments]);\r\n        }\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _writeStreamItem(streamItemMessage: StreamItemMessage): ArrayBuffer {\r\n        const payload = this._encoder.encode([MessageType.StreamItem, streamItemMessage.headers || {}, streamItemMessage.invocationId,\r\n        streamItemMessage.item]);\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _writeCompletion(completionMessage: CompletionMessage): ArrayBuffer {\r\n        const resultKind = completionMessage.error ? this._errorResult : completionMessage.result ? this._nonVoidResult : this._voidResult;\r\n\r\n        let payload: any;\r\n        switch (resultKind) {\r\n            case this._errorResult:\r\n                payload = this._encoder.encode([MessageType.Completion, completionMessage.headers || {}, completionMessage.invocationId, resultKind, completionMessage.error]);\r\n                break;\r\n            case this._voidResult:\r\n                payload = this._encoder.encode([MessageType.Completion, completionMessage.headers || {}, completionMessage.invocationId, resultKind]);\r\n                break;\r\n            case this._nonVoidResult:\r\n                payload = this._encoder.encode([MessageType.Completion, completionMessage.headers || {}, completionMessage.invocationId, resultKind, completionMessage.result]);\r\n                break;\r\n        }\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _writeCancelInvocation(cancelInvocationMessage: CancelInvocationMessage): ArrayBuffer {\r\n        const payload = this._encoder.encode([MessageType.CancelInvocation, cancelInvocationMessage.headers || {}, cancelInvocationMessage.invocationId]);\r\n\r\n        return BinaryMessageFormat.write(payload.slice());\r\n    }\r\n\r\n    private _readHeaders(properties: any): MessageHeaders {\r\n        const headers: MessageHeaders = properties[1] as MessageHeaders;\r\n        if (typeof headers !== \"object\") {\r\n            throw new Error(\"Invalid headers.\");\r\n        }\r\n        return headers;\r\n    }\r\n}\r\n"]}