{"version": 3, "file": "Utils.js", "sourceRoot": "", "sources": ["../../src/Utils.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAEvE,+BAA+B;AAC/B,eAAe;AACf,SAAgB,aAAa,CAAC,GAAQ;IAClC,OAAO,GAAG,IAAI,OAAO,WAAW,KAAK,WAAW;QAC5C,CAAC,GAAG,YAAY,WAAW;YAC3B,kEAAkE;YAClE,CAAC,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC;AACrE,CAAC;AALD,sCAKC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Copied from signalr/Utils.ts\r\n/** @private */\r\nexport function isArrayBuffer(val: any): val is ArrayBuffer {\r\n    return val && typeof ArrayBuffer !== \"undefined\" &&\r\n        (val instanceof ArrayBuffer ||\r\n        // Sometimes we get an ArrayBuffer that doesn't satisfy instanceof\r\n        (val.constructor && val.constructor.name === \"ArrayBuffer\"));\r\n}\r\n"]}