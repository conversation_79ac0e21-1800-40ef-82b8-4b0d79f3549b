"use strict";
// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessagePackHubProtocol = exports.VERSION = void 0;
// Version token that will be replaced by the prepack command
/** The version of the SignalR Message Pack protocol library. */
exports.VERSION = "6.0.6";
var MessagePackHubProtocol_1 = require("./MessagePackHubProtocol");
Object.defineProperty(exports, "MessagePackHubProtocol", { enumerable: true, get: function () { return MessagePackHubProtocol_1.MessagePackHubProtocol; } });
//# sourceMappingURL=index.js.map