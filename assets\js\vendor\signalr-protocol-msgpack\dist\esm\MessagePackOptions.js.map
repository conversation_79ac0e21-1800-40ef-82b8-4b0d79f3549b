{"version": 3, "file": "MessagePackOptions.js", "sourceRoot": "", "sources": ["../../src/MessagePackOptions.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n/**\r\n * MessagePack Options per:\r\n * {@link https://github.com/msgpack/msgpack-javascript#api msgpack-javascript Options}\r\n */\r\nexport interface MessagePackOptions {\r\n\r\n  /**\r\n   * @name extensionCodec encoding, decoding extensions: default ExtensionCodec.defaultCodec\r\n   */\r\n  extensionCodec?: any;\r\n\r\n  /**\r\n   * @name context user-defined context\r\n   */\r\n  context?: any;\r\n\r\n  // encode options\r\n\r\n  /**\r\n   * @name maxDepth maximum object depth for encoding\r\n   */\r\n  maxDepth?: number;\r\n\r\n  /**\r\n   * @name initialBufferSize starting encode buffer size\r\n   */\r\n  initialBufferSize?: number;\r\n\r\n  /**\r\n   * @name sortKeys Force a determinate key order for encoding\r\n   */\r\n  sortKeys?: boolean;\r\n\r\n  /**\r\n   * @name forceFloat32 Force floats to be encoded as 32-bit floats\r\n   */\r\n  forceFloat32?: boolean;\r\n\r\n  /**\r\n   * @name forceIntegerToFloat Force integers to be encoded as floats\r\n   */\r\n  forceIntegerToFloat?: boolean;\r\n\r\n  /**\r\n   * @name ignoreUndefined ignore undefined values when encoding\r\n   */\r\n  ignoreUndefined?: boolean;\r\n\r\n  // decode options\r\n\r\n  /**\r\n   * @name maxStrLength maximum string decoding length\r\n   */\r\n  maxStrLength?: number;\r\n\r\n  /**\r\n   * @name maxBinLength maximum binary decoding length\r\n   */\r\n  maxBinLength?: number;\r\n\r\n  /**\r\n   * @name maxArrayLength maximum array decoding length\r\n   */\r\n  maxArrayLength?: number;\r\n\r\n  /**\r\n   * @name maxMapLength maximum map decoding length\r\n   */\r\n  maxMapLength?: number;\r\n\r\n  /**\r\n   * @name maxExtLength maximum decoding length\r\n   */\r\n  maxExtLength?: number;\r\n}\r\n"]}