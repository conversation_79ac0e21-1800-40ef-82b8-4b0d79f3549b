{"version": 3, "file": "browser-index.js", "sourceRoot": "", "sources": ["../../src/browser-index.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAEvE,qHAAqH;AAErH,cAAc,SAAS,CAAC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This is where we add any polyfills we'll need for the browser. It is the entry module for browser-specific builds.\r\n\r\nexport * from \"./index\";\r\n"]}