{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAEvE,6DAA6D;AAC7D,gEAAgE;AAChE,MAAM,CAAC,MAAM,OAAO,GAAG,iBAAiB,CAAC;AAEzC,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Version token that will be replaced by the prepack command\r\n/** The version of the SignalR Message Pack protocol library. */\r\nexport const VERSION = \"0.0.0-DEV_BUILD\";\r\n\r\nexport { MessagePackHubProtocol } from \"./MessagePackHubProtocol\";\r\n\r\nexport { MessagePackOptions } from \"./MessagePackOptions\";\r\n"]}