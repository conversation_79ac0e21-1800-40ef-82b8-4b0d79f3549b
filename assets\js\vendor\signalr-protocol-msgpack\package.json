{"name": "@microsoft/signalr-protocol-msgpack", "version": "6.0.6", "description": "MsgPack Protocol support for ASP.NET Core SignalR", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "typings": "./dist/esm/index.d.ts", "umd": "./dist/browser/signalr-protocol-msgpack.js", "umd_name": "signalR.protocols.msgpack", "unpkg": "./dist/browser/signalr-protocol-msgpack.js", "directories": {"test": "spec"}, "sideEffects": false, "scripts": {"preclean": "cd ../common && yarn install --mutex network --frozen-lockfile", "clean": "node ../common/node_modules/rimraf/bin.js ./dist", "prebuild": "yarn run clean && yarn install --mutex network --frozen-lockfile", "build": "yarn run build:lint && yarn run build:esm && yarn run build:cjs && yarn run build:browser && yarn run build:uglify", "build:lint": "node ../common/node_modules/eslint/bin/eslint ./src --ext .ts --resolve-plugins-relative-to ../common", "build:esm": "node ../common/node_modules/typescript/bin/tsc --project ./tsconfig.json --module es2015 --outDir ./dist/esm -d", "build:cjs": "node ../common/node_modules/typescript/bin/tsc --project ./tsconfig.json --module commonjs --outDir ./dist/cjs", "build:browser": "node ../common/node_modules/webpack-cli/bin/cli.js", "build:uglify": "node ../common/node_modules/terser/bin/terser -m -c --ecma 2019 --module --source-map \"url='signalr-protocol-msgpack.min.js.map',content='./dist/browser/signalr-protocol-msgpack.js.map'\" --comments -o ./dist/browser/signalr-protocol-msgpack.min.js ./dist/browser/signalr-protocol-msgpack.js", "prepack": "node ../build/embed-version.js", "test": "echo \"Run 'yarn test' in the 'clients/ts' folder to test this package\" && exit 1"}, "keywords": ["signalr", "aspnetcore"], "repository": {"type": "git", "url": "git+https://github.com/dotnet/aspnetcore.git"}, "author": "Microsoft", "license": "MIT", "bugs": {"url": "https://github.com/dotnet/aspnetcore/issues"}, "homepage": "https://github.com/dotnet/aspnetcore/tree/main/src/SignalR#readme", "files": ["dist/**/*", "src/**/*"], "dependencies": {"@microsoft/signalr": ">=6.0.6", "@msgpack/msgpack": "^2.7.0"}, "devDependencies": {}, "resolutions": {"url-parse": ">=1.5.8", "node-fetch": ">=2.6.7"}}