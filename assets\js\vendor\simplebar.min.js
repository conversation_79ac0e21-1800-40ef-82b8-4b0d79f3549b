!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).SimpleBar=e()}(this,function(){"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function e(t,e){return t(e={exports:{}},e.exports),e.exports}function r(t){return t&&t.Math==Math&&t}function g(t){try{return!!t()}catch(t){return!0}}function m(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}function n(t){return T.call(t).slice(8,-1)}function c(t){if(null==t)throw TypeError("Can't call method on "+t);return t}function u(t){return R(c(t))}function y(t){return"object"==typeof t?null!==t:"function"==typeof t}function i(t,e){if(!y(t))return t;var r,i;if(e&&"function"==typeof(r=t.toString)&&!y(i=r.call(t)))return i;if("function"==typeof(r=t.valueOf)&&!y(i=r.call(t)))return i;if(!e&&"function"==typeof(r=t.toString)&&!y(i=r.call(t)))return i;throw TypeError("Can't convert object to primitive value")}function h(t,e){return W.call(t,e)}function o(t){return C?z.createElement(t):{}}function O(t){if(!y(t))throw TypeError(String(t)+" is not an object");return t}function f(e,r){try{F(S,e,r)}catch(t){S[e]=r}return r}function a(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++X+Y).toString(36)}function s(t){return G[t]||(G[t]=a(t))}var l,d,p,v,b,x,E,w,_="object",S=r(typeof globalThis==_&&globalThis)||r(typeof window==_&&window)||r(typeof self==_&&self)||r(typeof t==_&&t)||Function("return this")(),A=!g(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}),k={}.propertyIsEnumerable,L=Object.getOwnPropertyDescriptor,M={f:L&&!k.call({1:2},1)?function(t){var e=L(this,t);return!!e&&e.enumerable}:k},T={}.toString,j="".split,R=g(function(){return!Object("z").propertyIsEnumerable(0)})?function(t){return"String"==n(t)?j.call(t,""):Object(t)}:Object,W={}.hasOwnProperty,z=S.document,C=y(z)&&y(z.createElement),N=!A&&!g(function(){return 7!=Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}),I=Object.getOwnPropertyDescriptor,D={f:A?I:function(t,e){if(t=u(t),e=i(e,!0),N)try{return I(t,e)}catch(t){}if(h(t,e))return m(!M.f.call(t,e),t[e])}},P=Object.defineProperty,V={f:A?P:function(t,e,r){if(O(t),e=i(e,!0),O(r),N)try{return P(t,e,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},F=A?function(t,e,r){return V.f(t,e,m(1,r))}:function(t,e,r){return t[e]=r,t},B=e(function(t){var r=S["__core-js_shared__"]||f("__core-js_shared__",{});(t.exports=function(t,e){return r[t]||(r[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.2.1",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})}),H=B("native-function-to-string",Function.toString),q=S.WeakMap,$="function"==typeof q&&/native code/.test(H.call(q)),X=0,Y=Math.random(),G=B("keys"),U={},Q=S.WeakMap;E=$?(l=new Q,d=l.get,p=l.has,v=l.set,b=function(t,e){return v.call(l,t,e),e},x=function(t){return d.call(l,t)||{}},function(t){return p.call(l,t)}):(w=s("state"),U[w]=!0,b=function(t,e){return F(t,w,e),e},x=function(t){return h(t,w)?t[w]:{}},function(t){return h(t,w)});function K(t){return"function"==typeof t?t:void 0}function J(t,e){return arguments.length<2?K(pt[t])||K(S[t]):pt[t]&&pt[t][e]||S[t]&&S[t][e]}function Z(t){return isNaN(t=+t)?0:(0<t?gt:vt)(t)}function tt(t){return 0<t?yt(Z(t),9007199254740991):0}function et(l){return function(t,e,r){var i,n,o,s=u(t),a=tt(s.length),c=(n=a,(o=Z(r))<0?bt(o+n,0):mt(o,n));if(l&&e!=e){for(;c<a;)if((i=s[c++])!=i)return!0}else for(;c<a;c++)if((l||c in s)&&s[c]===e)return l||c||0;return!l&&-1}}function rt(t,e){var r,i=u(t),n=0,o=[];for(r in i)!h(U,r)&&h(i,r)&&o.push(r);for(;e.length>n;)h(i,r=e[n++])&&(~xt(o,r)||o.push(r));return o}function it(t,e){var r=Lt[kt(t)];return r==Tt||r!=Mt&&("function"==typeof e?g(e):!!e)}function nt(t,e){var r,i,n,o,s,a=t.target,c=t.global,l=t.stat;if(r=c?S:l?S[a]||f(a,{}):(S[a]||{}).prototype)for(i in e){if(o=e[i],n=t.noTargetGet?(s=Rt(r,i))&&s.value:r[i],!jt(c?i:a+(l?".":"#")+i,t.forced)&&void 0!==n){if(typeof o==typeof n)continue;!function(t,e){for(var r=St(e),i=V.f,n=D.f,o=0;o<r.length;o++){var s=r[o];h(t,s)||i(t,s,n(e,s))}}(o,n)}(t.sham||n&&n.sham)&&F(o,"sham",!0),dt(r,i,o,t)}}function ot(t){if("function"!=typeof t)throw TypeError(String(t)+" is not a function");return t}function st(i,n,t){if(ot(i),void 0===n)return i;switch(t){case 0:return function(){return i.call(n)};case 1:return function(t){return i.call(n,t)};case 2:return function(t,e){return i.call(n,t,e)};case 3:return function(t,e,r){return i.call(n,t,e,r)}}return function(){return i.apply(n,arguments)}}function at(t){return Object(c(t))}function ct(t){return Nt[t]||(Nt[t]=zt&&Ct[t]||(zt?Ct:a)("Symbol."+t))}function lt(t,e){var r;return Wt(t)&&("function"==typeof(r=t.constructor)&&(r===Array||Wt(r.prototype))||y(r)&&null===(r=r[It]))&&(r=void 0),new(void 0===r?Array:r)(0===e?0:e)}function ut(d){var p=1==d,v=2==d,g=3==d,y=4==d,b=6==d,m=5==d||b;return function(t,e,r,i){for(var n,o,s=at(t),a=R(s),c=st(e,r,3),l=tt(a.length),u=0,h=i||lt,f=p?h(t,l):v?h(t,0):void 0;u<l;u++)if((m||u in a)&&(o=c(n=a[u],u,s),d))if(p)f[u]=o;else if(o)switch(d){case 3:return!0;case 5:return n;case 6:return u;case 2:Dt.call(f,n)}else if(y)return!1;return b?-1:g||y?y:f}}function ht(t,e){var r=[][t];return!r||!g(function(){r.call(null,e||function(){throw 1},1)})}var ft={set:b,get:x,has:E,enforce:function(t){return E(t)?x(t):b(t,{})},getterFor:function(r){return function(t){var e;if(!y(t)||(e=x(t)).type!==r)throw TypeError("Incompatible receiver, "+r+" required");return e}}},dt=e(function(t){var e=ft.get,a=ft.enforce,c=String(H).split("toString");B("inspectSource",function(t){return H.call(t)}),(t.exports=function(t,e,r,i){var n=!!i&&!!i.unsafe,o=!!i&&!!i.enumerable,s=!!i&&!!i.noTargetGet;"function"==typeof r&&("string"!=typeof e||h(r,"name")||F(r,"name",e),a(r).source=c.join("string"==typeof e?e:"")),t!==S?(n?!s&&t[e]&&(o=!0):delete t[e],o?t[e]=r:F(t,e,r)):o?t[e]=r:f(e,r)})(Function.prototype,"toString",function(){return"function"==typeof this&&e(this).source||H.call(this)})}),pt=S,vt=Math.ceil,gt=Math.floor,yt=Math.min,bt=Math.max,mt=Math.min,xt=(et(!0),et(!1)),Et=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],wt=Et.concat("length","prototype"),Ot={f:Object.getOwnPropertyNames||function(t){return rt(t,wt)}},_t={f:Object.getOwnPropertySymbols},St=J("Reflect","ownKeys")||function(t){var e=Ot.f(O(t)),r=_t.f;return r?e.concat(r(t)):e},At=/#|\.prototype\./,kt=it.normalize=function(t){return String(t).replace(At,".").toLowerCase()},Lt=it.data={},Mt=it.NATIVE="N",Tt=it.POLYFILL="P",jt=it,Rt=D.f,Wt=Array.isArray||function(t){return"Array"==n(t)},zt=!!Object.getOwnPropertySymbols&&!g(function(){return!String(Symbol())}),Ct=S.Symbol,Nt=B("wks"),It=ct("species"),Dt=[].push,Pt={forEach:ut(0),map:ut(1),filter:ut(2),some:ut(3),every:ut(4),find:ut(5),findIndex:ut(6)},Vt=Pt.forEach,Ft=ht("forEach")?function(t){return Vt(this,t,1<arguments.length?arguments[1]:void 0)}:[].forEach;nt({target:"Array",proto:!0,forced:[].forEach!=Ft},{forEach:Ft});var Bt={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0};for(var Ht in Bt){var qt=S[Ht],$t=qt&&qt.prototype;if($t&&$t.forEach!==Ft)try{F($t,"forEach",Ft)}catch(t){$t.forEach=Ft}}var Xt=!("undefined"==typeof window||!window.document||!window.document.createElement),Yt=ct("species"),Gt=Pt.filter;nt({target:"Array",proto:!0,forced:!!g(function(){var t=[];return(t.constructor={})[Yt]=function(){return{foo:1}},1!==t.filter(Boolean).foo})},{filter:function(t){return Gt(this,t,1<arguments.length?arguments[1]:void 0)}});function Ut(){}var Qt=Object.keys||function(t){return rt(t,Et)},Kt=A?Object.defineProperties:function(t,e){O(t);for(var r,i=Qt(e),n=i.length,o=0;o<n;)V.f(t,r=i[o++],e[r]);return t},Jt=J("document","documentElement"),Zt=s("IE_PROTO"),te=function(){var t,e=o("iframe"),r=Et.length;for(e.style.display="none",Jt.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write("<script>document.F=Object<\/script>"),t.close(),te=t.F;r--;)delete te.prototype[Et[r]];return te()},ee=Object.create||function(t,e){var r;return null!==t?(Ut.prototype=O(t),r=new Ut,Ut.prototype=null,r[Zt]=t):r=te(),void 0===e?r:Kt(r,e)};U[Zt]=!0;var re=ct("unscopables"),ie=Array.prototype;null==ie[re]&&F(ie,re,ee(null));function ne(t){ie[re][t]=!0}var oe,se,ae,ce={},le=!g(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}),ue=s("IE_PROTO"),he=Object.prototype,fe=le?Object.getPrototypeOf:function(t){return t=at(t),h(t,ue)?t[ue]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?he:null},de=ct("iterator"),pe=!1;[].keys&&("next"in(ae=[].keys())?(se=fe(fe(ae)))!==Object.prototype&&(oe=se):pe=!0),null==oe&&(oe={}),h(oe,de)||F(oe,de,function(){return this});function ve(t,e,r){t&&!h(t=r?t:t.prototype,Ee)&&xe(t,Ee,{configurable:!0,value:e})}function ge(){return this}function ye(){return this}function be(t,e,r,i,n,o,s){var a,c;function l(t){if(t===n&&y)return y;if(!Se&&t in v)return v[t];switch(t){case"keys":case"values":case"entries":return function(){return new r(this,t)}}return function(){return new r(this)}}c=e+" Iterator",(a=r).prototype=ee(we,{next:m(1,i)}),ve(a,c,!1),ce[c]=ge;var u,h,f,d=e+" Iterator",p=!1,v=t.prototype,g=v[Ae]||v["@@iterator"]||n&&v[n],y=!Se&&g||l(n),b="Array"==e&&v.entries||g;if(b&&(u=fe(b.call(new t)),_e!==Object.prototype&&u.next&&(fe(u)!==_e&&(Oe?Oe(u,_e):"function"!=typeof u[Ae]&&F(u,Ae,ye)),ve(u,d,!0))),"values"==n&&g&&"values"!==g.name&&(p=!0,y=function(){return g.call(this)}),v[Ae]!==y&&F(v,Ae,y),ce[e]=y,n)if(h={values:l("values"),keys:o?y:l("keys"),entries:l("entries")},s)for(f in h)!Se&&!p&&f in v||dt(v,f,h[f]);else nt({target:e,proto:!0,forced:Se||p},h);return h}var me={IteratorPrototype:oe,BUGGY_SAFARI_ITERATORS:pe},xe=V.f,Ee=ct("toStringTag"),we=me.IteratorPrototype,Oe=Object.setPrototypeOf||("__proto__"in{}?function(){var r,i=!1,t={};try{(r=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(t,[]),i=t instanceof Array}catch(r){}return function(t,e){return O(t),function(){if(!y(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype")}(),i?r.call(t,e):t.__proto__=e,t}}():void 0),_e=me.IteratorPrototype,Se=me.BUGGY_SAFARI_ITERATORS,Ae=ct("iterator"),ke=ft.set,Le=ft.getterFor("Array Iterator"),Me=be(Array,"Array",function(t,e){ke(this,{type:"Array Iterator",target:u(t),index:0,kind:e})},function(){var t=Le(this),e=t.target,r=t.kind,i=t.index++;return!e||i>=e.length?{value:t.target=void 0,done:!0}:"keys"==r?{value:i,done:!1}:"values"==r?{value:e[i],done:!1}:{value:[i,e[i]],done:!1}},"values");ce.Arguments=ce.Array,ne("keys"),ne("values"),ne("entries");var Te=Object.assign,je=!Te||g(function(){var t={},e={},r=Symbol();return t[r]=7,"abcdefghijklmnopqrst".split("").forEach(function(t){e[t]=t}),7!=Te({},t)[r]||"abcdefghijklmnopqrst"!=Qt(Te({},e)).join("")})?function(t,e){for(var r=at(t),i=arguments.length,n=1,o=_t.f,s=M.f;n<i;)for(var a,c=R(arguments[n++]),l=o?Qt(c).concat(o(c)):Qt(c),u=l.length,h=0;h<u;)a=l[h++],A&&!s.call(c,a)||(r[a]=c[a]);return r}:Te;nt({target:"Object",stat:!0,forced:Object.assign!==je},{assign:je});function Re(t){var e,r,i;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),We))?r:ze?n(e):"Object"==(i=n(e))&&"function"==typeof e.callee?"Arguments":i}var We=ct("toStringTag"),ze="Arguments"==n(function(){return arguments}()),Ce={};Ce[ct("toStringTag")]="z";var Ne="[object z]"!==String(Ce)?function(){return"[object "+Re(this)+"]"}:Ce.toString,Ie=Object.prototype;Ne!==Ie.toString&&dt(Ie,"toString",Ne,{unsafe:!0});function De(r){return function(t){var e=String(c(t));return 1&r&&(e=e.replace(Fe,"")),2&r&&(e=e.replace(Be,"")),e}}var Pe="\t\n\v\f\r                　\u2028\u2029\ufeff",Ve="["+Pe+"]",Fe=RegExp("^"+Ve+Ve+"*"),Be=RegExp(Ve+Ve+"*$"),He=(De(1),De(2),De(3)),qe=S.parseInt,$e=/^[+-]?0[Xx]/,Xe=8!==qe(Pe+"08")||22!==qe(Pe+"0x16")?function(t,e){var r=He(String(t));return qe(r,e>>>0||($e.test(r)?16:10))}:qe;nt({global:!0,forced:parseInt!=Xe},{parseInt:Xe});function Ye(a){return function(t,e){var r,i,n=String(c(t)),o=Z(e),s=n.length;return o<0||s<=o?a?"":void 0:(r=n.charCodeAt(o))<55296||56319<r||o+1===s||(i=n.charCodeAt(o+1))<56320||57343<i?a?n.charAt(o):r:a?n.slice(o,o+2):i-56320+(r-55296<<10)+65536}}var Ge={codeAt:Ye(!1),charAt:Ye(!0)},Ue=Ge.charAt,Qe=ft.set,Ke=ft.getterFor("String Iterator");be(String,"String",function(t){Qe(this,{type:"String Iterator",string:String(t),index:0})},function(){var t,e=Ke(this),r=e.string,i=e.index;return i>=r.length?{value:void 0,done:!0}:(t=Ue(r,i),e.index+=t.length,{value:t,done:!1})});function Je(t,e,r){for(var i in e)dt(t,i,e[i],r);return t}function Ze(t,e,r){if(!(t instanceof e))throw TypeError("Incorrect "+(r?r+" ":"")+"invocation");return t}var tr=!g(function(){return Object.isExtensible(Object.preventExtensions({}))}),er=e(function(t){function r(t){e(t,i,{value:{objectID:"O"+ ++n,weakData:{}}})}var e=V.f,i=a("meta"),n=0,o=Object.isExtensible||function(){return!0},s=t.exports={REQUIRED:!1,fastKey:function(t,e){if(!y(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!h(t,i)){if(!o(t))return"F";if(!e)return"E";r(t)}return t[i].objectID},getWeakData:function(t,e){if(!h(t,i)){if(!o(t))return!0;if(!e)return!1;r(t)}return t[i].weakData},onFreeze:function(t){return tr&&s.REQUIRED&&o(t)&&!h(t,i)&&r(t),t}};U[i]=!0}),rr=(er.REQUIRED,er.fastKey,er.getWeakData,er.onFreeze,ct("iterator")),ir=Array.prototype,nr=ct("iterator"),or=e(function(t){function f(t,e){this.stopped=t,this.result=e}(t.exports=function(t,e,r,i,n){var o,s,a,c,l,u,h=st(e,r,i?2:1);if(n)o=t;else{if("function"!=typeof(s=function(){if(null!=t)return t[nr]||t["@@iterator"]||ce[Re(t)]}()))throw TypeError("Target is not iterable");if(void 0!==s&&(ce.Array===s||ir[rr]===s)){for(a=0,c=tt(t.length);a<c;a++)if((l=i?h(O(u=t[a])[0],u[1]):h(t[a]))&&l instanceof f)return l;return new f(!1)}o=s.call(t)}for(;!(u=o.next()).done;)if((l=function(t,e,r,i){try{return i?e(O(r)[0],r[1]):e(r)}catch(e){var n=t.return;throw void 0!==n&&O(n.call(t)),e}}(o,h,u.value,i))&&l instanceof f)return l;return new f(!1)}).stop=function(t){return new f(!0,t)}}),sr=ct("iterator"),ar=!1;try{var cr=0,lr={next:function(){return{done:!!cr++}},return:function(){ar=!0}};lr[sr]=function(){return this},Array.from(lr,function(){throw 2})}catch(t){}function ur(s,t,e,a,i){function r(t){var r=f[t];dt(f,t,"add"==t?function(t){return r.call(this,0===t?0:t),this}:"delete"==t?function(t){return!(i&&!y(t))&&r.call(this,0===t?0:t)}:"get"==t?function(t){return i&&!y(t)?void 0:r.call(this,0===t?0:t)}:"has"==t?function(t){return!(i&&!y(t))&&r.call(this,0===t?0:t)}:function(t,e){return r.call(this,0===t?0:t,e),this})}var n,o,c,l,u,h=S[s],f=h&&h.prototype,d=h,p=a?"set":"add",v={};return jt(s,"function"!=typeof h||!(i||f.forEach&&!g(function(){(new h).entries().next()})))?(d=e.getConstructor(t,s,a,p),er.REQUIRED=!0):jt(s,!0)&&(o=(n=new d)[p](i?{}:-0,1)!=n,c=g(function(){n.has(1)}),l=function(){if(!ar)return!1;var t=!1;try{var e={};e[sr]=function(){return{next:function(){return{done:t=!0}}}},new h(e)}catch(t){}return t}(),u=!i&&g(function(){for(var t=new h,e=5;e--;)t[p](e,e);return!t.has(-0)}),l||(((d=t(function(t,e){Ze(t,d,s);var r,i,n,o=(r=new h,Oe&&"function"==typeof(i=t.constructor)&&i!==d&&y(n=i.prototype)&&n!==d.prototype&&Oe(r,n),r);return null!=e&&or(e,o[p],o,a),o})).prototype=f).constructor=d),(c||u)&&(r("delete"),r("has"),a&&r("get")),(u||o)&&r(p),i&&f.clear&&delete f.clear),v[s]=d,nt({global:!0,forced:d!=h},v),ve(d,s),i||e.setStrong(d,s,a),d}function hr(t){return t.frozen||(t.frozen=new mr)}function fr(t,e){return gr(t.entries,function(t){return t[0]===e})}var dr=er.getWeakData,pr=ft.set,vr=ft.getterFor,gr=Pt.find,yr=Pt.findIndex,br=0,mr=function(){this.entries=[]};mr.prototype={get:function(t){var e=fr(this,t);if(e)return e[1]},has:function(t){return!!fr(this,t)},set:function(t,e){var r=fr(this,t);r?r[1]=e:this.entries.push([t,e])},delete:function(e){var t=yr(this.entries,function(t){return t[0]===e});return~t&&this.entries.splice(t,1),!!~t}};var xr={getConstructor:function(t,r,i,n){function o(t,e,r){var i=a(t),n=dr(O(e),!0);return!0===n?hr(i).set(e,r):n[i.id]=r,t}var s=t(function(t,e){Ze(t,s,r),pr(t,{type:r,id:br++,frozen:void 0}),null!=e&&or(e,t[n],t,i)}),a=vr(r);return Je(s.prototype,{delete:function(t){var e=a(this);if(!y(t))return!1;var r=dr(t);return!0===r?hr(e).delete(t):r&&h(r,e.id)&&delete r[e.id]},has:function(t){var e=a(this);if(!y(t))return!1;var r=dr(t);return!0===r?hr(e).has(t):r&&h(r,e.id)}}),Je(s.prototype,i?{get:function(t){var e=a(this);if(y(t)){var r=dr(t);return!0===r?hr(e).get(t):r?r[e.id]:void 0}},set:function(t,e){return o(this,t,e)}}:{add:function(t){return o(this,t,!0)}}),s}},Er=(e(function(t){function e(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}var i,r,n,o,s,a,c=ft.enforce,l=!S.ActiveXObject&&"ActiveXObject"in S,u=Object.isExtensible,h=t.exports=ur("WeakMap",e,xr,!0,!0);$&&l&&(i=xr.getConstructor(e,"WeakMap",!0),er.REQUIRED=!0,r=h.prototype,n=r.delete,o=r.has,s=r.get,a=r.set,Je(r,{delete:function(t){if(!y(t)||u(t))return n.call(this,t);var e=c(this);return e.frozen||(e.frozen=new i),n.call(this,t)||e.frozen.delete(t)},has:function(t){if(!y(t)||u(t))return o.call(this,t);var e=c(this);return e.frozen||(e.frozen=new i),o.call(this,t)||e.frozen.has(t)},get:function(t){if(!y(t)||u(t))return s.call(this,t);var e=c(this);return e.frozen||(e.frozen=new i),o.call(this,t)?s.call(this,t):e.frozen.get(t)},set:function(t,e){var r;return y(t)&&!u(t)?((r=c(this)).frozen||(r.frozen=new i),o.call(this,t)?a.call(this,t,e):r.frozen.set(t,e)):a.call(this,t,e),this}}))}),ct("iterator")),wr=ct("toStringTag"),Or=Me.values;for(var _r in Bt){var Sr=S[_r],Ar=Sr&&Sr.prototype;if(Ar){if(Ar[Er]!==Or)try{F(Ar,Er,Or)}catch(t){Ar[Er]=Or}if(Ar[wr]||F(Ar,wr,_r),Bt[_r])for(var kr in Me)if(Ar[kr]!==Me[kr])try{F(Ar,kr,Me[kr])}catch(t){Ar[kr]=Me[kr]}}}function Lr(){return Pr.Date.now()}var Mr="Expected a function",Tr=NaN,jr="[object Symbol]",Rr=/^\s+|\s+$/g,Wr=/^[-+]0x[0-9a-f]+$/i,zr=/^0b[01]+$/i,Cr=/^0o[0-7]+$/i,Nr=parseInt,Ir="object"==typeof t&&t&&t.Object===Object&&t,Dr="object"==typeof self&&self&&self.Object===Object&&self,Pr=Ir||Dr||Function("return this")(),Vr=Object.prototype.toString,Fr=Math.max,Br=Math.min;function Hr(i,n,t){var o,s,r,a,c,l,u=0,h=!1,f=!1,e=!0;if("function"!=typeof i)throw new TypeError(Mr);function d(t){var e=o,r=s;return o=s=void 0,u=t,a=i.apply(r,e)}function p(t){var e=t-l;return void 0===l||n<=e||e<0||f&&r<=t-u}function v(){var t,e=Lr();if(p(e))return g(e);c=setTimeout(v,(t=n-(e-l),f?Br(t,r-(e-u)):t))}function g(t){return c=void 0,e&&o?d(t):(o=s=void 0,a)}function y(){var t,e=Lr(),r=p(e);if(o=arguments,s=this,l=e,r){if(void 0===c)return u=t=l,c=setTimeout(v,n),h?d(t):a;if(f)return c=setTimeout(v,n),d(l)}return void 0===c&&(c=setTimeout(v,n)),a}return n=$r(n)||0,qr(t)&&(h=!!t.leading,r=(f="maxWait"in t)?Fr($r(t.maxWait)||0,n):r,e="trailing"in t?!!t.trailing:e),y.cancel=function(){void 0!==c&&clearTimeout(c),o=l=s=c=void(u=0)},y.flush=function(){return void 0===c?a:g(Lr())},y}function qr(t){var e=typeof t;return t&&("object"==e||"function"==e)}function $r(t){if("number"==typeof t)return t;if("symbol"==typeof(e=t)||e&&"object"==typeof e&&Vr.call(e)==jr)return Tr;var e,r;if(qr(t)&&(t=qr(r="function"==typeof t.valueOf?t.valueOf():t)?r+"":r),"string"!=typeof t)return 0===t?t:+t;t=t.replace(Rr,"");var i=zr.test(t);return i||Cr.test(t)?Nr(t.slice(2),i?2:8):Wr.test(t)?Tr:+t}function Xr(){return ei.Date.now()}var Yr=function(t,e,r){var i=!0,n=!0;if("function"!=typeof t)throw new TypeError(Mr);return qr(r)&&(i="leading"in r?!!r.leading:i,n="trailing"in r?!!r.trailing:n),Hr(t,e,{leading:i,maxWait:e,trailing:n})},Gr=/^\s+|\s+$/g,Ur=/^[-+]0x[0-9a-f]+$/i,Qr=/^0b[01]+$/i,Kr=/^0o[0-7]+$/i,Jr=parseInt,Zr="object"==typeof t&&t&&t.Object===Object&&t,ti="object"==typeof self&&self&&self.Object===Object&&self,ei=Zr||ti||Function("return this")(),ri=Object.prototype.toString,ii=Math.max,ni=Math.min;function oi(t){var e=typeof t;return t&&("object"==e||"function"==e)}function si(t){if("number"==typeof t)return t;if("symbol"==typeof(e=t)||e&&"object"==typeof e&&"[object Symbol]"==ri.call(e))return NaN;var e,r;if(oi(t)&&(t=oi(r="function"==typeof t.valueOf?t.valueOf():t)?r+"":r),"string"!=typeof t)return 0===t?t:+t;t=t.replace(Gr,"");var i=Qr.test(t);return i||Kr.test(t)?Jr(t.slice(2),i?2:8):Ur.test(t)?NaN:+t}var ai,ci=function(i,n,t){var o,s,r,a,c,l,u=0,h=!1,f=!1,e=!0;if("function"!=typeof i)throw new TypeError("Expected a function");function d(t){var e=o,r=s;return o=s=void 0,u=t,a=i.apply(r,e)}function p(t){var e=t-l;return void 0===l||n<=e||e<0||f&&r<=t-u}function v(){var t,e=Xr();if(p(e))return g(e);c=setTimeout(v,(t=n-(e-l),f?ni(t,r-(e-u)):t))}function g(t){return c=void 0,e&&o?d(t):(o=s=void 0,a)}function y(){var t,e=Xr(),r=p(e);if(o=arguments,s=this,l=e,r){if(void 0===c)return u=t=l,c=setTimeout(v,n),h?d(t):a;if(f)return c=setTimeout(v,n),d(l)}return void 0===c&&(c=setTimeout(v,n)),a}return n=si(n)||0,oi(t)&&(h=!!t.leading,r=(f="maxWait"in t)?ii(si(t.maxWait)||0,n):r,e="trailing"in t?!!t.trailing:e),y.cancel=function(){void 0!==c&&clearTimeout(c),o=l=s=c=void(u=0)},y.flush=function(){return void 0===c?a:g(Xr())},y},li="Expected a function",ui="__lodash_hash_undefined__",hi="[object Function]",fi="[object GeneratorFunction]",di=/^\[object .+?Constructor\]$/,pi="object"==typeof t&&t&&t.Object===Object&&t,vi="object"==typeof self&&self&&self.Object===Object&&self,gi=pi||vi||Function("return this")(),yi=Array.prototype,bi=Function.prototype,mi=Object.prototype,xi=gi["__core-js_shared__"],Ei=(ai=/[^.]+$/.exec(xi&&xi.keys&&xi.keys.IE_PROTO||""))?"Symbol(src)_1."+ai:"",wi=bi.toString,Oi=mi.hasOwnProperty,_i=mi.toString,Si=RegExp("^"+wi.call(Oi).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ai=yi.splice,ki=zi(gi,"Map"),Li=zi(Object,"create");function Mi(t){var e=-1,r=t?t.length:0;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function Ti(t){var e=-1,r=t?t.length:0;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function ji(t){var e=-1,r=t?t.length:0;for(this.clear();++e<r;){var i=t[e];this.set(i[0],i[1])}}function Ri(t,e){for(var r,i=t.length;i--;)if((r=t[i][0])===e||r!=r&&e!=e)return i;return-1}function Wi(t,e){var r,i=t.__data__;return("string"==(r=typeof e)||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==e:null===e)?i["string"==typeof e?"string":"hash"]:i.map}function zi(t,e){var r,i,n=null==t?void 0:t[e];return!Ni(r=n)||Ei&&Ei in r||!((i=Ni(r)?_i.call(r):"")==hi||i==fi||function(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}(r)?Si:di).test(function(t){if(null!=t){try{return wi.call(t)}catch(t){}try{return t+""}catch(t){}}return""}(r))?void 0:n}function Ci(n,o){if("function"!=typeof n||o&&"function"!=typeof o)throw new TypeError(li);var s=function(){var t=arguments,e=o?o.apply(this,t):t[0],r=s.cache;if(r.has(e))return r.get(e);var i=n.apply(this,t);return s.cache=r.set(e,i),i};return s.cache=new(Ci.Cache||ji),s}function Ni(t){var e=typeof t;return t&&("object"==e||"function"==e)}Mi.prototype.clear=function(){this.__data__=Li?Li(null):{}},Mi.prototype.delete=function(t){return this.has(t)&&delete this.__data__[t]},Mi.prototype.get=function(t){var e=this.__data__;if(Li){var r=e[t];return r===ui?void 0:r}return Oi.call(e,t)?e[t]:void 0},Mi.prototype.has=function(t){var e=this.__data__;return Li?void 0!==e[t]:Oi.call(e,t)},Mi.prototype.set=function(t,e){return this.__data__[t]=Li&&void 0===e?ui:e,this},Ti.prototype.clear=function(){this.__data__=[]},Ti.prototype.delete=function(t){var e=this.__data__,r=Ri(e,t);return!(r<0||(r==e.length-1?e.pop():Ai.call(e,r,1),0))},Ti.prototype.get=function(t){var e=this.__data__,r=Ri(e,t);return r<0?void 0:e[r][1]},Ti.prototype.has=function(t){return-1<Ri(this.__data__,t)},Ti.prototype.set=function(t,e){var r=this.__data__,i=Ri(r,t);return i<0?r.push([t,e]):r[i][1]=e,this},ji.prototype.clear=function(){this.__data__={hash:new Mi,map:new(ki||Ti),string:new Mi}},ji.prototype.delete=function(t){return Wi(this,t).delete(t)},ji.prototype.get=function(t){return Wi(this,t).get(t)},ji.prototype.has=function(t){return Wi(this,t).has(t)},ji.prototype.set=function(t,e){return Wi(this,t).set(t,e),this},Ci.Cache=ji;function Ii(t,e){for(var r=0,i=Object.keys(e);r<i.length;r++){var n=i[r];Object.defineProperty(t,n,{value:e[n],enumerable:!1,writable:!1,configurable:!0})}return t}var Di=Ci,Pi=function(){if("undefined"!=typeof Map)return Map;function i(t,r){var i=-1;return t.some(function(t,e){return t[0]===r&&(i=e,!0)}),i}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var e=i(this.__entries__,t),r=this.__entries__[e];return r&&r[1]},t.prototype.set=function(t,e){var r=i(this.__entries__,t);~r?this.__entries__[r][1]=e:this.__entries__.push([t,e])},t.prototype.delete=function(t){var e=this.__entries__,r=i(e,t);~r&&e.splice(r,1)},t.prototype.has=function(t){return!!~i(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(t,e){void 0===e&&(e=null);for(var r=0,i=this.__entries__;r<i.length;r++){var n=i[r];t.call(e,n[1],n[0])}},t;function t(){this.__entries__=[]}}(),Vi="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,Fi="undefined"!=typeof global&&global.Math===Math?global:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),Bi="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(Fi):function(t){return setTimeout(function(){return t(Date.now())},1e3/60)},Hi=["top","right","bottom","left","width","height","size","weight"],qi="undefined"!=typeof MutationObserver,$i=(Gi.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},Gi.prototype.removeObserver=function(t){var e=this.observers_,r=e.indexOf(t);~r&&e.splice(r,1),!e.length&&this.connected_&&this.disconnect_()},Gi.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},Gi.prototype.updateObservers_=function(){var t=this.observers_.filter(function(t){return t.gatherActive(),t.hasActive()});return t.forEach(function(t){return t.broadcastActive()}),0<t.length},Gi.prototype.connect_=function(){Vi&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),qi?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},Gi.prototype.disconnect_=function(){Vi&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},Gi.prototype.onTransitionEnd_=function(t){var e=t.propertyName,r=void 0===e?"":e;Hi.some(function(t){return!!~r.indexOf(t)})&&this.refresh()},Gi.getInstance=function(){return this.instance_||(this.instance_=new Gi),this.instance_},Gi.instance_=null,Gi),Xi=function(t){return t&&t.ownerDocument&&t.ownerDocument.defaultView||Fi},Yi=Zi(0,0,0,0);function Gi(){function t(){o&&(o=!1,i()),s&&r()}function e(){Bi(t)}function r(){var t=Date.now();if(o){if(t-a<2)return;s=!0}else s=!(o=!0),setTimeout(e,n);a=t}var i,n,o,s,a;this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=(i=this.refresh.bind(this),s=o=!(n=20),a=0,r)}function Ui(t){return parseFloat(t)||0}function Qi(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return t.reduce(function(t,e){return t+Ui(r["border-"+e+"-width"])},0)}var Ki="undefined"!=typeof SVGGraphicsElement?function(t){return t instanceof Xi(t).SVGGraphicsElement}:function(t){return t instanceof Xi(t).SVGElement&&"function"==typeof t.getBBox};function Ji(t){return Vi?Ki(t)?Zi(0,0,(e=t.getBBox()).width,e.height):function(t){var e=t.clientWidth,r=t.clientHeight;if(!e&&!r)return Yi;var i,n,o=Xi(t).getComputedStyle(t),s=function(t){for(var e={},r=0,i=["top","right","bottom","left"];r<i.length;r++){var n=i[r],o=t["padding-"+n];e[n]=Ui(o)}return e}(o),a=s.left+s.right,c=s.top+s.bottom,l=Ui(o.width),u=Ui(o.height);return"border-box"===o.boxSizing&&(Math.round(l+a)!==e&&(l-=Qi(o,"left","right")+a),Math.round(u+c)!==r&&(u-=Qi(o,"top","bottom")+c)),t!==Xi(t).document.documentElement&&(i=Math.round(l+a)-e,n=Math.round(u+c)-r,1!==Math.abs(i)&&(l-=i),1!==Math.abs(n)&&(u-=n)),Zi(s.left,s.top,l,u)}(t):Yi;var e}function Zi(t,e,r,i){return{x:t,y:e,width:r,height:i}}function tn(t,e){var r,i,n,o,s,a,c=(r=e.x,i=e.y,n=e.width,o=e.height,s="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,a=Object.create(s.prototype),Ii(a,{x:r,y:i,width:n,height:o,top:i,right:r+n,bottom:o+i,left:r}),a);Ii(this,{target:t,contentRect:c})}function en(t){if(!(this instanceof en))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var e=$i.getInstance(),r=new nn(t,e,this);on.set(this,r)}var rn=(an.prototype.isActive=function(){var t=Ji(this.target);return(this.contentRect_=t).width!==this.broadcastWidth||t.height!==this.broadcastHeight},an.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},an),nn=(sn.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof Xi(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)||(e.set(t,new rn(t)),this.controller_.addObserver(this),this.controller_.refresh())}},sn.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof Xi(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)&&(e.delete(t),e.size||this.controller_.removeObserver(this))}},sn.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},sn.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach(function(t){t.isActive()&&e.activeObservations_.push(t)})},sn.prototype.broadcastActive=function(){var t,e;this.hasActive()&&(t=this.callbackCtx_,e=this.activeObservations_.map(function(t){return new tn(t.target,t.broadcastRect())}),this.callback_.call(t,e,t),this.clearActive())},sn.prototype.clearActive=function(){this.activeObservations_.splice(0)},sn.prototype.hasActive=function(){return 0<this.activeObservations_.length},sn),on=new("undefined"!=typeof WeakMap?WeakMap:Pi);function sn(t,e,r){if(this.activeObservations_=[],this.observations_=new Pi,"function"!=typeof t)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=e,this.callbackCtx_=r}function an(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Zi(0,0,0,0),this.target=t}["observe","unobserve","disconnect"].forEach(function(e){en.prototype[e]=function(){var t;return(t=on.get(this))[e].apply(t,arguments)}});var cn=void 0!==Fi.ResizeObserver?Fi.ResizeObserver:en,ln=null,un=null;function hn(){if(null===ln){if("undefined"==typeof document)return ln=0;var t=document.body,e=document.createElement("div");e.classList.add("simplebar-hide-scrollbar"),t.appendChild(e);var r=e.getBoundingClientRect().right;t.removeChild(e),ln=r}return ln}Xt&&window.addEventListener("resize",function(){un!==window.devicePixelRatio&&(un=window.devicePixelRatio,ln=null)});function fn(l){return function(t,e,r,i){ot(e);var n=at(t),o=R(n),s=tt(n.length),a=l?s-1:0,c=l?-1:1;if(r<2)for(;;){if(a in o){i=o[a],a+=c;break}if(a+=c,l?a<0:s<=a)throw TypeError("Reduce of empty array with no initial value")}for(;l?0<=a:a<s;a+=c)a in o&&(i=e(i,o[a],a,n));return i}}var dn=[fn(!1),fn(!0)][0];nt({target:"Array",proto:!0,forced:ht("reduce")},{reduce:function(t){return dn(this,t,arguments.length,1<arguments.length?arguments[1]:void 0)}});var pn=V.f,vn=Function.prototype,gn=vn.toString,yn=/^\s*function ([^ (]*)/;!A||"name"in vn||pn(vn,"name",{configurable:!0,get:function(){try{return gn.call(this).match(yn)[1]}catch(t){return""}}});var bn,mn,xn=RegExp.prototype.exec,En=String.prototype.replace,wn=xn,On=(bn=/a/,mn=/b*/g,xn.call(bn,"a"),xn.call(mn,"a"),0!==bn.lastIndex||0!==mn.lastIndex),_n=void 0!==/()??/.exec("")[1];(On||_n)&&(wn=function(t){var e,r,i,n,o=this;return _n&&(r=new RegExp("^"+o.source+"$(?!\\s)",function(){var t=O(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}.call(o))),On&&(e=o.lastIndex),i=xn.call(o,t),On&&i&&(o.lastIndex=o.global?i.index+i[0].length:e),_n&&i&&1<i.length&&En.call(i[0],r,function(){for(n=1;n<arguments.length-2;n++)void 0===arguments[n]&&(i[n]=void 0)}),i});var Sn=wn;nt({target:"RegExp",proto:!0,forced:/./.exec!==Sn},{exec:Sn});function An(r,t,e,i){var o,n,s,a,c=ct(r),l=!g(function(){var t={};return t[c]=function(){return 7},7!=""[r](t)}),u=l&&!g(function(){var t=!1,e=/a/;return e.exec=function(){return t=!0,null},"split"===r&&(e.constructor={},e.constructor[Mn]=function(){return e}),e[c](""),!t});l&&u&&("replace"!==r||Tn)&&("split"!==r||jn)||(o=/./[c],s=(n=e(c,""[r],function(t,e,r,i,n){return e.exec===Sn?l&&!n?{done:!0,value:o.call(e,r,i)}:{done:!0,value:t.call(r,e,i)}:{done:!1}}))[0],a=n[1],dt(String.prototype,r,s),dt(RegExp.prototype,c,2==t?function(t,e){return a.call(t,this,e)}:function(t){return a.call(t,this)}),i&&F(RegExp.prototype[c],"sham",!0))}function kn(t,e,r){return e+(r?Rn(t,e).length:1)}function Ln(t,e){var r=t.exec;if("function"==typeof r){var i=r.call(t,e);if("object"!=typeof i)throw TypeError("RegExp exec method returned something other than an Object or null");return i}if("RegExp"!==n(t))throw TypeError("RegExp#exec called on incompatible receiver");return Sn.call(t,e)}var Mn=ct("species"),Tn=!g(function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}),jn=!g(function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]}),Rn=Ge.charAt;An("match",1,function(i,l,u){return[function(t){var e=c(this),r=null==t?void 0:t[i];return void 0!==r?r.call(t,e):new RegExp(t)[i](String(e))},function(t){var e=u(l,t,this);if(e.done)return e.value;var r=O(t),i=String(this);if(!r.global)return Ln(r,i);for(var n,o=r.unicode,s=[],a=r.lastIndex=0;null!==(n=Ln(r,i));){var c=String(n[0]);""===(s[a]=c)&&(r.lastIndex=kn(i,tt(r.lastIndex),o)),a++}return 0===a?null:s}]});var Wn=Math.max,zn=Math.min,Cn=Math.floor,Nn=/\$([$&'`]|\d\d?|<[^>]*>)/g,In=/\$([$&'`]|\d\d?)/g;An("replace",2,function(n,E,w){return[function(t,e){var r=c(this),i=null==t?void 0:t[n];return void 0!==i?i.call(t,r,e):E.call(String(r),t,e)},function(t,e){var r=w(E,t,this,e);if(r.done)return r.value;var i=O(t),n=String(this),o="function"==typeof e;o||(e=String(e));var s,a=i.global;a&&(s=i.unicode,i.lastIndex=0);for(var c=[];;){var l=Ln(i,n);if(null===l)break;if(c.push(l),!a)break;""===String(l[0])&&(i.lastIndex=kn(n,tt(i.lastIndex),s))}for(var u,h="",f=0,d=0;d<c.length;d++){l=c[d];for(var p=String(l[0]),v=Wn(zn(Z(l.index),n.length),0),g=[],y=1;y<l.length;y++)g.push(void 0===(u=l[y])?u:String(u));var b,m=l.groups,x=o?(b=[p].concat(g,v,n),void 0!==m&&b.push(m),String(e.apply(void 0,b))):function(o,s,a,c,l,t){var u=a+o.length,h=c.length,e=In;return void 0!==l&&(l=at(l),e=Nn),E.call(t,e,function(t,e){var r;switch(e.charAt(0)){case"$":return"$";case"&":return o;case"`":return s.slice(0,a);case"'":return s.slice(u);case"<":r=l[e.slice(1,-1)];break;default:var i=+e;if(0==i)return t;if(h<i){var n=Cn(i/10);return 0!==n&&n<=h?void 0===c[n-1]?e.charAt(1):c[n-1]+e.charAt(1):t}r=c[i-1]}return void 0===r?"":r})}(p,n,v,g,m,e);f<=v&&(h+=n.slice(f,v)+x,f=v+p.length)}return h+n.slice(f)}]});function Dn(t){return Array.prototype.reduce.call(t,function(t,e){var r=e.name.match(/data-simplebar-(.+)/);if(r){var i=r[1].replace(/\W+(.)/g,function(t,e){return e.toUpperCase()});switch(e.value){case"true":t[i]=!0;break;case"false":t[i]=!1;break;case void 0:t[i]=!0;break;default:t[i]=e.value}}return t},{})}function Pn(t){return t&&t.ownerDocument&&t.ownerDocument.defaultView?t.ownerDocument.defaultView:window}function Vn(t){return t&&t.ownerDocument?t.ownerDocument:document}var Fn=function(){function c(t,e){var a=this;this.onScroll=function(){var t=Pn(a.el);a.scrollXTicking||(t.requestAnimationFrame(a.scrollX),a.scrollXTicking=!0),a.scrollYTicking||(t.requestAnimationFrame(a.scrollY),a.scrollYTicking=!0)},this.scrollX=function(){a.axis.x.isOverflowing&&(a.showScrollbar("x"),a.positionScrollbar("x")),a.scrollXTicking=!1},this.scrollY=function(){a.axis.y.isOverflowing&&(a.showScrollbar("y"),a.positionScrollbar("y")),a.scrollYTicking=!1},this.onMouseEnter=function(){a.showScrollbar("x"),a.showScrollbar("y")},this.onMouseMove=function(t){a.mouseX=t.clientX,a.mouseY=t.clientY,(a.axis.x.isOverflowing||a.axis.x.forceVisible)&&a.onMouseMoveForAxis("x"),(a.axis.y.isOverflowing||a.axis.y.forceVisible)&&a.onMouseMoveForAxis("y")},this.onMouseLeave=function(){a.onMouseMove.cancel(),(a.axis.x.isOverflowing||a.axis.x.forceVisible)&&a.onMouseLeaveForAxis("x"),(a.axis.y.isOverflowing||a.axis.y.forceVisible)&&a.onMouseLeaveForAxis("y"),a.mouseX=-1,a.mouseY=-1},this.onWindowResize=function(){a.scrollbarWidth=a.getScrollbarWidth(),a.hideNativeScrollbar()},this.hideScrollbars=function(){a.axis.x.track.rect=a.axis.x.track.el.getBoundingClientRect(),a.axis.y.track.rect=a.axis.y.track.el.getBoundingClientRect(),a.isWithinBounds(a.axis.y.track.rect)||(a.axis.y.scrollbar.el.classList.remove(a.classNames.visible),a.axis.y.isVisible=!1),a.isWithinBounds(a.axis.x.track.rect)||(a.axis.x.scrollbar.el.classList.remove(a.classNames.visible),a.axis.x.isVisible=!1)},this.onPointerEvent=function(t){var e,r;a.axis.x.track.rect=a.axis.x.track.el.getBoundingClientRect(),a.axis.y.track.rect=a.axis.y.track.el.getBoundingClientRect(),(a.axis.x.isOverflowing||a.axis.x.forceVisible)&&(e=a.isWithinBounds(a.axis.x.track.rect)),(a.axis.y.isOverflowing||a.axis.y.forceVisible)&&(r=a.isWithinBounds(a.axis.y.track.rect)),(e||r)&&(t.preventDefault(),t.stopPropagation(),"mousedown"===t.type&&(e&&(a.axis.x.scrollbar.rect=a.axis.x.scrollbar.el.getBoundingClientRect(),a.isWithinBounds(a.axis.x.scrollbar.rect)?a.onDragStart(t,"x"):a.onTrackClick(t,"x")),r&&(a.axis.y.scrollbar.rect=a.axis.y.scrollbar.el.getBoundingClientRect(),a.isWithinBounds(a.axis.y.scrollbar.rect)?a.onDragStart(t,"y"):a.onTrackClick(t,"y"))))},this.drag=function(t){var e=a.axis[a.draggedAxis].track,r=e.rect[a.axis[a.draggedAxis].sizeAttr],i=a.axis[a.draggedAxis].scrollbar,n=a.contentWrapperEl[a.axis[a.draggedAxis].scrollSizeAttr],o=parseInt(a.elStyles[a.axis[a.draggedAxis].sizeAttr],10);t.preventDefault(),t.stopPropagation();var s=(("y"===a.draggedAxis?t.pageY:t.pageX)-e.rect[a.axis[a.draggedAxis].offsetAttr]-a.axis[a.draggedAxis].dragOffset)/(r-i.size)*(n-o);"x"===a.draggedAxis&&(s=a.isRtl&&c.getRtlHelpers().isRtlScrollbarInverted?s-(r+i.size):s,s=a.isRtl&&c.getRtlHelpers().isRtlScrollingInverted?-s:s),a.contentWrapperEl[a.axis[a.draggedAxis].scrollOffsetAttr]=s},this.onEndDrag=function(t){var e=Vn(a.el),r=Pn(a.el);t.preventDefault(),t.stopPropagation(),a.el.classList.remove(a.classNames.dragging),e.removeEventListener("mousemove",a.drag,!0),e.removeEventListener("mouseup",a.onEndDrag,!0),a.removePreventClickId=r.setTimeout(function(){e.removeEventListener("click",a.preventClick,!0),e.removeEventListener("dblclick",a.preventClick,!0),a.removePreventClickId=null})},this.preventClick=function(t){t.preventDefault(),t.stopPropagation()},this.el=t,this.minScrollbarWidth=20,this.options=Object.assign({},c.defaultOptions,{},e),this.classNames=Object.assign({},c.defaultOptions.classNames,{},this.options.classNames),this.axis={x:{scrollOffsetAttr:"scrollLeft",sizeAttr:"width",scrollSizeAttr:"scrollWidth",offsetSizeAttr:"offsetWidth",offsetAttr:"left",overflowAttr:"overflowX",dragOffset:0,isOverflowing:!0,isVisible:!1,forceVisible:!1,track:{},scrollbar:{}},y:{scrollOffsetAttr:"scrollTop",sizeAttr:"height",scrollSizeAttr:"scrollHeight",offsetSizeAttr:"offsetHeight",offsetAttr:"top",overflowAttr:"overflowY",dragOffset:0,isOverflowing:!0,isVisible:!1,forceVisible:!1,track:{},scrollbar:{}}},this.removePreventClickId=null,c.instances.has(this.el)||(this.recalculate=Yr(this.recalculate.bind(this),64),this.onMouseMove=Yr(this.onMouseMove.bind(this),64),this.hideScrollbars=ci(this.hideScrollbars.bind(this),this.options.timeout),this.onWindowResize=ci(this.onWindowResize.bind(this),64,{leading:!0}),c.getRtlHelpers=Di(c.getRtlHelpers),this.init())}c.getRtlHelpers=function(){var t=document.createElement("div");t.innerHTML='<div class="hs-dummy-scrollbar-size"><div style="height: 200%; width: 200%; margin: 10px 0;"></div></div>';var e=t.firstElementChild;document.body.appendChild(e);var r=e.firstElementChild;e.scrollLeft=0;var i=c.getOffset(e),n=c.getOffset(r);e.scrollLeft=999;var o=c.getOffset(r);return{isRtlScrollingInverted:i.left!==n.left&&n.left-o.left!=0,isRtlScrollbarInverted:i.left!==n.left}},c.getOffset=function(t){var e=t.getBoundingClientRect(),r=Vn(t),i=Pn(t);return{top:e.top+(i.pageYOffset||r.documentElement.scrollTop),left:e.left+(i.pageXOffset||r.documentElement.scrollLeft)}};var t=c.prototype;return t.init=function(){c.instances.set(this.el,this),Xt&&(this.initDOM(),this.scrollbarWidth=this.getScrollbarWidth(),this.recalculate(),this.initListeners())},t.initDOM=function(){var t,e,r=this;if(Array.prototype.filter.call(this.el.children,function(t){return t.classList.contains(r.classNames.wrapper)}).length)this.wrapperEl=this.el.querySelector("."+this.classNames.wrapper),this.contentWrapperEl=this.options.scrollableNode||this.el.querySelector("."+this.classNames.contentWrapper),this.contentEl=this.options.contentNode||this.el.querySelector("."+this.classNames.contentEl),this.offsetEl=this.el.querySelector("."+this.classNames.offset),this.maskEl=this.el.querySelector("."+this.classNames.mask),this.placeholderEl=this.findChild(this.wrapperEl,"."+this.classNames.placeholder),this.heightAutoObserverWrapperEl=this.el.querySelector("."+this.classNames.heightAutoObserverWrapperEl),this.heightAutoObserverEl=this.el.querySelector("."+this.classNames.heightAutoObserverEl),this.axis.x.track.el=this.findChild(this.el,"."+this.classNames.track+"."+this.classNames.horizontal),this.axis.y.track.el=this.findChild(this.el,"."+this.classNames.track+"."+this.classNames.vertical);else{for(this.wrapperEl=document.createElement("div"),this.contentWrapperEl=document.createElement("div"),this.offsetEl=document.createElement("div"),this.maskEl=document.createElement("div"),this.contentEl=document.createElement("div"),this.placeholderEl=document.createElement("div"),this.heightAutoObserverWrapperEl=document.createElement("div"),this.heightAutoObserverEl=document.createElement("div"),this.wrapperEl.classList.add(this.classNames.wrapper),this.contentWrapperEl.classList.add(this.classNames.contentWrapper),this.offsetEl.classList.add(this.classNames.offset),this.maskEl.classList.add(this.classNames.mask),this.contentEl.classList.add(this.classNames.contentEl),this.placeholderEl.classList.add(this.classNames.placeholder),this.heightAutoObserverWrapperEl.classList.add(this.classNames.heightAutoObserverWrapperEl),this.heightAutoObserverEl.classList.add(this.classNames.heightAutoObserverEl);this.el.firstChild;)this.contentEl.appendChild(this.el.firstChild);this.contentWrapperEl.appendChild(this.contentEl),this.offsetEl.appendChild(this.contentWrapperEl),this.maskEl.appendChild(this.offsetEl),this.heightAutoObserverWrapperEl.appendChild(this.heightAutoObserverEl),this.wrapperEl.appendChild(this.heightAutoObserverWrapperEl),this.wrapperEl.appendChild(this.maskEl),this.wrapperEl.appendChild(this.placeholderEl),this.el.appendChild(this.wrapperEl)}this.axis.x.track.el&&this.axis.y.track.el||(t=document.createElement("div"),e=document.createElement("div"),t.classList.add(this.classNames.track),e.classList.add(this.classNames.scrollbar),t.appendChild(e),this.axis.x.track.el=t.cloneNode(!0),this.axis.x.track.el.classList.add(this.classNames.horizontal),this.axis.y.track.el=t.cloneNode(!0),this.axis.y.track.el.classList.add(this.classNames.vertical),this.el.appendChild(this.axis.x.track.el),this.el.appendChild(this.axis.y.track.el)),this.axis.x.scrollbar.el=this.axis.x.track.el.querySelector("."+this.classNames.scrollbar),this.axis.y.scrollbar.el=this.axis.y.track.el.querySelector("."+this.classNames.scrollbar),this.options.autoHide||(this.axis.x.scrollbar.el.classList.add(this.classNames.visible),this.axis.y.scrollbar.el.classList.add(this.classNames.visible)),this.el.setAttribute("data-simplebar","init")},t.initListeners=function(){var e=this,t=Pn(this.el);this.options.autoHide&&this.el.addEventListener("mouseenter",this.onMouseEnter),["mousedown","click","dblclick"].forEach(function(t){e.el.addEventListener(t,e.onPointerEvent,!0)}),["touchstart","touchend","touchmove"].forEach(function(t){e.el.addEventListener(t,e.onPointerEvent,{capture:!0,passive:!0})}),this.el.addEventListener("mousemove",this.onMouseMove),this.el.addEventListener("mouseleave",this.onMouseLeave),this.contentWrapperEl.addEventListener("scroll",this.onScroll),t.addEventListener("resize",this.onWindowResize);var r=!1,i=t.ResizeObserver||cn;this.resizeObserver=new i(function(){r&&e.recalculate()}),this.resizeObserver.observe(this.el),this.resizeObserver.observe(this.contentEl),t.requestAnimationFrame(function(){r=!0}),this.mutationObserver=new t.MutationObserver(this.recalculate),this.mutationObserver.observe(this.contentEl,{childList:!0,subtree:!0,characterData:!0})},t.recalculate=function(){var t=Pn(this.el);this.elStyles=t.getComputedStyle(this.el),this.isRtl="rtl"===this.elStyles.direction;var e=this.contentEl.offsetWidth,r=this.heightAutoObserverEl.offsetHeight<=1,i=this.heightAutoObserverEl.offsetWidth<=1||0<e,n=this.contentWrapperEl.offsetWidth,o=this.elStyles.overflowX,s=this.elStyles.overflowY;this.contentEl.style.padding=this.elStyles.paddingTop+" "+this.elStyles.paddingRight+" "+this.elStyles.paddingBottom+" "+this.elStyles.paddingLeft,this.wrapperEl.style.margin="-"+this.elStyles.paddingTop+" -"+this.elStyles.paddingRight+" -"+this.elStyles.paddingBottom+" -"+this.elStyles.paddingLeft;var a=this.contentEl.scrollHeight,c=this.contentEl.scrollWidth;this.contentWrapperEl.style.height=r?"auto":"100%",this.placeholderEl.style.width=i?(e||c)+"px":"auto",this.placeholderEl.style.height=a+"px";var l=this.contentWrapperEl.offsetHeight;this.axis.x.isOverflowing=0!==e&&e<c,this.axis.y.isOverflowing=l<a,this.axis.x.isOverflowing="hidden"!==o&&this.axis.x.isOverflowing,this.axis.y.isOverflowing="hidden"!==s&&this.axis.y.isOverflowing,this.axis.x.forceVisible="x"===this.options.forceVisible||!0===this.options.forceVisible,this.axis.y.forceVisible="y"===this.options.forceVisible||!0===this.options.forceVisible,this.hideNativeScrollbar();var u=this.axis.x.isOverflowing?this.scrollbarWidth:0,h=this.axis.y.isOverflowing?this.scrollbarWidth:0;this.axis.x.isOverflowing=this.axis.x.isOverflowing&&n-h<c,this.axis.y.isOverflowing=this.axis.y.isOverflowing&&l-u<a,this.axis.x.scrollbar.size=this.getScrollbarSize("x"),this.axis.y.scrollbar.size=this.getScrollbarSize("y"),this.axis.x.scrollbar.el.style.width=this.axis.x.scrollbar.size+"px",this.axis.y.scrollbar.el.style.height=this.axis.y.scrollbar.size+"px",this.positionScrollbar("x"),this.positionScrollbar("y"),this.toggleTrackVisibility("x"),this.toggleTrackVisibility("y")},t.getScrollbarSize=function(t){if(void 0===t&&(t="y"),!this.axis[t].isOverflowing)return 0;var e=this.contentEl[this.axis[t].scrollSizeAttr],r=this.axis[t].track.el[this.axis[t].offsetSizeAttr],i=r/e,n=Math.max(~~(i*r),this.options.scrollbarMinSize);return this.options.scrollbarMaxSize&&(n=Math.min(n,this.options.scrollbarMaxSize)),n},t.positionScrollbar=function(t){var e,r,i,n,o,s,a;void 0===t&&(t="y"),this.axis[t].isOverflowing&&(e=this.contentWrapperEl[this.axis[t].scrollSizeAttr],r=this.axis[t].track.el[this.axis[t].offsetSizeAttr],i=parseInt(this.elStyles[this.axis[t].sizeAttr],10),n=this.axis[t].scrollbar,o=this.contentWrapperEl[this.axis[t].scrollOffsetAttr],s=(o="x"===t&&this.isRtl&&c.getRtlHelpers().isRtlScrollingInverted?-o:o)/(e-i),a=~~((r-n.size)*s),a="x"===t&&this.isRtl&&c.getRtlHelpers().isRtlScrollbarInverted?a+(r-n.size):a,n.el.style.transform="x"===t?"translate3d("+a+"px, 0, 0)":"translate3d(0, "+a+"px, 0)")},t.toggleTrackVisibility=function(t){void 0===t&&(t="y");var e=this.axis[t].track.el,r=this.axis[t].scrollbar.el;this.axis[t].isOverflowing||this.axis[t].forceVisible?(e.style.visibility="visible",this.contentWrapperEl.style[this.axis[t].overflowAttr]="scroll"):(e.style.visibility="hidden",this.contentWrapperEl.style[this.axis[t].overflowAttr]="hidden"),this.axis[t].isOverflowing?r.style.display="block":r.style.display="none"},t.hideNativeScrollbar=function(){this.offsetEl.style[this.isRtl?"left":"right"]=this.axis.y.isOverflowing||this.axis.y.forceVisible?"-"+this.scrollbarWidth+"px":0,this.offsetEl.style.bottom=this.axis.x.isOverflowing||this.axis.x.forceVisible?"-"+this.scrollbarWidth+"px":0},t.onMouseMoveForAxis=function(t){void 0===t&&(t="y"),this.axis[t].track.rect=this.axis[t].track.el.getBoundingClientRect(),this.axis[t].scrollbar.rect=this.axis[t].scrollbar.el.getBoundingClientRect(),this.isWithinBounds(this.axis[t].scrollbar.rect)?this.axis[t].scrollbar.el.classList.add(this.classNames.hover):this.axis[t].scrollbar.el.classList.remove(this.classNames.hover),this.isWithinBounds(this.axis[t].track.rect)?(this.showScrollbar(t),this.axis[t].track.el.classList.add(this.classNames.hover)):this.axis[t].track.el.classList.remove(this.classNames.hover)},t.onMouseLeaveForAxis=function(t){void 0===t&&(t="y"),this.axis[t].track.el.classList.remove(this.classNames.hover),this.axis[t].scrollbar.el.classList.remove(this.classNames.hover)},t.showScrollbar=function(t){void 0===t&&(t="y");var e=this.axis[t].scrollbar.el;this.axis[t].isVisible||(e.classList.add(this.classNames.visible),this.axis[t].isVisible=!0),this.options.autoHide&&this.hideScrollbars()},t.onDragStart=function(t,e){void 0===e&&(e="y");var r=Vn(this.el),i=Pn(this.el),n=this.axis[e].scrollbar,o="y"===e?t.pageY:t.pageX;this.axis[e].dragOffset=o-n.rect[this.axis[e].offsetAttr],this.draggedAxis=e,this.el.classList.add(this.classNames.dragging),r.addEventListener("mousemove",this.drag,!0),r.addEventListener("mouseup",this.onEndDrag,!0),null===this.removePreventClickId?(r.addEventListener("click",this.preventClick,!0),r.addEventListener("dblclick",this.preventClick,!0)):(i.clearTimeout(this.removePreventClickId),this.removePreventClickId=null)},t.onTrackClick=function(t,i){var n,e,r,o,s,a,c=this;void 0===i&&(i="y"),this.options.clickOnTrack&&(n=Pn(this.el),this.axis[i].scrollbar.rect=this.axis[i].scrollbar.el.getBoundingClientRect(),e=this.axis[i].scrollbar.rect[this.axis[i].offsetAttr],r=parseInt(this.elStyles[this.axis[i].sizeAttr],10),o=this.contentWrapperEl[this.axis[i].scrollOffsetAttr],s=("y"===i?this.mouseY-e:this.mouseX-e)<0?-1:1,a=-1==s?o-r:o+r,function t(){var e,r;-1==s?a<o&&(o-=40,c.contentWrapperEl.scrollTo(((e={})[c.axis[i].offsetAttr]=o,e)),n.requestAnimationFrame(t)):o<a&&(o+=40,c.contentWrapperEl.scrollTo(((r={})[c.axis[i].offsetAttr]=o,r)),n.requestAnimationFrame(t))}())},t.getContentElement=function(){return this.contentEl},t.getScrollElement=function(){return this.contentWrapperEl},t.getScrollbarWidth=function(){try{return"none"===getComputedStyle(this.contentWrapperEl,"::-webkit-scrollbar").display||"scrollbarWidth"in document.documentElement.style||"-ms-overflow-style"in document.documentElement.style?0:hn()}catch(t){return hn()}},t.removeListeners=function(){var e=this,t=Pn(this.el);this.options.autoHide&&this.el.removeEventListener("mouseenter",this.onMouseEnter),["mousedown","click","dblclick"].forEach(function(t){e.el.removeEventListener(t,e.onPointerEvent,!0)}),["touchstart","touchend","touchmove"].forEach(function(t){e.el.removeEventListener(t,e.onPointerEvent,{capture:!0,passive:!0})}),this.el.removeEventListener("mousemove",this.onMouseMove),this.el.removeEventListener("mouseleave",this.onMouseLeave),this.contentWrapperEl.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onWindowResize),this.mutationObserver.disconnect(),this.resizeObserver.disconnect(),this.recalculate.cancel(),this.onMouseMove.cancel(),this.hideScrollbars.cancel(),this.onWindowResize.cancel()},t.unMount=function(){this.removeListeners(),c.instances.delete(this.el)},t.isWithinBounds=function(t){return this.mouseX>=t.left&&this.mouseX<=t.left+t.width&&this.mouseY>=t.top&&this.mouseY<=t.top+t.height},t.findChild=function(t,e){var r=t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.msMatchesSelector;return Array.prototype.filter.call(t.children,function(t){return r.call(t,e)})[0]},c}();return Fn.defaultOptions={autoHide:!0,forceVisible:!1,clickOnTrack:!0,classNames:{contentEl:"simplebar-content",contentWrapper:"simplebar-content-wrapper",offset:"simplebar-offset",mask:"simplebar-mask",wrapper:"simplebar-wrapper",placeholder:"simplebar-placeholder",scrollbar:"simplebar-scrollbar",track:"simplebar-track",heightAutoObserverWrapperEl:"simplebar-height-auto-observer-wrapper",heightAutoObserverEl:"simplebar-height-auto-observer",visible:"simplebar-visible",horizontal:"simplebar-horizontal",vertical:"simplebar-vertical",hover:"simplebar-hover",dragging:"simplebar-dragging"},scrollbarMinSize:25,scrollbarMaxSize:0,timeout:1e3},Fn.instances=new WeakMap,Fn.initDOMLoadedElements=function(){document.removeEventListener("DOMContentLoaded",this.initDOMLoadedElements),window.removeEventListener("load",this.initDOMLoadedElements),Array.prototype.forEach.call(document.querySelectorAll("[data-simplebar]"),function(t){"init"===t.getAttribute("data-simplebar")||Fn.instances.has(t)||new Fn(t,Dn(t.attributes))})},Fn.removeObserver=function(){this.globalObserver.disconnect()},Fn.initHtmlApi=function(){this.initDOMLoadedElements=this.initDOMLoadedElements.bind(this),"undefined"!=typeof MutationObserver&&(this.globalObserver=new MutationObserver(Fn.handleMutations),this.globalObserver.observe(document,{childList:!0,subtree:!0})),"complete"===document.readyState||"loading"!==document.readyState&&!document.documentElement.doScroll?window.setTimeout(this.initDOMLoadedElements):(document.addEventListener("DOMContentLoaded",this.initDOMLoadedElements),window.addEventListener("load",this.initDOMLoadedElements))},Fn.handleMutations=function(t){t.forEach(function(t){Array.prototype.forEach.call(t.addedNodes,function(t){1===t.nodeType&&(t.hasAttribute("data-simplebar")?Fn.instances.has(t)||new Fn(t,Dn(t.attributes)):Array.prototype.forEach.call(t.querySelectorAll("[data-simplebar]"),function(t){"init"===t.getAttribute("data-simplebar")||Fn.instances.has(t)||new Fn(t,Dn(t.attributes))}))}),Array.prototype.forEach.call(t.removedNodes,function(t){1===t.nodeType&&(t.hasAttribute('[data-simplebar="init"]')?Fn.instances.has(t)&&Fn.instances.get(t).unMount():Array.prototype.forEach.call(t.querySelectorAll('[data-simplebar="init"]'),function(t){Fn.instances.has(t)&&Fn.instances.get(t).unMount()}))})})},Fn.getOptions=Dn,Xt&&Fn.initHtmlApi(),Fn});