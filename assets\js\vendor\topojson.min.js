!function(r,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n(r.topojson=r.topojson||{})}(this,function(r){"use strict";function t(r,n){var t=n.id,e=n.bbox,o=null==n.properties?{}:n.properties,a=i(r,n);return null==t&&null==e?{type:"Feature",properties:o,geometry:a}:null==e?{type:"Feature",id:t,properties:o,geometry:a}:{type:"Feature",id:t,bbox:e,properties:o,geometry:a}}function i(r,n){function o(r){return u(r)}function a(r){for(var n=[],t=0,e=r.length;t<e;++t)!function(r,n){n.length&&n.pop();for(var t=c[r<0?~r:r],e=0,o=t.length;e<o;++e)n.push(u(t[e],e));r<0&&F(n,o)}(r[t],n);return n.length<2&&n.push(n[0]),n}function t(r){for(var n=a(r);n.length<4;)n.push(n[0]);return n}function i(r){return r.map(t)}var u=T(r.transform),c=r.arcs;return function r(n){var t,e=n.type;switch(e){case"GeometryCollection":return{type:e,geometries:n.geometries.map(r)};case"Point":t=o(n.coordinates);break;case"MultiPoint":t=n.coordinates.map(o);break;case"LineString":t=a(n.arcs);break;case"MultiLineString":t=n.arcs.map(a);break;case"Polygon":t=i(n.arcs);break;case"MultiPolygon":t=n.arcs.map(i);break;default:return null}return{type:e,coordinates:t}}(n)}function n(r,n,t){var e,o,a,i,u,c,f;if(1<arguments.length)i=t,c=[],f=[],function r(n){switch((u=n).type){case"GeometryCollection":n.geometries.forEach(r);break;case"LineString":l(n.arcs);break;case"MultiLineString":case"Polygon":h(n.arcs);break;case"MultiPolygon":n.arcs.forEach(h)}}(n),f.forEach(null==i?function(r){c.push(r[0].i)}:function(r){i(r[0].g,r[r.length-1].g)&&c.push(r[0].i)}),e=c;else for(o=0,e=new Array(a=r.arcs.length);o<a;++o)e[o]=o;function s(r){var n=r<0?~r:r;(f[n]||(f[n]=[])).push({i:r,g:u})}function l(r){r.forEach(s)}function h(r){r.forEach(l)}return{type:"MultiLineString",arcs:x(r,e)}}function e(u,r){function t(n){n.forEach(function(r){r.forEach(function(r){(f[r=r<0?~r:r]||(f[r]=[])).push(n)})}),e.push(n)}function c(r){return function(r){for(var n,t=-1,e=r.length,o=r[e-1],a=0;++t<e;)n=o,o=r[t],a+=n[0]*o[1]-n[1]*o[0];return Math.abs(a)}(i(u,{type:"Polygon",arcs:[r]}).coordinates[0])}var f={},e=[],o=[];return r.forEach(function r(n){switch(n.type){case"GeometryCollection":n.geometries.forEach(r);break;case"Polygon":t(n.arcs);break;case"MultiPolygon":n.arcs.forEach(t)}}),e.forEach(function(r){if(!r._){var n=[],t=[r];for(r._=1,o.push(n);r=t.pop();)n.push(r),r.forEach(function(r){r.forEach(function(r){f[r<0?~r:r].forEach(function(r){r._||(r._=1,t.push(r))})})})}}),e.forEach(function(r){delete r._}),{type:"MultiPolygon",arcs:o.map(function(r){var n,t=[];if(r.forEach(function(r){r.forEach(function(r){r.forEach(function(r){f[r<0?~r:r].length<2&&t.push(r)})})}),1<(n=(t=x(u,t)).length))for(var e,o,a=1,i=c(t[0]);a<n;++a)(e=c(t[a]))>i&&(o=t[0],t[0]=t[a],t[a]=o,i=e);return t})}}function M(r,n,t){for(var e,o=n+(t---n>>1);n<o;++n,--t)e=r[n],r[n]=r[t],r[t]=e}function h(r){return null==r?{type:null}:("FeatureCollection"===r.type?function(r){var n={type:"GeometryCollection",geometries:r.features.map(o)};return null!=r.bbox&&(n.bbox=r.bbox),n}:"Feature"===r.type?o:a)(r)}function o(r){var n,t=a(r.geometry);for(n in null!=r.id&&(t.id=r.id),null!=r.bbox&&(t.bbox=r.bbox),r.properties){t.properties=r.properties;break}return t}function a(r){if(null==r)return{type:null};var n="GeometryCollection"===r.type?{type:"GeometryCollection",geometries:r.geometries.map(a)}:"Point"===r.type||"MultiPoint"===r.type?{type:r.type,coordinates:r.coordinates}:{type:r.type,arcs:r.coordinates};return null!=r.bbox&&(n.bbox=r.bbox),n}function p(r){var n,t=r[0],e=r[1];return e<t&&(n=t,t=e,e=n),t+31*e}function g(r,n){var t,e=r[0],o=r[1],a=n[0],i=n[1];return o<e&&(t=e,e=o,o=t),i<a&&(t=a,a=i,i=t),e===a&&o===i}function c(){return!0}function f(r){return r}function s(r){return null!=r.type}function y(r){var n=r[0],t=r[1],e=r[2];return Math.abs((n[0]-e[0])*(t[1]-n[1])-(n[0]-t[0])*(e[1]-n[1]))/2}function u(r){for(var n,t=-1,e=r.length,o=r[e-1],a=0;++t<e;)n=o,o=r[t],a+=n[0]*o[1]-n[1]*o[0];return Math.abs(a)/2}function v(r,n){return r[1][2]-n[1][2]}function b(r){return[r[0],r[1],0]}function l(r,n){return n-r}function m(r,n){for(var t,e,o=0,a=r.length,i=0,u=r[n?o++:a-1],c=u[0]*R,f=u[1]*R/2+z,s=B(f),l=D(f);o<a;++o){t=c,c=(u=r[o])[0]*R,f=u[1]*R/2+z,e=s,s=B(f),l;var h=c-t,p=0<=h?1:-1,g=p*h,y=l*(l=D(f)),v=e*s+y*B(g),b=y*p*D(g);i+=W(b,v)}return i}function d(r){return r}function E(r){function n(r){(r=o(r))[0]<a&&(a=r[0]),r[0]>u&&(u=r[0]),r[1]<i&&(i=r[1]),r[1]>c&&(c=r[1])}function t(r){switch(r.type){case"GeometryCollection":r.geometries.forEach(t);break;case"Point":n(r.coordinates);break;case"MultiPoint":r.coordinates.forEach(n)}}var e,o=T(r.transform),a=1/0,i=a,u=-a,c=-a;for(e in r.arcs.forEach(function(r){for(var n,t=-1,e=r.length;++t<e;)(n=o(r[t],t))[0]<a&&(a=n[0]),n[0]>u&&(u=n[0]),n[1]<i&&(i=n[1]),n[1]>c&&(c=n[1])}),r.objects)t(r.objects[e]);return[a,i,u,c]}function P(n,r){return"GeometryCollection"===r.type?{type:"FeatureCollection",features:r.geometries.map(function(r){return t(n,r)})}:t(n,r)}function x(h,o){function r(r,n){for(var t in r){var e=r[t];delete n[e.start],delete e.start,delete e.end,e.forEach(function(r){a[r<0?~r:r]=1}),i.push(e)}}var a={},p={},g={},i=[],u=-1;return o.forEach(function(r,n){var t,e=h.arcs[r<0?~r:r];e.length<3&&!e[1][0]&&!e[1][1]&&(t=o[++u],o[u]=r,o[n]=t)}),o.forEach(function(r){var n,t,e,o,a,i,u,c,f=(e=r,a=h.arcs[e<0?~e:e],i=a[0],h.transform?(o=[0,0],a.forEach(function(r){o[0]+=r[0],o[1]+=r[1]})):o=a[a.length-1],e<0?[o,i]:[i,o]),s=f[0],l=f[1];(n=g[s])?(delete g[n.end],n.push(r),n.end=l,(t=p[l])?(delete p[t.start],u=t===n?n:n.concat(t),p[u.start=n.start]=g[u.end=t.end]=u):p[n.start]=g[n.end]=n):(n=p[l])?(delete p[n.start],n.unshift(r),n.start=s,(t=g[s])?(delete g[t.end],c=t===n?n:t.concat(n),p[c.start=t.start]=g[c.end=n.end]=c):p[n.start]=g[n.end]=n):p[(n=[r]).start=s]=g[n.end=l]=n}),r(g,p),r(p,g),o.forEach(function(r){a[r<0?~r:r]||i.push([r])}),i}function w(r,n){for(var t=0,e=r.length;t<e;){var o=t+e>>>1;r[o]<n?t=1+o:e=o}return t}function k(r){if(null==r)return d;var u,c,f=r.scale[0],s=r.scale[1],l=r.translate[0],h=r.translate[1];return function(r,n){n||(u=c=0);var t=2,e=r.length,o=new Array(e),a=Math.round((r[0]-l)/f),i=Math.round((r[1]-h)/s);for(o[0]=a-u,u=a,o[1]=i-c,c=i;t<e;)o[t]=r[t],++t;return o}}function A(a,i,u,r,c,n){3===arguments.length&&(r=n=Array,c=null);for(var f=new r(a=1<<Math.max(4,Math.ceil(Math.log(a)/Math.LN2))),s=new n(a),l=a-1,t=0;t<a;++t)f[t]=c;return{set:function(r,n){for(var t=i(r)&l,e=f[t],o=0;e!=c;){if(u(e,r))return s[t]=n;if(++o>=a)throw new Error("full hashmap");e=f[t=t+1&l]}return f[t]=r,s[t]=n},maybeSet:function(r,n){for(var t=i(r)&l,e=f[t],o=0;e!=c;){if(u(e,r))return s[t];if(++o>=a)throw new Error("full hashmap");e=f[t=t+1&l]}return f[t]=r,s[t]=n},get:function(r,n){for(var t=i(r)&l,e=f[t],o=0;e!=c;){if(u(e,r))return s[t];if(++o>=a)break;e=f[t=t+1&l]}return n},keys:function(){for(var r=[],n=0,t=f.length;n<t;++n){var e=f[n];e!=c&&r.push(e)}return r}}}function L(r,n){return r[0]===n[0]&&r[1]===n[1]}function C(r){var n=O[0]^O[1];return 2147483647&(n<<5^n>>7^O[2]^O[3])}function S(r){function n(r,n,t,e){var o,a;c[t]!==r&&(c[t]=r,0<=(o=f[t])?(a=s[t],o===n&&a===e||o===e&&a===n||(++h,l[t]=1)):(f[t]=n,s[t]=e))}function o(r){return C(i[r])}function a(r,n){return L(i[r],i[n])}for(var i=r.coordinates,t=r.lines,e=r.rings,u=function(){for(var r=A(1.4*i.length,o,a,Int32Array,-1,Int32Array),n=new Int32Array(i.length),t=0,e=i.length;t<e;++t)n[t]=r.maybeSet(t,t);return n}(),c=new Int32Array(i.length),f=new Int32Array(i.length),s=new Int32Array(i.length),l=new Int8Array(i.length),h=0,p=0,g=i.length;p<g;++p)c[p]=f[p]=s[p]=-1;for(p=0,g=t.length;p<g;++p){var y=t[p],v=y[0],b=y[1],m=u[v],d=u[++v];for(++h,l[m]=1;++v<=b;)n(p,m,m=d,d=u[v]);++h,l[d]=1}for(p=0,g=i.length;p<g;++p)c[p]=-1;for(p=0,g=e.length;p<g;++p){var M=e[p],E=M[0]+1,P=M[1];for(n(p,u[P-1],m=u[E-1],d=u[E]);++E<=P;)n(p,m,m=d,d=u[E])}c=f=s=null;var x,w=function(o,a,i,r,u){3===arguments.length&&(r=Array,u=null);for(var c=new r(o=1<<Math.max(4,Math.ceil(Math.log(o)/Math.LN2))),f=o-1,n=0;n<o;++n)c[n]=u;return{add:function(r){for(var n=a(r)&f,t=c[n],e=0;t!=u;){if(i(t,r))return!0;if(++e>=o)throw new Error("full hashset");t=c[n=n+1&f]}return c[n]=r,!0},has:function(r){for(var n=a(r)&f,t=c[n],e=0;t!=u;){if(i(t,r))return!0;if(++e>=o)break;t=c[n=n+1&f]}return!1},values:function(){for(var r=[],n=0,t=c.length;n<t;++n){var e=c[n];e!=u&&r.push(e)}return r}}}(1.4*h,C,L);for(p=0,g=i.length;p<g;++p)l[x=u[p]]&&w.add(i[x]);return w}function G(r){for(var n,t,e,o,a,i=S(r),u=r.coordinates,c=r.lines,f=r.rings,s=0,l=c.length;s<l;++s)for(var h=c[s],p=h[0],g=h[1];++p<g;)i.has(u[p])&&(n={0:p,1:h[1]},h[1]=p,h=h.next=n);for(s=0,l=f.length;s<l;++s)for(var y=f[s],v=y[0],b=v,m=y[1],d=i.has(u[v]);++b<m;)i.has(u[b])&&(d?(n={0:b,1:y[1]},y[1]=b,y=y.next=n):(a=(o=m)-b,M(t=u,e=v,o),M(t,e,e+a),M(t,e+a,o),u[m]=u[v],d=!0,b=v));return r}function j(r){function n(r){var n,t,e,o,a,i,u,c;if(e=f.get(n=l[r[0]]))for(u=0,c=e.length;u<c;++u)if(function(r,n){var t=r[0],e=n[0],o=r[1];if(t-o!=e-n[1])return!1;for(;t<=o;++t,++e)if(!L(l[t],l[e]))return!1;return!0}(o=e[u],r))return r[0]=o[0],r[1]=o[1],0;if(a=f.get(t=l[r[1]]))for(u=0,c=a.length;u<c;++u)if(function(r,n){var t=r[0],e=n[0],o=r[1],a=n[1];if(t-o!=e-a)return!1;for(;t<=o;++t,--a)if(!L(l[t],l[a]))return!1;return!0}(i=a[u],r))return r[1]=i[0],r[0]=i[1],0;e?e.push(r):f.set(n,[r]),a?a.push(r):f.set(t,[r]),h.push(r)}function i(r,n){var t=r[0],e=n[0],o=r[1]-t;if(o==n[1]-e){for(var a=s(r),i=s(n),u=0;u<o;++u)if(!L(l[t+(u+a)%o],l[e+(u+i)%o]))return;return 1}}function u(r,n){var t=r[0],e=n[0],o=r[1],a=n[1],i=o-t;if(i==a-e){for(var u=s(r),c=i-s(n),f=0;f<i;++f)if(!L(l[t+(f+u)%i],l[a-(f+c)%i]))return;return 1}}function s(r){for(var n=r[0],t=r[1],e=n,o=e,a=l[e];++e<t;){var i=l[e];(i[0]<a[0]||i[0]===a[0]&&i[1]<a[1])&&(o=e,a=i)}return o-n}var t,e,l=r.coordinates,o=r.lines,a=r.rings,c=o.length+a.length;for(delete r.lines,delete r.rings,p=0,g=o.length;p<g;++p)for(t=o[p];t=t.next;)++c;for(p=0,g=a.length;p<g;++p)for(e=a[p];e=e.next;)++c;for(var f=A(2*c*1.4,C,L),h=r.arcs=[],p=0,g=o.length;p<g;++p)for(t=o[p];n(t),t=t.next;);for(p=0,g=a.length;p<g;++p)if((e=a[p]).next)for(;n(e),e=e.next;);else!function(r){var n,t,e,o,a;if(t=f.get(l[r[0]]))for(o=0,a=t.length;o<a;++o){if(i(e=t[o],r))return r[0]=e[0],r[1]=e[1];if(u(e,r))return r[0]=e[1],r[1]=e[0]}if(t=f.get(n=l[r[0]+s(r)]))for(o=0,a=t.length;o<a;++o){if(i(e=t[o],r))return r[0]=e[0],r[1]=e[1];if(u(e,r))return r[0]=e[1],r[1]=e[0]}t?t.push(r):f.set(n,[r]),h.push(r)}(e);return r}function _(r){function n(r){switch(r.type){case"GeometryCollection":r.geometries.forEach(n);break;case"Polygon":t(r.arcs);break;case"MultiPolygon":r.arcs.forEach(t)}}function t(r){for(var n=0,t=r.length;n<t;++n,++f)for(var e=r[n],o=0,a=e.length;o<a;++o){var i=e[o];i<0&&(i=~i);var u=c[i];null==u?c[i]=f:u!==f&&(c[i]=-1)}}var e,c=new Array(r.arcs.length),f=0;for(e in r.objects)n(r.objects[e]);return function(r){for(var n,t=0,e=r.length;t<e;++t)if(-1===c[(n=r[t])<0?~n:n])return!0;return!1}}function I(t,e,o){return e=null==e?Number.MIN_VALUE:+e,null==o&&(o=u),function(r,n){return o(P(t,{type:"Polygon",arcs:[r]}).geometry.coordinates[0],n)>=e}}var T=function(r){if(null==r)return d;var a,i,u=r.scale[0],c=r.scale[1],f=r.translate[0],s=r.translate[1];return function(r,n){n||(a=i=0);var t=2,e=r.length,o=new Array(e);for(o[0]=(a+=r[0])*u+f,o[1]=(i+=r[1])*c+s;t<e;)o[t]=r[t],++t;return o}},F=function(r,n){for(var t,e=r.length,o=e-n;o<--e;)t=r[o],r[o++]=r[e],r[e]=t},N=new ArrayBuffer(16),O=new Uint32Array(N),q=Math.PI,U=2*q,z=q/4,R=q/180,V=Math.abs,W=Math.atan2,B=Math.cos,D=Math.sin;r.bbox=E,r.feature=P,r.mesh=function(r){return i(r,n.apply(this,arguments))},r.meshArcs=n,r.merge=function(r){return i(r,e.apply(this,arguments))},r.mergeArcs=e,r.neighbors=function(r){function t(r,t){r.forEach(function(r){r<0&&(r=~r);var n=o[r];n?n.push(t):o[r]=[t]})}function e(r,n){r.forEach(function(r){t(r,n)})}var o={},n=r.map(function(){return[]}),a={LineString:t,MultiLineString:e,Polygon:e,MultiPolygon:function(r,n){r.forEach(function(r){e(r,n)})}};for(var i in r.forEach(function n(r,t){"GeometryCollection"===r.type?r.geometries.forEach(function(r){n(r,t)}):r.type in a&&a[r.type](r.arcs,t)}),o)for(var u=o[i],c=u.length,f=0;f<c;++f)for(var s=f+1;s<c;++s){var l,h=u[f],p=u[s];(l=n[h])[i=w(l,p)]!==p&&l.splice(i,0,p),(l=n[p])[i=w(l,h)]!==h&&l.splice(i,0,h)}return n},r.quantize=function(r,n){function t(r){return l(r)}function e(r){var n;switch(r.type){case"GeometryCollection":n={type:"GeometryCollection",geometries:r.geometries.map(e)};break;case"Point":n={type:"Point",coordinates:t(r.coordinates)};break;case"MultiPoint":n={type:"MultiPoint",coordinates:r.coordinates.map(t)};break;default:return r}return null!=r.id&&(n.id=r.id),null!=r.bbox&&(n.bbox=r.bbox),null!=r.properties&&(n.properties=r.properties),n}if(r.transform)throw new Error("already quantized");if(n&&n.scale)f=r.bbox;else{if(!(2<=(o=Math.floor(n))))throw new Error("n must be ≥2");var o,a=(f=r.bbox||E(r))[0],i=f[1],u=f[2],c=f[3];n={scale:[u-a?(u-a)/(o-1):1,c-i?(c-i)/(o-1):1],translate:[a,i]}}var f,s,l=k(n),h=r.objects,p={};for(s in h)p[s]=e(h[s]);return{type:"Topology",bbox:f,transform:n,objects:p,arcs:r.arcs.map(function(r){var n,t=0,e=1,o=r.length,a=new Array(o);for(a[0]=l(r[0],0);++t<o;)((n=l(r[t],t))[0]||n[1])&&(a[e++]=n);return 1===e&&(a[e++]=[0,0]),a.length=e,a})}},r.transform=T,r.untransform=k,r.topology=function(r,n){function t(r){r&&s.hasOwnProperty(r.type)&&s[r.type](r)}function e(r){var n=[];do{var t=f.get(r);n.push(r[0]<r[1]?t:~t)}while(r=r.next);return n}function o(r){return r.map(e)}var a=function(r){function n(r){null!=r&&f.hasOwnProperty(r.type)&&f[r.type](r)}function t(r){var n=r[0],t=r[1];n<a&&(a=n),u<n&&(u=n),t<i&&(i=t),c<t&&(c=t)}function e(r){r.forEach(t)}function o(r){r.forEach(e)}var a=1/0,i=1/0,u=-1/0,c=-1/0,f={GeometryCollection:function(r){r.geometries.forEach(n)},Point:function(r){t(r.coordinates)},MultiPoint:function(r){r.coordinates.forEach(t)},LineString:function(r){e(r.arcs)},MultiLineString:function(r){r.arcs.forEach(e)},Polygon:function(r){r.arcs.forEach(e)},MultiPolygon:function(r){r.arcs.forEach(o)}};for(var s in r)n(r[s]);return a<=u&&i<=c?[a,i,u,c]:void 0}(r=function(r){var n,t={};for(n in r)t[n]=h(r[n]);return t}(r)),i=0<n&&a&&function(r,n,t){function e(r){return[Math.round((r[0]-l)*p),Math.round((r[1]-h)*g)]}function o(r,n){for(var t,e,o,a,i,u=-1,c=0,f=r.length,s=new Array(f);++u<f;)t=r[u],a=Math.round((t[0]-l)*p),i=Math.round((t[1]-h)*g),a===e&&i===o||(s[c++]=[e=a,o=i]);for(s.length=c;c<n;)c=s.push([s[0][0],s[0][1]]);return s}function a(r){return o(r,2)}function i(r){return o(r,4)}function u(r){return r.map(i)}function c(r){null!=r&&y.hasOwnProperty(r.type)&&y[r.type](r)}var l=n[0],h=n[1],f=n[2],s=n[3],p=f-l?(t-1)/(f-l):1,g=s-h?(t-1)/(s-h):1,y={GeometryCollection:function(r){r.geometries.forEach(c)},Point:function(r){r.coordinates=e(r.coordinates)},MultiPoint:function(r){r.coordinates=r.coordinates.map(e)},LineString:function(r){r.arcs=a(r.arcs)},MultiLineString:function(r){r.arcs=r.arcs.map(a)},Polygon:function(r){r.arcs=u(r.arcs)},MultiPolygon:function(r){r.arcs=r.arcs.map(u)}};for(var v in r)c(r[v]);return{scale:[1/p,1/g],translate:[l,h]}}(r,a,n),u=j(G(function(r){function n(r){r&&f.hasOwnProperty(r.type)&&f[r.type](r)}function t(r){for(var n=0,t=r.length;n<t;++n)c[++a]=r[n];var e={0:a-t+1,1:a};return i.push(e),e}function e(r){for(var n=0,t=r.length;n<t;++n)c[++a]=r[n];var e={0:a-t+1,1:a};return u.push(e),e}function o(r){return r.map(e)}var a=-1,i=[],u=[],c=[],f={GeometryCollection:function(r){r.geometries.forEach(n)},LineString:function(r){r.arcs=t(r.arcs)},MultiLineString:function(r){r.arcs=r.arcs.map(t)},Polygon:function(r){r.arcs=r.arcs.map(e)},MultiPolygon:function(r){r.arcs=r.arcs.map(o)}};for(var s in r)n(r[s]);return{type:"Topology",coordinates:c,lines:i,rings:u,objects:r}}(r))),c=u.coordinates,f=A(1.4*u.arcs.length,p,g);r=u.objects,u.bbox=a,u.arcs=u.arcs.map(function(r,n){return f.set(r,n),c.slice(r[0],r[1]+1)}),delete u.coordinates,c=null;var s={GeometryCollection:function(r){r.geometries.forEach(t)},LineString:function(r){r.arcs=e(r.arcs)},MultiLineString:function(r){r.arcs=r.arcs.map(e)},Polygon:function(r){r.arcs=r.arcs.map(e)},MultiPolygon:function(r){r.arcs=r.arcs.map(o)}};for(var l in r)t(r[l]);return i&&(u.transform=i,u.arcs=function(r){for(var n=-1,t=r.length;++n<t;){for(var e,o,a=r[n],i=0,u=1,c=a.length,f=a[0],s=f[0],l=f[1];++i<c;)e=(f=a[i])[0],o=f[1],e===s&&o===l||(a[u++]=[e-s,o-l],s=e,l=o);1===u&&(a[u++]=[0,0]),a.length=u}return r}(u.arcs)),u},r.filter=function(r,t){function e(r){var n,t;switch(r.type){case"Polygon":n=(t=o(r.arcs))?{type:"Polygon",arcs:t}:{type:null};break;case"MultiPolygon":n=(t=r.arcs.map(o).filter(f)).length?{type:"MultiPolygon",arcs:t}:{type:null};break;case"GeometryCollection":n=(t=r.geometries.map(e).filter(s)).length?{type:"GeometryCollection",geometries:t}:{type:null};break;default:return r}return null!=r.id&&(n.id=r.id),null!=r.bbox&&(n.bbox=r.bbox),null!=r.properties&&(n.properties=r.properties),n}function o(r){return r.length&&(n=r[0],t(n,!1))?[r[0]].concat(r.slice(1).filter(a)):null;var n}function a(r){return t(r,!0)}var n,i=r.objects,u={};for(n in null==t&&(t=c),i)u[n]=e(i[n]);return function(r){function n(r){switch(r.type){case"GeometryCollection":r.geometries.forEach(n);break;case"LineString":e(r.arcs);break;case"MultiLineString":case"Polygon":r.arcs.forEach(e);break;case"MultiPolygon":r.arcs.forEach(o)}}function t(r){r<0&&(r=~r),v[r]||(v[r]=1,++b)}function e(r){r.forEach(t)}function o(r){r.forEach(e)}function a(r){var n;switch(r.type){case"GeometryCollection":n={type:"GeometryCollection",geometries:r.geometries.map(a)};break;case"LineString":n={type:"LineString",arcs:u(r.arcs)};break;case"MultiLineString":n={type:"MultiLineString",arcs:r.arcs.map(u)};break;case"Polygon":n={type:"Polygon",arcs:r.arcs.map(u)};break;case"MultiPolygon":n={type:"MultiPolygon",arcs:r.arcs.map(c)};break;default:return r}return null!=r.id&&(n.id=r.id),null!=r.bbox&&(n.bbox=r.bbox),null!=r.properties&&(n.properties=r.properties),n}function i(r){return r<0?~v[~r]:v[r]}function u(r){return r.map(i)}function c(r){return r.map(u)}var f,s,l=r.objects,h={},p=r.arcs,g=p.length,y=-1,v=new Array(g),b=0,m=-1;for(s in l)n(l[s]);for(f=new Array(b);++y<g;)v[y]&&(v[y]=++m,f[m]=p[y]);for(s in l)h[s]=a(l[s]);return{type:"Topology",bbox:r.bbox,transform:r.transform,objects:h,arcs:f}}({type:"Topology",bbox:r.bbox,transform:r.transform,objects:u,arcs:r.arcs})},r.filterAttached=_,r.filterAttachedWeight=function(r,n,t){var e=_(r),o=I(r,n,t);return function(r,n){return e(r,n)||o(r,n)}},r.filterWeight=I,r.planarRingArea=u,r.planarTriangleArea=y,r.presimplify=function(r,c){function f(r){l.remove(r),r[1][2]=c(r),l.push(r)}var n,i,u,s=r.transform?T(r.transform):b,l=(i=[],u=0,(n={}).push=function(r){return e(i[r._=u]=r,u++),u},n.pop=function(){if(!(u<=0)){var r,n=i[0];return 0<--u&&(r=i[u],o(i[r._=0]=r,0)),n}},n.remove=function(r){var n,t=r._;if(i[t]===r)return t!==--u&&(v(n=i[u],r)<0?e:o)(i[n._=t]=n,t),t},n);function e(r,n){for(;0<n;){var t=(n+1>>1)-1,e=i[t];if(0<=v(r,e))break;i[e._=n]=e,i[r._=n=t]=r}}function o(r,n){for(;;){var t=n+1<<1,e=t-1,o=n,a=i[o];if(e<u&&v(i[e],a)<0&&(a=i[o=e]),t<u&&v(i[t],a)<0&&(a=i[o=t]),o===n)break;i[a._=n]=a,i[r._=n=o]=r}}null==c&&(c=y);var t=r.arcs.map(function(r){for(var n,t=[],e=0,o=1,a=(r=r.map(s)).length-1;o<a;++o)(n=[r[o-1],r[o],r[o+1]])[1][2]=c(n),t.push(n),l.push(n);for(r[0][2]=r[a][2]=1/0,o=0,a=t.length;o<a;++o)(n=t[o]).previous=t[o-1],n.next=t[o+1];for(;n=l.pop();){var i=n.previous,u=n.next;n[1][2]<e?n[1][2]=e:e=n[1][2],i&&(i.next=u,i[2]=n[2],f(i)),u&&(u.previous=i,u[0]=n[0],f(u))}return r});return{type:"Topology",bbox:r.bbox,objects:r.objects,arcs:t}},r.quantile=function(r,n){var t=[];return r.arcs.forEach(function(r){r.forEach(function(r){isFinite(r[2])&&t.push(r[2])})}),t.length&&function(r,n){if(t=r.length){if((n=+n)<=0||t<2)return r[0];if(1<=n)return r[t-1];var t,e=(t-1)*n,o=Math.floor(e),a=r[o];return a+(r[o+1]-a)*(e-o)}}(t.sort(l),n)},r.simplify=function(r,i){i=null==i?Number.MIN_VALUE:+i;var n=r.arcs.map(function(r){for(var n,t=-1,e=0,o=r.length,a=new Array(o);++t<o;)(n=r[t])[2]>=i&&(a[e++]=[n[0],n[1]]);return a.length=e,a});return{type:"Topology",transform:r.transform,bbox:r.bbox,objects:r.objects,arcs:n}},r.sphericalRingArea=function(r,n){var t=m(r,!0);return n&&(t*=-1),2*(t<0?U+t:t)},r.sphericalTriangleArea=function(r){return 2*V(m(r,!1))},Object.defineProperty(r,"__esModule",{value:!0})});