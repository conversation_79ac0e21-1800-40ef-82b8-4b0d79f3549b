<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <link rel="icon" href="/assets/images/logoD.png" />
    <title>chatApp</title>
    <!-- Simple bar CSS -->
    <link
      rel="stylesheet"
      href="/assets/css/vendor/simplebar.css"
      asp-append-version="true"
    />
    <!-- Fonts CSS -->
    <link
      href="https://fonts.googleapis.com/css2?family=Overpass:ital,wght@0,100;0,200;0,300;0,400;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,600;1,700;1,800;1,900&display=swap"
      rel="stylesheet"
    />
    <!-- Icons CSS -->
    <link
      rel="stylesheet"
      href="/assets/css/vendor/feather.css"
      asp-append-version="true"
    />
    <link
      rel="stylesheet"
      href="/assets/fonts/tabler/tabler-icons.css"
      asp-append-version="true"
    />
    <link
      rel="stylesheet"
      href="/assets/css/vendor/select2.css"
      asp-append-version="true"
    />
    <link
      rel="stylesheet"
      href="/assets/css/vendor/dropzone.css"
      asp-append-version="true"
    />
    <link
      rel="stylesheet"
      href="/assets/css/vendor/uppy.min.css"
      asp-append-version="true"
    />
    <!-- @* <link rel="stylesheet" href="/assets/css/vendor/jquery.steps.css" asp-append-version="true">*@ -->
    <link
      rel="stylesheet"
      href="/assets/css/vendor/jquery.timepicker.css"
      asp-append-version="true"
    />
    <link
      rel="stylesheet"
      href="/assets/css/vendor/quill.snow.css"
      asp-append-version="true"
    />
    <link
      rel="stylesheet"
      href="/assets/css/vendor/dataTables.bootstrap4.css"
      asp-append-version="true"
    />
    <!-- DataTables -->

    <style>
      #loading-indicator {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(
          255,
          255,
          255,
          0.7
        ); /* Semi-transparent white background */
        display: none; /* Initially hidden */
        justify-content: center;
        align-items: center;
        z-index: 1000; /* Ensure it's on top */
      }

      #loading-indicator.loading-active {
        display: flex; /* Make it visible when 'loading-active' class is added */
      }

      .spinner-border {
        /* Example spinner styling using Bootstrap classes - adapt as needed */
        width: 3rem;
        height: 3rem;
      }
    </style>
    <!-- @await RenderSectionAsync("headars", required: false) -->
    <!-- Date Range Picker CSS -->
    <link
      rel="stylesheet"
      href="/assets/css/vendor/daterangepicker.css"
      asp-append-version="true"
    />
    <!-- App CSS -->

    <link
      rel="stylesheet"
      href="/assets/css/app.css"
      asp-append-version="true"
    />

    <!-- @await RenderSectionAsync("headarsAfterStyle", required: false) -->
    <link rel="stylesheet" href="/assets/css/chatcss/all-privacy-screen.css" />
    <link rel="stylesheet" href="/assets/css/chat-profile.css" />

    <link rel="stylesheet" href="/assets/css/chatcss/search-message.css" />
    <link rel="stylesheet" href="/assets/css/chatcss/Contact-info.css" />
    <link rel="stylesheet" href="/assets/css/chatcss/setings.css" />
    <link rel="stylesheet" href="/assets/css/chatcss/newChat.css" />
    <link rel="stylesheet" href="/assets/css/chatcss/status.css" />
    <link rel="stylesheet" href="/assets/css/chatcss/intro.css" />
    <link rel="stylesheet" href="/assets/css/chatcss/style.css" />
    <link href="/assets/css/chatcss/file-attachment.css" rel="stylesheet" />
    <link rel="stylesheet" href="/assets/css/chatcss/group.css" />
    <link rel="stylesheet" href="/assets/css/chatcss/appLoader.css" />

    <!-- Icon -->
    <link
      href="https://unpkg.com/boxicons@2.1.2/css/boxicons.min.css"
      rel="stylesheet"
    />

    <!-- Animation -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    />
  </head>
  <body class="vertical rtl">
    <!-- Loading Indicator -->

    <div class="loading" style="display: block">Loading&#8230;</div>

    <div class="wrapper">
      <!-- start header navbar -->
      <nav
        class="topnav navbar navbar-light bg-white flex-row border-bottom shadow z-index-1"
      >
        <button
          type="button"
          class="navbar-toggler text-muted mt-2 p-0 mr-3 collapseSidebar"
        >
          <i class="fe fe-menu navbar-toggler-icon"></i>
        </button>
        <!-- <form class="form-inline mr-auto searchform text-muted">
              <input class="form-control mr-sm-2 bg-transparent border-0 pl-4 text-muted" type="search" placeholder="Type something..." aria-label="Search">
            </form> -->
        <ul class="nav">
          <li class="nav-item nav-notif">
            <a class="nav-link text-muted my-2" href="/NoteOrSuggestion">
              <span class="fe fe-cast fe-16"></span>
              <span class="dot dot-md bg-success"></span>
              <span class="ml-2 fw-medium text-muted mb-0"> الاقتراحات</span>
            </a>
          </li>
        </ul>
      </nav>
      <!-- end header navbar -->
      <!-- start sidebar -->
      <aside
        class="sidebar-left border-right bg-white"
        id="leftSidebar"
        data-simplebar
      >
        <a
          href="#"
          class="btn collapseSidebar toggle-btn d-lg-none text-muted ml-2 mt-3"
          data-toggle="toggle"
        >
          <i class="fe fe-x"><span class="sr-only"></span></i>
        </a>
        <nav class="vertnav navbar navbar-light">
          <!-- nav bar -->
          <div class="w-100 mb-4 d-flex">
            <a
              class="navbar-brand mx-auto mt-2 flex-fill text-center"
              href="/Home"
            >
              <img
                src="/assets/images/logoD.png"
                id="logo"
                alt="..."
                class="img-fluid brand-lg"
              />
            </a>
          </div>
          <ul class="navbar-nav flex-fill w-100 mb-2">
            <li class="nav-item">
              <a href="/Home" aria-expanded="false" class="nav-link">
                <i class="fe fe-home fe-16"></i>
                <span class="ml-3 item-text">الصفحة الرئيسية</span>
                <span class="sr-only">(current)</span>
              </a>
            </li>
          </ul>
          <p class="text-muted nav-heading mb-1">
            <span>إدارة المنصة</span>
          </p>
          <ul class="navbar-nav flex-fill w-100 mb-2">
            <li class="nav-item w-100">
              <a class="nav-link" href="/Chat">
                <i class="fe fe-message-circle fe-16"></i>
                <span class="ml-3 item-text">سجلات الدردشة </span>
              </a>
            </li>
          </ul>
        </nav>
      </aside>
      <!-- end sidebar -->
      <!-- start main layout -->
      <main role="main" class="main-content">
        <!-- start container layout  -->
        <div id="toasts-container"></div>
        <div class="container-fluid">
          <div class="row justify-content-center">
            <div class="col-12">
              <!-- start header layout -->
              <div class="row align-items-center mb-4">
                <div class="col">
                  <h2 class="h5 page-title">AppChat</h2>
                </div>
              </div>
              <!-- end header layout -->
              <!-- start card data table -->
              <!-- @RenderBody() -->

              <div class="card shadow light-theme">
                <div id="loading-container" class="loading-container">
                  <!-- حالة التحميل -->
                  <div
                    class="loading-state"
                    id="loadingState"
                    style="display: none"
                  >
                    <div class="loading-spinner">
                      <div class="spinner"></div>
                    </div>

                    <div class="progress-container">
                      <div class="chat-progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                      </div>
                      <div class="progress-text" id="progressText">0%</div>
                    </div>
                    <div class="loading-details" id="loadingDetails">
                      جاري تحميل المحادثات...
                    </div>
                  </div>

                  <!-- حالة الخطأ -->
                  <div
                    class="error-state"
                    id="errorState"
                    style="display: none"
                  >
                    <div class="error-icon">
                      <svg viewBox="0 0 24 24" width="48" height="48">
                        <path
                          fill="currentColor"
                          d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"
                        />
                      </svg>
                    </div>
                    <div class="error-message" id="errorMessage">
                      حدث خطأ أثناء تحميل المحادثات
                    </div>
                    <button class="retry-button" id="retryButton">
                      إعادة المحاولة
                    </button>
                  </div>

                  <!-- حالة عدم الاتصال -->
                  <div
                    class="offline-state"
                    id="offlineState"
                    style="display: none"
                  >
                    <div class="offline-icon">
                      <svg viewBox="0 0 24 24" width="48" height="48">
                        <path
                          fill="currentColor"
                          d="M24 8.98C20.93 5.9 16.69 4 12 4S3.07 5.9 0 8.98L12 21 24 8.98zM2.92 9.07C5.51 7.08 8.67 6 12 6s6.49 1.08 9.08 3.07l-9.08 9.08-9.08-9.08z"
                        />
                      </svg>
                    </div>
                    <div class="offline-message">لا يوجد اتصال بالإنترنت</div>
                    <div class="offline-submessage">يرجى التحقق من اتصالك</div>
                    <button class="retry-button" id="retryOfflineButton">
                      إعادة المحاولة
                    </button>
                  </div>
                </div>

                <h1 class="media">متوفر فقط على سطح المكتب 😊😇</h1>
                <div id="chat-container" class="container main p-0 m-0">
                  <div
                    class="leftSide animate__animated animate__fadeInRight"
                    id="leftSid"
                  >
                    <!-- Header -->
                    <div class="header">
                      <!-- Profile Picture -->
                      <div class="userImg" id="imageUserProfile">
                        <img
                          src="/assets/avatars/imageGrey.jpg"
                          alt=""
                          class="cover"
                        />
                      </div>

                      <!-- Dropdown -->
                      <ul class="dropLeft" id="dropLeft">
                        <li
                          class="listItem"
                          role="button"
                          onclick="openTeamList()"
                        >
                          قائمة فرق خدمة العملاء
                        </li>

                        <li
                          class="listItem"
                          role="button"
                          onclick="openSettings()"
                        >
                          الإعدادات
                        </li>
                      </ul>

                      <!-- Icons -->
                      <div class="ICON">
                        <button
                          role="button"
                          class="icons"
                          onclick="openForm()"
                        >
                          <span class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="M19.005 3.175H4.674C3.642 3.175 3 3.789 3 4.821V21.02l3.544-3.514h12.461c1.033 0 2.064-1.06 2.064-2.093V4.821c-.001-1.032-1.032-1.646-2.064-1.646zm-4.989 9.869H7.041V11.1h6.975v1.944zm3-4H7.041V7.1h9.975v1.944z"
                              ></path>
                            </svg>
                          </span>
                        </button>

                        <button role="button" class="icons" id="dropDown2">
                          <span class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="M12 7a2 2 0 1 0-.001-4.001A2 2 0 0 0 12 7zm0 2a2 2 0 1 0-.001 3.999A2 2 0 0 0 12 9zm0 6a2 2 0 1 0-.001 3.999A2 2 0 0 0 12 15z"
                              ></path>
                            </svg>
                          </span>
                        </button>
                      </div>
                    </div>

                    <!-- Search Bar -->
                    <div class="search-bar">
                      <div>
                        <button class="search">
                          <span class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="M15.009 13.805h-.636l-.22-.219a5.184 5.184 0 0 0 1.256-3.386 5.207 5.207 0 1 0-5.207 5.208 5.183 5.183 0 0 0 3.385-1.255l.221.22v.635l4.004 3.999 1.194-1.195-3.997-4.007zm-4.808 0a3.605 3.605 0 1 1 0-7.21 3.605 3.605 0 0 1 0 7.21z"
                              ></path>
                            </svg>
                          </span>
                        </button>

                        <span class="go-back">
                          <svg
                            viewBox="0 0 24 24"
                            width="24"
                            height="24"
                            class=""
                          >
                            <path
                              fill="currentColor"
                              d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"
                            ></path>
                          </svg>
                        </span>
                        <input
                          type="text"
                          id="search-chats"
                          title="البحث على الدردشة"
                          aria-label="البحث على الدردشة"
                          placeholder="البحث على الدردشة"
                        />
                      </div>
                    </div>

                    <!-- Chats Box -->
                    <div id="chat-list" class="chats">
                      <!-- Chats 1 -->
                      <div
                        class="block item-chat-list"
                        onclick="openRightSide()"
                      >
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="احمد" aria-label="احمد">احمد</h4>
                            <p class="time">12:44 AM</p>
                          </div>
                          <div class="last-message-chat">
                            <div class="tick-icon">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20"
                                width="20"
                                height="20"
                                aria-label="read"
                                class="white-tick"
                              >
                                <path
                                  fill="currentColor"
                                  d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.879a.32.32 0 0 1-.484.033l-.358-.325a.319.319 0 0 0-.484.032l-.378.483a.418.418 0 0 0 .036.541l1.32 1.266c.143.14.361.125.484-.033l6.272-8.048a.366.366 0 0 0-.064-.512zm-4.1 0l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"
                                ></path>
                              </svg>
                            </div>

                            <div class="chat-text-icon">
                              <span class="text-last-message"
                                >ok thanks for your help i really
                                appreciate</span
                              >
                              <div class="unread">
                                <span class="numb">2</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="dropdown-icon">
                          <button class="pressed icons pP" id="dropDownaaa">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="M12 7a2 2 0 1 0-.001-4.001A2 2 0 0 0 12 7zm0 2a2 2 0 1 0-.001 3.999A2 2 0 0 0 12 9zm0 6a2 2 0 1 0-.001 3.999A2 2 0 0 0 12 15z"
                              ></path>
                            </svg>
                          </button>
                        </div>
                      </div>

                      <!-- Chats 2 -->
                      <div class="block item-chat-list">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="Abhishek" aria-label="Abhishek">
                              Abhishek
                            </h4>
                            <p class="time">7:30 PM</p>
                          </div>
                          <div class="last-message-chat">
                            <div class="tick-icon">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20"
                                width="20"
                                height="20"
                                aria-label="read"
                                class="blue-tick"
                              >
                                <path
                                  fill="currentColor"
                                  d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.879a.32.32 0 0 1-.484.033l-.358-.325a.319.319 0 0 0-.484.032l-.378.483a.418.418 0 0 0 .036.541l1.32 1.266c.143.14.361.125.484-.033l6.272-8.048a.366.366 0 0 0-.064-.512zm-4.1 0l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"
                                ></path>
                              </svg>
                            </div>

                            <div class="chat-text-icon">
                              <span class="text-last-message">Thank you.</span>
                              <div class="icon-more">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 19 20"
                                  width="19"
                                  height="20"
                                  class="hide animate__animated animate__fadeInUp"
                                >
                                  <path
                                    fill="currentColor"
                                    d="M3.8 6.7l5.7 5.7 5.7-5.7 1.6 1.6-7.3 7.2-7.3-7.2 1.6-1.6z"
                                  ></path>
                                </svg>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="dropdown-icon">
                          <button class="pressed icons pP" id="dropDownsss">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="M12 7a2 2 0 1 0-.001-4.001A2 2 0 0 0 12 7zm0 2a2 2 0 1 0-.001 3.999A2 2 0 0 0 12 9zm0 6a2 2 0 1 0-.001 3.999A2 2 0 0 0 12 15z"
                              ></path>
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="Newchat animate__animated animate__fadeInLeft"
                    id="Newchat"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <button
                          role="button"
                          class="icons"
                          onclick="closeForm()"
                        >
                          <i class="fe fe-arrow-right fe-24"></i>
                        </button>
                      </div>

                      <div class="newText">
                        <h2>قائمة المستخدمين</h2>
                      </div>
                    </div>
                    <!-- Search Bar -->
                    <div class="search-bar">
                      <div>
                        <button class="search">
                          <span class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="M15.009 13.805h-.636l-.22-.219a5.184 5.184 0 0 0 1.256-3.386 5.207 5.207 0 1 0-5.207 5.208 5.183 5.183 0 0 0 3.385-1.255l.221.22v.635l4.004 3.999 1.194-1.195-3.997-4.007zm-4.808 0a3.605 3.605 0 1 1 0-7.21 3.605 3.605 0 0 1 0 7.21z"
                              ></path>
                            </svg>
                          </span>
                        </button>

                        <span class="go-back">
                          <svg
                            viewBox="0 0 24 24"
                            width="24"
                            height="24"
                            class=""
                          >
                            <path
                              fill="currentColor"
                              d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"
                            ></path>
                          </svg>
                        </span>
                        <input
                          type="text"
                          id="user-search"
                          title="البحث عن مستخدم "
                          aria-label="البحث عن مستخدم "
                          placeholder="البحث عن مستخدم "
                        />
                      </div>
                    </div>
                    <!-- Chats -->

                    <div class="chats-new">
                      <div class="list-button">
                        <div class="block group" onclick="openGroup()">
                          <!-- Img -->
                          <div class="iconBox">
                            <i class="fe fe-users fe-18"></i>
                          </div>
                          <div class="head">
                            <h4
                              title="انشاء فريق جديد"
                              aria-label="انشاء فريق جديد"
                            >
                              انشاء فريق جديد
                            </h4>
                          </div>
                        </div>
                      </div>

                      <ul
                        class="nav nav-pills nav-fill mb-3 background-nav-chat"
                        id="pills-tab-chat"
                        role="tablist"
                      >
                        <li class="nav-item">
                          <a
                            class="nav-link m-1 p-1 active"
                            data-user-type-id="3"
                            data-toggle="pill"
                            href="#pills-platform"
                            role="tab"
                            aria-controls="pills-platform"
                            aria-selected="true"
                            >منصة</a
                          >
                        </li>
                        <li class="nav-item">
                          <a
                            class="nav-link m-1 p-1"
                            data-user-type-id="1"
                            data-toggle="pill"
                            href="#pills-client"
                            role="tab"
                            aria-controls="pills-client"
                            aria-selected="false"
                            >العملاء</a
                          >
                        </li>
                        <li class="nav-item">
                          <a
                            class="nav-link m-1 p-1"
                            data-user-type-id="2"
                            data-toggle="pill"
                            href="#pills-driver"
                            role="tab"
                            aria-controls="pills-driver"
                            aria-selected="false"
                            >السائقين</a
                          >
                        </li>
                      </ul>

                      <div class="tab-content mb-1" id="pills-tabContent">
                        <div
                          class="tab-pane fade show active"
                          role="tabpanel"
                          aria-labelledby="pills-home-tab"
                        >
                          <div class="chatbar">
                            <div class="list-contact" id="contact-list">
                              <div class="a">
                                <h3>A</h3>
                              </div>

                              <!-- Chats 1 -->
                              <div class="block top">
                                <!-- Img -->
                                <div class="imgBox">
                                  <img
                                    src="/assets/avatars/imageGrey.jpg"
                                    class="cover"
                                  />
                                </div>
                                <!-- Text -->
                                <div class="h-text">
                                  <div class="head">
                                    <h4 title="" aria-label=""></h4>
                                  </div>
                                  <div class="message">
                                    <p title="" aria-label=""></p>
                                  </div>
                                </div>
                              </div>

                              <!-- Chats 2 -->
                              <div class="block">
                                <!-- Img -->
                                <div class="imgBox">
                                  <img
                                    src="/assets/avatars/imageGrey.jpg"
                                    class="cover"
                                  />
                                </div>
                                <!-- Text -->
                                <div class="h-text">
                                  <div class="head">
                                    <h4 title="Anamika" aria-label="Anamika">
                                      Anamika
                                    </h4>
                                  </div>
                                  <div class="message">
                                    <p
                                      title="Simplicity is the Best Policy."
                                      aria-label="Simplicity is the Best Policy."
                                    >
                                      Simplicity is the Best Policy.
                                    </p>
                                  </div>
                                </div>
                              </div>

                              <!-- Chats 3 -->
                              <div class="block">
                                <!-- Img -->
                                <div class="imgBox">
                                  <img
                                    src="/assets/avatars/imageGrey.jpg"
                                    class="cover"
                                  />
                                </div>
                                <!-- Text -->
                                <div class="h-text">
                                  <div class="head">
                                    <h4 title="Abhishek" aria-label="Abhishek">
                                      Abhishek
                                    </h4>
                                  </div>
                                  <div class="message">
                                    <p
                                      title="Don't Compare Yourself with Anyone."
                                      aria-label="Don't Compare Yourself with Anyone."
                                    >
                                      Don't Compare Yourself with Anyone.
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="teams-list animate__animated animate__fadeInLeft"
                    id="teamList"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <button
                          role="button"
                          class="icons"
                          onclick="closeTeamList()"
                        >
                          <i class="fe fe-arrow-right fe-24"></i>
                        </button>
                      </div>

                      <div class="newText">
                        <h2>قائمة فرق خدمة العملاء</h2>
                      </div>
                    </div>
                    <!-- Selected Participants Area -->
                    <!-- Search Bar -->
                    <div class="search-bar">
                      <div>
                        <button class="search">
                          <span class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="M15.009 13.805h-.636l-.22-.219a5.184 5.184 0 0 0 1.256-3.386 5.207 5.207 0 1 0-5.207 5.208 5.183 5.183 0 0 0 3.385-1.255l.221.22v.635l4.004 3.999 1.194-1.195-3.997-4.007zm-4.808 0a3.605 3.605 0 1 1 0-7.21 3.605 3.605 0 0 1 0 7.21z"
                              ></path>
                            </svg>
                          </span>
                        </button>

                        <span class="go-back">
                          <svg
                            viewBox="0 0 24 24"
                            width="24"
                            height="24"
                            class=""
                          >
                            <path
                              fill="currentColor"
                              d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"
                            ></path>
                          </svg>
                        </span>
                        <input
                          type="text"
                          title="البحث عن فريق "
                          aria-label="البحث عن فريق"
                          placeholder="البحث عن فريق"
                          id="team-search"
                        />
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="elements" id="team-elementList">
                      <!-- Chats 1 -->
                      <div class="block top">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4
                              title="Abhishek Anant"
                              aria-label="Abhishek Anant"
                            >
                              Abhishek Anant
                            </h4>
                          </div>
                          <div class="message">
                            <p
                              title="Jai Shree Ram."
                              aria-label="Jai Shree Ram."
                            >
                              Jai Shree Ram.
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 2 -->
                      <div class="block">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="Anamika" aria-label="Anamika">
                              Anamika
                            </h4>
                          </div>
                          <div class="message">
                            <p
                              title="Simplicity is the Best Policy."
                              aria-label="Simplicity is the Best Policy."
                            >
                              Simplicity is the Best Policy.
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 3 -->
                      <div class="block">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="Abhishek" aria-label="Abhishek">
                              Abhishek
                            </h4>
                          </div>
                          <div class="message">
                            <p
                              title="Don't Compare Yourself with anyone."
                              aria-label="Don't Compare Yourself with anyone."
                            >
                              Don't Compare Yourself with anyone.
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 4 -->
                      <div class="block">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="Amrita" aria-label="Amrita">Amrita</h4>
                          </div>
                          <div class="message">
                            <p title="Trust on GOD" aria-label="Trust on GOD">
                              Trust on GOD
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- Next Button -->
                  </div>
                  <div
                    class="group animate__animated animate__fadeInLeft"
                    id="group"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <button
                          role="button"
                          class="icons"
                          onclick="closeGroup()"
                        >
                          <i class="fe fe-arrow-right fe-24"></i>
                        </button>
                      </div>

                      <div class="newText">
                        <h2>إضافة المشاركين في جروب</h2>
                      </div>
                    </div>
                    <!-- Selected Participants Area -->
                    <div class="selected-participants-container">
                      <!-- Selected participants will be added here by JS -->
                    </div>

                    <!-- Search Bar -->
                    <div class="search-bar">
                      <div>
                        <button class="search">
                          <span class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="M15.009 13.805h-.636l-.22-.219a5.184 5.184 0 0 0 1.256-3.386 5.207 5.207 0 1 0-5.207 5.208 5.183 5.183 0 0 0 3.385-1.255l.221.22v.635l4.004 3.999 1.194-1.195-3.997-4.007zm-4.808 0a3.605 3.605 0 1 1 0-7.21 3.605 3.605 0 0 1 0 7.21z"
                              ></path>
                            </svg>
                          </span>
                        </button>

                        <span class="go-back">
                          <svg
                            viewBox="0 0 24 24"
                            width="24"
                            height="24"
                            class=""
                          >
                            <path
                              fill="currentColor"
                              d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"
                            ></path>
                          </svg>
                        </span>
                        <input
                          type="text"
                          title="البحث عن مستخدم "
                          aria-label="البحث عن مستخدم"
                          placeholder="البحث عن مستخدم"
                        />
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-group">
                      <div class="a">
                        <h3>A</h3>
                      </div>

                      <!-- Chats 1 -->
                      <div class="block top">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4
                              title="Abhishek Anant"
                              aria-label="Abhishek Anant"
                            >
                              Abhishek Anant
                            </h4>
                          </div>
                          <div class="message">
                            <p
                              title="Jai Shree Ram."
                              aria-label="Jai Shree Ram."
                            >
                              Jai Shree Ram.
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 2 -->
                      <div class="block">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="Anamika" aria-label="Anamika">
                              Anamika
                            </h4>
                          </div>
                          <div class="message">
                            <p
                              title="Simplicity is the Best Policy."
                              aria-label="Simplicity is the Best Policy."
                            >
                              Simplicity is the Best Policy.
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 3 -->
                      <div class="block">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="Abhishek" aria-label="Abhishek">
                              Abhishek
                            </h4>
                          </div>
                          <div class="message">
                            <p
                              title="Don't Compare Yourself with anyone."
                              aria-label="Don't Compare Yourself with anyone."
                            >
                              Don't Compare Yourself with anyone.
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 4 -->
                      <div class="block">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="Amrita" aria-label="Amrita">Amrita</h4>
                          </div>
                          <div class="message">
                            <p title="Trust on GOD" aria-label="Trust on GOD">
                              Trust on GOD
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- Next Button -->
                  </div>
                  <!-- ****** شاشة إنشاء تفاصيل المجموعة ****** -->
                  <div
                    class="create-group-details animate__animated animate__fadeInLeft"
                    id="createGroupDetails"
                    style="display: none"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- زر الرجوع -->
                      <div class="ICON">
                        <button
                          role="button"
                          class="icons"
                          id="backToGroupSelectionBtn"
                        >
                          <i class="fe fe-arrow-right fe-24"></i>
                        </button>
                      </div>
                      <div class="newText">
                        <h2>إضافة المشاركين في الفريق</h2>
                      </div>
                    </div>

                    <!-- نموذج معلومات المجموعة -->
                    <div class="group-info-form">
                      <div class="group-avatar-section">
                        <label
                          for="groupImageInput"
                          class="group-avatar-wrapper"
                        >
                          <div
                            class="group-avatar-preview"
                            id="groupAvatarPreview"
                          >
                            <!-- أيقونة الصورة الرمزية الافتراضية -->
                          </div>
                          <input
                            type="file"
                            id="groupImageInput"
                            accept="image/*"
                            style="display: none"
                          />
                        </label>
                        <p class="avatar-hint">اضغط لتغيير الصورة</p>
                      </div>

                      <div class="form-group">
                        <label for="groupNameInput">اسم الفريق</label>
                        <input
                          type="text"
                          id="groupNameInput"
                          placeholder="أدخل اسم الفريق هنا"
                          required
                        />
                      </div>

                      <div class="form-group">
                        <label for="groupDescInput">وصف الفريق</label>
                        <input
                          type="text"
                          id="groupDescInput"
                          placeholder="اختياري"
                        />
                      </div>

                      <div class="selected-members-section">
                        <h3>
                          الأعضاء (<span id="selectedMembersCountDetails"
                            >0</span
                          >)
                        </h3>
                        <div
                          id="selectedMembersListDetails"
                          class="selected-members-list-details"
                        >
                          <!-- قائمة الأعضاء المحددين ستعرض هنا -->
                        </div>
                      </div>
                    </div>

                    <!-- زر الإنشاء -->
                    <div class="create-group-footer">
                      <button
                        id="createGroupConfirmBtn"
                        class="create-group-confirm-btn"
                        disabled
                      >
                        <span class="btn-text">إنشاء</span>
                        <span class="loader" style="display: none"></span>
                        <!-- عنصر التحميل -->
                      </button>
                    </div>
                  </div>
                  <!-- ****** نهاية شاشة إنشاء تفاصيل المجموعة ****** -->

                  <div
                    class="group animate__animated animate__fadeInLeft"
                    id="team"
                    style="display: none"
                  >
                    <!-- Same structure as the group screen but with team-specific IDs -->
                    <div class="header-Chat">
                      <div class="ICON">
                        <button
                          role="button"
                          class="icons"
                          onclick="closeTeam()"
                        >
                          <i class="fe fe-arrow-right fe-24"></i>
                        </button>
                      </div>
                      <div class="newText">
                        <h2>إضافة المشاركين في الفريق</h2>
                      </div>
                    </div>
                    <div class="selected-participants-container">
                      <!-- Selected participants will be added here by JS -->
                    </div>

                    <!-- Search Bar -->
                    <div class="search-bar">
                      <div>
                        <button class="search">
                          <span class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="M15.009 13.805h-.636l-.22-.219a5.184 5.184 0 0 0 1.256-3.386 5.207 5.207 0 1 0-5.207 5.208 5.183 5.183 0 0 0 3.385-1.255l.221.22v.635l4.004 3.999 1.194-1.195-3.997-4.007zm-4.808 0a3.605 3.605 0 1 1 0-7.21 3.605 3.605 0 0 1 0 7.21z"
                              ></path>
                            </svg>
                          </span>
                        </button>

                        <span class="go-back">
                          <svg
                            viewBox="0 0 24 24"
                            width="24"
                            height="24"
                            class=""
                          >
                            <path
                              fill="currentColor"
                              d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"
                            ></path>
                          </svg>
                        </span>
                        <input
                          type="text"
                          title="البحث عن مستخدم "
                          aria-label="البحث عن مستخدم"
                          placeholder="البحث عن مستخدم"
                        />
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-group">
                      <div class="a">
                        <h3>A</h3>
                      </div>

                      <!-- Chats 1 -->
                      <div class="block top">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4
                              title="Abhishek Anant"
                              aria-label="Abhishek Anant"
                            >
                              Abhishek Anant
                            </h4>
                          </div>
                          <div class="message">
                            <p
                              title="Jai Shree Ram."
                              aria-label="Jai Shree Ram."
                            >
                              Jai Shree Ram.
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 2 -->
                      <div class="block">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="Anamika" aria-label="Anamika">
                              Anamika
                            </h4>
                          </div>
                          <div class="message">
                            <p
                              title="Simplicity is the Best Policy."
                              aria-label="Simplicity is the Best Policy."
                            >
                              Simplicity is the Best Policy.
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 3 -->
                      <div class="block">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="Abhishek" aria-label="Abhishek">
                              Abhishek
                            </h4>
                          </div>
                          <div class="message">
                            <p
                              title="Don't Compare Yourself with anyone."
                              aria-label="Don't Compare Yourself with anyone."
                            >
                              Don't Compare Yourself with anyone.
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 4 -->
                      <div class="block">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="Amrita" aria-label="Amrita">Amrita</h4>
                          </div>
                          <div class="message">
                            <p title="Trust on GOD" aria-label="Trust on GOD">
                              Trust on GOD
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="create-team-details animate__animated animate__fadeInLeft"
                    id="createTeamDetails"
                    style="display: none"
                  >
                    <!-- Similar structure to createGroupDetails but with team-specific IDs -->
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- زر الرجوع -->
                      <div class="ICON">
                        <button
                          role="button"
                          class="icons"
                          id="backToTeamSelectionBtn"
                        >
                          <i class="fe fe-arrow-right fe-24"></i>
                        </button>
                      </div>
                      <div class="newText">
                        <h2>إضافة المشاركين في الفريق</h2>
                      </div>
                    </div>

                    <!-- نموذج معلومات المجموعة -->
                    <div class="group-info-form">
                      <div class="group-avatar-section">
                        <label
                          for="teamImageInput"
                          class="group-avatar-wrapper"
                        >
                          <div
                            class="group-avatar-preview"
                            id="groupAvatarPreview"
                          >
                            <!-- أيقونة الصورة الرمزية الافتراضية -->
                          </div>
                          <input
                            type="file"
                            id="teamImageInput"
                            accept="image/*"
                            style="display: none"
                          />
                        </label>
                        <p class="avatar-hint">اضغط لتغيير الصورة</p>
                      </div>

                      <div class="form-group">
                        <label for="teamNameInput">اسم الفريق</label>
                        <input
                          type="text"
                          id="teamNameInput"
                          placeholder="أدخل اسم الفريق هنا"
                          required
                        />
                      </div>

                      <div class="form-group">
                        <label for="teamDescInput">وصف الفريق</label>
                        <input
                          type="text"
                          id="teamDescInput"
                          placeholder="اختياري"
                        />
                      </div>

                      <div class="selected-members-section">
                        <h3>
                          الأعضاء (<span id="selectedTeamMembersCountDetails"
                            >0</span
                          >)
                        </h3>
                        <div
                          id="selectedTeamMembersListDetails"
                          class="selected-members-list-details"
                        >
                          <!-- قائمة الأعضاء المحددين ستعرض هنا -->
                        </div>
                      </div>
                    </div>

                    <!-- زر الإنشاء -->
                    <div class="create-group-footer">
                      <button
                        id="createTeamConfirmBtn"
                        class="create-group-confirm-btn"
                        disabled
                      >
                        <span class="btn-text">إنشاء</span>
                        <span class="loader" style="display: none"></span>
                        <!-- عنصر التحميل -->
                      </button>
                    </div>
                  </div>
                  <div
                    class="settings animate__animated animate__fadeInLeft"
                    id="settings"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <button
                          role="button"
                          class="icons"
                          onclick="closeSettings()"
                        >
                          <i class="fe fe-arrow-right fe-24"></i>
                        </button>
                      </div>

                      <div class="newText">
                        <h2>إعدادات</h2>
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-settings">
                      <!-- Profile -->
                      <div
                        class="top"
                        onclick="openProfile()"
                        id="aboutUserProfile"
                      >
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            alt=""
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text pl-2">
                          <div class="head">
                            <h4>اسم المستخدم</h4>
                          </div>
                          <div class="message">
                            <p>
                              أنا مطور واجهة أمامية / مصمم واجهة مستخدم وتجربة
                              مستخدم
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 1 -->
                      <div class="block" onclick="openNotifications()">
                        <!-- Img -->
                        <div class="icon-Box">
                          <button role="button" class="icons-setings">
                            <i class="fe fe-bell fe-24"></i>
                          </button>
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="إشعارات" aria-label="إشعارات">
                              إشعارات
                            </h4>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 3 -->
                      <div class="block" onclick="openSecurity()">
                        <!-- Img -->
                        <div class="icon-Box">
                          <button role="button" class="icons-setings">
                            <i class="fe fe-shield fe-24"></i>
                          </button>
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="الأمان" aria-label="الأمان">الأمان</h4>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="profile animate__animated animate__fadeInLeft"
                    id="profile"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <div
                          aria-disabled="false"
                          role="button"
                          class="icons"
                          data-tab="2"
                          title="محادثة جديدة"
                          aria-label="محادثة جديدة"
                          onclick="closeProfile()"
                        >
                          <i class="fe fe-arrow-right fe-24"></i>
                        </div>
                      </div>

                      <div class="newText">
                        <h2>الملف الشخصي</h2>
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-profile">
                      <!-- Profile -->
                      <div class="top">
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            alt=""
                            class="cover animate__animated animate__fadeIn"
                          />
                          <div class="middle">
                            <div
                              aria-disabled="false"
                              role="button"
                              class="icons-profile"
                              data-tab="2"
                              title="Camera"
                              aria-label="Camera"
                            >
                              <i class="fe fe-edit-2 fe-24"></i>
                            </div>
                            <div class="text">تغيير صورة الملف الشخصي</div>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 1 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="titlePro">
                            <p>اسمك</p>
                          </div>

                          <div class="head">
                            <h4>اسم مزيف</h4>
                            <div
                              aria-disabled="false"
                              role="button"
                              class="icons-prof"
                              data-tab="2"
                              title="Edit"
                              aria-label="Edit"
                            >
                              <i class="fe fe-edit-2 fe-24"></i>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="warning">
                        <div class="warn-text">
                          <h4>
                            هذا ليس اسم المستخدم أو رقم التعريف الشخصي الخاص بك.
                            سيكون هذا الاسم مرئيًا لجهات اتصال تطبيق الدردشة
                            لديك.
                          </h4>
                        </div>
                      </div>

                      <!-- Chats 2 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="titlePro">
                            <p>عن</p>
                          </div>

                          <div class="head about1">
                            <h4>
                              أنا مطور واجهة أمامية / مصمم واجهة مستخدم وتجربة
                              مستخدم
                            </h4>
                            <div
                              aria-disabled="false"
                              role="button"
                              class="icons-prof"
                              data-tab="2"
                              title="Edit"
                              aria-label="Edit"
                            >
                              <i class="fe fe-edit-2 fe-24"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="notifications animate__animated animate__fadeInLeft"
                    id="notifications"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <div
                          aria-disabled="false"
                          role="button"
                          class="icons"
                          data-tab="2"
                          title="محادثة جديدة"
                          aria-label="محادثة جديدة"
                          onclick="closeNotifications()"
                        >
                          <i class="fe fe-arrow-right fe-24"></i>
                        </div>
                      </div>

                      <div class="newText">
                        <h2>إشعارات</h2>
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-notifications">
                      <!-- Chats 1 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="titlePro">
                            <p>الرسائل</p>
                          </div>

                          <div class="head">
                            <label
                              class="form-check-label"
                              for="flexCheckDefault"
                            >
                              <h4>إشعارات الرسائل</h4>
                              <p>إظهار الإشعارات للرسائل الجديدة</p>
                            </label>
                            <input
                              class="form-check-input"
                              type="checkbox"
                              value=""
                              id="flexCheckDefault"
                              checked
                            />
                          </div>

                          <div class="S-head">
                            <label class="form-check-label" for="flexCheck">
                              <h4>معاينات العرض</h4>
                            </label>
                            <input
                              class="form-check-input"
                              type="checkbox"
                              value=""
                              id="flexCheck"
                              checked
                            />
                          </div>
                        </div>
                      </div>
                      <!-- Chats 2 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="T-head">
                            <label class="form-check-label" for="Check">
                              <h4>الأصوات</h4>
                              <p>تشغيل الأصوات للرسائل الواردة</p>
                            </label>
                            <input
                              class="form-check-input"
                              type="checkbox"
                              value=""
                              id="Check"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="privacy animate__animated animate__fadeInLeft"
                    id="privacy"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <div
                          aria-disabled="false"
                          role="button"
                          class="icons"
                          data-tab="2"
                          title="محادثة جديدة"
                          aria-label="محادثة جديدة"
                          onclick="closePrivacy()"
                        >
                          <span data-testid="chat" data-icon="chat" class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"
                              ></path>
                            </svg>
                          </span>
                        </div>
                      </div>

                      <div class="newText">
                        <h2>Privacy</h2>
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-privacy">
                      <!-- Chats 1 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="titlePro">
                            <p>Who can see my personal info</p>
                          </div>

                          <div class="head" onclick="openLast()">
                            <div class="text-inner">
                              <h4>Last seen</h4>
                              <p>Everyone</p>
                            </div>
                            <span class="material-symbols-outlined">
                              chevron_right
                            </span>
                          </div>

                          <div class="head" onclick="openPhoto()">
                            <div class="text-inner">
                              <h4>Profile photo</h4>
                              <p>Everyone</p>
                            </div>
                            <span class="material-symbols-outlined">
                              chevron_right
                            </span>
                          </div>

                          <div class="head" onclick="openAbout()">
                            <div class="text-inner">
                              <h4>About</h4>
                              <p>Everyone</p>
                            </div>
                            <span class="material-symbols-outlined">
                              chevron_right
                            </span>
                          </div>

                          <div class="F-head">
                            <div class="text-inner">
                              <label class="form-check-label" for="CheckRead">
                                <h4>Read Receipts</h4>
                                <p>
                                  If turned off, you won't send or receive Read
                                  receipts. Read receipts are always sent for
                                  group chats.
                                </p>
                              </label>
                            </div>
                            <input
                              class="form-check-input"
                              type="checkbox"
                              value=""
                              id="CheckRead"
                              checked
                            />
                          </div>
                        </div>
                      </div>

                      <!-- Chats 2 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="titlePro">
                            <p>Disappearing messages</p>
                          </div>

                          <div class="T-head" onclick="openDmessage()">
                            <div class="text-inner">
                              <h4>Default message timer</h4>
                              <p>Off</p>
                            </div>
                            <span class="material-symbols-outlined">
                              chevron_right
                            </span>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 2 -->
                      <div class="block">
                        <div class="h-text">
                          <div class="G-head" onclick="openGroups()">
                            <div class="text-inner">
                              <h4>Groups</h4>
                              <p>My contacts</p>
                            </div>
                            <span class="material-symbols-outlined">
                              chevron_right
                            </span>
                          </div>

                          <div class="R-head" onclick="openBlock()">
                            <div class="text-inner">
                              <h4>Blocked contacts</h4>
                              <p>1</p>
                            </div>
                            <span class="material-symbols-outlined">
                              chevron_right
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="last-seen animate__animated animate__fadeInLeft"
                    id="last-seen"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <div
                          aria-disabled="false"
                          role="button"
                          class="icons"
                          data-tab="2"
                          title="محادثة جديدة"
                          aria-label="محادثة جديدة"
                          onclick="closeLast()"
                        >
                          <span data-testid="chat" data-icon="chat" class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"
                              ></path>
                            </svg>
                          </span>
                        </div>
                      </div>

                      <div class="newText">
                        <h2>Last seen</h2>
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-last">
                      <!-- Chats 1 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="titlePro">
                            <p>
                              If you don't share your Last Seen, you won't be
                              able to see other people's Last Seen
                            </p>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="exampleRadios"
                                value=""
                                id="first"
                                checked
                              />
                              <label class="form-check-label" for="first">
                                <h4>Everyone</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="exampleRadios"
                                value=""
                                id="second"
                              />
                              <label class="form-check-label" for="second">
                                <h4>My contacts</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="exampleRadios"
                                value=""
                                id="third"
                              />
                              <label class="form-check-label" for="third">
                                <h4>My contacts except...</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="exampleRadios"
                                value=""
                                id="fourt"
                              />
                              <label class="form-check-label" for="fourt">
                                <h4>Nobody</h4>
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="p-photo animate__animated animate__fadeInLeft"
                    id="p-photo"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <div
                          aria-disabled="false"
                          role="button"
                          class="icons"
                          data-tab="2"
                          title="محادثة جديدة"
                          aria-label="محادثة جديدة"
                          onclick="closePhoto()"
                        >
                          <span data-testid="chat" data-icon="chat" class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"
                              ></path>
                            </svg>
                          </span>
                        </div>
                      </div>

                      <div class="newText">
                        <h2>Profile photo</h2>
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-photo">
                      <!-- Chats 1 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="titlePro">
                            <p>Who can see my Profile Photo</p>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="Radios"
                                value=""
                                id="P-first"
                                checked
                              />
                              <label class="form-check-label" for="P-first">
                                <h4>Everyone</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="Radios"
                                value=""
                                id="P-second"
                              />
                              <label class="form-check-label" for="P-second">
                                <h4>My contacts</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="Radios"
                                value=""
                                id="P-third"
                              />
                              <label class="form-check-label" for="P-third">
                                <h4>My contacts except...</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="Radios"
                                value=""
                                id="P-fourt"
                              />
                              <label class="form-check-label" for="P-fourt">
                                <h4>Nobody</h4>
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="about animate__animated animate__fadeInLeft"
                    id="about"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <div
                          aria-disabled="false"
                          role="button"
                          class="icons"
                          data-tab="2"
                          title="محادثة جديدة"
                          aria-label="محادثة جديدة"
                          onclick="closeAbout()"
                        >
                          <span data-testid="chat" data-icon="chat" class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"
                              ></path>
                            </svg>
                          </span>
                        </div>
                      </div>

                      <div class="newText">
                        <h2>About</h2>
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-about">
                      <!-- Chats 1 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="titlePro">
                            <p>Who can see my About</p>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="aboutRadios"
                                value=""
                                id="A-first"
                                checked
                              />
                              <label class="form-check-label" for="A-first">
                                <h4>Everyone</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="aboutRadios"
                                value=""
                                id="A-second"
                              />
                              <label class="form-check-label" for="A-second">
                                <h4>My contacts</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="aboutRadios"
                                value=""
                                id="A-third"
                              />
                              <label class="form-check-label" for="A-third">
                                <h4>My contacts except...</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="aboutRadios"
                                value=""
                                id="A-fourt"
                              />
                              <label class="form-check-label" for="A-fourt">
                                <h4>Nobody</h4>
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="D-message animate__animated animate__fadeInLeft"
                    id="D-message"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <div
                          aria-disabled="false"
                          role="button"
                          class="icons"
                          data-tab="2"
                          title="محادثة جديدة"
                          aria-label="محادثة جديدة"
                          onclick="closeDmessage()"
                        >
                          <span data-testid="chat" data-icon="chat" class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"
                              ></path>
                            </svg>
                          </span>
                        </div>
                      </div>

                      <div class="newText">
                        <h2>Default message timer</h2>
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-default">
                      <!-- Images -->
                      <div class="img-top">
                        <img
                          src="images/dissapering.png"
                          alt=""
                          class="disappear"
                        />
                      </div>

                      <!-- Chats 1 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="titlePro">
                            <p>
                              Start محادثة جديدةs with disappearing messages
                            </p>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="d-Radios"
                                value=""
                                id="D-first"
                              />
                              <label class="form-check-label" for="D-first">
                                <h4>24 hours</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="d-Radios"
                                value=""
                                id="D-second"
                              />
                              <label class="form-check-label" for="D-second">
                                <h4>7 days</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="d-Radios"
                                value=""
                                id="D-third"
                              />
                              <label class="form-check-label" for="D-third">
                                <h4>90 days</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="d-Radios"
                                value=""
                                id="D-fourt"
                                checked
                              />
                              <label class="form-check-label" for="D-fourt">
                                <h4>Off</h4>
                              </label>
                            </div>
                          </div>

                          <div class="Learn-more">
                            <a>Learn more</a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="groups animate__animated animate__fadeInLeft"
                    id="groups"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <div
                          aria-disabled="false"
                          role="button"
                          class="icons"
                          data-tab="2"
                          title="محادثة جديدة"
                          aria-label="محادثة جديدة"
                          onclick="closeGroups()"
                        >
                          <span data-testid="chat" data-icon="chat" class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"
                              ></path>
                            </svg>
                          </span>
                        </div>
                      </div>

                      <div class="newText">
                        <h2>Groups</h2>
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-groups">
                      <!-- Chats 1 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="titlePro">
                            <p>Who can add me to groups</p>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="groupsRadios"
                                value=""
                                id="G-first"
                              />
                              <label class="form-check-label" for="G-first">
                                <h4>Everyone</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="groupsRadios"
                                value=""
                                id="G-second"
                                checked
                              />
                              <label class="form-check-label" for="G-second">
                                <h4>My contacts</h4>
                              </label>
                            </div>
                          </div>

                          <div class="head">
                            <div class="form-check">
                              <input
                                class="form-check-input"
                                type="radio"
                                name="groupsRadios"
                                value=""
                                id="G-third"
                              />
                              <label class="form-check-label" for="G-third">
                                <h4>My contacts except...</h4>
                              </label>
                            </div>
                          </div>

                          <div class="admins">
                            <p>
                              Admins who can't add you to a group will have the
                              option of inviting you privately instead.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="blocks animate__animated animate__fadeInLeft"
                    id="blocks"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <div
                          aria-disabled="false"
                          role="button"
                          class="icons"
                          data-tab="2"
                          title="محادثة جديدة"
                          aria-label="محادثة جديدة"
                          onclick="closeBlock()"
                        >
                          <span data-testid="chat" data-icon="chat" class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"
                              ></path>
                            </svg>
                          </span>
                        </div>
                      </div>

                      <div class="newText">
                        <h2>Blocked contacts</h2>
                      </div>
                    </div>
                    <div class="chats-block">
                      <!-- Chats 1 -->
                      <div class="block">
                        <!-- Img -->
                        <div class="iconBox">
                          <div
                            aria-disabled="false"
                            role="button"
                            class="icons"
                            data-tab="2"
                            title="Add blocked contact"
                          >
                            <span
                              data-testid="add-user"
                              data-icon="add-user"
                              class=""
                            >
                              <svg
                                viewBox="0 0 24 24"
                                width="24"
                                height="24"
                                class=""
                              >
                                <path
                                  fill="currentColor"
                                  d="M14.7 12c2 0 3.6-1.6 3.6-3.6s-1.6-3.6-3.6-3.6-3.6 1.6-3.6 3.6 1.6 3.6 3.6 3.6zm-8.1-1.8V7.5H4.8v2.7H2.1V12h2.7v2.7h1.8V12h2.7v-1.8H6.6zm8.1 3.6c-2.4 0-7.2 1.2-7.2 3.6v1.8H22v-1.8c-.1-2.4-4.9-3.6-7.3-3.6z"
                                ></path>
                              </svg>
                            </span>
                          </div>
                        </div>

                        <div class="Add-text">
                          <div class="head">
                            <h4>Add blocked contact</h4>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 1 -->
                      <div class="block top">
                        <!-- Img -->
                        <div class="imgBox">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="cover"
                          />
                        </div>
                        <!-- Text -->
                        <div class="h-text">
                          <div class="head">
                            <h4 title="Virat Kohli" aria-label="Virat Kohli">
                              Virat Kohli
                            </h4>
                          </div>
                          <div class="message">
                            <p
                              title="Fun to chat with."
                              aria-label="Fun to chat with."
                            >
                              Fun to chat with.
                            </p>
                          </div>
                        </div>

                        <div class="iconBox">
                          <div
                            data-testid="chat-controls"
                            class="icons"
                            role="button"
                          >
                            <span
                              data-testid="x-alt"
                              data-icon="x-alt"
                              class=""
                            >
                              <svg
                                viewBox="0 0 24 24"
                                width="24"
                                height="24"
                                class=""
                              >
                                <path
                                  fill="currentColor"
                                  d="M17.25 7.8 16.2 6.75l-4.2 4.2-4.2-4.2L6.75 7.8l4.2 4.2-4.2 4.2 1.05 1.05 4.2-4.2 4.2 4.2 1.05-1.05-4.2-4.2 4.2-4.2z"
                                ></path>
                              </svg>
                            </span>
                          </div>
                        </div>
                      </div>

                      <div class="b-con">
                        <p>
                          Blocked contacts will no longer be able to call you or
                          send you messages
                        </p>
                      </div>
                    </div>
                  </div>

                  <div
                    class="security animate__animated animate__fadeInLeft"
                    id="security"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <div
                          aria-disabled="false"
                          role="button"
                          class="icons"
                          data-tab="2"
                          title="محادثة جديدة"
                          aria-label="محادثة جديدة"
                          onclick="closeSecurity()"
                        >
                          <i class="fe fe-arrow-right fe-24"></i>
                        </div>
                      </div>

                      <div class="newText">
                        <h2>الأمان</h2>
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-security">
                      <div class="top-pad">
                        <div class="icons-pad">
                          <div aria-disabled="false" class="icons" data-tab="2">
                            <span
                              data-testid="security-drawer-lock"
                              data-icon="security-drawer-lock"
                              class=""
                            >
                              <svg
                                width="84"
                                viewBox="0 0 84 84"
                                fill="none"
                                preserveAspectRatio="xMidYMid meet"
                                class=""
                              >
                                <circle
                                  cx="42"
                                  cy="42"
                                  r="42"
                                  fill="#07CD9E"
                                ></circle>
                                <path
                                  d="M53.241 33.432c0-2.079-.508-3.988-1.523-5.728a11.102 11.102 0 0 0-4.092-4.157C45.914 22.516 44.042 22 42.013 22c-2.046 0-3.934.516-5.662 1.547-1.713 1.015-3.077 2.4-4.092 4.157-1 1.74-1.5 3.65-1.5 5.728v4.375a5.657 5.657 0 0 0-2.925 2.658 6.438 6.438 0 0 0-.643 1.861c-.127.677-.19 1.684-.19 3.021v8.532c0 1.338.063 2.345.19 3.021a6.44 6.44 0 0 0 .643 1.861 5.668 5.668 0 0 0 2.355 2.393 5.91 5.91 0 0 0 1.808.653c.682.129 1.673.193 2.974.193h14.06c1.3 0 2.284-.064 2.95-.193a5.904 5.904 0 0 0 1.832-.653 5.668 5.668 0 0 0 2.355-2.393 6.44 6.44 0 0 0 .643-1.86c.127-.677.19-1.684.19-3.022v-8.532c0-1.337-.063-2.344-.19-3.02a6.438 6.438 0 0 0-.643-1.862 5.667 5.667 0 0 0-2.926-2.658v-4.375Zm-17.986 0c0-1.24.302-2.385.904-3.432a6.725 6.725 0 0 1 2.45-2.514 6.621 6.621 0 0 1 3.403-.918c1.221 0 2.347.306 3.378.918A6.725 6.725 0 0 1 47.84 30a6.76 6.76 0 0 1 .905 3.432v3.795h-13.49v-3.795Z"
                                  fill="#fff"
                                ></path>
                              </svg>
                            </span>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 1 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="text-yur text-center">
                            <h3>محادثاتك خاصة.</h3>
                            <p>
                              يحافظ التشفير الشامل على سرية رسائلك ومكالماتك
                              الشخصية بينك وبين الأشخاص الذين تختارهم. حتى تطبيق
                              الدردشة لا يستطيع قراءتها أو الاستماع إليها. وهذا
                              يشمل:
                            </p>
                            <ul>
                              <li>
                                <i class="fe fe-message-square fe-24"></i>
                                <span class="ml-3 item-text"
                                  >الرسائل النصية والصوتية</span
                                >
                              </li>
                              <li>
                                <i class="fe fe-file-text fe-24"></i>
                                <span class="ml-3 item-text"
                                  >الصور والفيديوهات والوثائق</span
                                >
                              </li>
                              <li>
                                <i class="fe fe-map-pin fe-24"></i>
                                <span class="ml-3 item-text"
                                  >مشاركة الموقع</span
                                >
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="modal fade"
                    id="exampleModal"
                    data-backdrop="static"
                    aria-labelledby="exampleModalLabel"
                    aria-hidden="true"
                  >
                    <div class="modal-dialog modal-dialog-centered">
                      <div class="modal-content">
                        <h5 class="m-title">Choose theme</h5>
                        <ul>
                          <li>
                            <div class="m-list">
                              <label class="form-check-label" for="light">
                                <input
                                  class="form-check-input"
                                  type="radio"
                                  name="groupsRadios"
                                  value=""
                                  id="light"
                                />
                                <h4>Light</h4>
                              </label>
                            </div>
                          </li>
                          <li>
                            <div class="m-list">
                              <label class="form-check-label" for="dark">
                                <input
                                  class="form-check-input"
                                  type="radio"
                                  name="groupsRadios"
                                  value=""
                                  id="dark"
                                  checked
                                />
                                <h4>Dark</h4>
                              </label>
                            </div>
                          </li>
                          <li>
                            <div class="m-list">
                              <label
                                class="form-check-label"
                                for="system-default"
                              >
                                <input
                                  class="form-check-input"
                                  type="radio"
                                  name="groupsRadios"
                                  value=""
                                  id="system-default"
                                />
                                <h4>System default</h4>
                              </label>
                            </div>
                          </li>
                        </ul>

                        <div class="m-btn">
                          <button
                            type="button"
                            class="btn1"
                            data-dismiss="modal"
                          >
                            Cancel
                          </button>
                          <button
                            type="button"
                            class="btn2"
                            data-dismiss="modal"
                          >
                            Ok
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="request animate__animated animate__fadeInLeft"
                    id="request"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <div
                          aria-disabled="false"
                          role="button"
                          tabindex="0"
                          class="icons"
                          data-tab="2"
                          title="محادثة جديدة"
                          aria-label="محادثة جديدة"
                          onclick="closeRequest()"
                        >
                          <i class="fe fe-arrow-right fe-24"></i>
                        </div>
                      </div>

                      <div class="newText">
                        <h2>Request Account Info</h2>
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-request">
                      <div class="top-pad">
                        <div class="icons-pad">
                          <div
                            aria-disabled="false"
                            tabindex="0"
                            class="icons"
                            data-tab="2"
                          >
                            <span
                              data-testid="document"
                              data-icon="document"
                              class=""
                            >
                              <svg
                                width="112"
                                height="112"
                                viewBox="0 0 112 112"
                                fill="none"
                                class=""
                              >
                                <path
                                  fill-rule="evenodd"
                                  clip-rule="evenodd"
                                  d="M112 56c0 30.928-25.072 56-56 56S0 86.928 0 56 25.072 0 56 0s56 25.072 56 56Zm-70.06-7.334h28a2 2 0 0 1 0 4h-28a2 2 0 1 1 0-4Zm0 10h28a2 2 0 0 1 0 4h-28a2 2 0 1 1 0-4Zm12 10h-12a2 2 0 1 0 0 4h12a2 2 0 1 0 0-4Z"
                                  fill="#06CF9C"
                                  fill-opacity=".15"
                                ></path>
                                <path
                                  fill-rule="evenodd"
                                  clip-rule="evenodd"
                                  d="M68 19H34a6 6 0 0 0-6 6v62a6 6 0 0 0 6 6h44a6 6 0 0 0 6-6V35L68 19ZM42 49h28a2 2 0 1 1 0 4H42a2 2 0 1 1 0-4Zm0 10h28a2 2 0 1 1 0 4H42a2 2 0 1 1 0-4Zm12 10H42a2 2 0 1 0 0 4h12a2 2 0 1 0 0-4Z"
                                  fill="#06CF9C"
                                ></path>
                                <path
                                  d="M84 35 68 19v10a6 6 0 0 0 6 6h10Z"
                                  fill="#00A884"
                                ></path>
                              </svg>
                            </span>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 1 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="text-yur">
                            <p>
                              Create a report of your ChatApp account
                              information and settings, which you can access or
                              port to another app. This report does not include
                              your messages.
                            </p>
                            <a href="">Learn more</a>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 2 -->
                      <div class="block top">
                        <!-- Icon -->
                        <div
                          aria-disabled="false"
                          tabindex="0"
                          class="icons-R"
                          data-tab="2"
                        >
                          <span
                            data-testid="business-hours"
                            data-icon="business-hours"
                            class=""
                          >
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="M12.514 6.858h-1.543v6.172l5.349 3.29.821-1.336-4.628-2.777V6.858zM12 1.714C6.342 1.714 1.714 6.344 1.714 12c0 5.658 4.628 10.287 10.286 10.287S22.286 17.658 22.286 12c0-5.656-4.628-10.286-10.286-10.286zm0 18.516c-4.526 0-8.23-3.703-8.23-8.23 0-4.525 3.703-8.228 8.23-8.228S20.23 7.475 20.23 12c0 4.526-3.704 8.23-8.23 8.23z"
                              ></path>
                            </svg>
                          </span>
                        </div>
                        <!-- Text -->
                        <div class="R-text">
                          <div class="head">
                            <h4 title="Request sent" aria-label="Request sent">
                              Request sent
                            </h4>
                          </div>
                          <div class="message">
                            <p
                              title="Ready by September 6, 20222"
                              aria-label="Ready by September 6, 20222"
                            >
                              Ready by September 6, 20222
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="block">
                        <div class="F-head">
                          <!-- Text -->
                          <div class="text-inner">
                            <p class="F-p">
                              Your report will be ready in about 3 days. You'll
                              have a few weeks to download your report after
                              it's available.
                            </p>

                            <p class="S-p">
                              Your request will be canceled if you make changes
                              to your account such as changing your number or
                              deleting your account.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="help animate__animated animate__fadeInLeft"
                    id="help"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <div
                          aria-disabled="false"
                          role="button"
                          tabindex="0"
                          class="icons"
                          data-tab="2"
                          title="محادثة جديدة"
                          aria-label="محادثة جديدة"
                          onclick="closeHelp()"
                        >
                          <i class="fe fe-arrow-right fe-24"></i>
                        </div>
                      </div>

                      <div class="newText">
                        <h2>Help</h2>
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-help">
                      <div class="img-Ani">
                        <div class="img-animated"></div>
                      </div>

                      <div class="text-Ani">
                        <p>Version 2.2232.8</p>
                      </div>

                      <!-- Chats 2 -->
                      <div class="block top">
                        <!-- Icon -->
                        <div
                          aria-disabled="false"
                          tabindex="0"
                          class="icons-R"
                          data-tab="2"
                        >
                          <span
                            data-testid="settings-help"
                            data-icon="settings-help"
                            class=""
                          >
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="M12 4.7c-4.5 0-8.2 3.7-8.2 8.2s3.7 8.2 8.2 8.2 8.2-3.7 8.2-8.2-3.7-8.2-8.2-8.2zm.8 13.9h-1.6V17h1.6v1.6zm1.7-6.3-.7.7c-.7.6-1 1.1-1 2.3h-1.6v-.4c0-.9.3-1.7 1-2.3l1-1.1c.3-.2.5-.7.5-1.1 0-.9-.7-1.6-1.6-1.6s-1.6.7-1.6 1.6H8.7c0-1.8 1.5-3.3 3.3-3.3s3.3 1.5 3.3 3.3c0 .8-.4 1.4-.8 1.9z"
                              ></path>
                            </svg>
                          </span>
                        </div>
                        <!-- Text -->
                        <div class="R-text">
                          <div class="head">
                            <h4 title="Help Center" aria-label="Help Center">
                              Help Center
                            </h4>
                          </div>
                        </div>
                      </div>

                      <div class="blocked">
                        <!-- Icon -->
                        <div
                          aria-disabled="false"
                          tabindex="0"
                          class="icons-R"
                          data-tab="2"
                        >
                          <span data-testid="group" data-icon="group" class="">
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              class=""
                            >
                              <path
                                d="M16.67 9.76a2.394 2.394 0 0 1 .029-1.226 2.222 2.222 0 0 1 .332-.664 2.114 2.114 0 0 1 1.106-.774A2.32 2.32 0 0 1 18.803 7c1.247 0 2.2.953 2.2 2.2 0 1.247-.953 2.2-2.2 2.2a2.352 2.352 0 0 1-.666-.096 2.219 2.219 0 0 1-.664-.332 2.115 2.115 0 0 1-.774-1.106 2.42 2.42 0 0 1-.028-.106Zm-9.295-.218a2.27 2.27 0 0 1-.143.528 2.163 2.163 0 0 1-.26.46 2.117 2.117 0 0 1-1.106.774 2.31 2.31 0 0 1-.666.096c-1.247 0-2.2-.953-2.2-2.2C3 7.953 3.953 7 5.2 7a2.352 2.352 0 0 1 .666.096 2.182 2.182 0 0 1 .664.332 2.115 2.115 0 0 1 .774 1.106 2.227 2.227 0 0 1 .071.324c.033.227.033.457 0 .684Zm15.418 4.764a3.165 3.165 0 0 1 .207.37V16h-3.95l-.016-.107c-.038-.152-.098-.408-.18-.543a5.534 5.534 0 0 0-.363-.543 7.06 7.06 0 0 0-.587-.686 7.212 7.212 0 0 0-1.627-1.252l-.053-.029c.031-.015.061-.032.094-.047l.06-.03c.599-.268 1.352-.456 2.287-.456.936 0 1.688.188 2.298.462a4.606 4.606 0 0 1 .989.612 4.312 4.312 0 0 1 .715.746c.048.063.09.123.126.18Zm-7.621-6.562a3.543 3.543 0 0 1-.063 1.334 3.397 3.397 0 0 1-.15.457 3.19 3.19 0 0 1-.383.68 3.124 3.124 0 0 1-1.189.973 3.268 3.268 0 0 1-.76.248c-.221.043-.445.064-.67.064-1.84 0-3.25-1.408-3.25-3.25S10.118 5 11.958 5a3.476 3.476 0 0 1 .985.142 3.25 3.25 0 0 1 .98.49 3.124 3.124 0 0 1 1.037 1.333 3.204 3.204 0 0 1 .213.78Zm2.653 8.075c.065.107.123.217.175.33V18H6v-1.85s.023-.054.072-.147a3.81 3.81 0 0 1 .103-.184 4.904 4.904 0 0 1 .309-.46 5.826 5.826 0 0 1 .509-.595 6.287 6.287 0 0 1 1.871-1.323c.03-.013.057-.028.087-.041.86-.388 1.691-.657 3.036-.657s2.202.27 3.078.664a6.37 6.37 0 0 1 1.943 1.356 6.05 6.05 0 0 1 .69.854c.049.074.09.141.127.202Zm-12.873.075L4.945 16H1v-1.324s.04-.088.125-.23c.025-.042.057-.089.091-.14a3.665 3.665 0 0 1 .286-.38 4.214 4.214 0 0 1 .556-.545 4.41 4.41 0 0 1 1-.617c.6-.27 1.352-.457 2.287-.457.936 0 1.689.188 2.287.457l.06.029c.027.012.051.026.077.039-.06.033-.12.066-.178.1a7.14 7.14 0 0 0-1.841 1.543 6.238 6.238 0 0 0-.471.634c-.058.088-.1.167-.152.256-.048.08-.07.148-.101.206-.072.136-.074.323-.074.323Z"
                                fill="currentColor"
                              ></path>
                            </svg>
                          </span>
                        </div>
                        <!-- Text -->
                        <div class="R-text">
                          <div class="head">
                            <h4 title="Contact us" aria-label="Contact us">
                              Contact us
                            </h4>
                          </div>
                        </div>
                      </div>

                      <div class="block top">
                        <!-- Icon -->
                        <div
                          aria-disabled="false"
                          tabindex="0"
                          class="icons-R"
                          data-tab="2"
                        >
                          <span
                            data-testid="settings-document"
                            data-icon="settings-document"
                            class=""
                          >
                            <svg
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              class=""
                            >
                              <path
                                fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8.83c0-.53-.21-1.04-.59-1.41l-4.83-4.83c-.37-.38-.88-.59-1.41-.59H6Zm7 6V3.5L18.5 9H14c-.55 0-1-.45-1-1Zm-5 4a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2H8Zm6 5a1 1 0 0 0-1-1H8a1 1 0 1 0 0 2h5a1 1 0 0 0 1-1Z"
                                fill="currentColor"
                              ></path>
                            </svg>
                          </span>
                        </div>
                        <!-- Text -->
                        <div class="R-text">
                          <div class="head">
                            <h4
                              title="Terms and Privacy Policy"
                              aria-label="Terms and Privacy Policy"
                            >
                              Terms and Privacy Policy
                            </h4>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="modal fade"
                    id="exampleModalSec"
                    data-backdrop="static"
                    aria-labelledby="exampleModalLabelSec"
                    aria-hidden="true"
                  >
                    <div class="modal-dialog modal-dialog-centered">
                      <div class="modal-content">
                        <h5 class="m-title">Log Out?</h5>
                        <ul>
                          <li>
                            <h4>Are you sure you want to log out?</h4>
                          </li>
                        </ul>

                        <div class="m-btn">
                          <button
                            type="button"
                            class="btn1"
                            data-dismiss="modal"
                          >
                            Cancel
                          </button>
                          <button
                            type="button"
                            class="btn2"
                            data-dismiss="modal"
                          >
                            Log out
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="status animate__animated animate__fadeInLeft"
                    id="status"
                  >
                    <!-- Header -->

                    <div class="header">
                      <!-- Profile Picture -->
                      <div class="imgText">
                        <div class="userImg">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            alt=""
                            class="cover"
                          />
                        </div>
                        <h4 title="My Status" aria-label="My Status">
                          My Status<br /><span aria-label="No Updates">
                            No updates
                          </span>
                        </h4>
                      </div>
                    </div>
                    <!-- Chats -->

                    <div
                      class="rightStatus animate__animated animate__fadeInRight"
                      id="rightStatus"
                    >
                      <div class="_shbxn">
                        <div class="_13bjQ">
                          <div class="status-placeholder">
                            <span
                              data-testid="status-v3-placeholder"
                              data-icon="status-v3-placeholder"
                              class=""
                            >
                              <svg
                                version="1.1"
                                id="Layer_1"
                                x="0"
                                y="0"
                                viewBox="0 0 80 80"
                                width="80"
                                height="80"
                                class=""
                              >
                                <path
                                  fill="currentColor"
                                  d="M30.566 78.982c-.222 0-.447-.028-.672-.087C12.587 74.324.5 58.588.5 40.631c0-3.509.459-6.989 1.363-10.343a2.625 2.625 0 0 1 5.068 1.366 34.505 34.505 0 0 0-1.182 8.977c0 15.578 10.48 29.226 25.485 33.188a2.625 2.625 0 0 1-.668 5.163zm19.355-.107C67.336 74.364 79.5 58.611 79.5 40.563c0-3.477-.452-6.933-1.345-10.27a2.624 2.624 0 1 0-5.071 1.356 34.578 34.578 0 0 1 1.166 8.914c0 15.655-10.545 29.319-25.646 33.23a2.625 2.625 0 0 0 1.317 5.082zM15.482 16.5C21.968 9.901 30.628 6.267 39.867 6.267c9.143 0 17.738 3.569 24.202 10.05a2.625 2.625 0 0 0 3.717-3.708C60.329 5.135 50.413 1.018 39.867 1.018c-10.658 0-20.648 4.191-28.128 11.802a2.624 2.624 0 1 0 3.743 3.68z"
                                ></path>
                              </svg>
                            </span>
                          </div>
                          <div class="status-text">
                            Click on a contact to view their status updates
                          </div>
                        </div>
                      </div>

                      <div class="ICON">
                        <div
                          aria-disabled="true"
                          role="button"
                          tabindex="0"
                          class="icons"
                          data-tab="0"
                          onclick="closeStatus()"
                        >
                          <i class="fe fe-arrow-right fe-24"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="Right-Container"
                    style="flex: 70%; display: flex; overflow: hidden"
                    id="message-area"
                  >
                    <div class="rightSide" style="display: none" id="rightSide">
                      <div class="header" id="navbar">
                        <div
                          class="imgText"
                          role="button"
                          onclick="openChatProfile()"
                        >
                          <div class="userImg">
                            <img
                              src="/assets/avatars/imageGrey.jpg"
                              alt=""
                              class="cover"
                              id="pic"
                            />
                          </div>
                          <!-- <h4 title="احمد" aria-label="احمد" id="name">احمد
                            <br><span aria-label="online" id="details">متصل</span></h4> -->
                          <div class="d-flex flex-column">
                            <h4 class="text-dark font-weight-bold" id="name">
                              Programmers
                            </h4>
                            <div class="last-time" id="details"></div>
                          </div>
                        </div>

                        <!-- Icons -->
                        <div class="chat-side">
                          <button class="icons pP" onclick="openSearch()">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="M15.9 14.3H15l-.3-.3c1-1.1 1.6-2.7 1.6-4.3 0-3.7-3-6.7-6.7-6.7S3 6 3 9.7s3 6.7 6.7 6.7c1.6 0 3.2-.6 4.3-1.6l.3.3v.8l5.1 5.1 1.5-1.5-5-5.2zm-6.2 0c-2.6 0-4.6-2.1-4.6-4.6s2.1-4.6 4.6-4.6 4.6 2.1 4.6 4.6-2 4.6-4.6 4.6z"
                              ></path>
                            </svg>
                          </button>

                          <div class="dropdown-icon">
                            <button class="pressed icons pP" id="dropDown">
                              <svg
                                viewBox="0 0 24 24"
                                width="24"
                                height="24"
                                class=""
                              >
                                <path
                                  fill="currentColor"
                                  d="M12 7a2 2 0 1 0-.001-4.001A2 2 0 0 0 12 7zm0 2a2 2 0 1 0-.001 3.999A2 2 0 0 0 12 9zm0 6a2 2 0 1 0-.001 3.999A2 2 0 0 0 12 15z"
                                ></path>
                              </svg>
                            </button>
                            <ul class="drop" id="drop">
                              <li
                                class="listItem"
                                role="button"
                                onclick="openChatProfile()"
                              >
                                معلومات الاتصال
                              </li>

                              <li
                                class="listItem"
                                role="button"
                                onclick="closeRightSide()"
                                id="closeChat"
                              >
                                إغلاق الدردشة
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <!-- ChatBox -->
                      <div class="chatBox" id="messages"></div>
                      <div id="emoji-picker-wrapper"></div>
                      <div class="chat-footer" id="input-area">
                        <div class="emojis__wrapper" id="emoji-wrap"></div>

                        <div class="chat-input-wrapper">
                          <div id="scrollDownBtn" class="icon-container">
                            <div class="notification-count">1</div>
                            <div class="icon">
                              <i class="fe fe-chevron-down fe-24"></i>
                            </div>
                            <!-- This is a down arrow symbol -->
                          </div>
                          <div
                            id="reply-div"
                            style="
                              padding-right: 30px;
                              padding-left: 30px;
                              padding-top: 10px;
                              display: none;
                            "
                          >
                            <div id="quoted-message" class="quoted-message">
                              <div class="quoted-content">
                                <span class="sender-name">Siraj</span>
                                <p class="quoted-text">
                                  Hello, this is testing
                                </p>
                              </div>
                              <span
                                class="close-quoted"
                                onclick="removeQuotedMessage()"
                                >✖</span
                              >
                            </div>
                          </div>
                          <!-- حاوية التسجيل الصوتي -->
                          <div
                            id="recording-container"
                            class="recording-container"
                            style="display: none"
                          >
                            <div class="recording-status">
                              <span id="recording-timer" class="recording-timer"
                                >00:00</span
                              >
                              <div
                                id="recording-waveform"
                                class="recording-waveform"
                              ></div>
                            </div>
                            <div class="recording-actions">
                              <div id="slide-to-cancel" class="slide-to-cancel">
                                <i class="fe fe-x"></i>
                                <span>اسحب للإلغاء</span>
                              </div>
                              <div id="slide-to-lock" class="slide-to-lock">
                                <i class="fe fe-lock"></i>
                                <span>اسحب للأعلى للقفل</span>
                              </div>
                              <button
                                id="stop-recording-btn"
                                class="stop-recording-btn"
                                style="display: none"
                              >
                                <i class="fe fe-square"></i>
                              </button>
                            </div>
                          </div>

                          <!-- حاوية معاينة التسجيل -->
                          <div
                            id="recording-preview"
                            class="recording-preview"
                            style="display: none"
                          >
                            <div class="preview-content">
                              <button
                                id="preview-play-btn"
                                class="preview-play-btn"
                              >
                                <i class="fe fe-play"></i>
                              </button>
                              <div
                                id="preview-waveform"
                                class="preview-waveform"
                              ></div>
                              <span
                                id="preview-duration"
                                class="preview-duration"
                                >00:00</span
                              >
                            </div>
                            <div class="preview-actions">
                              <button
                                id="preview-cancel-btn"
                                class="preview-cancel-btn"
                              >
                                <i class="fe fe-trash-2"></i>
                              </button>
                              <button
                                id="preview-send-btn"
                                class="preview-send-btn"
                              >
                                <i class="fe fe-send"></i>
                              </button>
                            </div>
                          </div>
                          <div
                            class="chat-input-container justify-self-end align-items-center flex-row"
                            id="reply-area"
                          >
                            <button
                              role="button"
                              class="icons"
                              id="sticker-icon"
                            >
                              <span class="">
                                <svg
                                  viewBox="0 0 24 24"
                                  width="24"
                                  height="24"
                                  class=""
                                >
                                  <path
                                    fill="currentColor"
                                    d="M9.153 11.603c.795 0 1.439-.879 1.439-1.962s-.644-1.962-1.439-1.962-1.439.879-1.439 1.962.644 1.962 1.439 1.962zm-3.204 1.362c-.026-.307-.131 5.218 6.063 5.551 6.066-.25 6.066-5.551 6.066-5.551-6.078 1.416-12.129 0-12.129 0zm11.363 1.108s-.669 1.959-5.051 1.959c-3.505 0-5.388-1.164-5.607-1.959 0 0 5.912 1.055 10.658 0zM11.804 1.011C5.609 1.011.978 6.033.978 12.228s4.826 10.761 11.021 10.761S23.02 18.423 23.02 12.228c.001-6.195-5.021-11.217-11.216-11.217zM12 21.354c-5.273 0-9.381-3.886-9.381-9.159s3.942-9.548 9.215-9.548 9.548 4.275 9.548 9.548c-.001 5.272-4.109 9.159-9.382 9.159zm3.108-9.751c.795 0 1.439-.879 1.439-1.962s-.644-1.962-1.439-1.962-1.439.879-1.439 1.962.644 1.962 1.439 1.962z"
                                  ></path>
                                </svg>
                              </span>
                            </button>

                            <div class="chat-attach">
                              <button
                                role="button"
                                class="icons Marg"
                                id="chat-popup"
                              >
                                <span class="">
                                  <svg
                                    viewBox="0 0 24 24"
                                    width="24"
                                    height="24"
                                    class=""
                                  >
                                    <path
                                      fill="currentColor"
                                      d="M1.816 15.556v.002c0 1.502.584 2.912 1.646 3.972s2.472 1.647 3.974 1.647a5.58 5.58 0 0 0 3.972-1.645l9.547-9.548c.769-.768 1.147-1.767 1.058-2.817-.079-.968-.548-1.927-1.319-2.698-1.594-1.592-4.068-1.711-5.517-.262l-7.916 7.915c-.881.881-.792 2.25.214 3.261.959.958 2.423 1.053 3.263.215l5.511-5.512c.28-.28.267-.722.053-.936l-.244-.244c-.191-.191-.567-.349-.957.04l-5.506 5.506c-.18.18-.635.127-.976-.214-.098-.097-.576-.613-.213-.973l7.915-7.917c.818-.817 2.267-.699 3.23.262.5.501.802 1.1.849 1.685.051.573-.156 1.111-.589 1.543l-9.547 9.549a3.97 3.97 0 0 1-2.829 1.171 3.975 3.975 0 0 1-2.83-1.173 3.973 3.973 0 0 1-1.172-2.828c0-1.071.415-2.076 1.172-2.83l7.209-7.211c.157-.157.264-.579.028-.814L11.5 4.36a.572.572 0 0 0-.834.018l-7.205 7.207a5.577 5.577 0 0 0-1.645 3.971z"
                                    ></path>
                                  </svg>
                                </span>
                              </button>

                              <div class="popup" id="popup">
                                <button
                                  aria-label="Document"
                                  title="Document"
                                  class="popopIcons"
                                  role="button"
                                >
                                  <span class="">
                                    <svg
                                      viewBox="0 0 53 53"
                                      width="53"
                                      height="53"
                                      class=""
                                    >
                                      <defs>
                                        <circle
                                          id="document-SVGID_1_"
                                          cx="26.5"
                                          cy="26.5"
                                          r="25.5"
                                        ></circle>
                                      </defs>
                                      <clipPath id="document-SVGID_2_">
                                        <use
                                          xlink:href="#document-SVGID_1_"
                                          overflow="visible"
                                        ></use>
                                      </clipPath>
                                      <g clip-path="url(#document-SVGID_2_)">
                                        <path
                                          fill="#5157AE"
                                          d="M26.5-1.1C11.9-1.1-1.1 5.6-1.1 27.6h55.2c-.1-19-13-28.7-27.6-28.7z"
                                        ></path>
                                        <path
                                          fill="#5F66CD"
                                          d="M53 26.5H-1.1c0 14.6 13 27.6 27.6 27.6s27.6-13 27.6-27.6H53z"
                                        ></path>
                                      </g>
                                      <g fill="#F5F5F5">
                                        <path
                                          id="svg-document"
                                          d="M29.09 17.09c-.38-.38-.89-.59-1.42-.59H20.5c-1.1 0-2 .9-2 2v16c0 1.1.89 2 1.99 2H32.5c1.1 0 2-.9 2-2V23.33c0-.53-.21-1.04-.59-1.41l-4.82-4.83zM27.5 22.5V18l5.5 5.5h-4.5c-.55 0-1-.45-1-1z"
                                        ></path>
                                      </g>
                                    </svg>
                                  </span>
                                  <input
                                    accept="*"
                                    type="file"
                                    multiple=""
                                    style="display: none"
                                  />
                                </button>

                                <button
                                  aria-label="Camera"
                                  title="Camera"
                                  class="popopIcons"
                                  role="button"
                                >
                                  <span class="">
                                    <svg
                                      viewBox="0 0 53 53"
                                      width="53"
                                      height="53"
                                      class=""
                                    >
                                      <defs>
                                        <circle
                                          id="camera-SVGID_1_"
                                          cx="26.5"
                                          cy="26.5"
                                          r="25.5"
                                        ></circle>
                                      </defs>
                                      <clipPath id="camera-SVGID_2_">
                                        <use
                                          xlink:href="#camera-SVGID_1_"
                                          overflow="visible"
                                        ></use>
                                      </clipPath>
                                      <g clip-path="url(#camera-SVGID_2_)">
                                        <path
                                          fill="#D3396D"
                                          d="M26.5-1.1C11.9-1.1-1.1 5.6-1.1 27.6h55.2c-.1-19-13-28.7-27.6-28.7z"
                                        ></path>
                                        <path
                                          fill="#EC407A"
                                          d="M53 26.5H-1.1c0 14.6 13 27.6 27.6 27.6s27.6-13 27.6-27.6H53z"
                                        ></path>
                                        <path
                                          fill="#D3396D"
                                          d="M17 24.5h15v9H17z"
                                        ></path>
                                      </g>
                                      <g fill="#F5F5F5">
                                        <path
                                          id="svg-camera"
                                          d="M27.795 17a3 3 0 0 1 2.405 1.206l.3.403a3 3 0 0 0 2.405 1.206H34.2a2.8 2.8 0 0 1 2.8 2.8V32a4 4 0 0 1-4 4H20a4 4 0 0 1-4-4v-9.385a2.8 2.8 0 0 1 2.8-2.8h1.295a3 3 0 0 0 2.405-1.206l.3-.403A3 3 0 0 1 25.205 17h2.59ZM26.5 22.25a5.25 5.25 0 1 0 .001 10.501A5.25 5.25 0 0 0 26.5 22.25Zm0 1.75a3.5 3.5 0 1 1 0 7 3.5 3.5 0 0 1 0-7Z"
                                        ></path>
                                      </g>
                                    </svg>
                                  </span>
                                </button>

                                <button
                                  aria-label="Photos &amp; Videos"
                                  title="Photos &amp; Videos"
                                  class="popopIcons"
                                  role="button"
                                >
                                  <span class="">
                                    <svg
                                      viewBox="0 0 53 53"
                                      width="53"
                                      height="53"
                                      class=""
                                    >
                                      <defs>
                                        <circle
                                          id="image-SVGID_1_"
                                          cx="26.5"
                                          cy="26.5"
                                          r="25.5"
                                        ></circle>
                                      </defs>
                                      <clipPath id="image-SVGID_2_">
                                        <use
                                          xlink:href="#image-SVGID_1_"
                                          overflow="visible"
                                        ></use>
                                      </clipPath>
                                      <g clip-path="url(#image-SVGID_2_)">
                                        <path
                                          fill="#AC44CF"
                                          d="M26.5-1.1C11.9-1.1-1.1 5.6-1.1 27.6h55.2c-.1-19-13-28.7-27.6-28.7z"
                                        ></path>
                                        <path
                                          fill="#BF59CF"
                                          d="M53 26.5H-1.1c0 14.6 13 27.6 27.6 27.6s27.6-13 27.6-27.6H53z"
                                        ></path>
                                        <path
                                          fill="#AC44CF"
                                          d="M17 24.5h18v9H17z"
                                        ></path>
                                      </g>
                                      <g fill="#F5F5F5">
                                        <path
                                          id="svg-image"
                                          d="M18.318 18.25h16.364c.863 0 1.727.827 1.811 1.696l.007.137v12.834c0 .871-.82 1.741-1.682 1.826l-.136.007H18.318a1.83 1.83 0 0 1-1.812-1.684l-.006-.149V20.083c0-.87.82-1.741 1.682-1.826l.136-.007h16.364Zm5.081 8.22-3.781 5.044c-.269.355-.052.736.39.736h12.955c.442-.011.701-.402.421-.758l-2.682-3.449a.54.54 0 0 0-.841-.011l-2.262 2.727-3.339-4.3a.54.54 0 0 0-.861.011Zm8.351-5.22a1.75 1.75 0 1 0 .001 3.501 1.75 1.75 0 0 0-.001-3.501Z"
                                        ></path>
                                      </g>
                                    </svg>
                                  </span>
                                  <input
                                    accept="image/*,video/mp4,video/3gpp,video/quicktime"
                                    type="file"
                                    multiple=""
                                    style="display: none"
                                  />
                                </button>
                              </div>
                            </div>

                            <input
                              autofocus
                              type="text"
                              id="input-send-message"
                              class="send-message"
                              placeholder="اكتب رسالة..."
                            />
                            <div class="send-voice-buttons">
                              <!-- <button id="send-message-btn" class="send-message-btn" title="Send">
                                    <i class="fe fe-send"></i>
                                </button> -->
                              <button
                                id="voice-record-btn"
                                class="voice-record-btn"
                                title="Voice Message"
                              >
                                <i class="fe fe-mic fe-16"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="Intro-Left" id="Intro-Left">
                      <div class="intro">
                        <div class="intro-svg">
                          <span class="IVxyB">
                            <svg
                              width="360"
                              viewBox="0 0 303 172"
                              fill="none"
                              class=""
                            >
                              <path
                                fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M229.565 160.229c32.647-10.984 57.366-41.988 53.825-86.81-5.381-68.1-71.025-84.993-111.918-64.932C115.998 35.7 108.972 40.16 69.239 40.16c-29.594 0-59.726 14.254-63.492 52.791-2.73 27.933 8.252 52.315 48.89 64.764 73.962 22.657 143.38 13.128 174.928 2.513Z"
                                fill="var(--intro-svg1)"
                              ></path>
                              <path
                                fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M131.589 68.942h.01c6.261 0 11.336-5.263 11.336-11.756S137.86 45.43 131.599 45.43c-5.081 0-9.381 3.466-10.822 8.242a7.302 7.302 0 0 0-2.404-.405c-4.174 0-7.558 3.51-7.558 7.838s3.384 7.837 7.558 7.837h13.216ZM105.682 128.716c3.504 0 6.344-2.808 6.344-6.27 0-3.463-2.84-6.27-6.344-6.27-1.156 0-2.24.305-3.173.839v-.056c0-6.492-5.326-11.756-11.896-11.756-5.29 0-9.775 3.413-11.32 8.132a8.025 8.025 0 0 0-2.163-.294c-4.38 0-7.93 3.509-7.93 7.837 0 4.329 3.55 7.838 7.93 7.838h28.552Z"
                                fill="var(--intro-svg2)"
                                fill-opacity="var(--intro-opacity)"
                              ></path>
                              <rect
                                x=".445"
                                y=".55"
                                width="50.58"
                                height="100.068"
                                rx="7.5"
                                transform="rotate(6 -391.775 121.507) skewX(.036)"
                                fill="#ffcf585c"
                                stroke="#316474"
                              ></rect>
                              <rect
                                x=".445"
                                y=".55"
                                width="50.403"
                                height="99.722"
                                rx="7.5"
                                transform="rotate(6 -356.664 123.217) skewX(.036)"
                                fill="var(--intro-svg3)"
                                stroke="#316474"
                              ></rect>
                              <path
                                d="m57.16 51.735-8.568 82.024a5.495 5.495 0 0 1-6.042 4.895l-32.97-3.465a5.504 5.504 0 0 1-4.897-6.045l8.569-82.024a5.496 5.496 0 0 1 6.041-4.895l5.259.553 22.452 2.36 5.259.552a5.504 5.504 0 0 1 4.898 6.045Z"
                                fill="var(--intro-svg4)"
                                stroke="#316474"
                              ></path>
                              <path
                                d="M26.2 102.937c.863.082 1.732.182 2.602.273.238-2.178.469-4.366.69-6.546l-2.61-.274c-.238 2.178-.477 4.365-.681 6.547Zm-2.73-9.608 2.27-1.833 1.837 2.264 1.135-.917-1.838-2.266 2.27-1.833-.92-1.133-2.269 1.834-1.837-2.264-1.136.916 1.839 2.265-2.27 1.835.92 1.132Zm-.816 5.286c-.128 1.3-.265 2.6-.41 3.899.877.109 1.748.183 2.626.284.146-1.31.275-2.614.413-3.925-.878-.092-1.753-.218-2.629-.258Zm16.848-8.837c-.506 4.801-1.019 9.593-1.516 14.396.88.083 1.748.192 2.628.267.496-4.794 1-9.578 1.513-14.37-.864-.143-1.747-.192-2.625-.293Zm-4.264 2.668c-.389 3.772-.803 7.541-1.183 11.314.87.091 1.74.174 2.601.273.447-3.912.826-7.84 1.255-11.755-.855-.15-1.731-.181-2.589-.306-.04.156-.069.314-.084.474Zm-4.132 1.736c-.043.159-.06.329-.077.49-.297 2.896-.617 5.78-.905 8.676l2.61.274c.124-1.02.214-2.035.33-3.055.197-2.036.455-4.075.627-6.115-.863-.08-1.724-.17-2.585-.27Z"
                                fill="#316474"
                              ></path>
                              <path
                                d="M17.892 48.489a1.652 1.652 0 0 0 1.468 1.803 1.65 1.65 0 0 0 1.82-1.459 1.652 1.652 0 0 0-1.468-1.803 1.65 1.65 0 0 0-1.82 1.459ZM231.807 136.678l-33.863 2.362c-.294.02-.54-.02-.695-.08a.472.472 0 0 1-.089-.042l-.704-10.042a.61.61 0 0 1 .082-.054c.145-.081.383-.154.677-.175l33.863-2.362c.294-.02.54.02.695.08.041.016.069.03.088.042l.705 10.042a.61.61 0 0 1-.082.054 1.678 1.678 0 0 1-.677.175Z"
                                fill="#fff"
                                stroke="#316474"
                              ></path>
                              <path
                                d="m283.734 125.679-138.87 9.684c-2.87.2-5.371-1.963-5.571-4.823l-6.234-88.905c-.201-2.86 1.972-5.35 4.844-5.55l138.87-9.684c2.874-.2 5.371 1.963 5.572 4.823l6.233 88.905c.201 2.86-1.971 5.349-4.844 5.55Z"
                                fill="#EEFAF6"
                              ></path>
                              <path
                                d="M144.864 135.363c-2.87.2-5.371-1.963-5.571-4.823l-6.234-88.905c-.201-2.86 1.972-5.35 4.844-5.55l138.87-9.684c2.874-.2 5.371 1.963 5.572 4.823l6.233 88.905c.201 2.86-1.971 5.349-4.844 5.55"
                                stroke="#316474"
                              ></path>
                              <path
                                d="m278.565 121.405-129.885 9.058c-2.424.169-4.506-1.602-4.668-3.913l-5.669-80.855c-.162-2.31 1.651-4.354 4.076-4.523l129.885-9.058c2.427-.169 4.506 1.603 4.668 3.913l5.669 80.855c.162 2.311-1.649 4.354-4.076 4.523Z"
                                fill="var(--intro-svg5)"
                                stroke="#316474"
                              ></path>
                              <path
                                d="m230.198 129.97 68.493-4.777.42 5.996c.055.781-.098 1.478-.363 1.972-.27.5-.611.726-.923.748l-165.031 11.509c-.312.022-.681-.155-1.017-.613-.332-.452-.581-1.121-.636-1.902l-.42-5.996 68.494-4.776c.261.79.652 1.483 1.142 1.998.572.6 1.308.986 2.125.929l24.889-1.736c.817-.057 1.491-.54 1.974-1.214.413-.577.705-1.318.853-2.138Z"
                                fill="#ffcf585c"
                                stroke="#316474"
                              ></path>
                              <path
                                d="m230.367 129.051 69.908-4.876.258 3.676a1.51 1.51 0 0 1-1.403 1.61l-168.272 11.735a1.51 1.51 0 0 1-1.613-1.399l-.258-3.676 69.909-4.876a3.323 3.323 0 0 0 3.188 1.806l25.378-1.77a3.32 3.32 0 0 0 2.905-2.23Z"
                                fill="#EEFAF6"
                                stroke="#316474"
                              ></path>
                              <circle
                                transform="rotate(-3.989 1304.861 -2982.552) skewX(.021)"
                                fill="#ffcf585c"
                                stroke="#316474"
                                r="15.997"
                              ></circle>
                              <path
                                d="m208.184 87.11-3.407-2.75-.001-.002a1.952 1.952 0 0 0-2.715.25 1.89 1.89 0 0 0 .249 2.692l.002.001 5.077 4.11v.001a1.95 1.95 0 0 0 2.853-.433l8.041-12.209a1.892 1.892 0 0 0-.573-2.643 1.95 1.95 0 0 0-2.667.567l-6.859 10.415Z"
                                fill="#fff"
                                stroke="#316474"
                              ></path>
                            </svg>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="ChatAbout animate__animated animate__fadeInRight"
                    id="ChatAbout"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <button
                          role="button"
                          class="icons"
                          onclick="closeChatProfile()"
                        >
                          <span class="">
                            <svg
                              fill="currentColor"
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                d="m19.1 17.2-5.3-5.3 5.3-5.3-1.8-1.8-5.3 5.4-5.3-5.3-1.8 1.7 5.3 5.3-5.3 5.3L6.7 19l5.3-5.3 5.3 5.3 1.8-1.8z"
                              ></path>
                            </svg>
                          </span>
                        </button>
                      </div>

                      <div class="newText">
                        <h2>معلومات الاتصال</h2>
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-chatAbout">
                      <div class="img-about">
                        <div class="img-Ani">
                          <img
                            src="/assets/avatars/imageGrey.jpg"
                            class="img-animated"
                            alt=""
                            draggable="false"
                          />
                        </div>

                        <div class="text-Ani">
                          <h3></h3>
                          <p></p>
                        </div>
                      </div>

                      <!-- Chats 2 -->
                      <div class="block">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="titlePro">
                            <p>عن</p>
                          </div>
                          <div class="bio">
                            <div class="text-inner">
                              <h4></h4>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Chats 2 -->
                      <div class="block">
                        <div class="h-text">
                          <div class="titlePro">
                            <p>الوسائط والروابط والوثائق</p>
                            <div class="pictures-text">
                              <i class="fe chevrons-left fe-16"></i>
                              <i class="fe fe-chevron-left fe-16"></i>
                            </div>
                          </div>
                          <div class="media-links">
                            <img
                              src="/assets/avatars/imageGrey.jpg"
                              alt=""
                              class="avatar"
                              draggable="false"
                            />
                            <img
                              src="/assets/avatars/imageGrey.jpg"
                              alt=""
                              class="avatar"
                              draggable="false"
                            />
                            <img
                              src="/assets/avatars/imageGrey.jpg"
                              alt=""
                              class="avatar"
                              draggable="false"
                            />
                          </div>
                        </div>
                      </div>

                      <!-- Group Members Section (for group chats only) -->
                      <div class="profile-section group-only">
                        <div class="members-header">
                          <h4 class="section-title">أعضاء المجموعة</h4>
                          <div class="members-count">
                            <h6>15</h6>
                          </div>
                        </div>
                        <!-- Members Search -->
                        <div class="members-search">
                          <input
                            type="text"
                            class="search-members-input"
                            placeholder="بحث عن أعضاء..."
                          />
                        </div>
                        <div class="list-button"></div>
                        <button
                          class="action-button add-members-btn admin-only"
                        >
                          <i class="fe fe-user-plus"></i>
                          إضافة أعضاء جدد
                        </button>
                        <!-- Members Container -->
                        <div class="members-container">
                          <!-- Members will be added here dynamically -->
                          <div class="no-members-message">
                            جاري تحميل الأعضاء...
                          </div>
                        </div>

                        <!-- Show More Button -->
                        <button class="show-more-members-btn d-none">
                          عرض المزيد
                        </button>
                      </div>

                      <!-- Chats 4 -->
                      <div class="bottom">
                        <!-- Text -->
                        <div class="h-text">
                          <div class="Block-head">
                            <div class="contact-star">
                              <span class="star">
                                <svg
                                  viewBox="0 0 24 24"
                                  width="24"
                                  height="24"
                                  class=""
                                >
                                  <path
                                    fill="currentColor"
                                    d="M12 2.8c-5.3 0-9.7 4.3-9.7 9.7s4.3 9.7 9.7 9.7 9.7-4.3 9.7-9.7-4.4-9.7-9.7-9.7zm-7.3 9.7c0-4 3.3-7.3 7.3-7.3 1.6 0 3.1.5 4.3 1.4L6.1 16.8c-.9-1.2-1.4-2.7-1.4-4.3zm7.3 7.3c-1.6 0-3-.5-4.2-1.4L17.9 8.3c.9 1.2 1.4 2.6 1.4 4.2 0 4-3.3 7.3-7.3 7.3z"
                                  ></path>
                                </svg>
                              </span>
                            </div>
                            <div class="contact-text">
                              <span class="star-text">Block احمد</span>
                            </div>
                          </div>

                          <div class="Block-head">
                            <div class="contact-star">
                              <span class="star">
                                <svg
                                  viewBox="0 0 24 24"
                                  width="24"
                                  height="24"
                                  class=""
                                >
                                  <path
                                    fill="currentColor"
                                    d="M6 18c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V6H6v12zM19 3h-3.5l-1-1h-5l-1 1H5v2h14V3z"
                                  ></path>
                                </svg>
                              </span>
                            </div>
                            <div class="contact-text">
                              <span class="star-text">حذف</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Chat Profile Container -->
                  <!-- Chat Profile Container -->
                  <div
                    id="ChatProfile"
                    class="chat-profile ChatAbout animate__animated animate__fadeInRight"
                  >
                    <!-- Profile Header -->
                    <div class="profile-header">
                      <button class="close-profile-btn">&times;</button>
                      <img
                        src="/assets/avatars/imageGrey.jpg"
                        alt="Profile"
                        class="profile-image"
                      />
                      <h3 class="profile-name">اسم المستخدم/المجموعة</h3>
                      <p class="profile-details">تفاصيل المستخدم/المجموعة</p>
                    </div>

                    <!-- Profile About Section -->
                    <div class="profile-section">
                      <p class="profile-about">
                        نبذة عن المستخدم أو وصف المجموعة
                      </p>
                    </div>

                    <!-- Media Section -->
                    <div class="profile-section">
                      <h4 class="section-title">الوسائط المشتركة</h4>

                      <!-- Media Tabs -->
                      <div class="media-tabs">
                        <div class="media-tab active" data-media-type="all">
                          الكل
                        </div>
                        <div class="media-tab" data-media-type="images">
                          صور
                        </div>
                        <div class="media-tab" data-media-type="videos">
                          فيديو
                        </div>
                        <div class="media-tab" data-media-type="files">
                          ملفات
                        </div>
                      </div>

                      <!-- Media Items Container -->
                      <div class="media-items-container">
                        <!-- Media items will be added here dynamically -->
                        <div class="no-media-message">لا توجد وسائط مشتركة</div>
                      </div>
                    </div>

                    <!-- Group Members Section (for group chats only) -->
                    <div class="profile-section group-only">
                      <h4 class="section-title">أعضاء المجموعة</h4>

                      <!-- Members Search -->
                      <div class="members-search">
                        <input
                          type="text"
                          class="search-members-input"
                          placeholder="بحث عن أعضاء..."
                        />
                      </div>
                      <div class="list-button"></div>
                      <button class="action-button add-members-btn admin-only">
                        <i class="fe fe-user-plus"></i>
                        إضافة أعضاء جدد
                      </button>
                      <!-- Members Container -->
                      <div class="members-container">
                        <!-- Members will be added here dynamically -->
                        <div class="no-members-message">
                          جاري تحميل الأعضاء...
                        </div>
                      </div>

                      <!-- Show More Button -->
                      <button class="show-more-members-btn d-none">
                        عرض المزيد
                      </button>
                    </div>

                    <!-- Group Actions Section (for group chats only) -->
                    <div class="profile-section group-actions group-only">
                      <!-- Add Members Button (for admins and owners only) -->
                      <button class="action-button add-members-btn admin-only">
                        <i class="fe fe-user-plus"></i>
                        إضافة أعضاء جدد
                      </button>

                      <!-- Leave Group Button -->
                      <button class="action-button leave-group-btn">
                        <i class="fe fe-log-out"></i>
                        الخروج من المجموعة
                      </button>
                    </div>

                    <!-- Individual Chat Actions Section (for individual chats only) -->
                    <div class="profile-section individual-only">
                      <!-- Individual-specific actions can be added here -->
                    </div>
                  </div>

                  <div
                    class="modal fade"
                    id="mute-modal"
                    data-backdrop="static"
                    aria-labelledby="exampleModalLabel"
                    aria-hidden="true"
                  >
                    <div class="modal-dialog modal-dialog-centered">
                      <div class="modal-content">
                        <h5 class="m-title">Mute "احمد" for...</h5>
                        <ul>
                          <li>
                            <div class="m-list">
                              <label class="form-check-label" for="8-hours">
                                <input
                                  class="form-check-input"
                                  type="radio"
                                  name="groupsRadios"
                                  value=""
                                  id="8-hours"
                                  checked
                                />
                                <h4>8 Hours</h4>
                              </label>
                            </div>
                          </li>
                          <li>
                            <div class="m-list">
                              <label class="form-check-label" for="week">
                                <input
                                  class="form-check-input"
                                  type="radio"
                                  name="groupsRadios"
                                  value=""
                                  id="week"
                                />
                                <h4>1 Week</h4>
                              </label>
                            </div>
                          </li>
                          <li>
                            <div class="m-list">
                              <label class="form-check-label" for="always">
                                <input
                                  class="form-check-input"
                                  type="radio"
                                  name="groupsRadios"
                                  value=""
                                  id="always"
                                />
                                <h4>Always</h4>
                              </label>
                            </div>
                          </li>
                        </ul>

                        <div class="m-btn">
                          <button
                            type="button"
                            class="btn1"
                            data-dismiss="modal"
                          >
                            Cancel
                          </button>
                          <button
                            type="button"
                            class="btn2"
                            data-dismiss="modal"
                          >
                            Mute notification
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="modal fade"
                    id="clear-modal"
                    data-backdrop="static"
                    aria-labelledby="exampleModalLabel"
                    aria-hidden="true"
                  >
                    <div class="modal-dialog modal-dialog-centered">
                      <div class="modal-content">
                        <h5 class="m-title">Clear this chat</h5>
                        <p class="modal-text">
                          Messages will only be removed from this device and
                          your devices on the newer versions of ChatApp.
                        </p>
                        <ul class="checkbox">
                          <li>
                            <div class="m-list">
                              <label
                                class="form-check-label"
                                for="keep-starred-message"
                              >
                                <input
                                  class="form-check-input"
                                  type="checkbox"
                                  name="groupsRadios"
                                  value=""
                                  id="keep-starred-message"
                                />
                                <h4>Keep Starred Message</h4>
                              </label>
                            </div>
                          </li>
                        </ul>
                        <div class="m-btn">
                          <button
                            type="button"
                            class="btn1"
                            data-dismiss="modal"
                          >
                            Cancel
                          </button>
                          <button
                            type="button"
                            class="btn2"
                            data-dismiss="modal"
                          >
                            Clear Chat
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="modal fade"
                    id="delete-modal"
                    data-backdrop="static"
                    aria-labelledby="exampleModalLabel"
                    aria-hidden="true"
                  >
                    <div class="modal-dialog modal-dialog-centered">
                      <div class="modal-content">
                        <h5 class="m-title">Delete this chat</h5>
                        <p class="modal-text v2">
                          Messages will only be removed from this device and
                          your devices on the newer versions of ChatApp.
                        </p>
                        <div class="m-btn">
                          <button
                            type="button"
                            class="btn1"
                            data-dismiss="modal"
                          >
                            Cancel
                          </button>
                          <button
                            type="button"
                            class="btn2"
                            data-dismiss="modal"
                          >
                            حذف
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="search-message animate__animated animate__fadeInRight"
                    id="search-message"
                  >
                    <!-- Header -->
                    <div class="header-Chat">
                      <!-- Icons -->
                      <div class="ICON">
                        <button
                          role="button"
                          class="icons"
                          onclick="closeSearch()"
                        >
                          <span class="">
                            <svg
                              fill="currentColor"
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                d="m19.1 17.2-5.3-5.3 5.3-5.3-1.8-1.8-5.3 5.4-5.3-5.3-1.8 1.7 5.3 5.3-5.3 5.3L6.7 19l5.3-5.3 5.3 5.3 1.8-1.8z"
                              ></path>
                            </svg>
                          </span>
                        </button>
                      </div>

                      <div class="newText">
                        <h2>Search Messages</h2>
                      </div>
                    </div>
                    <!-- Search Bar -->
                    <div class="search-bar">
                      <div>
                        <button class="search">
                          <span class="">
                            <svg
                              viewBox="0 0 24 24"
                              width="24"
                              height="24"
                              class=""
                            >
                              <path
                                fill="currentColor"
                                d="M15.009 13.805h-.636l-.22-.219a5.184 5.184 0 0 0 1.256-3.386 5.207 5.207 0 1 0-5.207 5.208 5.183 5.183 0 0 0 3.385-1.255l.221.22v.635l4.004 3.999 1.194-1.195-3.997-4.007zm-4.808 0a3.605 3.605 0 1 1 0-7.21 3.605 3.605 0 0 1 0 7.21z"
                              ></path>
                            </svg>
                          </span>
                        </button>

                        <span class="go-back">
                          <svg
                            viewBox="0 0 24 24"
                            width="24"
                            height="24"
                            class=""
                          >
                            <path
                              fill="currentColor"
                              d="m12 4 1.4 1.4L7.8 11H20v2H7.8l5.6 5.6L12 20l-8-8 8-8z"
                            ></path>
                          </svg>
                        </span>

                        <input
                          type="text"
                          title="Search"
                          aria-label="Search"
                          placeholder="Search"
                          class="search-Wrapper"
                        />
                      </div>
                    </div>
                    <!-- Chats -->
                    <div class="chats-search">
                      <!-- Chats 1 -->

                      <div class="a">
                        <h3>Searching for messages with احمد</h3>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- end  card data table -->
            </div>
            <!-- .col-12 -->
          </div>
        </div>
        <!-- end container layout  -->
      </main>
      <!-- end main layout -->
    </div>
    <!-- .wrapper -->

    <script src="/assets/js/vendor/jquery.min.js"></script>
    <script src="/assets/js/vendor/popper.min.js"></script>
    <script src="/assets/js/vendor/moment.min.js"></script>
    <script src="/assets/js/vendor/bootstrap.min.js"></script>
    <script src="/assets/js/vendor/simplebar.min.js"></script>
    <script src="/assets/js/vendor/daterangepicker.js"></script>
    <script src="/assets/js/vendor/jquery.stickOnScroll.js"></script>
    <script src="/assets/js/vendor/tinycolor-min.js"></script>
    <script src="/assets/js/vendor/config.js"></script>
    <script src="/assets/js/vendor/d3.min.js"></script>
    <script src="/assets/js/vendor/topojson.min.js"></script>
    <script src="/assets/js/vendor/datamaps.all.min.js"></script>
    <script src="/assets/js/vendor/datamaps-zoomto.js"></script>
    <script src="/assets/js/vendor/datamaps.custom.js"></script>
    <script src="/assets/js/vendor/Chart.min.js"></script>
    <script>
      /* defind global options */
      Chart.defaults.global.defaultFontFamily = base.defaultFontFamily;
      Chart.defaults.global.defaultFontColor = colors.mutedColor;
    </script>
    <script src="/assets/js/custom-js/loading.js"></script>
    <script src="/assets/js/custom-js/ErrorHandler.js"></script>

    <script src="/assets/js/vendor/gauge.min.js"></script>
    <script src="/assets/js/vendor/jquery.sparkline.min.js"></script>
    <script src="/assets/js/vendor/apexcharts.min.js"></script>
    <script src="/assets/js/vendor/apexcharts.custom.js"></script>
    <script src="/assets/js/vendor/jquery.mask.min.js"></script>
    <script src="/assets/js/vendor/select2.min.js"></script>
    @*
    <script src="/assets/js/vendor/jquery.steps.min.js"></script>
    *@
    <script src="/assets/js/vendor/jquery.validate.min.js"></script>
    <script src="/assets/js/vendor/jquery.timepicker.js"></script>
    <script src="/assets/js/vendor/dropzone.min.js"></script>
    <script src="/assets/js/vendor/uppy.min.js"></script>
    <script src="/assets/js/vendor/quill.min.js"></script>
    <script src="/assets/js/vendor/jquery.dataTables.min.js"></script>
    <script src="/assets/js/vendor/dataTables.bootstrap4.min.js"></script>
    <script src="/assets/js/vendor/datatables.net/js/dataTables.bootstrap4.min.js"></script>
    <script src="/assets/js/custom-js/CustomPluginDatatablesV1.js"></script>
    <script src="/assets/js/custom-js/customplugintoast.js"></script>
    <script src="/assets/js/custom-js/validation.js"></script>
    @*
    <script src="/assets/js/custom-js/hs-step-form/dist/hs-step-form.min.js"></script>
    *@
    <script src="/assets/js/custom-js/CustomPluginSelect2.js"></script>
    <script src="/assets/js/custom-js/upload-file.js"></script>
    <script src="/assets/js/custom-js/custompluginfunctions.js"></script>
    <script src="/assets/js/custom-js/CustomPluginSignalRClient.js"></script>

    <script>
      const $toastsContainer = $("#toasts-container");
      const $customToast = $toastsContainer.CustomToast();
      // Display toast after page refresh or redirection

      $(document).ready(function () {
        // Check if there's toast information in sessionStorage
        const toastInfo = sessionStorage.getItem("toastMessage");
        if (toastInfo) {
          // Parse the stored JSON data
          const options = JSON.parse(toastInfo);
          // Remove the toast information from sessionStorage
          sessionStorage.removeItem("toastMessage");
          // Show the toast
          $customToast.showToast(options);
        }
      });
      $.CustomPluginDatatablesV1.init(".js-datatable");
      $.Validation.init(".js-validate");

      $.CustomPluginSelect2.init(".js-select2");

      $(".drgpicker").daterangepicker({
        singleDatePicker: true,
        timePicker: false,
        showDropdowns: true,
        locale: {
          format: "MM/DD/YYYY",
          separator: " - ",
          applyLabel: "تطبيق",
          cancelLabel: "إلغاء",
          fromLabel: "من",
          toLabel: "إلى",
          customRangeLabel: "تحديد المدى",
          weekLabel: "أسبوع",
          daysOfWeek: [
            "الأحد",
            "الاثنين",
            "الثلاثاء",
            "الأربعاء",
            "الخميس",
            "الجمعة",
            "السبت",
          ],
          monthNames: [
            "يناير",
            "فبراير",
            "مارس",
            "إبريل",
            "مايو",
            "يونيو",
            "يوليو",
            "أغسطس",
            "سبتمبر",
            "أكتوبر",
            "نوفمبر",
            "ديسمبر",
          ],
          firstDay: 1,
        },
      });
      $(".time-input").timepicker({
        scrollDefault: "now",
        zindex: "9999" /* fix modal open */,
      });
      /** date range picker */
      if ($(".datetimes").length) {
        $(".datetimes").daterangepicker({
          timePicker: true,
          startDate: moment().startOf("hour"),
          endDate: moment().startOf("hour").add(32, "hour"),
          locale: {
            format: "M/DD hh:mm A",
          },
        });
      }
      var start = moment().subtract(29, "days");
      var end = moment();

      function cb(start, end) {
        $("#reportrange span").html(
          start.format("MMMM D, YYYY") + " - " + end.format("MMMM D, YYYY")
        );
      }
      $("#reportrange").daterangepicker(
        {
          startDate: start,
          endDate: end,
          locale: {
            format: "MM/DD/YYYY",
            separator: " - ",
            applyLabel: "تطبيق",
            cancelLabel: "إلغاء",
            fromLabel: "من",
            toLabel: "إلى",
            customRangeLabel: "تحديد المدى",
            weekLabel: "أسبوع",
            daysOfWeek: [
              "الأحد",
              "الاثنين",
              "الثلاثاء",
              "الأربعاء",
              "الخميس",
              "الجمعة",
              "السبت",
            ],
            monthNames: [
              "يناير",
              "فبراير",
              "مارس",
              "إبريل",
              "مايو",
              "يونيو",
              "يوليو",
              "أغسطس",
              "سبتمبر",
              "أكتوبر",
              "نوفمبر",
              "ديسمبر",
            ],
            firstDay: 1,
          },
          ranges: {
            اليوم: [moment(), moment()],
            أمس: [moment().subtract(1, "days"), moment().subtract(1, "days")],
            "آخر 7 أيام": [moment().subtract(6, "days"), moment()],
            "آخر 30 يومًا": [moment().subtract(29, "days"), moment()],
            "هذا الشهر": [moment().startOf("month"), moment().endOf("month")],
            "الشهر الماضي": [
              moment().subtract(1, "month").startOf("month"),
              moment().subtract(1, "month").endOf("month"),
            ],
          },
        },
        cb
      );
      cb(start, end);

      $(".input-placeholder").mask("00/00/0000", {
        placeholder: "__/__/____",
      });
      $(".input-zip").mask("00000-000", {
        placeholder: "____-___",
      });
      $(".input-money").mask("#.##0,00", {
        reverse: true,
      });
      $(".input-phoneus").mask("(*************");
      $(".input-mixed").mask("AAA 000-S0S");
      $(".input-ip").mask("0ZZ.0ZZ.0ZZ.0ZZ", {
        translation: {
          Z: {
            pattern: /[0-9]/,
            optional: true,
          },
        },
        placeholder: "___.___.___.___",
      });
      // editor
      var editor = document.getElementById("editor");
      if (editor) {
        var toolbarOptions = [
          [
            {
              font: [],
            },
          ],
          [
            {
              header: [1, 2, 3, 4, 5, 6, false],
            },
          ],
          ["bold", "italic", "underline", "strike"],
          ["blockquote", "code-block"],
          [
            {
              header: 1,
            },
            {
              header: 2,
            },
          ],
          [
            {
              list: "ordered",
            },
            {
              list: "bullet",
            },
          ],
          [
            {
              script: "sub",
            },
            {
              script: "super",
            },
          ],
          [
            {
              indent: "-1",
            },
            {
              indent: "+1",
            },
          ], // outdent/indent
          [
            {
              direction: "rtl",
            },
          ], // text direction
          [
            {
              color: [],
            },
            {
              background: [],
            },
          ], // dropdown with defaults from theme
          [
            {
              align: [],
            },
          ],
          ["clean"], // remove formatting button
        ];
      }
      // Example starter JavaScript for disabling form submissions if there are invalid fields
      (function () {
        "use strict";
        window.addEventListener(
          "load",
          function () {
            // Fetch all the forms we want to apply custom Bootstrap validation styles to
            var forms = document.getElementsByClassName("needs-validation");
            // Loop over them and prevent submission
            var validation = Array.prototype.filter.call(
              forms,
              function (form) {
                form.addEventListener(
                  "submit",
                  function (event) {
                    if (form.checkValidity() === false) {
                      event.preventDefault();
                      event.stopPropagation();
                    }
                    form.classList.add("was-validated");
                  },
                  false
                );
              }
            );
          },
          false
        );
      })();
    </script>
    <script>
      var uptarg = document.getElementById("drag-drop-area");
      if (uptarg) {
        var uppy = Uppy.Core()
          .use(Uppy.Dashboard, {
            inline: true,
            target: uptarg,
            proudlyDisplayPoweredByUppy: false,
            theme: "dark",
            width: 770,
            height: 210,
            plugins: ["Webcam"],
          })
          .use(Uppy.Tus, {
            endpoint: "https://master.tus.io/files/",
          });
        uppy.on("complete", (result) => {
          console.log(
            "Upload complete! We’ve uploaded these files:",
            result.successful
          );
        });
      }
    </script>
    <script src="/assets/js/apps.js"></script>
    <script>
      window.dataLayer = window.dataLayer || [];

      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());
      gtag("config", "***********-1");
    </script>

    <script>
      //const signalRClient = new SignalRClient();
      window.addEventListener("offline", function () {
        console.error("Lost internet connection.");
        alert(
          "فقدت الاتصال بالإنترنت. الرجاء التحقق من إعدادات الشبكة والمحاولة مرة أخرى."
        );
      });
    </script>
    <script src="/assets/js/vendor/signalr/dist/browser/signalr.js"></script>
    <script src="/assets/js/vendor/signalr/dist/browser/signalr.min.js"></script>
    <script src="/assets/js/vendor/signalr-protocol-msgpack/dist/browser/signalr-protocol-msgpack.js"></script>
    <script src="/assets/js/vendor/signalr-protocol-msgpack/dist/browser/signalr-protocol-msgpack.min.js"></script>

    <script src="/assets/js/chat-js/appChat.js"></script>
    <script src="/assets/js/chat-js/date-utils.js"></script>
    <script src="/assets/js/chat-js/models.js"></script>
    <script src="/assets/js/chat-js/models/ApiResponse.js"></script>
    <script src="/assets/js/chat-js/models/ChatModel.js"></script>
    <script src="/assets/js/chat-js/models/MessageModel.js"></script>
    <script src="/assets/js/chat-js/dbManager.js"></script>
    <script src="/assets/js/chat-js/messageGenerator.js"></script>
    <script src="/assets/js/chat-js/AjaxManager.js"></script>
    <script src="/assets/js/chat-js/chatProcessor.js"></script>
    <script src="/assets/js/chat-js/contactProcessor.js"></script>
    <script src="/assets/js/chat-js/contactListManager.js"></script>

    <script src="/assets/js/chat-js/UserProcessor.js"></script>
    <script src="/assets/js/chat-js/UserListManager.js"></script>
    <script src="/assets/js/chat-js/groupdestination.js"></script>
    <script src="/assets/js/chat-js/services/groupservice.js"></script>

    <script src="/assets/js/chat-js/chatProfile.js"></script>
    <script src="/assets/js/chat-js/realTimeEventHandler.js"></script>
    <script src="/assets/js/chat-js/connectionStatusManager.js"></script>
    <script src="/assets/js/chat-js/chatListManager.js"></script>
    <script src="/assets/js/chat-js/signalRManager.js"></script>
    <script src="/assets/js/chat-js/fileUploadManager.js"></script>
    <script src="/assets/js/chat-js/fileInteractions.js"></script>
    <script src="/assets/js/chat-js/team/teamservice.js"></script>
    <script src="/assets/js/chat-js/team/teamlistmanager.js"></script>
    <script src="/assets/js/chat-js/voiceRecorder.js"></script>
    <script src="/assets/js/chat-js/scriptChats.js"></script>

    <script src="/assets/js/chat-js/appLoader.js"></script>

    <script type="module">
      import { Picker } from "https://esm.sh/emoji-picker-element@1.18.2";

      // $(document).ready(function () {

      const picker = new Picker({ locale: "en" });
      const emojiPickerWrapper = document.getElementById(
        "emoji-picker-wrapper"
      );
      emojiPickerWrapper.appendChild(picker);

      const input = document.getElementById("input-send-message");
      const emojiBtn = document.getElementById("sticker-icon");

      let isPickerVisible = false;

      emojiBtn.addEventListener("click", () => {
        // Toggle the visibility of the emoji picker
        isPickerVisible = !isPickerVisible;

        if (isPickerVisible) {
          // Position the emoji picker at the bottom of the viewport
          emojiPickerWrapper.style.position = "fixed"; // Position it fixed relative to the viewport
          emojiPickerWrapper.style.top = "auto"; // Reset the top position
          emojiPickerWrapper.style.bottom = "10%"; // Align the bottom of the picker with the bottom of the viewport
          emojiPickerWrapper.style.left = "34%"; // Adjust the left position if needed

          emojiPickerWrapper.style.display = "block"; // Show the picker
        } else {
          emojiPickerWrapper.style.display = "none"; // Hide the picker
        }
      });

      picker.addEventListener("emoji-click", (event) => {
        const emoji = event.detail.emoji.unicode;
        const inputField = input;

        // Get the current cursor position
        const start = inputField.selectionStart;
        const end = inputField.selectionEnd;

        // Insert emoji at cursor position
        inputField.value =
          inputField.value.substring(0, start) +
          emoji +
          inputField.value.substring(end);

        console.log(inputField.value);
        // Move the cursor to the end of the inserted emoji
        inputField.selectionStart = inputField.selectionEnd =
          start + emoji.length;

        inputField.focus(); // Refocus the input field

        emojiPickerWrapper.style.display = "none"; // Hide the picker after selection
        isPickerVisible = false; // Update visibility state
      });

      // Optional: Hide the picker if clicking outside
      $(document).click((event) => {
        if (
          !emojiPickerWrapper.contains(event.target) &&
          !emojiBtn.contains(event.target)
        ) {
          emojiPickerWrapper.style.display = "none";
          isPickerVisible = false; // Update visibility state
        }
      });
      // });
    </script>

    <script>
      $(".copyCode").click(function (e) {
        e.preventDefault();
        let temp = $("<input>");
        $("body").append(temp);
        temp.val($(this).text()).select();
        document.execCommand("copy");
        temp.remove();
        $(this).html(
          '<i class="fa fa-clipboard" aria-hidden="true"></i> Copied!'
        );
      });
    </script>
  </body>
</html>
