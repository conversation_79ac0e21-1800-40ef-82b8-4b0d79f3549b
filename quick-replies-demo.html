<!DOCTYPE html>
<html lang="en" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Quick Replies Demo - WhatsApp Business Style</title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="/assets/css/chatcss/style.css" />
    <link rel="stylesheet" href="/assets/css/chatcss/quick-replies.css" />

    <!-- Feather Icons -->
    <link
      href="https://unpkg.com/feather-icons@4.28.0/dist/feather.css"
      rel="stylesheet"
    />

    <style>
      body {
        margin: 0;
        padding: 20px;
        background: var(--appBackground);
        color: var(--h4);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
      }

      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        background: var(--side);
        border-radius: 8px;
        overflow: hidden;
      }

      .demo-header {
        padding: 20px;
        background: var(--secondary);
        border-bottom: 1px solid var(--border-right);
      }

      .demo-title {
        margin: 0;
        font-size: 24px;
        color: var(--h4);
      }

      .demo-subtitle {
        margin: 8px 0 0 0;
        color: var(--primary);
        font-size: 14px;
      }

      .demo-chat-area {
        height: 400px;
        background: var(--appBackground);
        position: relative;
        display: flex;
        flex-direction: column;
      }

      .demo-messages {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
      }

      .demo-message {
        background: var(--secondary);
        padding: 12px 16px;
        border-radius: 8px;
        margin-bottom: 12px;
        max-width: 70%;
      }

      .demo-message.sent {
        background: var(--chat);
        margin-left: auto;
        color: white;
      }

      .demo-input-area {
        padding: 16px;
        background: var(--secondary);
        border-top: 1px solid var(--border-right);
      }

      .chat-input-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
      }

      .chat-input-container {
        display: flex;
        align-items: center;
        gap: 8px;
        background: var(--block-footer);
        border-radius: 6px;
        padding: 8px;
      }

      .demo-input {
        flex: 1;
        background: transparent;
        border: none;
        outline: none;
        color: var(--h4);
        font-size: 15px;
        padding: 8px 12px;
      }

      .demo-input::placeholder {
        color: var(--primary);
      }

      .icons {
        background: transparent;
        border: none;
        color: var(--icons);
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        transition: background-color 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .icons:hover {
        background-color: var(--icon-focus);
      }

      .demo-controls {
        padding: 20px;
        background: var(--secondary);
        border-top: 1px solid var(--border-right);
      }

      .demo-button {
        background: var(--unread);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        margin-right: 10px;
        font-size: 14px;
      }

      .demo-button:hover {
        background: #00916a;
      }

      .demo-button.secondary {
        background: var(--block);
        color: var(--h4);
      }

      .demo-button.secondary:hover {
        background: var(--listItemHover);
      }

      .demo-info {
        background: var(--block);
        padding: 16px;
        border-radius: 6px;
        margin-bottom: 16px;
        border-left: 4px solid var(--unread);
      }

      .demo-info h4 {
        margin: 0 0 8px 0;
        color: var(--h4);
      }

      .demo-info p {
        margin: 0;
        color: var(--primary);
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div class="demo-container">
      <div class="demo-header">
        <h1 class="demo-title">Quick Replies Demo</h1>
        <p class="demo-subtitle">
          WhatsApp Business Web Style Quick Replies System
        </p>
      </div>

      <div class="demo-controls">
        <div class="demo-info">
          <h4>How to Test:</h4>
          <p>
            1. <strong>Slash Commands:</strong> Type "/" in the input field to
            see quick replies dropdown<br />
            2. <strong>Search:</strong> Type "/welcome" or "/thanks" to filter
            quick replies<br />
            3. <strong>Navigation:</strong> Use arrow keys to navigate, Enter to
            select, Escape to close<br />
            4. <strong>Button Access:</strong> Click the quick replies button
            (✓) for popup menu<br />
            5. <strong>Management:</strong> Click "Manage" to add, edit, or
            delete quick replies
          </p>
        </div>

        <button class="demo-button" onclick="addSampleMessage()">
          Add Sample Message
        </button>
        <button class="demo-button secondary" onclick="clearMessages()">
          Clear Messages
        </button>
        <button class="demo-button secondary" onclick="resetQuickReplies()">
          Reset Quick Replies
        </button>
      </div>

      <div class="demo-chat-area">
        <div class="demo-messages" id="demo-messages">
          <div class="demo-message">
            <strong>System:</strong> Quick Replies Demo loaded. Click the ✓
            button below to test quick replies!
          </div>
        </div>

        <div class="demo-input-area">
          <div class="chat-input-wrapper">
            <!-- Quick Replies Dropdown -->
            <div
              id="quick-replies-dropdown"
              class="quick-replies-dropdown-inline"
              style="display: none"
            >
              <div class="quick-replies-container">
                <div class="quick-replies-header-inline">
                  <div class="quick-replies-header-left">
                    <div class="quick-replies-icon">
                      <i class="fe fe-zap"></i>
                    </div>
                    <span class="quick-replies-title-inline"
                      >Quick Replies</span
                    >
                  </div>
                  <div class="quick-replies-header-right">
                    <button
                      class="quick-replies-manage-btn-inline"
                      onclick="showQuickRepliesManagement()"
                      title="Manage Quick Replies"
                    >
                      <i class="fe fe-settings"></i>
                    </button>
                    <button
                      class="quick-replies-close"
                      onclick="hideQuickRepliesDropdown()"
                    >
                      <i class="fe fe-x"></i>
                    </button>
                  </div>
                </div>
                <div
                  class="quick-replies-list-inline"
                  id="quick-replies-list-inline"
                >
                  <!-- Quick replies will be populated here -->
                </div>
              </div>
            </div>

            <div class="chat-input-container" id="reply-area">
              <button role="button" class="icons" id="sticker-icon">
                <svg viewBox="0 0 24 24" width="24" height="24">
                  <path
                    fill="currentColor"
                    d="M9.153 11.603c.795 0 1.439-.879 1.439-1.962s-.644-1.962-1.439-1.962-1.439.879-1.439 1.962.644 1.962 1.439 1.962zm-3.204 1.362c-.026-.307-.131 5.218 6.063 5.551 6.066-.25 6.066-5.551 6.066-5.551-6.078 1.416-12.129 0-12.129 0zm11.363 1.108s-.669 1.959-5.051 1.959c-3.505 0-5.388-1.164-5.607-1.959 0 0 5.912 1.055 10.658 0zM11.804 1.011C5.609 1.011.978 6.033.978 12.228s4.826 10.761 11.021 10.761S23.02 18.423 23.02 12.228c.001-6.195-5.021-11.217-11.216-11.217zM12 21.354c-5.273 0-9.381-3.886-9.381-9.159s3.942-9.548 9.215-9.548 9.548 4.275 9.548 9.548c-.001 5.272-4.109 9.159-9.382 9.159zm3.108-9.751c.795 0 1.439-.879 1.439-1.962s-.644-1.962-1.439-1.962-1.439.879-1.439 1.962.644 1.962 1.439 1.962z"
                  />
                </svg>
              </button>

              <!-- Quick Replies Button will be inserted here by the manager -->

              <input
                type="text"
                id="input-send-message"
                class="demo-input"
                placeholder="Type a message..."
              />

              <button class="icons" onclick="sendDemoMessage()">
                <svg viewBox="0 0 24 24" width="24" height="24">
                  <path
                    fill="currentColor"
                    d="M1.101 21.757L23.8 12.028 1.101 2.3l.011 7.912 13.623 1.816-13.623 1.817-.011 7.912z"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Required Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Mock objects for demo -->
    <script>
      // Mock global objects for demo
      window.chat = { id: "demo-chat-001" };
      window.ChatProcessor = {
        processedData: {
          currentUser: {
            id: "demo-user-001",
            tokenInfo: { token: "demo-token" },
          },
        },
      };

      // Mock functions
      window.saveSendingMessage = function (messageData) {
        console.log("Mock saveSendingMessage called with:", messageData);
        addDemoMessage(messageData.messageText || "Message sent", true);
      };

      window.getAccessToken = function () {
        return { token: "demo-token" };
      };

      // Demo functions
      function addDemoMessage(text, isSent = false) {
        const messagesContainer = document.getElementById("demo-messages");
        const messageDiv = document.createElement("div");
        messageDiv.className = `demo-message ${isSent ? "sent" : ""}`;
        messageDiv.innerHTML = `<strong>${
          isSent ? "You" : "Demo"
        }:</strong> ${text}`;
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }

      function sendDemoMessage() {
        const input = document.getElementById("input-send-message");
        if (input.value.trim()) {
          addDemoMessage(input.value, true);
          input.value = "";
        }
      }

      function addSampleMessage() {
        const messages = [
          "Hello! How can I help you today?",
          "Thank you for your message.",
          "Our business hours are 9 AM to 6 PM.",
          "Please let me know if you need anything else.",
        ];
        const randomMessage =
          messages[Math.floor(Math.random() * messages.length)];
        addDemoMessage(randomMessage, false);
      }

      function clearMessages() {
        const messagesContainer = document.getElementById("demo-messages");
        messagesContainer.innerHTML =
          '<div class="demo-message"><strong>System:</strong> Messages cleared. Quick Replies are still available!</div>';
      }

      async function resetQuickReplies() {
        if (window.DBManager && window.initializeSampleQuickReplies) {
          await window.DBManager.clearQuickReplies();
          await window.initializeSampleQuickReplies();
          addDemoMessage(
            "Quick replies have been reset to default samples.",
            false
          );
        }
      }

      // Global function to hide quick replies dropdown
      window.hideQuickRepliesDropdown = function () {
        if (window.quickRepliesManager) {
          window.quickRepliesManager.hideInlineDropdown();
        }
      };

      // Global function to show quick replies management
      window.showQuickRepliesManagement = function () {
        if (window.quickRepliesManager) {
          window.quickRepliesManager.showManagementModal();
          window.quickRepliesManager.hideInlineDropdown();
        }
      };

      // Handle Enter key
      document
        .getElementById("input-send-message")
        .addEventListener("keypress", function (e) {
          if (e.key === "Enter") {
            sendDemoMessage();
          }
        });
    </script>

    <!-- Database and Quick Replies Scripts -->
    <script src="/assets/js/chat-js/dbManager.js"></script>
    <script src="/assets/js/chat-js/AjaxManager.js"></script>
    <script src="/assets/js/chat-js/quickRepliesManager.js"></script>
    <script src="/assets/js/chat-js/quickRepliesInit.js"></script>

    <!-- Initialize Quick Replies -->
    <script>
      document.addEventListener("DOMContentLoaded", async function () {
        try {
          // Initialize database
          await DBManager.initializeDB();

          // Initialize Quick Replies Manager
          if (window.quickRepliesManager) {
            await window.quickRepliesManager.initialize();
            console.log("Quick Replies Manager initialized for demo");

            // Initialize sample data
            if (window.initializeSampleQuickReplies) {
              await window.initializeSampleQuickReplies();
            }

            addDemoMessage(
              'Quick Replies system is ready! Type "/" to test slash commands or click the ✓ button.',
              false
            );
          } else {
            addDemoMessage("Error: Quick Replies Manager not found.", false);
          }
        } catch (error) {
          console.error("Demo initialization error:", error);
          addDemoMessage(
            "Error initializing Quick Replies demo: " + error.message,
            false
          );
        }
      });
    </script>
  </body>
</html>
